<template>
  <div class="container">
    <coupon-meta-search @form-search="searchCouponMetas" @reset="resetParamsAndData" />
    <coupon-meta-table ref="table" />  
  </div>
</template>

<script setup name="CouponMeta">
import CouponMetaSearch from './coupon/CouponMetaSearch.vue';
import CouponMetaTable from './coupon/CouponMetaTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchCouponMetas = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
