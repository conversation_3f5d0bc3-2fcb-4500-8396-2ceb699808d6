<template>
  <el-card style="margin-top: 10px; margin-bottom: -10px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加临停规则</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="180">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3"
              @click="handleEdit(scope.row.id)">
              编辑
            </el-button>
            <el-button link type="primary" v-if="scope.row.audit_state == 2" @click="handleDetail(scope.row.id)"> 查看
            </el-button>
            <el-button link v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" type="primary"
              @click="submitAudit(scope.row.id)">
              提交审核
            </el-button>
            <el-button link v-if="scope.row.audit_state == 2 && scope.row.valid_state == 0" type="primary"
              @click="cancel(scope.row.id)">
              撤销
            </el-button>
            <el-button link type="danger" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3"
              @click="handleDel(scope.row.id)">
              删除
            </el-button>
            <el-button link type="primary" @click="testParkFee(scope.row.id)">预览计费规则</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="规则名称" align="center" min-width="200" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" min-width="120" />
        <el-table-column prop="park_region_name" label="关联子场" align="center" min-width="200" />
        <el-table-column prop="cross_time_fee_type_desc" label="跨分段计费方式" align="center" min-width="180" />
        <el-table-column prop="cross_time_start_fee_type_desc" label="跨分段起收计费方式" align="center" min-width="180" />
        <el-table-column label="单笔封顶费用" align="center" min-width="120">
          <template v-slot="scope">
            <span v-if="scope.row.bill_top_mode == 1">{{ scope.row.bill_top_money }}</span>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="首日封顶" align="center" min-width="100">
          <template v-slot="scope">
            <span v-if="scope.row.first_day_limit_mode == 1">{{ scope.row.first_day_limit_money }}</span>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column label="每日封顶" align="center" min-width="100">
          <template v-slot="scope">
            <span v-if="scope.row.each_day_limit_mode == 1">{{ scope.row.each_day_limit_money }}</span>
            <span v-else>无</span>
          </template>
        </el-table-column>
        <el-table-column prop="valid_start_time" label="生效日期" align="center" min-width="200" />
        <el-table-column label="生效状态" align="center" min-width="120">
          <template v-slot="scope">
            <span v-if="scope.row.valid_state == 1" style="color: #02860f">{{ scope.row.valid_state_desc }}</span>
            <span v-else style="color: #fb001e"> {{ scope.row.valid_state_desc }}</span>
          </template>
        </el-table-column>
        <el-table-column label="审核状态" align="center" min-width="120">
          <template v-slot="scope">
            <span v-if="scope.row.audit_state == 2" style="color: #02860f">{{ scope.row.audit_state_desc }}</span>
            <span v-else style="color: #fb001e"> {{ scope.row.audit_state_desc }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </el-card>

  <el-drawer v-model="previewState.drawerVisible" :with-header="false">
    <div style="display: flex">
      <span style="display: inline-block; height: 32px; line-height: 32px">停车时间：</span>
      <el-date-picker v-model="previewState.dateRange" type="datetimerange" range-separator="至"
        start-placeholder="入场开始时间" end-placeholder="入场结束时间" style="width: 100px" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss" />&ensp;
      <el-button type="primary" @click="previewCalcModel">预 览</el-button>
    </div>
    <div class="title">基本信息</div>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">车场ID：</span>
          <span class="value">{{ previewState.data.park_id }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">车场名称：</span>
          <span class="value">{{ previewState.data.park_name }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">子场ID：</span>
          <span class="value">{{ previewState.data.park_region_id }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">子场名称：</span>
          <span class="value">{{ previewState.data.park_region_name }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">收费金额：</span>
          <span class="value" style="color: #fb001e">{{ previewState.data.total_money }}&ensp;元</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">停车时长：</span>
          <span class="value">{{ previewState.data.duration_text }}</span>
        </div>
      </el-col>
    </el-row>
    <div class="title">计费明细</div>
    <el-table :data="previewState.data.details" border style="margin-top: 10px; height: calc(100vh - 338px)">
      <el-table-column prop="start_time" label="开始时间" align="center"></el-table-column>
      <el-table-column prop="end_time" label="结束时间" align="center"></el-table-column>
      <el-table-column prop="money" label="计费金额" align="center">
        <template v-slot="scope">
          <span style="color: #fb001e">{{ scope.row.money }}</span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="handleDrawerClose">关 闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script name="ParkFeeTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parkFeeService from '@/service/park/ParkFeeService';
import { activeRouteTab } from '@/utils/tabKit';
import { getToken } from '@/utils/common';
import { getIamTokenOpen, getOpenUrl, getIamAndNormal } from '@/utils/iamFlow';
// import { useUser } from '@/stores/user';

// const user = useUser();
const tableData = ref([]);
const park_id = ref('');
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: '',
    page: 1,
    limit: 30
  }
});

// 预览计费
const previewState = reactive({
  drawerVisible: false,
  form: {
    fee_model_to_park_id: undefined,
    in_time: undefined,
    out_time: undefined
  },
  data: {
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    park_in_record_id: undefined,
    total_money: 0,
    duration: undefined,
    duration_text: undefined,
    details: []
  },
  dateRange: []
});
onMounted(() => {
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log("接收")
  getList(data.queryParams)
}

const getList = (params) => {
  loading.value = true;
  console.log(params);
  if (params.park_id != undefined && params.park_id != '') {
    park_id.value = params.park_id;
  } else {
    params.park_id = park_id.value;
  }
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkFeeService.pagingParkFees(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  activeRouteTab({
    path: '/park/parkFee/parkFeeAdd',
    query: {
      park_id: park_id.value
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleEdit = (id) => {
  activeRouteTab({
    path: '/park/parkFee/parkFeeEdit',
    query: {
      feeId: id,
      park_id: park_id.value
    }
  });
};
const handleDel = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkFeeService
      .deleteParkFee(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleDetail = (id) => {
  activeRouteTab({
    path: '/park/parkFee/parkFeeDetail',
    query: {
      feeId: id,
      parkId: park_id.value
    }
  });
};

//审核
const submitAudit = (id) => {
  // 确认后跳转至临时规则申请页面
  if (!getIamAndNormal(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/TemporaryRule?id=${id}&parkToken=${getToken()}`))) {
    ElMessageBox.confirm('请确认是否提交审核？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      parkFeeService
        .submitAuditParkFee(id)
        .then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }


};

//撤销
const cancel = (id) => {
  ElMessageBox.confirm('请确认是否撤销？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkFeeService
      .cancelParkFee(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//计费模型测试
const testParkFee = (val) => {
  previewState.form = {
    fee_model_to_park_id: val,
    in_time: undefined,
    out_time: undefined
  };
  previewState.data = {
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    park_in_record_id: undefined,
    total_money: 0,
    duration: undefined,
    duration_text: undefined,
    details: []
  };
  previewState.dateRange = [];
  previewState.drawerVisible = true;
};

const previewCalcModel = () => {
  if (previewState.dateRange.length <= 0) {
    ElMessage({
      message: '请选择时间段',
      type: 'warning'
    });
    return false;
  }
  previewState.form.in_time = previewState.dateRange[0];
  previewState.form.out_time = previewState.dateRange[1];
  parkFeeService.previewCalcModel(previewState.form).then((response) => {
    console.log(response);
    if (response.success === true) {
      previewState.data = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const handleDrawerClose = () => {
  previewState.drawerVisible = false;
};

defineExpose({
  getList
});
</script>

<style lang="scss" scoped>
.desc {
  line-height: 36px;
}

.label {
  color: rgba(0, 0, 0, 0.65);
  padding-left: 10px;
  padding-right: 10px;
}

.value {
  color: rgba(0, 0, 0, 0.8);
}

.title {
  height: 32px;
  line-height: 32px;
  width: 100%;
  background-color: #eaeaea;
  padding-left: 8px;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 10px;
}

:deep(.el-drawer__body) {
  padding: 10px !important;
}
</style>
