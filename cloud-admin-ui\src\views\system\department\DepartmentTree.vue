<template>
  <el-card class="department-tree" shadow="never">
    <template #header>
      <div class="card-header">
        <span>部门树</span>
      </div>
    </template>
    <el-tree
      ref="departmentTree"
      :data="treeData.departmentTree"
      :check-strictly="true"
      default-expand-all
      :expand-on-click-node="false"
      accordion
      node-key="id"
      highlight-current
      :filter-node-method="filterNode"
      @node-click="nodeClick"
    />
  </el-card>
</template>

<script setup>
import departmentService from '@/service/system/DepartmentService';
import { ElMessage, ElNotification } from 'element-plus';
import { reactive, onMounted } from 'vue';

const queryDept = reactive({
  parent_department_id: 0,
  name: ''
});
const treeData = reactive({
  departmentTree: []
});
const emits = defineEmits(['initData']);

onMounted(() => {
  initDepartmentTree();
});
const initDepartmentTree = () => {
  departmentService.departmentTree().then((res) => {
    if (res.success == true) {
      treeData.departmentTree = res.data;
    } else {
      ElMessage({
        message: res.detail_message,
        type: 'error'
      });
    }
  });
};
const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};
const nodeClick = (data) => {
  console.log(data);
  queryDept.parent_department_id = data.id;
  queryDept.name = data.label;
  emits('initData', queryDept);
};
defineExpose({ initDepartmentTree });
</script>

<style lang="scss" scoped>
.department-tree {
  height: calc(100vh - 123px);
}

:deep(.el-card__header) {
  padding: 10px 10px;
}
</style>
