import $ from '@/utils/axios';

/**
 * 页面接口层
 */

/**
 * 添加页面
 * @returns {*}
 */
export const createPage = (data) => {
  return $({
    url: '/console/page/createPage',
    method: 'post',
    data
  });
};

/**
 * 修改页面
 * @returns {*}
 */
export const updatePage = (data) => {
  return $({
    url: '/console/page/updatePage',
    method: 'post',
    data
  });
};

/**
 * 删除页面
 * @returns {*}
 */
export const deletePage = (menuId) => {
  return $({
    url: '/console/page/deletePage/' + menuId,
    method: 'get'
  });
};

/**
 * 获取页面
 * @returns {*}
 */
export const getPageById = (id) => {
  return $({
    url: '/console/page/getPageById/' + id,
    method: 'get'
  });
};
