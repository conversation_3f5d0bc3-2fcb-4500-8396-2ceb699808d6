/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 业务审核表格数据
export const pagingBizAudits = (data) => {
  return $({
    url: '/console/park/audit/biz/pagingBizAudits',
    method: 'post',
    data
  });
};

// 获取业务审核表单
export const getBizAuditFormById = (id) => {
  return $({
    url: '/console/park/audit/biz/getBizAuditFormById/' + id,
    method: 'get'
  });
};

// 审核通过
export const passAudit = (data) => {
  return $({
    url: '/console/park/audit/biz/passAudit',
    method: 'post',
    data
  });
};
export const passAudits = (data) => {
  return $({
    url: '/console/park/audit/biz/passAudits',
    method: 'post',
    data
  });
};

// 审核拒绝
export const rejectAudit = (data) => {
  return $({
    url: '/console/park/audit/biz/rejectAudit',
    method: 'post',
    data
  });
};
export const rejectAudits = (data) => {
  return $({
    url: '/console/park/audit/biz/rejectAudits',
    method: 'post',
    data
  });
};

// 获取待审核业务个数
export const getWaitAuditCount = (data) => {
  return $({
    url: '/console/park/audit/biz/getWaitAuditCount',
    method: 'post',
    data
  });
};
