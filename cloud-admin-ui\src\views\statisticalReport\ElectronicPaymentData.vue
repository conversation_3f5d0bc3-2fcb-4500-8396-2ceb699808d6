<template>
  <div class="container">
    <electronic-payment-data-search @form-search="searchElectronicPaymentDataList" @reset="resetParamsAndData" />
    <electronic-payment-data-table ref="table" />
  </div>
</template>

<script name="ElectronicPaymentData" setup>
import ElectronicPaymentDataSearch from './electronicPaymentData/ElectronicPaymentDataSearch.vue';
import ElectronicPaymentDataTable from './electronicPaymentData/ElectronicPaymentDataTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchElectronicPaymentDataList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
