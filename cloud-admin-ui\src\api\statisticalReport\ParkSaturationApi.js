/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询饱和度
// POST /console/statistics/park/space/use/saturation/selectByParkAndTimeRange
// 接口ID：304194305
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-304194305
export const getParkSpaceSaturation = (data) => {
  return $({
    url: '/console/statistics/park/space/use/saturation/selectByParkAndTimeRange',
    method: 'post',
    data
  });
};

// 导出饱和度
// POST /console/statistics/park/space/use/saturation/exportParkSpaceSaturationExcel
// 接口ID：304238329
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-304238329
export const exportParkSpaceSaturationExcel = (data) => {
  return $({
    url: '/console/statistics/park/space/use/saturation/exportParkSpaceSaturationExcel',
    method: 'post',
    data
  });
};
