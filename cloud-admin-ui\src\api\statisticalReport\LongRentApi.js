/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询长租收入全览
export const pagingLongRent = (data) => {
  return $({
    url: '/console/statistics/income/total/pagingRentIncomeByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/income/total/exportRentIncomeByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportHzData = (data) => {
  return $({
    url: '/console/statistics/income/total/exportRentIncomeSummaryByPeriod',
    method: 'post',
    data
  });
}
