/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找设备
export const pagingDevice = (data) => {
  return $({
    url: '/console/park/device/pagingParkDevice',
    method: 'post',
    data
  });
};

// 新建设备
export const createDevice = (data) => {
  return $({
    url: '/console/park/device/createParkDevice',
    method: 'post',
    data
  });
};

// 修改设备
export const updateDevice = (data) => {
  return $({
    url: '/console/park/device/updateParkDevice',
    method: 'post',
    data
  });
};

// 删除设备
export const deleteDevice = (id) => {
  return $({
    url: '/console/park/device/deleteParkDevice/' + id,
    method: 'post'
  });
};

// 启用
export const enableParkDevice = (id) => {
  return $({
    url: '/console/park/device/enableParkDevice/' + id,
    method: 'post'
  });
};

// 禁用
export const disableParkDevice = (id) => {
  return $({
    url: '/console/park/device/disableParkDevice/' + id,
    method: 'post'
  });
};

// 获取设备列表
export const listParkDevice = (data) => {
  return $({
    url: '/console/park/device/listParkDevice',
    method: 'post',
    data
  });
};

// 子场列表
export const listParkRegion = (parkId) => {
  return $({
    url: '/console/park/region/listParkRegion/' + parkId,
    method: 'get'
  });
};

// 通道列表
export const listParkGateway = (parkRegionId) => {
  return $({
    url: '/console/park/gateway/listParkGateway/' + parkRegionId,
    method: 'get'
  });
};
