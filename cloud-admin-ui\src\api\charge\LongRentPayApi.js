/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询长租缴费
export const pagingLongRentPay = (data) => {
  return $({
    url: '/console/park/fee/rentSpaceRecords/pagingRentSpaceRecords',
    method: 'post',
    data
  });
};
// 线上申请退款
export const applyRefund = (data) => {
  return $({
    url: '/console/park/fee/rentSpaceRecords/refundApply',
    method: 'post',
    data
  });
};
// 线下申请退款
export const offlineRefundApply = (data) => {
  return $({
    url: '/console/park/fee/rentSpaceRecords/offlineRefundApply',
    method: 'post',
    data
  });
};
// 导出长租缴费
export const exportLongRentPay = (data) => {
  return $({
    url: '/console/park/fee/rentSpaceRecords/exportRentSpaceRecords',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};
// 计算退款天数和金额
export const calculateRefundAmount = (data) => {
  return $({
    url: '/console/park/fee/rentSpaceRecords/calculateRefundAmount',
    method: 'post',
    data
  });
};
