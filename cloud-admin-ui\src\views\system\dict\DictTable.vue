<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <div>
        <el-button type="primary" @click="handleCreate()">新 增</el-button>
        <el-button type="danger" @click="batchDelete()">批量删除</el-button>
      </div>
      <div>
        <el-input v-model="data.queryParams.name" class="w-50 m-2" placeholder="字典名称" @keyup.enter="searchDicts" clearable>
          <template #suffix
            ><el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="字典名称" align="center" />
        <el-table-column prop="code" label="字典编码" align="center" />
        <el-table-column prop="value" label="字典值" align="center" />
        <el-table-column prop="weight" label="排序" align="center" />
        <el-table-column prop="updated_at" label="更新时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="新增字典" v-model="dictCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="50px" :rules="data.rules" :model="data.form">
          <el-form-item prop="name" label="标签">
            <el-input v-model="data.form.name" />
          </el-form-item>
          <el-form-item prop="code" label="编码">
            <el-input v-model="data.form.code" />
          </el-form-item>
          <el-form-item prop="value" label="值">
            <el-input v-model="data.form.value" type="number" />
          </el-form-item>
          <el-form-item prop="weight" label="排序">
            <el-input v-model="data.form.weight" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createDict(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改字典" v-model="dictUpdateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="name" label="标签">
            <el-input v-model="data.updateForm.name" />
          </el-form-item>
          <el-form-item prop="code" label="编码">
            <el-input v-model="data.updateForm.code" />
          </el-form-item>
          <el-form-item prop="value" label="值">
            <el-input v-model="data.updateForm.value" />
          </el-form-item>
          <el-form-item prop="weight" label="排序">
            <el-input v-model="data.updateForm.weight" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateDict(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="DictTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import dictService from '@/service/system/DictService';
import { useRoute } from 'vue-router';
import { Search } from '@element-plus/icons-vue';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const dictIds = ref([]);
const total = ref(0);
const route = useRoute();
const dictCreateDialogVisible = ref(false);
const dictUpdateDialogVisible = ref(false);
const data = reactive({
  params: route.query,
  queryParams: {
    id: '',
    page: 1,
    limit: 30
  },
  form: {
    name: undefined,
    code: undefined,
    value: undefined,
    weight: undefined,
    dict_type_id: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入标签',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入编码',
        trigger: 'blur'
      }
    ],
    value: [
      {
        required: true,
        message: '请输入值',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  if (data.params.dictTypeId !== null && data.params.dictTypeId !== undefined) {
    data.queryParams.id = data.params.dictTypeId;
    data.form.dict_type_id = data.params.dictTypeId;
  }
  getList(data.queryParams);
});

// 右上角字典名称筛查条件
const searchDicts = () => {
  getList(data.queryParams);
};

// 分页查询字典列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  dictService.pagingDict(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};
// 新建字典
const handleCreate = () => {
  data.form = {
    name: undefined,
    code: undefined,
    value: undefined,
    weight: undefined,
    dict_type_id: data.params.dictTypeId
  };
  dictCreateDialogVisible.value = true;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建字典
const createDict = (addForm) => {
  addForm.validate().then(() => {
    dictService
      .createDict(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          addForm.resetFields();
          getList(data.queryParams);
          dictCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除字典
const handleDelete = (val) => {
  dictIds.value[0] = val;
  batchDelete(val);
};
const handleSelectionChange = (val) => {
  dictIds.value = val;
};
const batchDelete = () => {
  if (dictIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的字典',
      type: 'warning'
    });
  } else {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const pushIds = [];
      for (let i = 0; i < dictIds.value.length; i++) {
        pushIds.push(parseInt(dictIds.value[i].id));
      }
      data.form.id = pushIds;
      dictService
        .deleteDict(data.form)
        .then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.message,
              type: 'error'
            });
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }
};
// 修改字典
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    value: row.value,
    weight: row.weight,
    dict_type_id: data.params.dictTypeId
  };
  dictUpdateDialogVisible.value = true;
};
// 提交并保存修改字典
const updateDict = (editForm) => {
  editForm.validate().then(() => {
    dictService
      .updateDict(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          dictUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  dictCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  dictUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
