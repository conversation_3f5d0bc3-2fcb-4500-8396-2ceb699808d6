/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 坐席管理-列表
export const watchSeatsList = (data) => {
  return $({
    url: '/console/watch/seats/list',
    method: 'post',
    data
  });
};

// 坐席管理-新增
export const watchSeatsAdd = (data) => {
  return $({
    url: '/console/watch/seats/add',
    method: 'post',
    data
  });
};

// 坐席管理-启用/禁用
export const watchSeatsEditStatus = (data) => {
  return $({
    url: '/console/watch/seats/editStatus',
    method: 'post',
    data
  });
};

// 坐席管理-分配值守员工
export const watchSeatsAssignEmployees = (data) => {
  return $({
    url: '/console/watch/seats/assignEmployees',
    method: 'post',
    data
  });
};

// 坐席管理-查看详情
export const watchSeatsDetail = (data) => {
  return $({
    url: '/console/watch/seats/detail/' + data,
    method: 'get'
  });
};

// 坐席管理-查看值守员工列表
export const watchListEmployees = (name) => {
  return $({
    url: '/console/employee/watchListEmployees?name=' + (name ? name : ''),
    method: 'get'
  });
};
