/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询自然周周转率
export const pagingNaturalWeek = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/listNatureTurnroundRates',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/exportNatureTurnroundRates',
    method: 'post',
    data
  });
};
