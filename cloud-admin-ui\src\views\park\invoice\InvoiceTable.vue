<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加发票抬头</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template #default="scope">
            <el-button link type="success" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
            <el-button link type="primary" @click="handleRegister(scope.row)"> 票通注册 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="title" label="发票抬头" align="center" />
        <el-table-column prop="tax_no" label="发票税号" align="center" />
        <el-table-column prop="company_address" label="公司地址" align="center" />
        <el-table-column prop="company_mobile" label="公司电话" align="center" />
        <el-table-column prop="company_bank_account" label="公司开户行" align="center" />
        <el-table-column prop="account_no" label="开户行账号" align="center" />
        <el-table-column prop="einvoice_desc" label="是否支持电子票" align="center" />
        <el-table-column prop="especial_invoice_desc" label="是否支持电子专票" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加发票抬头" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="600px">
        <el-form ref="addForm" label-width="140px" :rules="data.rules" :model="data.form">
          <el-form-item prop="title" label="发票抬头">
            <el-input v-model="data.form.title" maxlength="50" placeholder="请输入发票抬头" />
          </el-form-item>
          <el-form-item prop="tax_no" label="发票税号">
            <el-input v-model="data.form.tax_no" maxlength="15" placeholder="请输入发票税号" />
          </el-form-item>
          <el-form-item prop="company_address" label="公司地址">
            <el-input v-model="data.form.company_address" maxlength="50" placeholder="请输入公司地址" />
          </el-form-item>
          <el-form-item prop="company_mobile" label="公司电话">
            <el-input v-model="data.form.company_mobile" maxlength="11" placeholder="请输入公司电话" />
          </el-form-item>
          <el-form-item prop="company_bank_account" label="公司开户行">
            <el-input v-model="data.form.company_bank_account" maxlength="20" placeholder="请输入公司开户行" />
          </el-form-item>
          <el-form-item prop="account_no" label="开户行账号">
            <el-input v-model="data.form.account_no" maxlength="20" placeholder="请输入开户行账号" />
          </el-form-item>
          <el-form-item prop="invoice" label="是否支持电子票">
            <el-radio-group v-model="data.form.invoice">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="special_invoice" label="是否支持电子专票">
            <el-radio-group v-model="data.form.special_invoice">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="tax_rate" label="税率">
            <el-input v-model="data.form.tax_rate" maxlength="50" placeholder="请输入税率" />
          </el-form-item>
          <el-form-item prop="tax_cod" label="税收分类编码">
            <el-input v-model="data.form.tax_cod" maxlength="50" placeholder="请输入税收分类编码" />
          </el-form-item>
          <el-form-item prop="terminal_code" label="税盘号">
            <el-input v-model="data.form.terminal_code" maxlength="50" placeholder="请输入税盘号" />
          </el-form-item>
          <el-form-item prop="einvoic_req" label="发票平台">
            <el-select v-model="data.form.einvoic_req" placeholder="发票平台" clearable>
              <el-option v-for="item in einvoicReq" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="drawer" label="开票人">
            <el-input v-model="data.form.drawer" maxlength="20" placeholder="请输入开票人" />
          </el-form-item>
          <el-form-item prop="payee" label="收款人">
            <el-input v-model="data.form.payee" maxlength="20" placeholder="请输入收款人" />
          </el-form-item>
          <el-form-item prop="checker" label="复核人">
            <el-input v-model="data.form.checker" maxlength="20" placeholder="请输入复核人" />
          </el-form-item>
          <el-form-item prop="legal_person_name" label="法人名称">
            <el-input v-model="data.form.legal_person_name" maxlength="20" placeholder="请输入法人名称" />
          </el-form-item>
          <el-form-item prop="contacts_name" label="联系人名称">
            <el-input v-model="data.form.contacts_name" maxlength="20" placeholder="请输入联系人名称" />
          </el-form-item>
          <el-form-item prop="contacts_email" label="联系人邮箱">
            <el-input v-model="data.form.contacts_email" placeholder="请输入联系人邮箱" />
          </el-form-item>
          <el-form-item prop="contacts_phone" label="联系人电话">
            <el-input v-model="data.form.contacts_phone" maxlength="20" placeholder="请输入联系人电话" />
          </el-form-item>
          <el-form-item prop="region_code" label="地区编码">
            <el-input v-model="data.form.region_code" maxlength="20" placeholder="请输入地区编码" />
          </el-form-item>
          <el-form-item prop="city_name" label="城市名称">
            <el-input v-model="data.form.city_name" maxlength="20" placeholder="请输入城市名称" />
          </el-form-item>
          <el-form-item prop="tax_control_device_type" label="税控设备类型">
            <el-select v-model="data.form.tax_control_device_type" placeholder="请选择税控设备类型" clearable>
              <el-option v-for="item in einvoicDeviceType" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="invoice_issue_kind_code" label="开具发票种类">
            <el-select v-model="data.form.invoice_issue_kind_code" placeholder="请选择发票种类" clearable>
              <el-option v-for="item in einvoicKindCode" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="account" label="电子税局登录账号">
            <el-input v-model="data.form.account" maxlength="20" placeholder="请输入电子税局登录账号" />
          </el-form-item>
          <el-form-item prop="cross_city" label="跨地市标志">
            <el-radio-group v-model="data.form.cross_city">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="tax_registration_certificate_url" label="营业执照">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="data.form.tax_registration_certificate_url" :src="data.form.tax_registration_certificate_url" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createInvoice(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="编辑发票抬头" v-model="updateDialogVisible" :close-on-click-modal="false" width="600px" @close="closeEditDialog(editForm)">
        <el-form ref="editForm" label-width="140px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="title" label="发票抬头">
            <el-input v-model="data.updateForm.title" maxlength="50" placeholder="请输入发票抬头" />
          </el-form-item>
          <el-form-item prop="tax_no" label="发票税号">
            <el-input v-model="data.updateForm.tax_no" placeholder="请输入发票税号" />
          </el-form-item>
          <el-form-item prop="company_address" label="公司地址">
            <el-input v-model="data.updateForm.company_address" maxlength="50" placeholder="请输入公司地址" />
          </el-form-item>
          <el-form-item prop="company_mobile" label="公司电话">
            <el-input v-model="data.updateForm.company_mobile" maxlength="15" placeholder="请输入公司电话" />
          </el-form-item>
          <el-form-item prop="company_bank_account" label="公司开户行">
            <el-input v-model="data.updateForm.company_bank_account" maxlength="20" placeholder="请输入公司开户行" />
          </el-form-item>
          <el-form-item prop="account_no" label="开户行账号">
            <el-input v-model="data.updateForm.account_no" maxlength="20" placeholder="请输入开户行账号" />
          </el-form-item>
          <el-form-item prop="invoice" label="是否支持电子票">
            <el-radio-group v-model="data.updateForm.invoice">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="special_invoice" label="是否支持电子专票">
            <el-radio-group v-model="data.updateForm.special_invoice">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="tax_rate" label="税率">
            <el-input v-model="data.updateForm.tax_rate" maxlength="50" placeholder="请输入税率" />
          </el-form-item>
          <el-form-item prop="tax_cod" label="税收分类编码">
            <el-input v-model="data.updateForm.tax_cod" maxlength="50" placeholder="请输入税收分类编码" />
          </el-form-item>
          <el-form-item prop="terminal_code" label="税盘号">
            <el-input v-model="data.updateForm.terminal_code" maxlength="50" placeholder="请输入税盘号" />
          </el-form-item>
          <el-form-item prop="einvoic_req" label="发票平台">
            <el-select v-model="data.updateForm.einvoic_req" placeholder="发票平台" clearable>
              <el-option v-for="item in einvoicReq" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="drawer" label="开票人">
            <el-input v-model="data.updateForm.drawer" maxlength="20" placeholder="请输入开票人" />
          </el-form-item>
          <el-form-item prop="payee" label="收款人">
            <el-input v-model="data.updateForm.payee" maxlength="20" placeholder="请输入收款人" />
          </el-form-item>
          <el-form-item prop="checker" label="复核人">
            <el-input v-model="data.updateForm.checker" maxlength="20" placeholder="请输入复核人" />
          </el-form-item>
          <el-form-item prop="legal_person_name" label="法人名称">
            <el-input v-model="data.updateForm.legal_person_name" maxlength="20" placeholder="请输入法人名称" />
          </el-form-item>
          <el-form-item prop="contacts_name" label="联系人名称">
            <el-input v-model="data.updateForm.contacts_name" maxlength="20" placeholder="请输入联系人名称" />
          </el-form-item>
          <el-form-item prop="contacts_email" label="联系人邮箱">
            <el-input v-model="data.updateForm.contacts_email" placeholder="请输入联系人邮箱" />
          </el-form-item>
          <el-form-item prop="contacts_phone" label="联系人电话">
            <el-input v-model="data.updateForm.contacts_phone" maxlength="20" placeholder="请输入联系人电话" />
          </el-form-item>
          <el-form-item prop="region_code" label="地区编码">
            <el-input v-model="data.updateForm.region_code" maxlength="20" placeholder="请输入地区编码" />
          </el-form-item>
          <el-form-item prop="city_name" label="城市名称">
            <el-input v-model="data.updateForm.city_name" maxlength="20" placeholder="请输入城市名称" />
          </el-form-item>
          <el-form-item prop="tax_control_device_type" label="税控设备类型">
            <el-select v-model="data.updateForm.tax_control_device_type" placeholder="请选择税控设备类型" clearable>
              <el-option v-for="item in einvoicDeviceType" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="invoice_issue_kind_code" label="开具发票种类">
            <el-select v-model="data.updateForm.invoice_issue_kind_code" placeholder="请选择发票种类" clearable>
              <el-option v-for="item in einvoicKindCode" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="account" label="电子税局登录账号">
            <el-input v-model="data.updateForm.account" maxlength="50" placeholder="请输入电子税局登录账号" />
          </el-form-item>
          <el-form-item prop="cross_city" label="跨地市标志">
            <el-radio-group v-model="data.updateForm.cross_city">
              <el-radio label="1">是</el-radio>
              <el-radio label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="tax_registration_certificate_url" label="营业执照">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="data.updateForm.tax_registration_certificate_url" :src="data.updateForm.tax_registration_certificate_url" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon">
                <Plus />
              </el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateInvoice(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="InvoiceTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parkInvoiceService from '@/service/park/ParkInvoiceService';
import commonService from '@/service/common/CommonService';
import { getToken } from '@/utils/common';

const validateTaxNo = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^[A-Z0-9]{15}$|^[A-Z0-9]{17}$|^[A-Z0-9]{18}$|^[A-Z0-9]{20}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的税号'));
    }
  }
  callback();
};
const einvoicReq = ref([]);
const einvoicDeviceType = ref([]);
const einvoicKindCode = ref([]);
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const park_id = ref('');
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    park_id: undefined,
    title: undefined,
    tax_no: undefined,
    company_address: undefined,
    company_mobile: undefined,
    company_bank_account: undefined,
    account_no: undefined,
    invoice: undefined,
    special_invoice: undefined,
    tax_rate: undefined,
    tax_cod: undefined,
    terminal_code: undefined,
    einvoic_req: undefined,
    drawer: undefined,
    payee: undefined,
    checker: undefined,
    legal_person_name: undefined,
    contacts_name: undefined,
    contacts_email: undefined,
    contacts_phone: undefined,
    region_code: undefined,
    city_name: undefined,
    tax_registration_certificate_url: undefined,
    tax_control_device_type: undefined,
    register_code: undefined,
    register_msg: undefined,
    invoice_issue_kind_code: undefined,
    account: undefined,
    cross_city: undefined
  },
  updateForm: {},
  rules: {
    title: [
      {
        required: true,
        message: '请输入发票抬头',
        trigger: 'blur'
      }
    ],
    tax_no: [
      {
        required: true,
        message: '请输入发票税号',
        trigger: 'change'
      },
      {
        trigger: 'blur',
        validator: validateTaxNo
      }
    ],
    company_address: [
      {
        required: true,
        message: '请输入公司地址',
        trigger: 'change'
      }
    ],
    company_mobile: [
      {
        required: true,
        message: '请输入公司电话',
        trigger: 'change'
      }
    ],
    company_bank_account: [
      {
        required: true,
        message: '请输入公司开户行',
        trigger: 'blur'
      }
    ],
    account_no: [
      {
        required: true,
        message: '请输入开户行账号',
        trigger: 'blur'
      }
    ],
    invoice: [
      {
        required: true,
        message: '请选择是否支持电子票',
        trigger: 'blur'
      }
    ],
    special_invoice: [
      {
        required: true,
        message: '请选择是否支持电子专票',
        trigger: 'blur'
      }
    ],
    cross_city: [
      {
        required: true,
        message: '请选择跨地市标志',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  status.value = true;
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'einvoicReq', enum_value: 'EnumEInvoiceReqType' },
    { enum_key: 'einvoicDeviceType', enum_value: 'EnumInvoiceDeviceType' },
    { enum_key: 'einvoicKindCode', enum_value: 'EnumInvoiceKindCode' }
  ];
  commonService.findEnums('park', param).then((response) => {
    einvoicReq.value = response.data.einvoicReq;
    einvoicDeviceType.value = response.data.einvoicDeviceType;
    einvoicKindCode.value = response.data.einvoicKindCode;
  });
};
// 分页查询发票列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  parkInvoiceService.pagingInvoice(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 添加发票抬头
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form = {
    park_id: park_id.value,
    title: undefined,
    tax_no: undefined,
    company_address: undefined,
    company_mobile: undefined,
    company_bank_account: undefined,
    account_no: undefined,
    invoice: '1',
    special_invoice: '1',
    tax_rate: undefined,
    tax_cod: undefined,
    terminal_code: undefined,
    einvoic_req: undefined,
    drawer: undefined,
    payee: undefined,
    checker: undefined,
    register_code: undefined,
    invoice_issue_kind_code: undefined,
    account: undefined,
    cross_city: undefined
  };
  createDialogVisible.value = true;
  status.value = false;
};

const handleEdit = (val) => {
  console.log(val);
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    title: val.title,
    tax_no: val.tax_no,
    company_address: val.company_address,
    company_mobile: val.company_mobile,
    company_bank_account: val.company_bank_account,
    account_no: val.account_no,
    invoice: val.einvoice + '',
    special_invoice: val.especial_invoice + '',
    tax_rate: val.tax_rate,
    tax_cod: val.tax_cod,
    terminal_code: val.terminal_code,
    einvoic_req: val.einvoic_req,
    drawer: val.drawer,
    payee: val.payee,
    checker: val.checker,
    legal_person_name: val.legal_person_name,
    contacts_name: val.contacts_name,
    contacts_email: val.contacts_email,
    contacts_phone: val.contacts_phone,
    region_code: val.region_code,
    city_name: val.city_name,
    tax_registration_certificate_url: val.tax_registration_certificate_url,
    tax_control_device_type: val.tax_control_device_type ? Number(val.tax_control_device_type) : '',
    invoice_issue_kind_code: val.invoice_issue_kind_code ? Number(val.invoice_issue_kind_code) : '',
    account: val.account,
    cross_city: val.cross_city + ''
  };
  updateDialogVisible.value = true;
};

const updateInvoice = (editForm) => {
  editForm.validate().then(() => {
    parkInvoiceService
      .updateInvoice(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleRegister = (val) => {
  if (data.form.register_code === '2') {
    //审核不通过，提示审核信息，需要修改后再次审核，是否确认
    ElMessageBox.confirm('审核失败，原因:' + data.form.register_msg + ',确定重新进行注册吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        parkInvoiceService
          .registerInvoice(val.id)
          .then((response) => {
            if (response.data.code === '2') {
              ElMessage({
                message: response.data.msg,
                type: 'error'
              });
              // getList(data.queryParams);
            } else {
              ElMessage({
                message: response.detail_message != '' ? response.detail_message : response.data.msg,
                type: 'success'
              });
            }
          })
          .catch(() => {
            getList(data.queryParams);
          });
      })
      .catch(() => {});
  } else {
    parkInvoiceService
      .registerInvoice(val.id)
      .then((response) => {
        if (response.data.code === '2') {
          ElMessage({
            message: response.data.msg,
            type: 'error'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.data.msg,
            type: 'success'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  }
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建发票抬头
const createInvoice = (addForm) => {
  addForm.validate().then(() => {
    parkInvoiceService
      .createInvoice(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除发票抬头
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkInvoiceService
      .deleteInvoice(row)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};

// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};

const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/invoice/uploadBusinessLicense');
const headers = reactive({
  Authorization: getToken()
});
const handleAvatarSuccess = (res) => {
  if (createDialogVisible.value === true) {
    data.form.tax_registration_certificate_url = res.data.picture_url;
  }
  if (updateDialogVisible.value === true) {
    data.updateForm.tax_registration_certificate_url = res.data.picture_url;
  }
};

const beforeAvatarUpload = (file) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage({
      message: '上传文件大小不能超过 100MB!',
      type: 'error'
    });
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  border: 1px dashed rgb(196, 196, 204);
}
</style>
