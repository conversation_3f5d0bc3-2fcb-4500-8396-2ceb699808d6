<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column prop="month" label="月份" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" width="100" />
        <!-- <el-table-column prop="park_id" label="大区" align="center" />
        <el-table-column prop="park_id" label="城市分公司" align="center" />
        <el-table-column prop="park_id" label="所在省份" align="center" />
        <el-table-column prop="park_id" label="所在城市" align="center" />
        <el-table-column prop="park_id" label="所在区域" align="center" /> -->
        <el-table-column prop="organizational_structure" label="组织架构" align="center" />
        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="main_brand" label="车标" align="center" />
        <el-table-column prop="plate_no_cnt" label="车牌数" align="center" />
        <el-table-column prop="rank" label="排名" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="NoVehicleModelTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import plateNoInAndOutTimesService from '@/service/statisticalReport/PlateNoInAndOutTimesService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(11);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
const getList = (params) => {
  loading.value = true;
  const { carType, ...otherData } = params;
  data.queryParams = params;
  params.type = 0;
  plateNoInAndOutTimesService.pagingPlateNoInAndOutTimes(otherData).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.filter((e) => e.sub_brand.includes(params.carType));
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>
