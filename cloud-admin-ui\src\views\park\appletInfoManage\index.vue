<!-- 小程序信息管理 -->
<template>
  <el-card shadow="never" style="margin: 10px 0px">
    <div class="opers">
      <el-form ref="updateForm" label-width="120px" :rules="data.rules" :model="data.form">
        <el-form-item prop="remarks" label="公告信息">
          <el-input type="textarea" rows="3" v-model="data.form.remarks" placeholder="公告信息" style="width: 100%" />
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="updateRemarks">保 存</el-button>
    </div>
  </el-card>
</template>

<script name="AppletInfoManage" setup>
import { reactive, ref } from 'vue';
import appletInfoManageService from '@/service/park/AppletInfoManageService';
import { ElMessage } from 'element-plus';

const initRemarks = (params) => {
  appletInfoManageService.getParkMiniManageInfo(params).then((response) => {
    data.form.id = response.data.id;
    data.form.name = response.data.name;
    data.form.remarks = response.data.remarks;
  });
};

const updateRemarks = () => {
  updateForm.value.validate().then(() => {
    appletInfoManageService.updateParkMiniManageInfo(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

const data = reactive({
  queryParams: {
    park_id: undefined
  },
  form: {
    id: undefined,
    name: undefined,
    remarks: undefined
  },
  rules: {
    remarks: [
      {
        required: true,
        message: '请填写公告信息',
        trigger: 'blur'
      }
    ]
  }
});

const updateForm = ref();

defineExpose({
  initRemarks
});
</script>
<style lang="scss" scoped>
.footer {
  padding-top: 20px;
  padding-bottom: 20px;
  text-align: center;
}
:deep(.el-form) {
  width: 100%;
}
</style>
