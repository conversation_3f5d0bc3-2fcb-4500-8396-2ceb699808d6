<template>
  <!-- <div v-if="!initSuccess" class="shadowbox">
      <div class="gth">!</div>
      <div>{{ errmsg }}</div>
      <span @click="back" class="th-b-xq" style="font-size: 14px; margin-left: 10px">返回监控中心></span>
    </div> -->
  <div class="lastCallBox" v-loading="loading && { text: loadingStr }">
    <!-- 最左列 -->
    <div class="imgCol">
      <div>
        <div class="imgTit">实时监控</div>
        <div class="imgone">
          <!-- <img src="https://pic.huidawanan.com/group1/M00/00/1E/rBRAEWOrsjaAQ8qhAALCHNF9Yd4735.jpg" alt="" /> -->
          <!-- <videihls></videihls> -->
          <!-- https://open.ys7.com/ezopen/h5/iframe?url=ezopen://open.ys7.com/FM8748397/1.live&autoplay=1&accessToken=at.8pnpmiv0cexb0gyh2dvd1yruahcxiefo-2326z4w1wc-1ep5hvz-0e8zchzsc -->
          <iframe v-if='duty.callInfo.monitor_video_url' :src="duty.callInfo.monitor_video_url" width="600" height="440" id="ysOpenDevice" allowfullscreen>
          </iframe>
          <el-empty v-else description="暂无数据" />
        </div>
        <!-- <div class="buttonClass">
          <div @click="s_Refalsh">
            <el-icon :class="{ iconR: refalsh }" :size="15">
              <Refresh />
            </el-icon>重新获取
          </div>
        </div> -->
      </div>
      <div>
        <div class="imgNav">
          <div v-for="(i, index) in navBarDate" :key="index" :class="{ acImg: navBarIndx == index }"
            @click="changeNarBar(index)">{{ i }}</div>
        </div>
        <!-- 出场 -->
        <div v-if="isLeave">
          <div class="img" v-if="navBarIndx == 0">
            <img v-if="duty.callInfo.car_in_photo" :src="duty.callInfo.car_in_photo" alt=""
              @click="checkInPicture(duty.callInfo.car_in_photo)" />
            <el-empty v-if="!duty.callInfo.car_in_photo" description="暂无数据" />
          </div>
          <div class="img" v-if="navBarIndx == 1">
            <img v-if="duty.callInfo.car_photo" :src="duty.callInfo.car_photo"
              @click="checkInPicture(duty.callInfo.car_photo)" alt="" />
            <el-empty v-if="!duty.callInfo.car_photo" description="暂无数据" />
          </div>
        </div>
        <!-- 入场 -->
        <div v-if="!isLeave">
          <div class="img" v-if="navBarIndx == 0">
            <img v-if="duty.callInfo.car_photo" :src="duty.callInfo.car_photo" alt=""
              @click="checkInPicture(duty.callInfo.car_photo)" />
            <el-empty v-if="!duty.callInfo.car_photo" description="暂无数据" />
          </div>
          <div class="img" v-if="navBarIndx == 1">
            <el-empty description="暂无数据" />
          </div>
        </div>
        <!-- <div class="noImg" v-else>
          <el-icon :size="40">
            <Picture />
          </el-icon>
          暂无数据
        </div> -->
      </div>
    </div>
    <!-- 中间列 -->
    <div class="infoCol">
      <div class="infoCol-one">
        <div class="infoCol-t">
          <div class="fill"></div>
          {{ duty.callInfo.park_name }}
        </div>
        <div class="infoCol-b">
          <div>呼叫通道: {{ duty.callInfo.gateway_type == 1 ? duty.callInfo.gateway_out_name : duty.callInfo.gateway_name }}
          </div>
          <div>通道类型: {{ duty.callInfo.gateway_type == 1 ? '出口' : duty.callInfo.gateway_type == 2 ? '入口' : '出入口' }}</div>
          <!-- <div>呼叫时间: {{ duty.callInfo.gateway_type == 1 ? duty.callInfo.to_time : duty.callInfo.in_time }}</div> -->
          <div>呼叫时间: {{ hjTime }}</div>
          <div v-if="!isLeave">剩余车位: {{ duty.callInfo.surplus_space }}个</div>
        </div>
      </div>
      <div class="infoCol-two">
        <div class="infoCol-t">
          <div class="fill"></div>
          <div style="flex: 1">{{ !isLeave ? '入场车辆' : '出场车辆' }}</div>
          <span v-if="duty.callInfo.plate_no" style="color: #f56c6c; font-weight: 400">{{
            noIn ? '车辆无未出场的入场记录，计费失败，需要人工处理' : ''
          }}</span>
          <div v-if="isLeave" style="margin-left: auto; color: #4b7a02; font-weight: normal">{{
            duty.callInfo.out_state_desc }}</div>
        </div>
        <div v-if="!isLeave" style="height: calc(100% - 40px)">
          <div class="enterInfo">
            <div class="enterInfoT">
              <div class="carNmber">{{ duty.callInfo.plate_no }}</div>
              <el-button class="fixCar" @click="handleUpdatePlateNo" :disabled="duty.callInfo.has_car_in_record ||
                duty.callInfo.plate_no === undefined ||
                duty.callInfo.completePayed?.display ||
                duty.callInfo.parkOrder?.should_pay_money === 0
                ">
                修改车牌
              </el-button>
            </div>
            <div class="enterInfoB">
              <el-button style="width: 90px" :disabled="duty.callInfo.plate_no === undefined ||
                duty.callInfo.parkOrder.order_state === 2 ||
                (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单'))
                " @click="init">刷新</el-button>
              <el-button style="width: 120px" type="primary" @click="manualCapture"
                :disabled="duty.callInfo.parkOrder?.order_state === 2">手动抓拍</el-button>
              <el-button style="width: 110px" @click="cancelEntry('取消入场')" :disabled="duty.callInfo.plate_no === undefined ||
                (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单')) ||
                (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
                (duty.callInfo.parkOrder?.should_pay_money === '0' && duty.callInfo.parkOrder?.order_state === 2) ||
                (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
                ">取消入场</el-button>
              <el-button style="width: 150px" type="primary" @click="inTg" :disabled="duty.callInfo.plate_no === undefined ||
                duty.callInfo.parkOrder?.order_state === 2 ||
                (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
                (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
                ">入场抬杆</el-button>
            </div>
          </div>
        </div>
        <div v-else style="height: calc(100% - 40px)">
          <div class="carInfo">
            <div>
              <div>{{ duty.callInfo.plate_no || '暂无车辆信息' }}</div>
              <!-- <div>(长租车:2002-11-22 02:11:22 ~ 2022-11-22 02:11:22)</div> -->
            </div>
            <el-button class="fixCar" @click="handleUpdatePlateNo" :disabled="duty.callInfo.has_car_in_record ||
              duty.callInfo.plate_no === undefined ||
              duty.callInfo.completePayed?.display ||
              duty.callInfo.parkOrder?.should_pay_money === 0
              ">
              修改车牌
            </el-button>
          </div>
          <div class="infoCol-two-con">
            <div>车辆类型: {{ duty.callInfo.car_type_desc }}</div>
            <div>停车时长: {{ duty.callInfo.duration_text }}</div>
            <div>入场时间: {{ duty.callInfo.in_time }}</div>
            <div>出场时间: {{ duty.callInfo.to_time }}</div>
            <div>入场通道: {{ duty.callInfo.gateway_in_name || duty.callInfo.gateway_name }}</div>
            <div>出场通道: {{ duty.callInfo.gateway_out_name }}</div>
          </div>
        </div>
      </div>
      <div class="infoCol-three">
        <div class="infoCol-t">
          <div class="fill"></div>
          <div style="flex: 1">收费信息</div>
          <span v-if="duty.callInfo.plate_no" style="color: #f56c6c; font-weight: 400">{{ noIn ? '此车辆无入场记录' : ''
          }}</span>
        </div>
        <div v-if="!isLeave" class="infoCol-three-con">
          <el-empty description="暂无数据" />
        </div>
        <div v-else class="infoCol-three-cons">
          <div class="three-top">
            <div class="th-t">
              还需支付: <span style="color: #01a7f0">{{ duty.callInfo.should_pay_money || 0 }}元</span>
            </div>
            <div class="th-b">
              <!-- <div>应收费用: {{ duty.callInfo.total_money }}元 <span class="th-b-xq">详情></span></div> -->
              <div>应收费用: {{ duty.callInfo.total_money || 0 }}元</div>
              <div class="th-b-ym">
                优免: {{ duty.callInfo.current_coupon_money || 0 }}元
                <el-button type="primary" size="small" @click="handleCancelCoupon"
                  :disabled="!duty.callInfo.usedCouponFlag">取消优免</el-button>
              </div>
              <div>电子支付: {{ duty.callInfo.history_payed_money || 0 }}元</div>
              <div>已抵扣金额: {{ duty.callInfo.history_derate_coupon_money || 0 }}元</div>
              <div>
                已支付: <span style="color: #4b7a02">{{ duty.callInfo.payed_money || 0 }}元</span>
              </div>
              <div>
                找零金额: <span style="color: #ffac00">{{ duty.callInfo.cash_zero || 0 }}元</span>
              </div>
              <div>
                历史欠费: <span style="color: #f56c6c">{{ duty.callInfo.unpaidValue || 0 }}元</span>
              </div>
            </div>
          </div>
          <div class="three-center">
            <!-- <div class="th-r">当前费率:{{ carLx[duty.callInfo.car_type] }}</div> -->
            <div class="th-r">当前费率:</div>
            <div class="th-l">
              <el-button :disabled="duty.callInfo.plate_no === undefined ||
                duty.callInfo.stop_car_type !== 1 ||
                (duty.callInfo.parkOrder.should_pay_money === '0' && duty.callInfo.parkOrder.order_state === 2)
                " v-for="(i, index) in carLx" :key="index" :class="carLxIndex == index ? 'caractive' : ''"
                @click="switchCarType(index)">
                {{ i }}
              </el-button>
            </div>
          </div>
          <div class="three-bottom">
            <el-button @click="init" :disabled="duty.callInfo.plate_no === undefined ||
              duty.callInfo.parkOrder.order_state === 2 ||
              (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单'))
              ">刷新</el-button>
            <el-button type="primary" @click="manualCapture"
              :disabled="duty.callInfo.parkOrder?.order_state === 2">手动抓拍</el-button>
            <el-button type="primary" @click="handMovement" :disabled="duty.callInfo.has_car_in_record ||
              duty.callInfo.plate_no === undefined ||
              duty.callInfo.plate_no === '无牌车' ||
              duty.callInfo.plate_no == ''
              ">手动补录</el-button>
            <el-button @click="specialOut" :disabled="noIn ||
              !duty.callInfo.has_car_in_record ||
              duty.callInfo.plate_no === undefined ||
              (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单')) ||
              (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
              (duty.callInfo.parkOrder?.should_pay_money === '0' && duty.callInfo.parkOrder?.order_duty.callInfo === 2) ||
              (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
              ">特殊放行</el-button>
            <el-button @click="cancelEntry('取消放行')" :disabled="noIn ||
              duty.callInfo.plate_no === undefined ||
              (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单')) ||
              (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
              (duty.callInfo.parkOrder?.should_pay_money === '0' && duty.callInfo.parkOrder?.order_state === 2) ||
              (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
              ">取消放行</el-button>
            <el-button type="primary" v-if="!duty.callInfo.has_car_in_record" @click="handleManualLift" :disabled="duty.callInfo.plate_no === undefined ||
              duty.callInfo.parkOrder?.order_state === 2 ||
              (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
              (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
              ">手动抬杆</el-button>
            <el-button v-if="duty.callInfo.has_car_in_record" type="primary" @click="handleChargeAndPass" :disabled="noIn ||
              duty.callInfo.plate_no === undefined ||
              (duty.callInfo.memo !== undefined && duty.callInfo.memo.includes('白名单')) ||
              (duty.callInfo.stop_car_type !== 1 && !!duty.callInfo.stop_car_type) ||
              (duty.callInfo.parkOrder?.should_pay_money === '0' && duty.callInfo.parkOrder?.order_state === 2) ||
              (duty.callInfo.out_type == 4 && duty.callInfo.car_type == 0)
              ">收费放行</el-button>
          </div>
        </div>
      </div>
    </div>
    <!-- 右边列 -->
    <div class="reasonCol">
      <div class="reasonCol-one">
        <div class="reasonCol-one-tit">工具箱</div>
        <!-- 为了好维护清晰就这样写了 -->
        <div class="reasonCol-one-con">
          <el-button v-for="(item, i) in utilsList" :key="i" @click="utilsHandler(item)" style="width: 116px" :disabled="gjxqx.includes(item.name)
            ? duty.callInfo.has_car_in_record || duty.callInfo.plate_no === undefined || duty.callInfo.plate_no == ''
            : false
            ">{{ item.name }}</el-button>
        </div>
      </div>
      <div class="reasonCol-two">
        <div class="infoCol-t">
          <div class="fill"></div>
          呼叫原因
        </div>
        <div class="reasonCol-two-tit"><span style="color: #f5222d">*</span> 业务类型</div>
        <div class="reasonCol-two-ywlx">
          <div v-for="(item, i) in ywLx" :key="i" :class="ywlxIndex == item.value ? 'ywlxactive' : ''"
            @click="chooseywlx(item)">
            {{ item.key }}
          </div>
        </div>
        <div v-if="ywlxIndex == '14'" class="reasonCol-two-tit"><span style="color: #f5222d">*</span>备注</div>
        <div v-if="ywlxIndex == '14'" class="reasonCol-two-bz">
          <el-input type="textarea" v-model="reason_remark"></el-input>
        </div>
        <el-button :disabled="noIn" class="reasonCol-two-submit" type="primary"
          :class="!(duty.callInfo.plate_no === undefined || duty.callInfo.plate_no == '') ? '' : ''"
          @click="completeService(false)">完成本次服务</el-button>
        <el-button class="reasonCol-two-submit reasonCol-two-submit2" style="margin-top: 0px" v-if="noIn"
          @click="completeService('close')">关闭页面</el-button>
      </div>
    </div>
    <!-- 修改车牌号 -->
    <el-dialog v-model="plateNoDialogVisible" title="修改车牌号" width="450px" :before-close="handlePlateNoClose"
      :close-on-click-modal="false">
      <el-form :model="plateNo.form" label-position="top">
        <el-form-item label="车牌号" class="required">
          <el-row :gutter="10">
            <el-col :span="6">
              <el-select v-model="plateNo.form.plateNoFirst" filterable style="width: 100%" disabled
                v-if="plateNo.noPlateCarFlag == '1'">
                <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
              <el-select v-else v-model="plateNo.form.plateNoFirst" filterable style="width: 100%">
                <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input type="text" v-model="plateNo.form.plateNoSecond" show-word-limit maxlength="10"
                style="width: 100%" disabled v-if="plateNo.noPlateCarFlag == '1'" />
              <el-input type="text" v-model="plateNo.form.plateNoSecond" show-word-limit maxlength="10"
                style="width: 100%" v-else />
            </el-col>
            <el-col :span="6">
              <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" disabled
                v-if="plateNo.noPlateCarFlag == '1'">
                <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
              <el-select v-model="plateNo.form.plateNoThirdly" filterable style="width: 100%" v-else>
                <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
            </el-col>
            <el-col :span="4">
              <el-checkbox v-model="plateNo.noPlateCarFlag" true-label="1" false-label="0" @change="changeToNoPlate"
                style="display: inline; margin-top: 10px">
                无牌车
              </el-checkbox>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <div style="margin-top: 30px; text-align: center">
        <el-space>
          <el-button type="primary" @click="submitPlateNo()">确 认</el-button>
          <el-button @click="resetPlateNo()">重 置</el-button>
        </el-space>
      </div>
    </el-dialog>
    <!-- 手动抬杆 -->
    <el-dialog v-model="manualLift.dialogVisible" title="手动抬杆" width="400px" :before-close="handleManualLiftClose"
      :close-on-click-modal="false">
      <el-form ref="manualLiftFormRef" :model="manualLift.form" :rules="manualLift.rules" label-position="top">
        <el-form-item prop="money" label="放行金额">
          <el-input-number v-model="manualLift.form.money" placeholder="请输入" style="width: 100%" :min="0"
            :precision="2" />
        </el-form-item>
        <el-form-item prop="out_reason" label="放行原因">
          <el-select v-model="manualLift.form.out_reason" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in out_reason_options" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="memo" label="备注">
          <el-input type="textarea" :rows="4" v-model="manualLift.form.memo" placeholder="请输入" style="width: 100%" />
        </el-form-item>
      </el-form>
      <div style="margin-top: 30px; text-align: center">
        <el-space>
          <el-button type="primary" @click="calcManualLiftCarFee(manualLiftFormRef)">车辆计费</el-button>
          <el-button @click="resetManualLiftCarFee(manualLiftFormRef)">重 置</el-button>
        </el-space>
      </div>
    </el-dialog>
    <on-tocars ref="onTocarsRef"> </on-tocars>
    <specialRelease ref="specialReleaseRef" @clearData="clearData"></specialRelease>
    <addressBook ref="addressBookRef"></addressBook>
    <openFunction ref="openFunctionRef"></openFunction>
    <arrearsOfFees ref="arrearsOfFeesRef"></arrearsOfFees>
    <matchEntryRecords ref="matchEntryRecordsRef" @calcManualPassCarFee="calcManualPassCarFee"></matchEntryRecords>
    <premiumCoupon ref="premiumCouponRef"></premiumCoupon>
    <handMovementtem ref="handMovementRef" @sdblInit="sdblInit(10)"></handMovementtem>
  </div>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup>
import unpaidFeesService from '@/service/charge/UnpaidFeesService';
import commonService from '@/service/common/CommonService';
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { useUser } from '@/stores/user';
import { activeRouteTab, closeCurrentTab } from '@/utils/tabKit';
import { ElMessage, ElMessageBox } from 'element-plus';
import { computed, onActivated, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import addressBook from './components/addressBook.vue';
import arrearsOfFees from './components/arrearsOfFees.vue';
import handMovementtem from './components/handMovementtem.vue';
import matchEntryRecords from './components/matchEntryRecords.vue';
import onTocars from './components/onTocars.vue';
import openFunction from './components/openFunction.vue';
import premiumCoupon from './components/premiumCoupon.vue';
import specialRelease from './components/specialRelease.vue';
const loading = ref(false);
const duty = useDuty();
const user = useUser();
const route = useRoute();
const refalsh = ref(false); //控制是否刷新
const navBarDate = ['入场照片', '出场照片']; //navcer数据
const navBarIndx = ref(0); //控制tabbar
const isLeave = ref(true); //false入场 true出场
const carLxIndex = ref(0);
const carLx = ['默认无牌车', '小型车', '大型车', '新能源车', '摩托车']; //当前费率car类型
const ywLx = ref([]); // 业务类型
const utilsList = ref([]);
const sip_no = ref();
const onTocarsRef = ref(null);
const addressBookRef = ref(null);
const openFunctionRef = ref(null);
const arrearsOfFeesRef = ref(null);
const matchEntryRecordsRef = ref(null);
const premiumCouponRef = ref(null);
const handMovementRef = ref(null);
const errmsg = ref(null);
const initSuccess = ref(true);
const videoUrl = ref(null);
const gjxqx = ref(['匹配入场']);
const gatewayType = ref(-1); // 用于判断其他入点击进来时是入口还是出口
const plateNoFirsts = ref([
  { label: '京' },
  { label: '冀' },
  { label: '晋' },
  { label: '蒙' },
  { label: '辽' },
  { label: '吉' },
  { label: '黑' },
  { label: '沪' },
  { label: '苏' },
  { label: '浙' },
  { label: '皖' },
  { label: '闽' },
  { label: '赣' },
  { label: '鲁' },
  { label: '豫' },
  { label: '鄂' },
  { label: '湘' },
  { label: '粤' },
  { label: '桂' },
  { label: '琼' },
  { label: '渝' },
  { label: '川' },
  { label: '贵' },
  { label: '云' },
  { label: '藏' },
  { label: '陕' },
  { label: '甘' },
  { label: '青' },
  { label: '宁' },
  { label: '新' },
  { label: '民航' },
  { label: '使' },
  { label: '无' }
]);
const plateNoThirdlies = ref([
  { label: '警' },
  { label: '学' },
  { label: '使' },
  { label: '领' },
  { label: '挂' },
  { label: '应急' },
  { label: '无' }
]);
onActivated(() => {
  // d5e48349-6a99-42c8-a88d-a0d06f1d41dc
  // route.query.sIp = 25437114;
  if (Object.keys(route.query).length !== 0 && undefined !== route.query.sIp) {
    sip_no.value = route.query.sIp;
    gatewayType.value = route.query.gatewayType ? route.query.gatewayType : -1;
  } else {
    // ElMessage({
    //   message: '未选择可操作记录',
    //   type: 'warning'
    // });
    return;
  }
  duty.talkBeginTime = new Date().getTime();
  // init();
});
const handlePlateNoClose = () => {
  plateNoDialogVisible.value = false;
};
const callInfo = ref({
  id: null,
  car_in_biz_no: null,
  park_id: null,
  park_name: null,
  park_region_id: null,
  park_region_name: null,
  gateway_id: null,
  gateway_name: null,
  in_time: null,
  plate_no: null,
  car_type: null,
  car_photo: null,
  out_state: null,
  in_type: null,
  stop_car_type: null,
  has_plate_no: null,
  next_in: null,
  car_type_desc: null,
  in_type_desc: null,
  out_state_desc: null,
  car_photo_url: null,
  car_photo2_url: null
});
const noIn = ref(true);
const clearData = () => {
  const obj = {
    ...duty.callInfo
  };
  duty.callInfo = {};
  duty.callInfo.gateway_id = obj.gateway_id;
  duty.callInfo.gateway_name = obj.gateway_name;
  duty.callInfo.gateway_type = obj.gateway_type;
  duty.callInfo.gateway_type_desc = obj.gateway_type_desc;
  duty.callInfo.gateway_in_id = obj.gateway_in_id;
  duty.callInfo.gateway_in_name = obj.gateway_in_name;
  duty.callInfo.gateway_out_id = obj.gateway_out_id;
  duty.callInfo.gateway_out_name = obj.gateway_out_name;
  duty.callInfo.park_id = obj.park_id;
  duty.callInfo.park_name = obj.park_name;
  duty.callInfo.car_in_biz_no = obj.car_in_biz_no;
  duty.callInfo.CallSheetID = obj.CallSheetID;
  duty.callInfo.plate_noCopy = obj.plate_no;
  // 出场类型，默认-人工放行
  duty.callInfo.out_type = 2;
};
const init = async () => {
  ywlxIndex.value = 1;
  reason_remark.value = null;
  // 根据sip_no查询是入口还是出口
  callInfo.value = {};
  duty.callInfo = {};
  duty.callInfo.parkOrder = {};
  // duty.callInfo.has_car_in_record = true;
  let res1 = {};
  let params = {};
  // 监控中心-得处理异常事件进入
  if (gatewayType.value >= 0) {
    res1 = {
      data: {
        type: gatewayType.value
      }
    };
    params = {
      gateway_id: sip_no.value
    };
  } else {
    // 拨号进入
    params = {
      sip_no: sip_no.value
    };
    res1 = await UnattendedApi.operateselectGatewayInfo({ sip_no: sip_no.value });
  }

  let api = '';
  if (res1.data.type == undefined || res1.data.type == null) {
    ElMessage({
      message: '未获取到通道类型，请检查通道是否存在',
      type: 'warning'
    });
    return;
  }
  // 出口
  if (res1.data.type == 1) {
    isLeave.value = true;
    api = 'operateexport';
    utilsList.value = [
      { name: '入场记录', path: '/charge/chargeAdmin' },
      { name: '出场记录', path: '/charge/carOutRecord' },
      { name: '停车订单', path: '/charge/parkFee' },
      { name: '欠费订单查询', path: '/charge/unpaidFees' },
      { name: '匹配入场', path: '' },
      { name: '优免券', path: '' },
      { name: '一户多车查询', path: '/car/spaceRentApply' },
      // { name: '车场开放功能', path: '/park/parkInfo' },
      { name: '应急通讯录', path: '/system/emergencyContactList' }
    ];
  }
  // 入口
  if (res1.data.type == 2) {
    isLeave.value = false;
    api = 'operateentrance';
    utilsList.value = [
      { name: '入场记录', path: '/charge/chargeAdmin' },
      { name: '出场记录', path: '/charge/carOutRecord' },
      { name: '停车订单', path: '/charge/parkFee' },
      { name: '一户多车查询', path: '/car/spaceRentApply' },
      // { name: '车场开放功能', path: '/park/parkInfo' },
      { name: '应急通讯录', path: '/system/emergencyContactList' }
    ];
  }
  duty.isLeave = isLeave.value;
  // params.plate_no = '苏E703Y5';
  await UnattendedApi[api](params)
    .then((res2) => {
      if (res2.success) {
        // 修改电话条状态为忙碌
        window.holly?.setStatus(1);
        duty.nowServiceEnd = false;
        initSuccess.value = true;
        duty.callInfo = {
          ...duty.callInfo,
          ...res2.data,
          parkOrder: res2.data
        };
        duty.query = {
          sIp: route.query.sIp,
          gatewayType: route.query.gatewayType
        };
        carLxIndex.value = duty.callInfo.car_type;
        noIn.value = false;
        if ((!res2.data.car_in_biz_no || res2.data.car_in_biz_no == '') && res1.data.type == 1) {
          noIn.value = true;
          // return;
        }
        duty.noIn = noIn.value;
        if (res2.data.has_car_in_record) {
          // 有入场记录，为收费放行，属于正常离场
          duty.callInfo.out_type = 1;
        } else {
          // 无入场记录，默认为人工放行
          duty.callInfo.out_type = 2;
        }
        queryLastParkInRecord();
        setTimeout(() => {
          if (duty.callInfo.parkOrder == null || duty.callInfo.parkOrder.id == null) {
            // 不存在订单，生成订单，且为临停车
            if (duty.callInfo.stop_car_type === 1) {
              getParkOrder(0);
            }
          } else {
            // 存在订单，刷新订单
            switchCarType(duty.callInfo.car_type, 'init');
          }
          unpaid();
        }, 300);
      } else {
        initSuccess.value = false;
        errmsg.value = res2.message;
      }
    })
    .catch((err) => {
      initSuccess.value = false;
      errmsg.value = '获取信息失败';
    });
};
const unpaidData = ref([]);
const unpaid = () => {
  if (!duty.callInfo.plate_no || duty.callInfo.plate_no == '') {
    return;
  }
  unpaidFeesService
    .recordsPage({
      plate_no: duty.callInfo.plate_no,
      order_states: [4],
      park_id: duty.callInfo.park_id,
      park_name: duty.callInfo.park_name,
      park_region_id: '',
      car_types: [],
      pay_type: [],
      refund_states: [],
      // out_start_time: lastYearToday.value + ' 00:00:00',
      // out_end_time: currentDate.value + ' 23:59:59',
      page: 1,
      limit: 300
    })
    .then((response) => {
      if (response.success === true) {
        unpaidData.value = response.data.rows;
        let allNum = 0;
        response.data.rows.forEach((item) => {
          allNum += item.should_pay_money;
        });
        duty.callInfo.unpaidValue = allNum.toFixed(2);
      }
    });
};
// 监听接通时间或者点击时间更新页面
const initTimer = ref(null);
const videoShow = ref(false);
const hjTime = ref('');
const formatDateTime = (timestamp, format = 'YYYY-MM-DD HH:mm:ss') => {
  const date = new Date(timestamp);

  const map = {
    YYYY: date.getFullYear(),
    MM: (date.getMonth() + 1).toString().padStart(2, '0'),
    DD: date.getDate().toString().padStart(2, '0'),
    HH: date.getHours().toString().padStart(2, '0'),
    mm: date.getMinutes().toString().padStart(2, '0'),
    ss: date.getSeconds().toString().padStart(2, '0')
  };

  return format.replace(/YYYY|MM|DD|HH|mm|ss/g, (matched) => map[matched]);
};

// 使用示例
console.log(formatDateTime(Date.now())); // "2023-05-15 14:30:45"
console.log(formatDateTime(Date.now(), 'YYYY/MM/DD HH:mm')); // "2023/05/15 14:30"
watch(
  () => duty.talkBeginTime,
  (newId) => {
    videoShow.value = false;
    hjTime.value = formatDateTime(duty.eventStartTime);
    setTimeout(() => {
      videoShow.value = true;
    }, 1000);
    if (!newId) {
      return;
    }

    sip_no.value = null;
    gatewayType.value = -1;
    if (initTimer.value) {
      clearTimeout(initTimer.value);
    }
    initTimer.value = setTimeout(() => {
      if (Object.keys(route.query).length !== 0 && undefined !== route.query.sIp) {
        sip_no.value = route.query.sIp;
        gatewayType.value = route.query.gatewayType ? route.query.gatewayType : -1;
      } else {
        // ElMessage({
        //   message: '未选择可操作记录',
        //   type: 'warning'
        // });
        // return;
      }
      duty.talkBeginTime = null;
      init();
    }, 300);
  },
  { immediate: true } // 立即执行一次
);
const utilsHandler = (item) => {
  if (item.name == '一户多车查询') {
    onTocarsRef.value.dialogVisible = true;
    onTocarsRef.value.carno = duty.callInfo.plate_no;
    onTocarsRef.value.getList();
    return;
  }
  if (item.name == '应急通讯录') {
    addressBookRef.value.dialogVisible = true;
    return;
  }
  if (item.name == '车场开放功能') {
    openFunctionRef.value.dialogVisible = true;
    return;
  }
  if (item.name == '欠费订单查询') {
    arrearsOfFeesRef.value.dialogVisible = true;
    arrearsOfFeesRef.value.getList(unpaidData.value);
    return;
  }
  if (item.name == '匹配入场') {
    matchEntryRecordsRef.value.dialogVisible = true;
    return;
  }
  if (item.name == '优免券') {
    premiumCouponRef.value.dialogVisible = true;
    premiumCouponRef.value.getData();
    return;
  }
  activeRouteTab({
    path: item.path,
    query: {
      parkId: duty.callInfo.park_id,
      parkName: duty.callInfo.park_name,
      plateNo: duty.callInfo.plate_no,
      sIp: route.query.sIp,
      gatewayType: route.query.gatewayType
    }
  });
};
// 修改车牌号
const plateNoDialogVisible = ref(false);
const plateNo = reactive({
  form: {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  },
  noPlateCarFlag: '0'
});
const changeToNoPlate = (val) => {
  if (val == '1') {
    plateNo.form = {
      plateNoFirst: undefined,
      plateNoSecond: undefined,
      plateNoThirdly: undefined
    };
  }
};
const handleUpdatePlateNo = () => {
  let plate_no = duty.callInfo.plate_no;
  //普通
  var c_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //特种
  var ts_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{4}[学挂领试超练警]{1}$/u;
  //武警
  var wj_reg = /^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9A-Z]{5}$/iu;
  //军牌
  var j_reg = /^[QVKHBSLJNGCEZ]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //新能源
  // 小型车
  var xs_reg =
    /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[DF]{1}[1-9ABCDEFGHJKLMNPQRSTUVWXYZ]{1}[0-9]{4}$/u;

  // 大型车
  var xb_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9]{5}[DF]{1}$/u;
  //民航
  var mh_reg = /^民航[0-9A-Z]{5}$/u;
  //使馆
  var s_reg = /^[1-3]{1}[0-9]{2}[0-9A-Z]{3}使$/u;
  var s1_reg = /^使[0-9]{6}$/u;
  //领馆
  var l_reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[1-3]{1}[0-9]{2}[0-9A-Z]{2}领$/u;
  //应急
  var yj_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[0-9A-Z]{5}应急$/u;
  //判断并进行拆分
  if (c_reg.test(plate_no) || xs_reg.test(plate_no) || xb_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (ts_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (wj_reg.test(plate_no) || j_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no;
    plateNo.form.plateNoThirdly = '无';
  }
  if (mh_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 2);
    plateNo.form.plateNoSecond = plate_no.substring(2);
    plateNo.form.plateNoThirdly = '无';
  }
  if (s_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = '无';
    plateNo.form.plateNoSecond = plate_no.substring(0, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (s1_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1);
    plateNo.form.plateNoThirdly = '无';
  }
  if (l_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 1);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 1);
  }
  if (yj_reg.test(plate_no)) {
    plateNo.form.plateNoFirst = plate_no.substring(0, 1);
    plateNo.form.plateNoSecond = plate_no.substring(1, plate_no.length - 2);
    plateNo.form.plateNoThirdly = plate_no.substring(plate_no.length - 2);
  }
  plateNo.noPlateCarFlag = '0';
  plateNoDialogVisible.value = true;
};
// 查询最近一条入场记录
const queryLastParkInRecord = (type) => {
  if (duty.callInfo.plate_no === undefined) {
    return;
  }

  const param = {
    park_region_id: duty.callInfo.park_region_id,
    plate_no: duty.callInfo.plate_no,
    park_id: duty.callInfo.park_id
  };
  UnattendedApi.queryLastParkInRecord(param).then((res) => {
    if (res.success) {
      if (res.data) {
        if (type == '车辆计费' || type == '修改车牌') {
          duty.callInfo = {
            ...duty.callInfo,
            ...res.data
          };
          // duty.callInfo.parkOrder = res.data;
        } else {
          // duty.callInfo = {
          //   ...duty.callInfo,
          //   ...res.data
          // };
        }
        duty.callInfo.lastParkInRecord = res.data;
        duty.callInfo.car_type = res.data.car_type;
        carLxIndex.value = res.data.car_type;
        duty.callInfo.pre_car_type = res.data.car_type;
        if (res.data?.car_in_biz_no) {
          noIn.value = false;
          duty.noIn = noIn.value;
        }
      }
      // 白名单不调用生成订单的方法
      if ((duty.callInfo.park_order_id === undefined && duty.callInfo.stop_car_type === 1) || duty.callInfo.out_type == 4) {
        getParkOrder(0);
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
// 查询入场记录
const queryParkInRecord = () => { };

// 查询出场记录
const queryParkOutRecord = (row) => {
  if (duty.callInfo.plate_no === undefined) {
    return;
  }

  const param = {
    plate_no: duty.callInfo.plate_no,
    start_time: row.time1 ? row.time1 : '',
    end_time: row.time2 ? row.time2 : '',
    has_plate_no: duty.callInfo.plate_no ? true : false,
    park_id: duty.callInfo.park_id,
    park_region_id: duty.callInfo.park_region_id
  };
  UnattendedApi.queryParkOutRecord(param).then((res) => {
    if (res.success) {
      duty.callInfo.outRecords = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};

// 查询可用优免券
const queryAvailableCoupons = () => {
  if (duty.callInfo.plate_no === undefined) {
    return;
  }

  const param = {
    plate_no: duty.callInfo.plate_no,
    park_id: duty.callInfo.park_id
  };
  UnattendedApi.queryAvailableCoupons(param).then((res) => {
    if (res.success) {
      duty.callInfo.availableCoupons = res.data;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
// 按入场时间生成当前需缴费订单
const getParkOrder = (require_refresh_order) => {
  if (duty.callInfo.lastParkInRecord?.car_in_biz_no == null) {
    ElMessage({
      message: '无入场记录，不能生成订单',
      type: 'warning'
    });
    return;
  }

  const param = {
    car_in_record_id: duty.callInfo.lastParkInRecord.id,
    require_refresh_order: require_refresh_order,
    car_type: duty.callInfo.car_type
  };
  // 无牌车手动匹配时候的处理
  if (duty.callInfo.out_type == 4) {
    param.prk_gateway_id = duty.callInfo.gateway_id;
  }

  UnattendedApi.getParkOrder(param).then((res) => {
    if (res.success) {
      if (res.data?.car_in_biz_no) {
        noIn.value = false;
        duty.noIn = noIn.value;
      }
      if (res.data.order_no) {
        duty.callInfo.park_order_no = res.data.order_no;
      }
      duty.callInfo.parkOrder = res.data;
      duty.callInfo = {
        ...duty.callInfo,
        ...res.data
      };

      // 手动匹配处理
      if (duty.callInfo.out_type === 4) {
        if (duty.callInfo.parkOrder.order_state === 2 || duty.callInfo.parkOrder.should_pay_money === '0.00') {
          duty.callInfo.memo = '临停车，已支付完成或待支付金额为0，自动开闸离场';
          if (duty.callInfo.car_type != 0) {
            testLift();
          }
        } else {
          duty.callInfo.memo = '临停车，正常计费，等待支付';
        }
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
// 自动抬杆
const testLift = () => {
  UnattendedApi.pushOpenStrobe({
    park_id: duty.callInfo.park_id,
    gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
    car_in_biz_no: duty.callInfo.car_in_biz_no,
    car_photo: duty.callInfo.car_photo,
    car_photo2: duty.callInfo.car_photo2
  }).then((res) => {
    if (!res.success) {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
//修改车牌号提交
const submitPlateNo = () => {
  if (plateNo.noPlateCarFlag == '0') {
    //有车牌车
    if (plateNo.form.plateNoFirst == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoSecond == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }

    if (plateNo.form.plateNoThirdly == undefined) {
      ElMessage({
        message: '请输入车牌号！',
        type: 'error'
      });
      return;
    }
    var regex = /^[0-9A-Z]+$/;
    if (!regex.test(plateNo.form.plateNoSecond)) {
      ElMessage({
        message: '请输入正确的车牌号！',
        type: 'error'
      });
      return;
    }
    const param = {
      first_no: plateNo.form.plateNoFirst,
      mid_no: plateNo.form.plateNoSecond,
      last_no: plateNo.form.plateNoThirdly,
      id: duty.callInfo.lastParkInRecord.id,
      no_plate_car: 0,
      gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
      old_plate_no: duty.callInfo.plate_no,
      park_id: duty.callInfo.park_id,
      park_name: duty.callInfo.park_name
    };
    var plate_no = '';
    if (plateNo.form.plateNoFirst != '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly != '无') {
      plate_no = plateNo.form.plateNoSecond + plateNo.form.plateNoThirdly;
    }
    if (plateNo.form.plateNoThirdly == '无' && plateNo.form.plateNoFirst != '无') {
      plate_no = plateNo.form.plateNoFirst + plateNo.form.plateNoSecond;
    }
    if (plateNo.form.plateNoFirst == '无' && plateNo.form.plateNoThirdly == '无') {
      plate_no = plateNo.form.plateNoSecond;
    }
    UnattendedApi.updateCarInRecord(param).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: '修改车牌号成功'
        });
        duty.callInfo.plate_no = plate_no;
        // queryLastParkInRecord('修改车牌');
        plateNoDialogVisible.value = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  } else {
    //无牌车
    const plateParam = {
      park_id: duty.callInfo.lastParkInRecord.park_id
    };
    UnattendedApi.generateCarNoPlateNo(plateParam).then((res) => {
      if (res.success) {
        updatePlateNo(res.data.plate_no);
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  }
};
const updatePlateNo = (plate_no) => {
  const param = {
    first_no: plateNo.form.plateNoFirst,
    mid_no: plate_no,
    last_no: plateNo.form.plateNoThirdly,
    id: duty.callInfo.lastParkInRecord.id,
    no_plate_car: 1,
    gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id
  };
  UnattendedApi.updateCarInRecord(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: '修改车牌号成功'
      });
      duty.callInfo.plate_no = plate_no;
      plateNoDialogVisible.value = false;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
// 取消优免
const handleCancelCoupon = () => {
  if (duty.callInfo.parkOrder?.car_in_biz_no == null) {
    ElMessage({
      message: '无订单信息，不允许取消优免操作',
      type: 'warning'
    });
    return;
  }

  if (duty.callInfo.order_state === 2) {
    ElMessage({
      message: '订单已完成，不允许取消优免操作',
      type: 'warning'
    });
    return;
  }

  let str = `parkOrderId=${duty.callInfo.park_order_id}`;
  UnattendedApi.cancelFreeCoupon(str).then((res) => {
    if (res.success) {
      duty.callInfo.parkOrder = res.data;

      duty.callInfo.usedCouponFlag = false;
      duty.callInfo = {
        ...duty.callInfo,
        ...res.data
      };
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
const loadingNum = ref(10);
const loadingNumTimer = ref(null);
const loadingStr = computed(() => {
  return `加载中，预计还需要${loadingNum.value}秒, 若未获取到数据请手动刷新`;
});

const sdblInit = (timeCount) => {
  loading.value = true;
  // 倒计时
  loadingNum.value = timeCount;
  if (loadingNumTimer.value) {
    clearInterval(loadingNumTimer.value);
  }
  // loadingStr.value = '加载中，预计还需要' + loadingNum.value + '秒';
  loadingNumTimer.value = setInterval(() => {
    if (loadingNum.value > 0) {
      loadingNum.value--;
    } else {
      clearInterval(loadingNumTimer.value);
    }
    // loadingStr.value = '加载中，预计还需要' + loadingNum.value + '秒';
  }, 1000);
  setTimeout(() => {
    init();
    loading.value = false;
  }, timeCount * 1000); //
};
// 手动抬杆
const manualLiftFormRef = ref();
const manualLift = reactive({
  dialogVisible: false,
  form: {
    money: undefined,
    out_reason: undefined,
    memo: undefined
  },
  rules: {
    money: [{ required: true, message: '请输入放行金额', trigger: 'blur' }],
    out_reason: [{ required: true, message: '请选择放行原因', trigger: 'change' }]
  }
});
// 打开手动抬杆对话框
const handleManualLift = () => {
  manualLift.dialogVisible = true;
  initDict();
};
// 关闭手动抬杆对话框
const handleManualLiftClose = () => {
  manualLiftFormRef.value.resetFields();
  manualLift.dialogVisible = false;
};

// 手动抬杆 - 人工放行
const calcManualLiftCarFee = (formRef) => {
  // 重置出场类型，人工放行
  duty.callInfo.out_type = 2;

  if (duty.callInfo.parkOrder?.car_in_biz_no == null) {
    ElMessage({
      message: '无订单信息，不允许手动抬杆',
      type: 'warning'
    });
    return;
  }
  if (duty.callInfo.lastParkInRecord?.car_in_biz_no == null) {
    ElMessage({
      message: '无最近入场记录，不允许手动抬杆',
      type: 'warning'
    });
    return;
  }
  // if (duty.callInfo.car_out_biz_no == null || duty.callInfo.car_out_biz_no == undefined) {
  //   ElMessage({
  //     message: '无出场流水号，不允许手动抬杆',
  //     type: 'warning'
  //   });
  //   return;
  // }

  formRef.validate().then(() => {
    const param = {
      park_id: duty.callInfo.park_id,
      gateway_id: duty.callInfo.gateway_out_id,
      park_order_no: duty.callInfo.park_order_no,
      car_in_biz_no: duty.callInfo.car_in_biz_no,
      out_type: duty.callInfo.out_type,
      out_state: 2, // 异常离场
      plate_no: duty.callInfo.plate_no,
      money: manualLift.form.money,
      out_reason: manualLift.form.out_reason,
      memo: manualLift.form.memo
    };
    UnattendedApi.chargeAndPass(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '手动抬杆放行成功',
          type: 'success'
        });

        formRef.resetFields();
        manualLift.dialogVisible = false;

        // 清空数据
        clearData();
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};
// 手动抬杆 - 重置
const resetManualLiftCarFee = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();
};
//重置
const resetPlateNo = () => {
  plateNo.form = {
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  };
  plateNo.noPlateCarFlag = '0';
};
// 选择费率
const changCarRate = (index) => {
  carLxIndex.value = index;
};
// 点击刷新
const s_Refalsh = () => {
  refalsh.value = true;
  //   模拟请求
  setTimeout(() => {
    refalsh.value = false;
  }, 2000);
};
//切换tabber
const changeNarBar = (index) => {
  navBarIndx.value = index;
};

const ywlxIndex = ref('1');
const reason_remark = ref(null);
const chooseywlx = (item) => {
  ywlxIndex.value = item.value;
};
commonService.findEnums('watch', [{ enum_key: 'watchCallReasonType', enum_value: 'EnumWatchCallReasonType' }]).then((response) => {
  if (response.success) {
    ywLx.value = response.data.watchCallReasonType;
  }
});
const out_reason_options = ref([]);
// 初始化字典值
const initDict = () => {
  const param = [
    {
      enum_key: 'out_reason_options',
      enum_value: 'EnumOutReason'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    out_reason_options.value = response.data.out_reason_options;
  });
};
const completeService = async (title) => {
  if (title) {
    // closeCurrentTab({
    //   path: '/Console/Console'
    // });
    // 修改电话条状态为空闲
    duty.query = {};
    duty.nowServiceEnd = true;
    // 修改电话条状态为空闲
    window.holly?.setStatus(0);
    duty.callInfo = {};
    closeCurrentTab({
      path: '/Console/Console'
    });
    activeRouteTab({
      path: '/monitoringMan/monitoringMan',
      query: {}
    });
    return;
  }
  if (ywlxIndex.value == 4 && (!reason_remark.value || reason_remark.value.length == 0)) {
    ElMessage({
      message: '请填写呼叫原因！',
      type: 'warning'
    });
    return;
  }
  let res0 = null;
  let type = false;
  let params = {};
  if (route.query.gatewayType && route.query.gatewayType.length > 0) {
    type = true;
    params = {
      call_id: '',
      park_id: duty.callInfo.park_id,
      gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
      call_type: 1,
      call_time: Math.floor((new Date().getTime() - duty.eventStartTime) / 1000),
      user_id: user.user_id,
      call_no: '',
      plate_no: duty.callInfo.plate_no || duty.callInfo.plate_noCopy,
      car_in_biz_no: duty.callInfo.car_in_biz_no || ''
    };
  } else {
    params = {
      call_id: duty.peer.Data.CallSheetID,
      park_id: duty.callInfo.park_id,
      gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
      call_type: duty.call_type,
      call_time: Math.floor((new Date().getTime() - duty.eventStartTime) / 1000),
      user_id: user.user_id,
      call_no: duty.dutycall_no,
      plate_no: duty.callInfo.plate_no || duty.callInfo.plate_noCopy,
      car_in_biz_no: duty.callInfo.car_in_biz_no || ''
    };
  }
  UnattendedApi.recordadd(params).then((res0) => {
    UnattendedApi.recordaddCallReason({
      // watch_call_record_id: type ? res0.data : duty.callInfo.CallSheetID,
      watch_call_record_id: res0.data,
      reason_type: ywlxIndex.value,
      reason_remark: reason_remark.value
    }).then((res) => {
      if (res.success) {
        ElMessage({
          message: '已完成本次服务',
          type: 'success'
        });
        duty.query = {};
        duty.nowServiceEnd = true;
        // 修改电话条状态为空闲
        window.holly?.setStatus(0);
        duty.callInfo = {};
        closeCurrentTab({
          path: '/Console/Console'
        });
        activeRouteTab({
          path: '/monitoringMan/monitoringMan',
          query: {}
        });
      }
    });
  });
};
// 手动抓拍
const manualCapture = async () => {
  let res = await UnattendedApi.triggerCamera({
    park_id: duty.callInfo.park_id,
    gateway_id: isLeave.value ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
    recognition: true
  });

  if (res.success) {
    ElMessage({
      message: res.message,
      type: 'success'
    });
    sdblInit(5);
  } else {
    ElMessage({
      message: res.detail_message || res.message,
      type: 'error'
    });
  }
};
// 手动补录
const handMovement = async () => {
  handMovementRef.value.dialogVisible = true;
  handMovementRef.value.querySentryGateway();
};
//取消入场
const cancelEntry = (name) => {
  ElMessageBox.confirm('是否需要' + name + '?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      UnattendedApi.cancelPass({
        park_id: duty.callInfo.park_id,
        gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
        plate_no: duty.callInfo.plate_no
      }).then((res) => {
        if (res.success) {
          ElMessage({
            message: name + '成功！',
            type: 'success'
          });
          // init();
          clearData();
        } else {
          ElMessage({
            message: res.detail_message || res.message,
            type: 'error'
          });
        }
      });
    })
    .catch(() => { });
};
//入场抬杆
const inTg = () => {
  UnattendedApi.pushOpenStrobe({
    park_id: duty.callInfo.park_id,
    gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
    car_in_biz_no: duty.callInfo.car_in_biz_no,
    car_photo: duty.callInfo.car_photo,
    car_photo2: duty.callInfo.car_photo2
  }).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message,
        type: 'success'
      });
      init();
    } else {
      ElMessage({
        message: res.detail_message || res.message,
        type: 'error'
      });
    }
  });
};
// 特殊放行
const specialReleaseRef = ref(null);
const specialOut = () => {
  specialReleaseRef.value.dialogVisible = true;
  specialReleaseRef.value.specialPass.form.money = duty.callInfo.should_pay_money;
  // 重置出场类型，特殊放行
  duty.callInfo.out_type = 3;
};
// 车辆计费
const calcManualPassCarFee = (row) => {
  duty.callInfo.out_type = 4;

  // 设置参数
  duty.callInfo.plate_no = row.plate_no;
  duty.callInfo.has_car_in_record = true;

  // 查询最近一条入场记录
  queryLastParkInRecord('车辆计费');
  matchEntryRecordsRef.value.dialogVisible = false;
  // 查询入场记录
  // queryParkInRecord();

  // 查询出场记录
  // queryParkOutRecord(row);

  // 查询可用优免券
  // queryAvailableCoupons();
};
// 切换费率
const switchCarType = (i, type) => {
  if (duty.callInfo.parkOrder?.car_in_biz_no == null || !duty.callInfo.has_car_in_record) {
    if (!type) {
      ElMessage({
        message: '无订单信息，不允许刷新订单',
        type: 'warning'
      });
    }

    return;
  }

  const param = {
    park_order_id: duty.callInfo.park_order_id,
    park_order_no: duty.callInfo.park_order_no,
    car_type: i,
    park_name: duty.callInfo.park_name,
    pre_car_type: duty.callInfo.car_type,
    pre_order_money: (Number(duty.callInfo.should_pay_money || 0) + Number(duty.callInfo.current_coupon_money || 0)).toFixed(2)
  };
  // if (!duty.callInfo.park_order_id || !duty.callInfo.park_order_no) {
  //   return;
  // }
  UnattendedApi.refreshOrder(param).then((res) => {
    if (res.success) {
      if (!type) {
        ElMessage({
          message: '切换计费方式成功',
          type: 'success'
        });
      }

      duty.callInfo = {
        ...duty.callInfo,
        ...res.data
      };
      carLxIndex.value = i;
      duty.callInfo.parkOrder = res.data;
      duty.callInfo.pre_car_type = duty.callInfo.car_type;
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
  });
};
// 收费放行
const handleChargeAndPass = () => {
  // 重置出场类型，人工放行】
  if (duty.callInfo.parkOrder?.car_in_biz_no == null) {
    ElMessage({
      message: '无订单信息，不允许收费放行',
      type: 'warning'
    });
    return;
  }
  if (duty.callInfo.lastParkInRecord?.car_in_biz_no == null) {
    ElMessage({
      message: '无最近入场记录，不允许收费放行',
      type: 'warning'
    });
    return;
  }
  // if (duty.callInfo.car_out_biz_no == null || duty.callInfo.car_out_biz_no == undefined) {
  //   ElMessage({
  //     message: '无出场流水号，不允许收费放行',
  //     type: 'warning'
  //   });
  //   return;
  // }
  ElMessageBox.confirm('是否需要收费放行?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'info'
  })
    .then(() => {
      const param = {
        park_id: duty.callInfo.park_id,
        gateway_id: duty.callInfo.gateway_out_id,
        park_order_no: duty.callInfo.park_order_no,
        car_in_biz_no: duty.callInfo.car_in_biz_no,
        money: duty.callInfo.should_pay_money,
        out_type: duty.callInfo.out_type,
        out_reason: '',
        out_state: 1,
        memo: '',
        plate_no: duty.callInfo.plate_no
      };
      UnattendedApi.chargeAndPass(param).then((res) => {
        if (res.success) {
          ElMessage({
            message: '收费放行成功',
            type: 'success'
          });

          // 收费成功，清空数据
          clearData();

          // 收费成功，刷新车场统计信息
          // statParkStatus();
        } else {
          ElMessage({
            dangerouslyUseHTMLString: true,
            message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
            type: 'error'
          });
        }
      });
    })
    .catch(() => { });
};
const dialogVisible = ref(false);
const title = ref('');
const dialogImageUrl = ref('');
const checkInPicture = (url) => {
  if (url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = url;
  }
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  user-select: none;
}

.shadowbox {
  // position: fixed;
  // top: 0;
  // left: 0;
  width: 100%;
  height: 780px;
  background: #fff;
  display: flex;
  align-items: center;
  justify-content: center;

  .gth {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #ff7c00;
    color: #fff;
    text-align: center;
    line-height: 40px;
    font-weight: bold;
    font-size: 20px;
  }

  >div {
    color: #ff7c00;
    font-size: 20px;
    margin-left: 10px;
  }
}

.lastCallBox {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 10px;

  >div {
    border-radius: 5px;
  }

  .imgone {
    height: calc(100% - 80px);
    width: 100%;
    padding: 5px 5px 0 5px;

    img {
      height: 100%;
      width: 100%;
      border-radius: 5px;
    }
  }

  .iconR {
    animation: an_iconR 1s linear infinite;

    @keyframes an_iconR {
      0% {
        transform: rotate(0);
      }

      100% {
        transform: rotate(360deg);
      }
    }
  }

  .buttonClass {
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;

    >div {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 5px;
    }
  }

  .imgCol {
    flex: 3;
    display: flex;
    flex-direction: column;
    gap: 10px;

    >div {
      flex: 1;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .imgTit {
        height: 40px;
        display: flex;
        font-size: 14px;
        align-items: center;
        padding-left: 10px;
        border-bottom: 1px solid #e7e7e7;
        font-weight: 700;
        color: #01a7f0;
      }

      .img {
        height: calc(100% - 40px);
        width: 100%;
        padding: 5px;

        img {
          height: 100%;
          width: 100%;
          border-radius: 5px;
        }
      }

      .noImg {
        height: calc(100% - 40px);
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
        font-size: 14px;
      }
    }

    .imgNav {
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 150px;

      >div {
        cursor: pointer;
      }

      .acImg {
        color: #01a7f0;
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: -5px;
          left: 50%;
          transform: translateX(-50%);
          width: 60%;
          height: 2px;
          border-radius: 2px;
          background-color: #01a7f0;
        }
      }
    }
  }

  .infoCol-t {
    height: 40px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 700;

    .fill {
      height: 35%;
      width: 3px;
      border-radius: 3px;
      background-color: #01a7f0;
      margin-right: 5px;
    }
  }

  .infoCol {
    flex: 5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    >div {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    }

    .fixCar {
      padding: 2px 10px;
      border: 1px solid #d9d9d9;
      border-radius: 3px;

      &:hover {
        color: #01a7f0;
        border-color: #01a7f0;
      }
    }

    .infoCol-one {
      flex: 1.5;
      padding: 0 10px 10px 10px;

      .infoCol-b {
        height: calc(100% - 40px);
        width: 100%;
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        padding: 5px;
        font-size: 14px;
        display: grid;
        background-color: #fafafa;
        grid-template-columns: repeat(2, 1fr);
        align-items: center;
      }
    }

    .infoCol-two {
      flex: 2.5;
      padding: 0 10px 10px 10px;
      font-size: 14px;

      .enterInfo {
        height: calc(100% - 40px);

        .enterInfoT {
          height: 90%;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          gap: 10px;

          .carNmber {
            font-size: 30px;
            font-weight: 700;
          }
        }

        .enterInfoB {
          height: 10%;
          display: flex;
          //   //   align-items: center;
          justify-content: center;
          margin-top: 10px;
          gap: 20px;

          .el-button {
            font-size: 14px;
          }
        }
      }

      .carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;

        div:first-child {
          display: flex;
          align-items: center;
          gap: 10px;

          div:first-child {
            padding: 3px 20px;
            background-color: #01a7f0;
            border-radius: 5px;
            color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
          }
        }
      }

      .infoCol-two-con {
        height: calc(100% - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
      }
    }

    .infoCol-three {
      flex: 5;
      padding: 0 10px 10px 10px;

      .infoCol-three-con {
        height: calc(100% - 40px);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 10px;
      }

      .infoCol-three-cons {
        height: calc(100% - 40px);

        .three-top {
          height: 60%;
          padding: 5px;
          border: 1px solid #9fc1e1;
          border-radius: 5px;
          background-color: #fafafa;
          font-size: 14px;

          .th-t {
            height: 30px;
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 700;
          }

          .th-b {
            height: calc(100% - 30px);
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            align-items: center;

            .th-b-ym {
              display: flex;
              align-items: center;
              gap: 20px;

              .el-button {
                font-size: 14px;
              }
            }
          }
        }

        .three-center {
          height: 20%;
          display: flex;
          align-items: center;
          font-weight: 700;

          .th-r {
            // width: 15%;
            margin-left: 20px;
            white-space: nowrap;
          }

          .th-l {
            cursor: pointer;
            display: flex;
            align-items: center;
            font-size: 14px;
            margin-left: 20px;
            font-weight: normal;
            border-radius: 5px;
            overflow: hidden;
            // border: 1px solid #d9d9d9;

            >div:not(:last-child) {
              // border-right: 1px solid #d9d9d9;
            }

            >div {
              padding: 7px 25px;
              white-space: nowrap;
            }
          }

          .caractive {
            background-color: #01a7f0;
            color: #fff;
          }
        }

        .three-bottom {
          height: 20%;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px;

          .el-button {
            font-size: 14px;
          }
        }
      }
    }
  }

  .reasonCol {
    flex: 2;
    padding: 0 10px 10px 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .reasonCol-one {
      height: fit-content;
      display: flex;
      flex-direction: column;
      background-color: #fff;
      border-radius: 5px;

      .reasonCol-one-tit {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #01a7f0;
        font-size: 14px;
        font-weight: 700;
        border-bottom: 1px solid #e7e7e7;
      }

      .reasonCol-one-con {
        max-height: 250px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        justify-items: center;
        align-items: center;
        gap: 10px;
        padding: 10px;

        // > button {
        //   height: 30px;
        //   width: 100px;
        //   border: 1px solid #9fc1e1;
        //   display: flex;
        //   align-items: center;
        //   justify-content: center;
        //   border-radius: 5px;
        //   font-size: 14px;
        //   cursor: pointer;

        //   &:hover {
        //     background-color: #ecf5ff;
        //     border-color: #409eff;
        //     color: #409eff;
        //   }
        // }
        .el-button {
          margin-left: 0 !important;
        }
      }
    }

    .reasonCol-two {
      flex: 1;
      background-color: #fff;
      border-radius: 5px;
      padding: 0 10px 10px 10px;
      font-size: 14px;

      .reasonCol-two-tit {
        color: #7b7b7b;
        margin: 5px 0;
      }

      .reasonCol-two-ywlx {
        // display: grid;
        // grid-template-columns: repeat(4, 1fr);
        // justify-items: center;
        // align-items: center;
        display: flex;
        flex-wrap: wrap;
        gap: 5px;

        >div {
          padding: 2px 10px;
          border: 1px solid #d9d9d9;
          border-radius: 5px;
          font-size: 14px;
          cursor: pointer;
        }

        .ywlxactive {
          background-color: #01a7f0;
          color: #fff;
        }
      }

      .reasonCol-two-submit {
        height: 40px;
        width: 70%;
        border-radius: 5px;

        display: flex;
        align-items: center;
        justify-content: center;
        margin: 20px auto;

        &:hover {
          filter: brightness(0.9);
        }
      }

      .reasonCol-two-submi2 {
        color: #fff;
        // background-color: #ff7c00;
      }
    }
  }
}

.th-b-xq {
  font-size: 10px;
  color: #01a7f0;
  text-decoration: underline;
  margin-left: 10px;
  cursor: pointer;

  &:hover {
    color: blue;
  }
}
</style>
