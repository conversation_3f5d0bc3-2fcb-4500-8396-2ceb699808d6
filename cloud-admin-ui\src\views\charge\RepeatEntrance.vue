<template>
  <div class="container">
    <repeat-entrance-search @form-search="searchRepeatEntranceList" @reset="resetParamsAndData" />
    <repeat-entrance-table ref="table" />
  </div>
</template>

<script setup name="RepeatEntrance">
import RepeatEntranceSearch from './repeatEntrance/RepeatEntranceSearch.vue';
import RepeatEntranceTable from './repeatEntrance/RepeatEntranceTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageRepeatEntrance = (queryParams) => {
  table.value.getList(queryParams);
};

const searchRepeatEntranceList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageRepeatEntrance
});
</script>
