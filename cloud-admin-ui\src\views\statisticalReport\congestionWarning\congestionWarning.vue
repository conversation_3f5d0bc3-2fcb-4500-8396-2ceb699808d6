<template>
  <div class="container">
    <park-space-availability-search @form-search="searchParkSpaceAvailabilityList" />
    <park-space-availability-table ref="table" />
  </div>
</template>

<script name="ParkSpaceAvailability" setup>
import ParkSpaceAvailabilitySearch from './congestionWarning/congestionWarningSearch.vue';
import ParkSpaceAvailabilityTable from './congestionWarning/congestionWarningTable.vue';
import { ref } from 'vue';

const table = ref(null);

const searchParkSpaceAvailabilityList = (queryParams) => {
  console.log('search');
  table.value.getList(queryParams);
};
</script>
