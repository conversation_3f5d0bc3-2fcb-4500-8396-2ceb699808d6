import * as appointment from '@/api/charge/AppointmentRecordApi';

/**
 * 预约记录
 */
export default {
  /**
   * 分页查询预约记录
   */
  pagingAppointmentRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        appointment.pagingAppointmentRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出预约记录
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        appointment.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
