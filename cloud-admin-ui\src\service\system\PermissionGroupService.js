import * as peimissionGroup from '@/api/system/PermissionGroupApi';

/**
 *  权限组层
 */
export default {
  /**
   * 分页查询权限组
   */
  pagingPermissionGroup(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.pagingPermissionGroup(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 根据权限组ID查询权限信息
   */
  getPermissionById(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.getPermissionById(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增权限组
   */
  createPermissionGroup(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.createPermissionGroup(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改权限组
   */
  updatePermissionGroup(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.updatePermissionGroup(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除权限组
   */
  deletePermissionGroup(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.deletePermissionGroup(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 权限组列表
   */
  permissionGroupList(data) {
    return new Promise((resolve, reject) => {
      try {
        peimissionGroup.permissionGroupList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
