import * as invoice from '@/api/park/ParkInvoiceApi';

/**
 * 车场-发票
 */
export default {
  /**
   * 分页查找发票信息
   */
  pagingInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.pagingInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建发票信息
   */
  createInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.createInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 编辑发票信息
   */
  updateInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.updateInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 发票信息删除
   */
  deleteInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.deleteInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 注册发票
   */
  registerInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.registerInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
