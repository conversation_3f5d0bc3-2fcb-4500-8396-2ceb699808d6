<template>
  <div>
    <electronic-pay-percent-search @form-search="searchElectronicPayPercentList" @reset="resetParamsAndData" />
    <electronic-pay-percent-table ref="table" />
  </div>
</template>

<script name="ElectronicPayPercent" setup>
import ElectronicPayPercentSearch from './electronicPayPercent/ElectronicPayPercentSearch.vue';
import ElectronicPayPercentTable from './electronicPayPercent/ElectronicPayPercentTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchElectronicPayPercentList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
