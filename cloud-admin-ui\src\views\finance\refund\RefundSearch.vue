<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.refund_types" placeholder="退款类型" clearable multiple>
        <el-option v-for="item in refundTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.refund_states" placeholder="退款状态" clearable multiple>
        <el-option v-for="item in refundStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="申请开始日期"
        end-placeholder="申请结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="RefundSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import commonService from '@/service/common/CommonService';
import { reactive, onMounted, ref } from 'vue';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';

const emits = defineEmits(['form-search']);
const refundTypeList = ref([]);
const refundStateList = ref([]);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    refund_types: [],
    refund_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);

onMounted(() => {
  initSelects();
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  // if (user.role_id == 1) {
  //   return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'refundTypeList',
      enum_value: 'EnumRefundType'
    },
    {
      enum_key: 'refundStateList',
      enum_value: 'EnumRefundState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    refundTypeList.value = response.data.refundTypeList;
  });
  commonService.findEnums('order', param).then((response) => {
    refundStateList.value = response.data.refundStateList;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
    // //判断组织架构是否选择
    // ElMessage({
    //   message: '请选择停车场进行查询',
    //   type: 'warning'
    // });
    // return false;
  }
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    refund_types: [],
    refund_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
