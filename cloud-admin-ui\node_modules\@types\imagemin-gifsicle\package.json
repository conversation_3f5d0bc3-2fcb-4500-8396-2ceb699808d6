{"name": "@types/imagemin-gifsicle", "version": "7.0.4", "description": "TypeScript definitions for imagemin-gifsicle", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin-gifsicle", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "romain-faust", "url": "https://github.com/romain-faust"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-gifsicle"}, "scripts": {}, "dependencies": {"@types/imagemin": "*"}, "typesPublisherContentHash": "e552a6edc70f673e29124cca6053a1e1457466d6212bf0dfe0bd30f135dce875", "typeScriptVersion": "4.5"}