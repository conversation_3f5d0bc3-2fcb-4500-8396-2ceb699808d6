<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space><div></div></el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 195px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleDownLoad(scope.row)"> 下载 </el-button>
            <!--            <el-button link type="danger" @click="batchDelete(scope.row.id)"> 删除 </el-button>-->
          </template>
        </el-table-column>
        <el-table-column prop="name" label="文件名称" align="center" min-width="180" />
        <el-table-column prop="module_desc" label="所属模块" align="center" min-width="180" />
        <el-table-column prop="size" label="大小（字节）" align="center" min-width="180" />
        <el-table-column prop="operator_name" label="导出人" align="center" min-width="180" />
        <el-table-column prop="expired_time" label="失效时间" align="center" min-width="180" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ReportDownloadTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import commonService from '@/service/common/CommonService';
import reportDownloadService from '@/service/documentCenter/ReportDownloadService';
import { saveToFile } from '@/utils/utils.js';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  reportDownloadService.pagingReportExportRecords(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 下载
const handleDownLoad = (row) => {
  commonService.fileDownload(row.path).then((res) => {
    let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
    saveToFile(res.data, decodeURIComponent(fileName));
  });
};

// 删除
const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    reportDownloadService
      .deleteReportExportRecord(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
