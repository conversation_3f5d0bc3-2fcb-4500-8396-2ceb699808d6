<template>
  <div class="container">
    <div style="display: flex; align-items: center">
      <div v-for="(item, i) in msgData" :key="i" style="margin-right: 20px">{{ item }}</div>
    </div>
  </div>
</template>

<script setup name="SpecialRelease">
import sentryBoxRecordService from '@/service/charge/SentryBoxRecordService';
import { ref } from 'vue';

const msgData = ref([]);
const getData = (params) => {
  sentryBoxRecordService.countAgentEventLogByType(params).then((response) => {
    if (response.success) {
      msgData.value = response.data.map((item) => {
        return item.event_type_desc + '：' + item.type_count + '条';
      });
    } else {
      msgData.value = '数据加载失败';
    }
  });
};

defineExpose({
  getData
});
</script>
