<template>
  <div class="container">
    <car-intercept-record-search @form-search="searchCarInterceptRecord" @reset="resetParamsAndData" />
    <car-intercept-record-table ref="table" />  
  </div>
</template>

<script setup name="CarInterceptRecord">
import CarInterceptRecordSearch from './carInterceptRecord/CarInterceptRecordSearch.vue';
import CarInterceptRecordTable from './carInterceptRecord/CarInterceptRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchCarInterceptRecord = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};

defineExpose({
  searchCarInterceptRecord
});
</script>
