/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询临停车位日均收入
export const pagingParkCarDailyPayment = (data) => {
  return $({
    url: '/console/statistics/park/space/average/income/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/park/space/average/income/exportByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportAllData = (data) => {
  return $.post("/console/statistics/park/space/average/income/exportSummaryByPeriod", data)
}
