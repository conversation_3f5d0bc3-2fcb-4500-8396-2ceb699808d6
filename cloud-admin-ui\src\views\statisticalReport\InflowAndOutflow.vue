<template>
  <div class="container">
    <inflow-and-outflow-search @form-search="searchInflowAndOutflowList" @reset="resetParamsAndData" />
    <inflow-and-outflow-table ref="table" />
  </div>
</template>

<script name="InflowAndOutflow" setup>
import InflowAndOutflowSearch from './inflowAndOutflow/InflowAndOutflowSearch.vue';
import InflowAndOutflowTable from './inflowAndOutflow/InflowAndOutflowTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchInflowAndOutflowList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
