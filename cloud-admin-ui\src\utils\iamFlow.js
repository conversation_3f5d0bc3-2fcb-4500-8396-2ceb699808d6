import { ElMessageBox } from 'element-plus';
import { useUser } from '@/stores/user';
export const getOpenUrl = (url, isToken) => {
  const user = useUser();
  const origin = window.location.origin;
  let baseUrl = '';
  // 生产环境
  if (origin.indexOf('park.huidawanan.com') != -1) {
    baseUrl = 'https://wzt.wandacm.cn/';
  }
  // 测试环境
  if (origin.indexOf('test.huidawanan.com') != -1) {
    baseUrl = 'https://test.huidawanan.cn/';
  }

  // 本地测试
  if (origin.indexOf('localhost') != -1) {
    // baseUrl = 'http://localhost/';
    baseUrl = 'https://test.huidawanan.cn/';
  }
  if (isToken) {
    return baseUrl + url + `?token=${user.iam_token.split('Bearer ')[1]}`;
  } else {
    return baseUrl + url;
  }
};

// 仅iam登录下的操作
export const getIamTokenOpen = (url) => {
  const user = useUser();
  if (user.iam_token.length <= 0) {
    ElMessageBox.confirm('请切换iam登录账号开启审批权限', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const VITE_GLOB_IAM_LOGIN_URL = import.meta.env.VITE_GLOB_IAM_LOGIN_URL;
      const VITE_GLOB_MIDDLE_LOGIN_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_URL;
      const u1 = encodeURIComponent(location.href.split('?')[0]);
      console.log(
        `${VITE_GLOB_IAM_LOGIN_URL}/idp/oauth2/authorize?redirect_uri=${VITE_GLOB_MIDDLE_LOGIN_URL}/jpaas/user/qrcodeLogin&state=${u1}&client_id=wd_zhxf&response_type=code`
      );
      return location.replace(
        `${VITE_GLOB_IAM_LOGIN_URL}/idp/oauth2/authorize?redirect_uri=${VITE_GLOB_MIDDLE_LOGIN_URL}/jpaas/user/qrcodeLogin&state=${u1}&client_id=wd_zhxf&response_type=code`
      );
    });
  } else {
    // 中台审批流
    return window.open(url, '_blank');
  }
};

// iam登录和常规登录均可操作
export const getIamAndNormal = (url) => {
  // const user = useUser();
  // if (user.iam_token.length <= 0) {
  //   return false;
  // } else {
  //   // 中台审批流
  //   return window.open(url, '_blank');
  // }
  return false;
};

export const GetIamTokenOpenFlag = () => {
  const user = useUser();
  if (user.iam_token.length <= 0) {
    ElMessageBox.confirm('请切换iam登录账号开启审批权限', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const VITE_GLOB_IAM_LOGIN_URL = import.meta.env.VITE_GLOB_IAM_LOGIN_URL;
      const VITE_GLOB_MIDDLE_LOGIN_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_URL;
      const u1 = encodeURIComponent(location.href.split('?')[0]);
      console.log(
        `${VITE_GLOB_IAM_LOGIN_URL}/idp/oauth2/authorize?redirect_uri=${VITE_GLOB_MIDDLE_LOGIN_URL}/jpaas/user/qrcodeLogin&state=${u1}&client_id=wd_zhxf&response_type=code`
      );
      return location.replace(
        `${VITE_GLOB_IAM_LOGIN_URL}/idp/oauth2/authorize?redirect_uri=${VITE_GLOB_MIDDLE_LOGIN_URL}/jpaas/user/qrcodeLogin&state=${u1}&client_id=wd_zhxf&response_type=code`
      );
    });
    return false;
  } else {
    return true;
  }
};
