<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="temporaryParkService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 300px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="etc_pay_money" label="ETC缴费" align="center" />
        <el-table-column prop="etc_pay_count" label="ETC缴费笔数" align="center" />
        <el-table-column prop="epay_money" label="电子支付" align="center" />
        <el-table-column prop="epay_count" label="电子支付笔数" align="center" />
        <el-table-column prop="parking_third_party_income" label="第三方会员总收入（元）" align="center" min-width="180" />
        <el-table-column prop="parking_third_party_count" label="第三方会员笔数" align="center" min-width="180" />
        <el-table-column prop="total_car_out" label="出场车次" align="center" />
        <el-table-column prop="total_car_in" label="进场车次" align="center" />
        <el-table-column prop="total_money" label="现金合计" align="center" width="100" />
        <el-table-column prop="total_count" label="现金总笔数" align="center" width="100" />
        <el-table-column prop="money" label="岗亭现金" align="center" />
        <el-table-column prop="manual_money" label="手动抬杆收费" align="center" />
        <el-table-column prop="received_money" label="中央岗亭已收现金" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="TemporaryParkMoneyOverviewTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import temporaryParkService from '@/service/statisticalReport/TemporaryParkService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  temporaryParkService.pagingTemporaryPark(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
