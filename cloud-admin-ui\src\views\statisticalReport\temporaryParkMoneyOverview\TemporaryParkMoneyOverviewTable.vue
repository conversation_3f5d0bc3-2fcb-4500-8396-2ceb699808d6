<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space
        ><div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div></el-space
      >
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 340px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column label="时间周期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="etc_pay_money" label="ETC缴费" align="center" />
        <el-table-column prop="etc_pay_count" label="ETC缴费笔数" align="center" />
        <el-table-column prop="epay_money" label="电子支付" align="center" />
        <el-table-column prop="epay_count" label="电子支付笔数" align="center" />
        <el-table-column prop="parking_third_party_income" label="第三方会员总收入（元）" align="center" min-width="180" />
        <el-table-column prop="parking_third_party_count" label="第三方会员笔数" align="center" min-width="180" />
        <el-table-column prop="total_car_out" label="出场车次" align="center" />
        <el-table-column prop="total_car_in" label="进场车次" align="center" />
        <el-table-column prop="total_money" label="现金合计" align="center" width="100" />
        <el-table-column prop="total_count" label="现金总笔数" align="center" width="100" />
        <el-table-column prop="money" label="岗亭现金" align="center" />
        <el-table-column prop="manual_money" label="手动抬杆收费" align="center" />
        <el-table-column prop="received_money" label="中央岗亭已收现金" align="center" />
      </el-table>
    </div>
       <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30,100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
     </div>
  </el-card>
</template>

<script name="TemporaryParkMoneyOverviewTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import temporaryParkService from '@/service/statisticalReport/TemporaryParkService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total=ref(0)
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
       page: 1,
       limit:30
  }
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
   getList({})
}
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({})
}
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(12);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
const getList = (params) => {
  loading.value = true;
    data.queryParams =  {...data.queryParams,...params};
  temporaryParkService.pagingTemporaryPark(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      loading.value = false;
      total.value= Number(response.data.total);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end
}
</style>
