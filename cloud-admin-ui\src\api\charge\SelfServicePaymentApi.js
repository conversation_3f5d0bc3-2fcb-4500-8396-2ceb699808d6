import $ from '@/utils/axios';

// 分页查询入场记录
export const pagingInvoiceRecords = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/pagingInvoiceRecords',
    method: 'post',
    data
  });
};
// 保存提现操作记录
export const saveWithdrawalRecords = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/saveWithdrawalRecords',
    method: 'post',
    data
  });
};
// 分页查询提现操作记录
export const pagingWithdrawalRecords = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/pagingWithdrawalRecords',
    method: 'post',
    data
  });
};
// 分页查询现金缴费记录
export const pagingCashChangeRecords = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/pagingCashChangeRecords',
    method: 'post',
    data
  });
};
// 所有缴费机查询
export const listPayMachines = () => {
  return $({
    url: '/console/park/fee/invoiceRecords/listPayMachines',
    method: 'get'
  });
};
// 导出缴费机管理
export const exportInvoiceRecords = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/exportInvoiceRecords',
    method: 'post',
    data
  });
};
// 导出缴费机提现记录
export const exportPayMachineWithdrawRecord = (data) => {
  return $({
    url: '/console/park/fee/invoiceRecords/exportPayMachineWithdrawRecord',
    method: 'post',
    data
  });
};
