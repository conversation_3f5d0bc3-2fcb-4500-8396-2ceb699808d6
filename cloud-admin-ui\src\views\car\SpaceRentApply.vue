<template>
  <div>
    <space-rent-apply-search @form-search="searchSpaceRentApplyList" @reset="resetParamsAndData" />
    <space-rent-apply-table ref="table" />  
  </div>
</template>

<script setup name="SpaceRentApply">
import SpaceRentApplySearch from './spaceRentApply/SpaceRentApplySearch.vue';
import SpaceRentApplyTable from './spaceRentApply/SpaceRentApplyTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchSpaceRentApplyList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
