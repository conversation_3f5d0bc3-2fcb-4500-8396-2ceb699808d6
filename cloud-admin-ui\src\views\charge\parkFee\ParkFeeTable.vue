<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton btnType="default" :exportFunc="parkFeeService.exportParkFee"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
        </DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 430px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button link type="primary"
              v-if="scope.row.order_state === 2 && (scope.row.refund_state === 3 || scope.row.refund_state === 4) && scope.row.payed_money !== 0"
              @click="refundMoney(scope.row)">
              申请退款
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="订单号" align="center" width="300" />
        <el-table-column prop="park_name" label="停车场名称" align="center" width="150" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="150" />
        <el-table-column prop="in_time" label="入场时间" align="center" width="175" />
        <el-table-column prop="in_gateway_name" label="入场通道" align="center" width="120" />
        <el-table-column prop="in_car_photo" label="入场图片" align="center" width="110">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="out_car_photo" label="出场图片" align="center" width="110">
          <template #default="scope">
            <el-button link type="primary" @click="checkOutPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="to_time" label="出场时间" align="center" width="175" />
        <el-table-column prop="out_gateway_name" label="出场通道" align="center" width="120" />
        <el-table-column prop="time" label="停车时长" align="center" width="100" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" width="100" />
        <el-table-column prop="stop_car_type_desc" label="停车类型" align="center" width="100">
          <template #default="scope">
            <span
              v-if="scope.row.stop_car_type === undefined || scope.row.stop_car_type === null || scope.row.stop_car_type === ''">
              临停车辆</span>
            <span v-else> {{ scope.row.stop_car_type_desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" label="车牌号" align="center" width="100" />
        <el-table-column prop="order_state_desc" label="订单状态" align="center" width="100" />
        <el-table-column prop="pay_time" label="支付时间" align="center" width="100" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" width="100" />
        <el-table-column prop="pay_channel_desc" label="支付渠道" align="center" width="100" />
        <el-table-column prop="order_money" label="应交金额" align="center" width="100" />
        <el-table-column prop="debate_money" label="优惠金额" align="center" width="100" />
        <el-table-column prop="payed_money" label="实缴金额" align="center" width="100" />
        <el-table-column prop="transfer_amount" label="找零金额" align="center" width="100" />
        <el-table-column prop="state" label="找零状态" align="center" width="100" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="100" />
        <el-table-column prop="charge_name" label="收费员" align="center" width="100" />
        <el-table-column prop="invoice_state_desc" label="发票状态" align="center" width="100" />
        <el-table-column prop="is_hong_kong_macau_desc" label="是否港澳车" align="center" width="100" />
        <el-table-column prop="pay_channel_order_no" label="交易单号" align="center" width="100" />
        <el-table-column prop="special_loss_money" label="特殊处理损失" align="center" width="120" />
        <el-table-column prop="flush_loss_money" label="被冲车辆损失" align="center" width="120" />
        <el-table-column prop="next_in_desc" label="次入记录" align="center" width="100" />

      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <!-- 申请退款 -->
    <el-dialog v-if="refundDialogVisible" width="500px" title="申请退款" v-model="refundDialogVisible"
      @close="closeDialog(refund)">
      <el-form ref="refund" label-width="90px" :rules="data.rules" :model="data.refundForm">
        <el-form-item label=" 车牌号">
          {{ data.refundForm.plate_no }}
        </el-form-item>
        <el-form-item prop="refund_user" label="姓名">
          <el-input v-model="data.refundForm.refund_user" placeholder="姓名" />
        </el-form-item>
        <el-form-item prop="mobile" label="手机号">
          <el-input v-model="data.refundForm.mobile" placeholder="手机号" />
        </el-form-item>
        <el-form-item prop="refund_money" label="退款金额">
          <el-input v-model="data.refundForm.refund_money" placeholder="退款金额" />
        </el-form-item>
        <el-form-item prop="refund_channel" label="退款渠道">
          <el-select v-model="data.refundForm.refund_channel" style="width: 100%" placeholder="退款渠道">
            <el-option v-for="item in refundWayList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="refund_account" label="退款账号">
          <el-input v-model="data.refundForm.refund_account" placeholder="退款账号" />
        </el-form-item>
        <el-form-item prop="refund_reason" label="退款原因">
          <el-input v-model="data.refundForm.refund_reason" type="textarea" placeholder="退款原因" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="applyRefund(refund)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="ParkFeeTable" setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, onActivated, onMounted } from 'vue';
import parkFeeService from '@/service/charge/ParkFeeService';
import commonService from '@/service/common/CommonService';
import DownloadButton from '@/components/DownloadButton.vue';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const refund = ref();
const refundWayList = ref([]);
const refundDialogVisible = ref(false);
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const title = ref('');
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  refundForm: {
    id: undefined,
    refund_user: undefined,
    mobile: undefined,
    refund_money: undefined,
    should_pay_money: undefined,
    payed_money: undefined,
    refund_channel: undefined,
    refund_account: undefined,
    refund_reason: undefined,
    plate_no: undefined
  },
  rules: {
    refund_user: [
      {
        required: true,
        message: '请输入退款人姓名',
        trigger: 'blur'
      }
    ],
    mobile: [
      {
        required: true,
        message: '请输入退款人手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    refund_money: [
      {
        required: true,
        message: '请输入退款金额',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: (rule, value, callback) => {
          if (value > data.refundForm.payed_money) {
            callback(new Error('退款金额不能大于实缴金额!'));
          } else {
            callback();
          }
        }
      }
    ],
    refund_channel: [
      {
        required: true,
        message: '请选择退款渠道',
        trigger: 'blur'
      }
    ],
    refund_account: [
      {
        required: true,
        message: '请输入退款账号',
        trigger: 'blur'
      }
    ]
  }
});

onActivated(() => {
  // getList(data.queryParams);
  initSelects();
});

onMounted(() => {
  data.queryParams.order_states = [1, 2];
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = (event) => {
  console.log('接收', event.data, event);
  getList(data.queryParams);
};
const initSelects = () => {
  const param = [
    {
      enum_key: 'refundWayList',
      enum_value: 'EnumRefundChannelType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    refundWayList.value = response.data.refundWayList;
  });
};

const getList = (params) => {
  if (!params?.park_id || params?.park_id == '') return;
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;

  const { park_name, ...newParams } = params;
  parkFeeService.pagingParkFee(newParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 查看入场图片
const checkInPicture = (row) => {
  if (row.in_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '入场图片';
    dialogImageUrl.value = row.in_car_photo_url;
  }
};

// 查看出场图片
const checkOutPicture = (row) => {
  if (row.out_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '出场图片';
    dialogImageUrl.value = row.out_car_photo_url;
  }
};

// 申请退款
const refundMoney = (row) => {
  data.refundForm = {
    id: row.id,
    park_id: row.park_id,
    order_no: row.order_no,
    refund_user: row.refund_user,
    mobile: row.mobile,
    refund_money: row.refund_money,
    should_pay_money: row.should_pay_money,
    payed_money: row.payed_money,
    refund_channel: row.refund_channel,
    refund_account: row.refund_account,
    refund_reason: row.refund_reason,
    plate_no: row.plate_no
  };
  data.refundForm.plate_no = row.plate_no;
  refundDialogVisible.value = true;
};
const applyRefund = (refund) => {
  console.log(data.refundForm);
  refund.validate().then(() => {
    parkFeeService
      .applyRefund(data.refundForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          refundDialogVisible.value = false;

          // 表单流程  停车缴费-临停退款申请单
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const closeDialog = (refund) => {
  refund.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
