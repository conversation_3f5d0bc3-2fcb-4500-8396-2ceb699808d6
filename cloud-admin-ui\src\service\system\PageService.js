import * as page from '@/api/system/PageApi';

/**
 * 页面服务层
 */
export default {
  /**
   * 添加页面
   */
  createPage(param) {
    return new Promise((resolve, reject) => {
      try {
        page.createPage(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改页面
   */
  updatePage(param) {
    return new Promise((resolve, reject) => {
      try {
        page.updatePage(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除页面
   */
  deletePage(menuId) {
    return new Promise((resolve, reject) => {
      try {
        page.deletePage(menuId).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取页面
   */
  getPageById(id) {
    return new Promise((resolve, reject) => {
      try {
        page.getPageById(id).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
