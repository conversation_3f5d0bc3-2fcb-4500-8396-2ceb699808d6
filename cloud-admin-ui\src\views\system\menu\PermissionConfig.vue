<template>
  <el-card shadow="never" class="permission-card table-warp">
    <template #header>
      <div class="card-header">
        <span>权限配置</span>
        <div>
          <el-button type="primary" @click="createPermission"> 添加权限 </el-button>
        </div>
      </div>
    </template>
    <el-table ref="multipleTable" :data="tableData" border>
      <el-table-column type="selection" width="40" style="text-align: right" />
      <el-table-column prop="action" label="操作" align="center" width="100">
        <template #default="scope">
          <el-link type="primary" :underline="false" @click="updatePermission(scope.row)"> 修改 </el-link>&ensp;
          <el-link type="danger" :underline="false" @click="deletePermission(scope.row)"> 删除 </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="权限名称" align="center" />
      <el-table-column prop="code" label="权限代码" align="center" />
      <el-table-column prop="permission_group_id" label="权限分组" align="center">
        <template #default="scope">
          <span v-for="item in permissionGroupList" :value="item.id" :key="item.id">{{
            '' + scope.row.permission_group_id === item.id ? item.group_name : ''
          }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="type" label="权限类型" align="center"
        ><template #default="scope">
          <span v-for="item in typeList" :value="item.value" :key="item.value">{{ '' + scope.row.type === item.value ? item.key : '' }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="data.queryParams.page"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="data.queryParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
    <!-- 添加权限 -->
    <el-dialog
      title="添加权限"
      v-model="createPermissionDialogVisible"
      :close-on-click-modal="false"
      @close="closeAddDialog(createForm)"
      width="500px"
    >
      <el-form ref="createForm" :model="data.permissionCreateForm" label-width="100px" :rules="data.permissionRules">
        <el-form-item label="页面名称" prop="page_name">
          <el-input v-model="data.permissionCreateForm.page_name" :disabled="true" />
        </el-form-item>
        <el-form-item label="权限分组" prop="permission_group_id">
          <el-select v-model="data.permissionCreateForm.permission_group_id" style="width: 100%">
            <el-option v-for="item in permissionGroupList" :key="item.id" :label="item.group_name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="data.permissionCreateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="data.permissionCreateForm.code" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelCreatePermission(createForm)"> 取 消 </el-button>
        <el-button type="primary" @click="submitCreatePermission(createForm)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 修改权限 -->
    <el-dialog
      title="修改权限"
      v-model="updatePermissionDialogVisible"
      :close-on-click-modal="false"
      @close="closeEditDialog(updateForm)"
      width="500px"
    >
      <el-form ref="updateForm" :model="data.permissionUpdateForm" label-width="100px" :rules="data.permissionRules">
        <el-form-item label="页面名称" prop="page_name">
          <el-input v-model="data.permissionUpdateForm.page_name" :disabled="true" />
        </el-form-item>
        <el-form-item label="权限分组" prop="permission_group_id">
          <el-select v-model="data.permissionUpdateForm.permission_group_id" style="width: 100%">
            <el-option v-for="item in permissionGroupList" :key="item.id" :label="item.group_name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="data.permissionUpdateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="data.permissionUpdateForm.code" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelUpdatePermission(updateForm)"> 取 消 </el-button>
        <el-button type="primary" @click="submitUpdatePermission(updateForm)"> 确 定 </el-button>
      </div>
    </el-dialog>
  </el-card>
</template>

<script name="PermissionConfig" setup>
import permissionService from '@/service/system/PermissionService';
import permissionGroupService from '@/service/system/PermissionGroupService';
import commonService from '@/service/common/CommonService';
import { reactive, ref, watch, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const props = defineProps({
  menu: {
    type: Object,
    required: true,
    default: () => ({
      id: undefined,
      name: undefined
    })
  }
});
const multipleTable = ref();
const createForm = ref();
const updateForm = ref();
const createPermissionDialogVisible = ref(false);
const updatePermissionDialogVisible = ref(false);
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const permissionGroupList = ref([]);
const typeList = ref([]);
const data = reactive({
  permissionCreateForm: {
    page_id: undefined,
    name: '',
    code: '',
    permission_group_id: ''
  },
  permissionUpdateForm: {
    page_id: undefined,
    id: undefined,
    name: '',
    code: '',
    permission_group_id: '',
    page_name: ''
  },
  queryParams: {
    page_id: undefined,
    page: 1,
    limit: 30
  },
  permissionRules: {
    name: [
      {
        required: true,
        message: '请输入权限名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入权限编码',
        trigger: 'blur'
      }
    ],
    permission_group_id: [
      {
        required: true,
        message: '请选择权限分组',
        trigger: 'blur'
      }
    ]
  }
});

onActivated(() => {
  initSelects();
});

watch(
  () => props.menu,
  (newVal) => {
    if (newVal.id !== undefined) {
      getList({
        page: 1,
        limit: 30,
        page_id: newVal.id
      });
    } else {
      tableData.value = [];
      total.value = 0;
    }
  }
);
// 页面初始化获取权限组列表和枚举
const initSelects = () => {
  permissionGroupService.permissionGroupList().then((response) => {
    if (response.success === true) {
      permissionGroupList.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
  const param = [
    {
      enum_key: 'typeList',
      enum_value: 'EnumPermissionType'
    }
  ];
  commonService.findEnums('system', param).then((response) => {
    typeList.value = response.data.typeList;
  });
};
// 分页查询权限列表
const getList = (queryParams) => {
  data.queryParams = queryParams;
  permissionService.pagingPermissions(data.queryParams).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 新建权限
const createPermission = () => {
  if (props.menu.type === 'menu' || props.menu.id === undefined) {
    ElMessage({
      message: '请先选择一条页面数据',
      type: 'warning'
    });
    return;
  }
  data.permissionCreateForm.page_id = props.menu.id;
  data.permissionCreateForm.page_name = props.menu.name;
  createPermissionDialogVisible.value = true;
};
// 取消新建权限
const cancelCreatePermission = (createForm) => {
  createForm.resetFields();
  createPermissionDialogVisible.value = false;
};
// 提交并保存新建权限
const submitCreatePermission = (createForm) => {
  createForm.validate().then(() => {
    permissionService.createPermission(data.permissionCreateForm).then((response) => {
      if (response.success) {
        createPermissionDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        createForm.resetFields();
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 修改权限
const updatePermission = (row) => {
  data.permissionUpdateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    permission_group_id: row.permission_group_id,
    page_name: props.menu.name
  };
  updatePermissionDialogVisible.value = true;
};
// 取消修改权限
const cancelUpdatePermission = (updateForm) => {
  updateForm.resetFields();
  updatePermissionDialogVisible.value = false;
};
// 提交并保存修改权限
const submitUpdatePermission = (updateForm) => {
  updateForm.validate().then(() => {
    permissionService.updatePermission(data.permissionUpdateForm).then((response) => {
      if (response.success) {
        updatePermissionDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        updateForm.resetFields();
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 删除权限
const deletePermission = (row) => {
  const ids = [row.id];
  ElMessageBox.confirm('确定删除此权限吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      permissionService.deletePermission(ids).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};
const closeAddDialog = (createForm) => {
  createForm.resetFields();
};
const closeEditDialog = (updateForm) => {
  updateForm.resetFields();
};
</script>
<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 6px 10px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.permission-card {
  margin-top: 10px;
}
</style>
