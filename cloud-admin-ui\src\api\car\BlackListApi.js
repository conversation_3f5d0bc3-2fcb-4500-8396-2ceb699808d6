/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 黑名单表格数据查询
export const pagingBlackLists = (data) => {
  return $({
    url: '/console/park/black/list/pagingBlackLists',
    method: 'post',
    data
  });
};

// 新增黑名单
export const createBlackList = (data) => {
  return $({
    url: '/console/park/black/list/createBlackList',
    method: 'post',
    data
  });
};

// 修改黑名单
export const updateBlackList = (data) => {
  return $({
    url: '/console/park/black/list/updateBlackList',
    method: 'post',
    data
  });
};

// 删除黑名单
export const deleteBlackList = (id) => {
  return $({
    url: '/console/park/black/list/deleteBlackList/' + id,
    method: 'post'
  });
};

//导出黑名单
export const exportBlackLists = (data) => {
  return $({
    url: '/console/park/black/list/exportBlackLists',
    method: 'post',
    data
  });
};
