<template>
  <el-card class="table table-warp" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">新 增</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="success" @click="showDict(scope.row.id)"> 字典数据 </el-button>
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="name" label="字典名称" align="center" />
        <el-table-column prop="code" label="字典编码" align="center" />
        <el-table-column prop="updated_at" label="修改时间" align="center" />
        <el-table-column prop="memo" label="备注" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="新增字典" v-model="dictTypeCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="80px" :rules="data.rules" :model="data.form">
          <el-form-item prop="name" label="字典名称">
            <el-input v-model="data.form.name" />
          </el-form-item>
          <el-form-item prop="code" label="字典编码">
            <el-input v-model="data.form.code" />
          </el-form-item>
          <el-form-item prop="memo" label="备注">
            <el-input v-model="data.form.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createDictType(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="修改字典"
        v-model="dictTypeUpdateDialogVisible"
        :close-on-click-modal="false"
        @close="closeEditDialog(editForm)"
        width="500px"
      >
        <el-form ref="editForm" label-width="80px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="name" label="字典名称">
            <el-input v-model="data.updateForm.name" />
          </el-form-item>
          <el-form-item prop="code" label="字典编码">
            <el-input v-model="data.updateForm.code" />
          </el-form-item>
          <el-form-item prop="memo" label="备注">
            <el-input v-model="data.updateForm.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateDictType(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="DictTypeTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { activeRouteTab } from '@/utils/tabKit';
import dictService from '@/service/system/DictService';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const dictTypeCreateDialogVisible = ref(false);
const dictTypeUpdateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    dict_name: '',
    dict_code: '',
    page: 1,
    limit: 30
  },
  form: {
    name: undefined,
    code: undefined,
    memo: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入字典名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入字典编码',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
  status.value = true;
});
// 分页查询字典列表数据
const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  dictService.pagingDictType(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};
// 查看字典数据
const showDict = (id) => {
  activeRouteTab({
    path: '/system/dict',
    query: {
      dictTypeId: id
    }
  });
};

// 新建字典类型
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form = {
    name: undefined,
    code: undefined,
    memo: undefined
  };
  dictTypeCreateDialogVisible.value = true;
  status.value = false;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建字典类型
const createDictType = (addForm) => {
  addForm.validate().then(() => {
    dictService
      .createDictType(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          addForm.resetFields();
          getList(data.queryParams);
          dictTypeCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除字典类型
const handleDelete = (val) => {
  batchDelete(val);
};
const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    dictService
      .deleteDictType(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 修改字典类型
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    memo: row.memo
  };
  dictTypeUpdateDialogVisible.value = true;
};
// 提交并保存修改字典类型
const updateDictType = (editForm) => {
  editForm.validate().then(() => {
    dictService
      .updateDictType(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          dictTypeUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  dictTypeCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  dictTypeUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
