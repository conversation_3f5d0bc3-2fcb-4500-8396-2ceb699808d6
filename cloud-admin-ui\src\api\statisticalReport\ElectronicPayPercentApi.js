/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询电子支付占比
export const pagingElectronicPay = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/pagingByPeriod',
    method: 'post',
    data
  });
};

// 按日导出
export const exportDataByDay = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/exportByPeriod',
    method: 'post',
    data
  });
};

// 汇总导出
export const exportDataGather = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/exportSummaryByPeriod',
    method: 'post',
    data
  });
};
