/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询电子支付占比
export const pagingElectronicPay = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/listElectronicPaymentProportions',
    method: 'post',
    data
  });
};

// 按日导出
export const exportDataByDay = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/exportDayElectronicPaymentProportions',
    method: 'post',
    data
  });
};

// 汇总导出
export const exportDataGather = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/proportion/exportElectronicPaymentProportionCollect',
    method: 'post',
    data
  });
};
