<template>
  <div class="container">
    <cash-transfer-search @form-search="searchCashTransferList" @reset="resetParamsAndData" />
    <cash-transfer-top-groups @search="searchCashTransferTopGroups" ref="search_btn" />
    <cash-transfer-table ref="table" />
  </div>
</template>

<script setup name="CashTransfer">
import CashTransferSearch from './cashTransfer/CashTransferSearch.vue';
import CashTransferTable from './cashTransfer/CashTransferTable.vue';
import CashTransferTopGroups from './cashTransfer/CashTransferTopGroups.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const search_btn = ref(null);
const params = reactive({});

const searchCashTransferList = (queryParams) => {
  const { park_name, ...newQueryParams } = queryParams;
  search_btn.value.countTransferByState(newQueryParams);
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  params.order_states = [1, 2];
  // table.value.getList(params);
};
const searchCashTransferTopGroups = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
