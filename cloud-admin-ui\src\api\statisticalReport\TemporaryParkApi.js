/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询临停收入全览
export const pagingTemporaryPark = (data) => {
  return $({
    url: '/console/statistics/income/total/listParkingIncomeTotals',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/income/total/exportParkingIncomeTotals',
    method: 'post',
    data
  });
};
