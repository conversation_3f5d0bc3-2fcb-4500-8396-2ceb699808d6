/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询临停收入全览
export const pagingTemporaryPark = (data) => {
  return $({
    url: '/console/statistics/income/total/pagingParkingIncomeByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/income/total/exportParkingIncomeByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportDataTotal = (data) => {
  return $({
    url: '/console/statistics/income/total/exportParkingIncomeSummaryByPeriod',
    method: 'post',
    data
  });
}
