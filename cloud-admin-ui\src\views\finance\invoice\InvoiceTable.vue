<template>
  <el-card shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 178px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_name" label="用户名" align="center" />
        <el-table-column prop="refund_type_desc" label="电话" align="center" />
        <el-table-column prop="refund_state_desc" label="抬头类型" align="center" />
        <el-table-column prop="apply_operator" label="抬头内容" align="center" />
        <el-table-column prop="created_at" label="税号" align="center" />
        <el-table-column prop="audit_reason" label="发票内容" align="center" />
        <el-table-column prop="park_name" label="预留手机号" align="center" />
        <el-table-column prop="refund_type_desc" label="预留邮箱" align="center" />
        <el-table-column prop="refund_state_desc" label="发票金额" align="center" />
        <el-table-column prop="apply_operator" label="开票时间" align="center" />
        <el-table-column prop="created_at" label="是否开票" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="InvoiceTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import invoiceService from '@/service/finance/InvoiceService';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  // getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  invoiceService.pagingInvoice(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
