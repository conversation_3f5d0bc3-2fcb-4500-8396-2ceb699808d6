/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询APP应用
export const queryApp = (data) => {
  return $({
    url: '/console/park/app/pagingApp',
    method: 'post',
    data
  });
};

// 创建APP信息
export const createApp = (data) => {
  return $({
    url: '/console/park/app/createApp',
    method: 'post',
    data
  });
};

// 编辑APP信息
export const updateApp = (data) => {
  return $({
    url: '/console/park/app/updateApp',
    method: 'post',
    data
  });
};

// 分页查询版本历史信息
export const queryHistApp = (data) => {
  return $({
    url: '/console/park/app/pagingHistoryApp',
    method: 'post',
    data
  });
};

//查询单个APP对象
export const getApp = (appId) => {
  return $({
    url: '/console/park/app/getApp?appId=' + appId,
    method: 'get'
  });
};

//提交新版本信息
export const releaseApp = (data) => {
  return $({
    url: '/console/park/app/releaseApp',
    method: 'post',
    data
  });
};
// 分管车场授权
export const parkingAuthority = (data) => {
  return $({
    url: '/console/park/app/parkingAuthority',
    method: 'post',
    data
  });
};
// 查询车场信息
export const getParkList = (data) => {
  return $({
    url: '/console/park/park/listPark',
    method: 'post',
    data
  });
};

// 查询员工授权充车场信息
export const appParkList = (appId) => {
  return $({
    url: '/console/park/app/appParkList/' + appId,
    method: 'post'
  });
};
