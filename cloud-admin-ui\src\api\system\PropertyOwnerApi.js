/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找产权方
export const pagingPropertyOwner = (data) => {
  return $({
    url: '/console/park/propertyOwner/pagingPropertyOwner',
    method: 'post',
    data
  });
};

// 新建产权方
export const createPropertyOwner = (data) => {
  return $({
    url: '/console/park/propertyOwner/addPropertyOwner',
    method: 'post',
    data
  });
};

// 修改产权方
export const updatePropertyOwner = (data) => {
  return $({
    url: '/console/park/propertyOwner/updatePropertyOwner',
    method: 'post',
    data
  });
};

// 删除产权方
export const deletePropertyOwner = (id) => {
  return $({
    url: '/console/park/propertyOwner/deletePropertyOwner/' + id,
    method: 'post'
  });
};

// 产权方列表
export const listPropertyOwner = () => {
  return $({
    url: '/console/park/propertyOwner/listPropertyOwner',
    method: 'get'
  });
};
