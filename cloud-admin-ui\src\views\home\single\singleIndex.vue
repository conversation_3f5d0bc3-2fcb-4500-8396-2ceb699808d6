<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-13 19:21:29
 * @LastEditTime: 2024-06-25 18:53:53
 * @LastEditors: 达万安 段世煜
 * @Description: 单店版首页
 * @FilePath: \cloud-admin-ui\src\views\home\single\singleIndex.vue
-->
<template>
  <single-header @filter="handleFilter" />
  <div class="display-container">
    <div class="column" style="width: 37.5%">
      <parking-statistics ref="parkingStatisticsRef" />
      <parking-pay ref="parkingPayRef" />
      <rent-pay ref="rentPayRef" />
    </div>
    <div class="column" style="width: 37.5%">
      <to-do ref="toDoRef" />
      <change-shifts ref="changeShiftsRef" />
      <assessment-info ref="assessmentInfoRef" />
    </div>
    <div class="column" style="width: 25%">
      <banner />
      <parking-info />
      <notice />
      <question />
    </div>
  </div>
</template>

<script setup>
import parkingStatistics from './parkingStatistics.vue';
import parkingPay from './parkingPay.vue';
import rentPay from './rentPay.vue';
import toDo from './toDo.vue';
import changeShifts from './changeShifts.vue';
import assessmentInfo from './assessmentInfo.vue';
import banner from './banner.vue';
import parkingInfo from './parkingInfo.vue';
import notice from './notice.vue';
import question from './question.vue';
import singleHeader from './singleHeader.vue';
import { onMounted, ref } from 'vue';

const parkingStatisticsRef = ref(null);
const parkingPayRef = ref(null);
const rentPayRef = ref(null);
const toDoRef = ref(null);
const changeShiftsRef = ref(null);
const assessmentInfoRef = ref(null);
const handleFilter = (val) => {
  console.log('valvalval', val);
  parkingStatisticsRef.value?.fetchData(val);
  parkingPayRef.value?.fetchData(val);
  rentPayRef.value?.fetchData(val);
  toDoRef.value?.fetchData(val);
  changeShiftsRef.value?.fetchData(val);
  assessmentInfoRef.value?.fetchData(val);
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
  handleResize();
});
const ratio = ref('scale(0.883)');
const handleResize = () => {
  const targetX = 2360;
  // 获取html的宽度和高度（不包含滚动条）
  const currentX = document.querySelector('.frame-content').clientWidth;
  console.log('currentX===', currentX);
  // 计算缩放比例
  const ratioNumber = currentX / targetX;
  console.log('ratioNumber===', ratioNumber);
  ratio.value = `scale(${ratioNumber})`;
};
</script>

<style scoped lang="scss">
.display-container {
  display: flex;
  gap: 20px;
  width: 2340px;
  // height: 1080px;
  // width: 100%;
  height: 1154px;
  transform: v-bind(ratio);
  transform-origin: left top;
  overflow: hidden;
  color: #333;
  .column {
    display: flex;
    flex-direction: column;
    gap: 20px;
  }
  :deep(.el-table thead .el-table__cell) {
    background-color: #f4f7fd;
  }
}
</style>
