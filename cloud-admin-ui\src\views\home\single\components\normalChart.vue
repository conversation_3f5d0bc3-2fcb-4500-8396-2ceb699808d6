<template>
  <div ref="chartRef" class="chart-warp"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { isArray } from 'lodash';
import { markRaw, onBeforeUnmount, onMounted, ref, nextTick } from 'vue';

const props = defineProps({
  type: {
    type: String,
    default: 'bar'
  },
  // 颜色数组
  color: {
    type: Array,
    default: () => ['#00dbf2', '#ff917c', '#1890ff', '#6dfacd', '#ffa800', '#ff5b00', '#ff3000', '#ea7ccc', '#DDD7C6', '#5470c6']
  },
  // 是否只有一组数据
  single: {
    type: Boolean,
    default: false
  },
  // 是否展示顶端label
  showLabel: {
    type: Boolean,
    default: false
  },
  showYLabel: {
    type: Boolean,
    default: true
  },
  // 条形图宽度
  barWidth: {
    type: Number,
    default: 24
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  },
  // 图表间隔
  barGap: {
    type: String,
    default: '30%'
  },
  legendPosition: {
    type: Object
  },
  grid: {
    type: Object,
    default: () => {
      return {
        // 网格距离顶部距离
        top: 10,
        // 网格距离底部距离
        bottom: 10,
        // 网格距离左边距离
        left: 30
      };
    }
  },
  labelFormatter: {
    type: String,
    default: '{c}'
  },
  itemLabelPosition: {
    type: String,
    default: 'top'
  },
  yLeftUnit: {
    type: String,
    default: ''
  }
});
// 图表容器ref
const chartRef = ref(null);
// 图表实例
const pLeft = ref(null);
const pRight = ref(null);
let chartInstance = null;
onMounted(async () => {
  await nextTick();
  // eslint-disable-next-line vue/no-mutating-props
  chartInstance = markRaw(echarts.init(chartRef.value));
});
/**
 * @description: 初始化图表
 */
const setData = (data, xData, yAxis = ['']) => {
  const options = {
    // 是否显示提示框
    tooltip: {
      trigger: 'item'
    },
    // 标题
    title: {
      // 是否显示
      show: !!props.title,
      // 标题文本
      text: props.title,
      // 标题文本样式
      textStyle: {
        fontWeight: 'normal',
        fontSize: 16
      },
      // 标题位置
      left: 'center'
    },
    // 图例
    legend: {
      // 图例方向
      orient: 'horizontal',
      // 图例图标
      icon: props.type === 'bar' ? 'roundRect' : '',
      // 图例位置
      bottom: '1%',
      // 图例位置
      x: 'center',
      // 图例文本样式
      textStyle: {
        color: '#333'
      },
      // 图例宽度
      itemWidth: 10,
      // 图例高度
      itemHeight: 6
    },
    // x轴
    xAxis: {
      // 是否显示
      show: !props.single,
      // x轴类型
      type: 'category',
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      // x轴轴标签文本样式
      axisLabel: {
        color: '#333'
      },
      // x轴数据
      data: xData || []
    },
    // y轴
    yAxis: dealY(yAxis),
    // 网格
    grid: {
      ...props.grid,
      left: pLeft.value || props.grid.left,
      right: pRight.value || props.grid.right
    },
    // 颜色
    color: props.color,
    // 数据
    series: dealData(data, yAxis)
  };
  if (props.legendPosition) {
    options.legend = {
      ...props.legendPosition,
      ...options.legend
    };
  }
  chartInstance.clear();
  chartInstance.setOption(options, true);
};

/**
 * @description 处理series数据
 * @param {*} data series数据
 *  @param {*} yAxis y轴数据 用于多y轴判断
 */
const dealData = (data, yAxis) => {
  const seriesData = [];
  // 遍历data，将每一项的值添加到seriesData中
  data.forEach((element, index) => {
    seriesData.push({
      // 类型
      type: props.type,
      // 位置
      top: 'top',
      // 最大宽度
      barMaxWidth: props.barWidth,
      // 间距
      barGap: props.barGap,
      // 名称
      name: element.name,
      // 数据
      data: isArray(element.value) ? element.value : [element.value],
      // y轴索引
      yAxisIndex: yAxis && yAxis.length > 1 ? index : 0,
      // 标签
      label: {
        // 是否显示
        show: props.showLabel,
        formatter: props.labelFormatter,
        // 位置
        position: props.itemLabelPosition,
        // 颜色
        color: '#333'
      },
      lineStyle: {
        width: 2
      }
    });
  });
  // 返回seriesData
  return seriesData;
};
/**
 * @description 处理y轴数据
 * @param {*} yAxis y轴数据
 */
const dealY = (data) => {
  const yAxisData = [];
  // 遍历data，将每一项添加到yAxisData中
  data.forEach((element, index) => {
    yAxisData.push({
      // 类型
      type: 'value',
      // 名称
      name: props.showYLabel ? element : '',
      // 位置
      position: index % 2 === 0 ? 'left' : 'right',
      // 线条样式
      lineStyle: {
        opaticy: 0
      },
      // 标签样式
      axisLabel: {
        color: '#696969',
        formatter: ` {value}${props.yLeftUnit}`
      },
      // 分割线样式
      splitLine: {
        lineStyle: {
          color: '#e8e8e8'
        }
      },
      // 名称文本样式
      nameTextStyle: {
        color: '#696969'
      }
    });
  });
  // 返回yAxisData
  return yAxisData;
};

const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData,
  pLeft,
  pRight
});
</script>

<style lang="scss" scoped>
.chart-warp {
  width: 100%;
  height: 100%;
}
</style>
