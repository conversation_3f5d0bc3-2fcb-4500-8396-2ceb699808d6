<template>
  <div class="toggle" @click="toggleScreen">
    <div class="toggle-icon in" v-if="!isFull"></div>
    <div class="toggle-icon out" v-else></div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';

onMounted(() => {
  document.addEventListener('fullscreenchange', handleFullscreenChange);
  document.addEventListener('mozfullscreenchange', handleFullscreenChange); // Firefox
  document.addEventListener('webkitfullscreenchange', handleFullscreenChange); // Chrome, Safari and Opera
  document.addEventListener('MSFullscreenChange', handleFullscreenChange); // Internet Explorer and Edge
});
const isFull = ref(false);
const toggleScreen = () => {
  const element = document.getElementById('fullScreenContainer');
  if (isFull.value) {
    exitFullScreen();
  } else {
    fullScreen(element);
  }
  isFull.value = !isFull.value;
};

const fullScreen = (element) => {
  if (element.requestFullscreen) {
    element.requestFullscreen();
  } else if (element.mozRequestFullScreen) {
    element.mozRequestFullScreen();
  } else if (element.webkitRequestFullscreen) {
    element.webkitRequestFullscreen();
  } else if (element.msRequestFullscreen) {
    element.msRequestFullscreen();
  }
};

const exitFullScreen = () => {
  if (document.exitFullscreen) {
    document.exitFullscreen();
  } else if (document.mozCancelFullScreen) {
    document.mozCancelFullScreen();
  } else if (document.webkitExitFullscreen) {
    document.webkitExitFullscreen();
  } else if (document.msExitFullscreen) {
    document.msExitFullscreen();
  }
};

function handleFullscreenChange() {
  if (document.fullscreenElement || document.mozFullScreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
    isFull.value = true;
  } else {
    isFull.value = false;
  }
}

onUnmounted(() => {
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  document.removeEventListener('mozfullscreenchange', handleFullscreenChange); // Firefox
  document.removeEventListener('webkitfullscreenchange', handleFullscreenChange); // Chrome, Safari and Opera
  document.removeEventListener('MSFullscreenChange', handleFullscreenChange); // Internet Explorer and Edge
});
</script>

<style lang="scss">
.toggle {
  cursor: pointer;
  position: absolute;
  right: 30px;
  top: 10px;
}
.toggle-icon {
  width: 30px;
  height: 30px;
  background-size: 100% 100%;
}
.in {
  background-image: url('@/assets/icon/in.png');
}
.out {
  background-image: url('@/assets/icon/out.png');
}
</style>