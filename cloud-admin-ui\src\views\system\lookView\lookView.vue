<template>
  <div class="lastCallBox">
    <div class="imgCol">
      <div>
        <div class="infoCol-t" style="padding-left: 10px">
          <div class="fill"></div>
          呼叫道口照片
        </div>
        <div class="imgTit">入场图片</div>
        <div class="img">
          <img src="https://pic.huidawanan.com/group1/M00/00/1E/rBRAEWOrsjaAQ8qhAALCHNF9Yd4735.jpg" alt="" />
        </div>
      </div>
      <div>
        <div class="imgTit">出场图片</div>
        <div class="img">
          <img src="https://pic.huidawanan.com/group1/M00/00/1E/rBRAEWOrsjaAQ8qhAALCHNF9Yd4735.jpg" alt="" />
        </div>
      </div>
    </div>
    <div class="infoCol">
      <div class="infoCol-one">
        <div class="infoCol-t">
          <div class="fill"></div>
          呼叫信息
        </div>
        <div class="infoCol-b">
          <div>车场名称: xxxx</div>
          <div>开始时间: 2022-11-22 02:11:22</div>
          <div>呼叫位置: xxx</div>
          <div>结束时间: 2022-11-22 02:11:22</div>
          <div>呼叫类型: xxx</div>
          <div>处理时长: xxx</div>
          <div>接听人员: xxx</div>
        </div>
      </div>
      <div class="infoCol-bu">
        <div class="infoCol-two">
          <div class="infoCol-t">
            <div class="fill"></div>
            停车记录
          </div>
          <div class="carInfo">
            <div>京A·123C4</div>
            <div></div>
          </div>
          <div class="infoCol-two-con">
            <div>停车类型: 小型车</div>
            <div>停车时长: 11分</div>
            <div>入场时间: 2022-11-22 02:11:22</div>
            <div>出场时间: 2002-11-22 02:11:22</div>
            <div>入场通道: 1号入口</div>
            <div>出场通道: 1号出口</div>
          </div>
        </div>
        <div class="infoCol-three">
          <div class="infoCol-t">
            <div class="fill"></div>
            收费信息
          </div>
          <div class="three-carInfo">
            <div>实际支付: 0<span style="color: #01a7f0">元</span></div>
            <div>当前费率: 小型车</div>
          </div>
          <div class="infoCol-three-con">
            <div>应收费用: 3元</div>
            <div>优免: 2元</div>
            <div>电子支付: 11元</div>
            <div>已抵扣金额: 1元</div>
            <div>已支付: <span style="color: #00cb60">3元</span></div>
            <div>找零金额: <span style="color: #ffac00">0元</span></div>
          </div>
        </div>
        <div class="infoCol-four">
          <div class="infoCol-t">
            <div class="fill"></div>
            处理结果
          </div>
          <div class="res-con">
            <div>呼叫原因:测试</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup></script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  user-select: none;
}
.lastCallBox {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 10px;
  > div {
    border-radius: 5px;
  }
  .imgCol {
    flex: 3.5;
    display: flex;
    flex-direction: column;
    gap: 10px;
    overflow: hidden;
    > div {
      flex: 1;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      .imgTit {
        height: 40px;
        display: flex;
        font-size: 12px;
        align-items: center;
        padding-left: 10px;
        border-bottom: 1px solid #e7e7e7;
        font-weight: 700;
        color: #01a7f0;
      }
      .img {
        height: calc(100% - 60px);
        width: 100%;
        padding: 5px;
        img {
          height: 100%;
          width: 100%;
          border-radius: 5px;
        }
      }
    }
  }
  .infoCol-t {
    height: 40px;
    display: flex;
    align-items: center;
    font-size: 12px;
    font-weight: 700;
    .fill {
      height: 35%;
      width: 3px;
      border-radius: 3px;
      background-color: #01a7f0;
      margin-right: 5px;
    }
  }
  .infoCol {
    flex: 6.5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    .infoCol-one {
      flex: 3;
      padding: 0 10px 10px 10px;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      .infoCol-b {
        height: calc(100% - 40px);
        width: 100%;
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        padding: 5px;
        font-size: 11px;
        display: grid;
        background-color: #fafafa;
        grid-template-columns: repeat(2, 1fr);
        align-items: center;
      }
    }
    .infoCol-bu {
      flex: 7;
      min-width: 0;
      display: grid;
      gap: 10px;
      grid-template-columns: repeat(4, 1fr);
      grid-template-areas:
        'a a a c'
        'b b b c';
      > div {
        background-color: #fff;
        border-radius: 5px;
        box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      }
    }

    .infoCol-two {
      height: 100%;
      width: 100%;
      padding: 0 10px 10px 10px;
      font-size: 11px;
      grid-area: a;
      .carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;
        color: #4b7a02;
        div:first-child {
          padding: 3px 20px;
          background-color: #01a7f0;
          border-radius: 5px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
      .infoCol-two-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
      }
    }
    .infoCol-three {
      height: 100%;
      width: 100%;
      padding: 0 10px 10px 10px;
      grid-area: b;
      .infoCol-three-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
        font-size: 11px;
      }
      .three-carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 700;
        font-size: 12px;
      }
    }
    .infoCol-four {
      height: 100%;
      width: 100%;
      grid-area: c;
      padding: 0 10px 10px 10px;
      font-size: 11px;
      overflow: hidden;
      .res-con {
        height: calc(100% - 40px);
        width: 100%;
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        padding: 10px;
        background-color: #fafafa;
        > div {
          height: 100%;
          overflow: hidden;
          white-space: wrap;
        }
      }
    }
  }
}
</style>
