import * as etcReconcileApi from '@/api/finance/ETCReconcileApi';

/**
 * ETC 对账
 */
export default {
  // 查询停车场对账汇总
  parkTempTotalByDay(data) {
    return new Promise((resolve, reject) => {
      try {
        etcReconcileApi.parkTempTotalByDay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  // 根据对账日期、车场ID查询etc对账信息
  getEtcAccInfos(data) {
    return new Promise((resolve, reject) => {
      try {
        etcReconcileApi.getEtcAccInfos(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  // 导出etc对账明细excel
  exportAccFile(data) {
    return new Promise((resolve, reject) => {
      try {
        etcReconcileApi.exportAccFile(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
