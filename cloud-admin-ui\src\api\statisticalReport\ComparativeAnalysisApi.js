import $ from '@/utils/axios';

// 分页查询对比分析
// POST console/statistics/tempStop/compare/analysis/listCompareAnalysis
// 接口ID：305333767
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-305333767
export const getCompareAnalysisList = (data) => {
  return $({
    url: '/console/statistics/tempStop/compare/analysis/listCompareAnalysis',
    method: 'post',
    data
  });
};

// 导出对比分析数据
// POST console/statistics/tempStop/compare/analysis/exportCompareAnalysis
// 接口ID：305410946
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-305410946
export const exportCompareAnalysis = (data) => {
  return $({
    url: '/console/statistics/tempStop/compare/analysis/exportCompareAnalysis',
    method: 'post',
    data
  });
};

// echarts图表数据
// POST console/statistics/tempStop/compare/analysis/echarts
// 接口ID：306198523
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-306198523
export const getCompareAnalysisChart = (data) => {
  return $({
    url: '/console/statistics/tempStop/compare/analysis/echarts',
    method: 'post',
    data
  });
};
