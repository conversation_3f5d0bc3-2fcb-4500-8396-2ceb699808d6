<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 280px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="120" />
          <el-table-column prop="park_name" label="车场名称" align="center" min-width="120" />
          <el-table-column prop="park_id" label="车场ID" align="center" min-width="100" />
          <el-table-column prop="region_name" label="大区" align="center" min-width="120" />
          <el-table-column prop="org_department_name" label="城市分公司" align="center" min-width="150" />
          <el-table-column prop="province_name" label="所在省份" align="center" min-width="150" />
          <el-table-column prop="city_name" label="所在城市" align="center" min-width="150" />
          <el-table-column prop="district_name" label="所在区县" align="center" min-width="150" />
          <!-- <el-table-column label="省/市/区" align="center" min-width="200">
            <template #default="{ row }">
              <span v-if="row.province_name !== null && row.province_name !== '' && row.province_name !== undefined"> {{ row.province_name }}/ </span>
              <span v-if="row.city_name !== null && row.city_name !== '' && row.city_name !== undefined"> {{ row.city_name }}/ </span>
              <span v-if="row.district_name !== null && row.district_name !== '' && row.district_name !== undefined">
                {{ row.district_name }}
              </span>
            </template>
          </el-table-column> -->
          <el-table-column prop="total_space_number" label="车位数" align="center" min-width="100" />
        </el-table-column>
        <el-table-column label="时间点对应的信息" align="center">
          <el-table-column v-for="(time1, index1) in timeSlots60" :key="index1" :label="getTime1Label(time1)" align="center">
            <el-table-column v-for="(time2, index2) in split60Minutes(4)" :key="index2" :label="time2" align="center" min-width="120">
              <template #default="{ row }">
                <span>{{ getSaturationRate(row, index1, index2) }}</span>
              </template>
            </el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkSaturationTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import { getParkSpaceSaturation } from '@/api/statisticalReport/ParkSaturationApi';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    parkId: undefined,
    orgDepartmentId: [],
    park_name: undefined,
    page: 1,
    limit: 30
  }
});
const timeSlots15 = ref([]);
const timeSlots60 = ref([]);

onMounted(() => {
  timeSlots60.value = getTimeSlots();
  timeSlots15.value = getTimeSlots(15);
});

const getTimeSlots = (interval = 60) => {
  const slots = [];
  const date = dayjs().startOf('day');
  const totalMinutes = 24 * 60;
  for (let minutes = 0; minutes < totalMinutes; minutes += interval) {
    slots.push(date.add(minutes, 'minute').format('HHmm'));
  }
  return slots;
};

const split60Minutes = (dimension) => {
  // 验证输入
  if (typeof dimension !== 'number' || dimension <= 0 || 60 % dimension !== 0) {
    throw new Error('维度必须是60的约数（1, 2, 3, 4, 5, 6, 10, 12, 15, 20, 30, 60）');
  }
  const interval = 60 / dimension; // 每个区间包含的分钟数
  const result = [];
  for (let i = 0; i < dimension; i++) {
    const start = i * interval;
    // 处理不是最后一个区间，确保开始时间往后推一分钟
    const adjustedStart = i > 0 ? start + 1 : start;
    const end = (i + 1) * interval;
    result.push(`${adjustedStart}-${end}分钟`);
  }
  return result;
}

const getTime1Label = (time) => {
  const hour = time.slice(0, 2);
  return Number(hour) > 9 ? `${hour}点` : `${time.slice(1, 2)}点`;
};

const convertToPercentage = (arr) => {
  return arr.map((item) => {
    const newItem = { ...item }; // 创建新对象避免修改原对象
    Object.entries(newItem).forEach(([key, value]) => {
      if (key.startsWith('time')) {
        // 将小数转换为百分比（乘以100并保留两位小数）
        newItem[key] = `${(value * 100).toFixed(2)}%`;
      }
    });
    return newItem;
  });
};

const getSaturationRate = (row, index1, index2) => {
  let startIdx = 4 * index1 + index2;
  const endIdx = startIdx + 1;
  return startIdx === timeSlots15.value.length - 1
    ? row[`time${timeSlots15.value[startIdx]}2359`]
    : row[`time${timeSlots15.value[startIdx]}${timeSlots15.value[endIdx]}`];
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  const queryParams = {
    ...params,
    parkId: params.park_id,
    orgDepartmentId: params.organization_ids
  };
  getParkSpaceSaturation(queryParams).then((response) => {
      if (response.success === true) {
        tableData.value = convertToPercentage(response.data.rows);
        total.value = parseInt(response.data.total);
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
