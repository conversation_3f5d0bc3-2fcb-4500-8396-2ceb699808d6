import * as permission from '@/api/system/PermissionApi';

/**
 * 权限服务层
 */
export default {
  /**
   * 分页查询权限
   */
  pagingPermissions(param) {
    return new Promise((resolve, reject) => {
      try {
        permission.pagingPermissions(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 添加权限
   */
  createPermission(param) {
    return new Promise((resolve, reject) => {
      try {
        permission.createPermission(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改权限
   */
  updatePermission(param) {
    return new Promise((resolve, reject) => {
      try {
        permission.updatePermission(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除权限
   */
  deletePermission(param) {
    return new Promise((resolve, reject) => {
      try {
        permission.deletePermission(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
