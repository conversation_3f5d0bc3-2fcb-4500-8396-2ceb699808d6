import * as device<PERSON>pi from '@/api/park/DeviceApi';

/**
 * 车场-设备管理
 */
export default {
  /**
   * 分页查询
   */
  pagingDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.pagingDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建设备
   */
  createDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.createDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改设备
   */
  updateDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.updateDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除设备
   */
  deleteDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.deleteDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用
   */
  enableParkDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.enableParkDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 禁用
   */
  disableParkDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.disableParkDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取设备列表
   */
  listParkDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.listParkDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 子场列表
   */
  listParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.listParkRegion(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 通道列表
   */
  listParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceApi.listParkGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
