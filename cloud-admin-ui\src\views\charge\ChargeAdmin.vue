<template>
  <div class="container" style="position: relative">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="入场记录" name="carInRecord">
        <car-in-record ref="inRecord" />
      </el-tab-pane>
      <el-tab-pane label="已删除记录" name="deleteRecord">
        <delete-record ref="hasDelete" />
      </el-tab-pane>
    </el-tabs>
    <div v-if="sIp" class="back">
      <el-button @click="back">返 回</el-button>
    </div>
  </div>
</template>

<script name="ChargeAdmin" setup>
import { closeCurrentTab } from '@/utils/tabKit';
import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute } from 'vue-router';
import carInRecord from './CarInRecord.vue';
import deleteRecord from './DeleteRecord.vue';
// 在组件中
const route = useRoute();
// 返回上一页
watch(
  () => route.query,
  (newId) => {
    console.log(newId, 'ssssssssssssssss');
  },
  { immediate: true } // 立即执行一次
);
const back = () => {
  // closeCurrentTab({
  //   path: '/charge/chargeAdmin'
  // });
  closeCurrentTab({
    path: '/monitoringMan/Console/Console',
    query: {
      sIp: route.query?.sIp,
      gatewayType: route.query?.gatewayType
    }
  });
};
const activeName = ref('carInRecord');
const inRecord = ref(null);
const hasDelete = ref(null);
const sIp = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

onMounted(() => {
  // inRecord.value.pageCarInRecord(params);
  console.log(route.query, 'ssss');
  sIp.value = route.query?.sIp;
});

const handleClick = (tab) => {
  // if (tab.props.name === 'carInRecord') {
  //   inRecord.value.pageCarInRecord(params);
  // }
  // if (tab.props.name === 'deleteRecord') {
  //   hasDelete.value.pageDeleteRecord(params);
  // }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 10px 10px 0px 10px;
  background-color: #f6f6f6;
}
.back {
  position: absolute;
  right: 12px;
  top: 4px;
}
</style>
