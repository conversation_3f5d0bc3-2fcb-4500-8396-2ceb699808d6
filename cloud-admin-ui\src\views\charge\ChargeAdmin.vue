<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="入场记录" name="carInRecord">
        <car-in-record ref="inRecord" />
      </el-tab-pane>
      <el-tab-pane label="已删除记录" name="deleteRecord">
        <delete-record ref="hasDelete" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="ChargeAdmin" setup>
import carInRecord from './CarInRecord.vue';
import deleteRecord from './DeleteRecord.vue';
import { ref, reactive, onMounted } from 'vue';

const activeName = ref('carInRecord');
const inRecord = ref(null);
const hasDelete = ref(null);

const params = reactive({
  page: 1,
  limit: 30
});

onMounted(() => {
  // inRecord.value.pageCarInRecord(params);
});

const handleClick = (tab) => {
  // if (tab.props.name === 'carInRecord') {
  //   inRecord.value.pageCarInRecord(params);
  // }
  // if (tab.props.name === 'deleteRecord') {
  //   hasDelete.value.pageDeleteRecord(params);
  // }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 10px 10px 0px 10px;
  background-color: #f6f6f6;
}
</style>
