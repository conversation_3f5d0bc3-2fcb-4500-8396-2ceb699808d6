<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-29 14:57:03
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\statisticalReport\parkingCarFlow\ParkingCarFlowTab.vue
 * @Description: {}
-->
<template>
  <el-card class="tab" shadow="never">
    <el-row :gutter="10">
      <el-col :span="24" style="margin-top: 10px">
        <el-radio-group v-model="radio" size="large" @change="radioChange">
          <el-radio-button label="车流明细" value="1" />
          <el-radio-button label="进出流量" value="2" />
          <el-radio-button label="流量趋势" value="3" />
          <el-radio-button label="分时段进出流量" value="4" />
        </el-radio-group>
      </el-col>
    </el-row>
  </el-card>
</template>

<script name="ParkingCarFlowSearch" setup>
// import { onMounted } from 'vue';

// const emit = defineEmits(['update:radio']);
const radio = defineModel('radio');
// const radioChange = () => {
//   emit('radio-change', radio.value);
// };
</script>

<style lang="scss" scoped>
.tab {
  margin-top: 10px;
}
</style>
