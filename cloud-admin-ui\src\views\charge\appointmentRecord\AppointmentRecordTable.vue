<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <el-button type="default" @click="exportData()">导 出</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 324px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_code" label="停车场编号" align="center" />
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="payed_money" label="预约费" align="center" />
        <el-table-column prop="plan_start_time" label="入场时间" align="center" />
        <el-table-column prop="plan_end_time" label="出场时间" align="center" />
        <el-table-column prop="state_desc" label="预约状态" align="center" />
        <el-table-column prop="mbr_member_name" label="会员昵称" align="center" />
        <el-table-column prop="mbr_member_mobile" label="手机号" align="center" />
        <el-table-column prop="invoice_state_desc" label="发票状态" align="center" width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="AppointmentRecordTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import appointmentRecordService from '@/service/charge/AppointmentRecordService';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
      park_id: undefined,
      park_name: undefined,
    page: 1,
    limit: 30
  }
});

onActivated(() => {
  // getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  appointmentRecordService.pagingAppointmentRecord(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 导出
const exportData = () => {
  appointmentRecordService.exportData(data.queryParams).then((response) => {
    if (response.success == true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      window.open(response.data, '_blank');
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
