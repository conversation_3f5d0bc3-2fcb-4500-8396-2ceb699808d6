import * as carOutRecordApi from '@/api/charge/CarOutRecordApi';

/**
 * 出场记录
 */
export default {
  /**
   * 分页查询出场记录
   */
  pagingCarOutRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carOutRecordApi.pagingCarOutRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出出场记录
   */
  exportCarOutRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carOutRecordApi.exportCarOutRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
