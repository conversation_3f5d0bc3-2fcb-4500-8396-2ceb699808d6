<template>
  <div>
    <el-row :gutter="10">
      <el-col :xs="24" :sm="4">
        <document-classification ref="docType_tree" @initData="initDocTypeTree" />
      </el-col>
      <el-col :xs="24" :sm="20">
        <el-card class="document_classification my-table-container" shadow="never">
          <template #header>
            <div class="header-container">
              <div style="height: 40px">
                <div style="line-height: 40px">
                  <span v-if="firstName != undefined && firstName != ''">{{ firstName }} ＞</span>
                  <span v-if="parentName != undefined && parentName != ''">{{ parentName }} ＞</span>
                  <span>{{ docTypeName }}</span>
                </div>
              </div>
              <document-center-search style="margin-right: -100px" @form-search="searchDocumentCenterList" @reset="resetParamsAndData" />
            </div>
          </template>
          <div class="table-warp">
            <el-space>
              <el-button type="primary" @click="handleAdd">添加文档</el-button>
            </el-space>
          </div>
          <document-center-table ref="doc_table" @getDocTypeName="getDocTypeName" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
<script name="DocumentCenter" setup>
import DocumentClassification from './DocumentClassification.vue';
import DocumentCenterSearch from './documentCenter/DocumentCenterSearch.vue';
import DocumentCenterTable from './documentCenter/DocumentCenterTable.vue';
import { reactive, ref, onActivated, getCurrentInstance } from 'vue';
import { activeRouteTab } from '@/utils/tabKit';
import { ElMessage } from 'element-plus';

const doc_table = ref(DocumentCenterTable);
const doc_type_id = ref('');
const docTypeName = ref('');
const parentName = ref('');
const firstName = ref('');
const data = reactive({
  queryParams: {
    title: undefined,
    doc_type_id: undefined,
    updator_name: undefined,
    page: 1,
    limit: 30
  }
});
let queryParams = reactive({
  doc_type_id: 0
});
let document = reactive({
  name: '',
  parent_id: undefined,
  doc_type_id: undefined
});
const { proxy } = getCurrentInstance();
onActivated(() => {
  initDocumentData(); // 初始化查询
});
// 树节点点击调用
const initDocTypeTree = (documentList) => {
  // 获取分类名称并展示
  docTypeName.value = documentList.name;
  parentName.value = documentList.parent_name;
  firstName.value = documentList.first_name;
  doc_type_id.value = documentList.doc_type_id;
  document = documentList;
  initDocumentData();
};
// 构建查询
const initDocumentData = () => {
  queryParams = {
    doc_type_id: document.doc_type_id,
    parent_id: document.parent_id,
    name: document.name
  };
  doc_table.value.getList(queryParams);
};
const docType_tree = ref(null);
// 查询分类下的文档
const searchDocumentCenterList = (queryParams) => {
  data.queryParams = {
    doc_type_id: document.doc_type_id,
    title: queryParams.title,
    updator_name: queryParams.updator_name
  };
  Object.assign(data.queryParams, { page: 1, limit: 30 });
  doc_table.value.getList(data.queryParams);
};
const resetParamsAndData = () => {
  data.queryParams = {
    page: 1,
    limit: 30
  };
  doc_table.value.getList(data.queryParams);
};
// 新建文档
const handleAdd = () => {
  if (doc_type_id.value === '' || doc_type_id.value === undefined) {
    ElMessage({
      message: '请先选择文档分类!',
      type: 'warning'
    });
    return false;
  }
  activeRouteTab({
    path: '/system/documentAdd',
    query: {
      doc_type_id: doc_type_id.value,
      docTypeName: docTypeName.value
    }
  });
};

const getDocTypeName = () => {};
</script>

<style lang="scss" scoped>
.header-container {
  display: flex;
  justify-content: space-between;
}
</style>
