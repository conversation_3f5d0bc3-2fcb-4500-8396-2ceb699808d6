<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 280px)">
        <el-table-column prop="month" label="月份" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <!-- <el-table-column prop="park_id" label="大区" align="center" />
        <el-table-column prop="park_id" label="城市分公司" align="center" />
        <el-table-column prop="park_id" label="所在省份" align="center" />
        <el-table-column prop="park_id" label="所在城市" align="center" />
        <el-table-column prop="park_id" label="所在区域" align="center" /> -->
        <el-table-column prop="organizational_structure" label="组织架构" align="center" min-width="180" />
        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_car_in" label="单月进场车牌数量" align="center" min-width="180" />
        <el-table-column prop="total_car_in_one" label="单月进场1次车牌数量" align="center" min-width="180" />
        <el-table-column prop="car_in_one_proportion" label="单月进场1次车牌占比" align="center" min-width="180">
          <template #="{ row }"> {{ multiply(row.car_in_one_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>

        <el-table-column prop="total_car_in_two" label="单月进场2次车牌数量" align="center" min-width="180" />
        <el-table-column prop="car_in_two_proportion" label="单月进场2次车牌占比" align="center" min-width="180">
          <template #="{ row }"> {{ multiply(row.car_in_two_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>

        <el-table-column prop="total_car_in_three" label="单月进场3次及以上车牌数量" align="center" min-width="210" />
        <el-table-column prop="car_in_three_proportion" label="单月进场3次及以上车牌占比" align="center" min-width="210">
          <template #="{ row }"> {{ multiply(row.car_in_three_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[10, 30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CarInTimesPercentTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import carInTimesPercentService from '@/service/statisticalReport/CarInTimesPercentService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { multiply } from '@/utils/computeData';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(10);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });
function getFirstAndLastDay(yearMonth) {
  // 1. 获取当前或指定的年月（UTC时间避免时区干扰）
  let year, month;
  if (yearMonth) {
    [year, month] = yearMonth.split('-').map(Number);
  } else {
    const now = new Date();
    year = now.getFullYear();
    month = now.getMonth() + 1; // getMonth()返回0-11
  }

  // 2. 计算当月第一天和最后一天（使用UTC方法）
  const firstDay = new Date(Date.UTC(year, month - 1, 1));
  const lastDay = new Date(Date.UTC(year, month, 0)); // 下个月的第0天

  // 3. 格式化为YYYY-MM-DD（本地日期，避免时区转换）
  function formatLocal(date) {
    const y = date.getFullYear();
    const m = String(date.getMonth() + 1).padStart(2, '0');
    const d = String(date.getDate()).padStart(2, '0');
    return `${y}-${m}-${d}`;
  }

  console.log(firstDay, lastDay, 'firstDay');
  return {
    firstDay: formatLocal(firstDay), // 本地格式化的第一天
    lastDay: formatLocal(lastDay) // 本地格式化的最后一天
  };
}
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  data.queryParams.time_type = 2;
  data.queryParams.start_time = getFirstAndLastDay(data.queryParams.month).firstDay;
  data.queryParams.end_time = getFirstAndLastDay(data.queryParams.month).lastDay;
  carInTimesPercentService.pagingCarInTimesPercent(data.queryParams).then((response) => {
    if (response.success === true) {
      total.value = Number(response.data.total);
      tableData.value = response.data.rows;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
