<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="carInTimesPercentService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="month" label="月份" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="组织架构" align="center" min-width="180" />
        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_car_in" label="单月进场车牌数量" align="center" min-width="180" />
        <el-table-column prop="total_car_in_one" label="单月进场1次车牌数量" align="center" min-width="180" />
        <el-table-column prop="car_in_one_proportion" label="单月进场1次车牌占比" align="center" min-width="180" />
        <el-table-column prop="total_car_in_two" label="单月进场2次车牌数量" align="center" min-width="180" />
        <el-table-column prop="car_in_two_proportion" label="单月进场2次车牌占比" align="center" min-width="180" />
        <el-table-column prop="total_car_in_three" label="单月进场3次及以上车牌数量" align="center" min-width="210" />
        <el-table-column prop="car_in_three_proportion" label="单月进场3次及以上车牌占比" align="center" min-width="210" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="CarInTimesPercentTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import carInTimesPercentService from '@/service/statisticalReport/CarInTimesPercentService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  carInTimesPercentService.pagingCarInTimesPercent(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
