<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.park_name" placeholder="车场名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.park_region_name" placeholder="子场名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.code" placeholder="车位编号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" placeholder="车位类型" multiple clearable>
        <el-option v-for="item in typeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.properties" placeholder="车位属性" multiple clearable>
        <el-option v-for="item in propertyList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="ParkSpaceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_region_name: undefined,
    code: undefined,
    types: undefined,
    properties: undefined,
    page: 1,
    limit: 30
  }
});
const propertyList = ref([]);
const typeList = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'propertyList', enum_value: 'EnumParkSpaceProperty' },
    { enum_key: 'typeList', enum_value: 'EnumParkSpaceType' }
  ];
  commonService.findEnums('park', param).then((response) => {
    propertyList.value = response.data.propertyList;
    typeList.value = response.data.typeList;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: undefined,
    park_region_name: undefined,
    code: undefined,
    types: undefined,
    properties: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
