<template>
  <div class="container">
    <el-form ref="editForm" :model="form" label-width="122px" :rules="rules">
      <el-card shadow="hover">
        <template #header>
          <div style="display: inline-block; line-height: 32px">基本信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场编号">
                <el-input v-model="form.code" readonly placeholder="请输入停车场编号" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场名称" prop="name">
                <el-input v-model="form.name" maxlength="30" show-word-limit placeholder="请输入停车场名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场状态" prop="state">
                <el-select v-model="form.state" placeholder="停车场状态" style="width: 100%" clearable>
                  <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="省市区" class="required">
                <el-row :gutter="8" style="width: 100%">
                  <el-col :span="8">
                    <el-select v-model="form.province_code" placeholder="省" style="width: 100%" clearable @change="changeProvince" ref="province">
                      <el-option v-for="item in provinces" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select v-model="form.city_code" placeholder="市" style="width: 100%" clearable @change="changeCity" ref="city">
                      <el-option v-for="item in cities" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                  </el-col>
                  <el-col :span="8">
                    <el-select v-model="form.district_code" placeholder="区" style="width: 100%" clearable @change="changeDistrict" ref="dist">
                      <el-option v-for="item in areas" :key="item.code" :label="item.name" :value="item.code" />
                    </el-select>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场坐标" class="required">
                <el-row :gutter="8">
                  <el-col :span="8">
                    <el-input v-model="form.longitude" readonly placeholder="经度" />
                  </el-col>
                  <el-col :span="8">
                    <el-input v-model="form.latitude" readonly placeholder="纬度" />
                  </el-col>
                  <el-col :span="8">
                    <el-button type="primary" @click="openMap" style="position: relative; top: -2px"> 打开地图 </el-button>
                  </el-col>
                </el-row>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场地址" prop="address">
                <el-input v-model="form.address" maxlength="100" show-word-limit placeholder="请输入停车场详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场类型" prop="type">
                <el-select v-model="form.type" placeholder="请选择停车场类型" style="width: 100%" multiple clearable>
                  <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运营方" prop="org_department_id">
                <el-select ref="selectTree" v-model="form.org_department_name" placeholder="请选择运营方" style="width: 100%">
                  <el-option :value="localtionName" :label="form.org_department_name" style="height: 200px; overflow: auto; background-color: #fff">
                    <el-tree :data="treeData" :check-strictly="true" accordion highlight-current @node-click="handleNodeClick" />
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权方">
                <el-select
                  v-model="form.property_id"
                  placeholder="请选择产权方"
                  style="width: 100%"
                  clearable
                  @change="changeProperty"
                  ref="property"
                >
                  <el-option v-for="item in properties" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="总车位数" prop="total_spaces"> <el-input v-model="form.total_spaces" disabled /></el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场面积">
                <el-input-number
                  v-model="form.area"
                  max="100000"
                  :precision="2"
                  min="1"
                  :step="0.1"
                  style="width: 90%"
                  placeholder="请输入停车场面积"
                />
                <div class="el-input-number-append">㎡</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开放时段"
                ><!--开放时段为数组 -->
                <el-time-picker
                  v-model="timeValue"
                  is-range
                  format="HH:mm:ss"
                  value-format="HH:mm:ss"
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="限高">
                <el-input-number
                  v-model="form.limited_height"
                  max="10"
                  min="1"
                  :precision="2"
                  :step="0.1"
                  style="width: 100%"
                  placeholder="请输入停车场限高"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="终端截止日期" class="required">
                <el-date-picker
                  v-model="form.terminal_by_date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="请选择终端截止日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车场上线时间" class="required">
                <el-date-picker
                  v-model="form.online_date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  type="date"
                  placeholder="请选择车场上线时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="租赁期开始时间" prop="lease_term_start_date">
                <el-date-picker
                  v-model="form.lease_term_start_date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="请选择租赁期开始日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="租赁期结束时间" prop="lease_term_end_date">
                <el-date-picker
                  v-model="form.lease_term_end_date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="请选择租赁期开始日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权证号">
                <el-input v-model="form.ownership_no" placeholder="请输入产权证号" maxlength="30" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">商户支付信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="子商户ID">
                <el-input v-model="form.sub_mchid" placeholder="请输入子商户ID" maxlength="30" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="子商户应用ID">
                <el-input v-model="form.sub_appid" placeholder="请输入关联公众号应用" maxlength="30" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="支付宝授权令牌">
                <el-input v-model="form.app_auth_token" placeholder="请输入支付宝授权令牌" maxlength="500" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="ETC商户编号">
                <el-input v-model="form.etc_merchant_id" placeholder="请输入ETC商户编号" maxlength="30" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC子商户编号">
                <el-input v-model="form.etc_sub_merchant_id" placeholder="请输入ETC子商户编号" maxlength="30" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC证书密码">
                <el-input v-model="form.etc_cert_pass" placeholder="请输入ETC证书密码" maxlength="50" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="ETC商户证书">
                <el-link>{{ form.etc_cert_path }}</el-link>
                <el-upload
                  :limit="1"
                  :action="uploadEtcCertUrl"
                  :headers="headers"
                  :data="{ etcMerchantId: form.etc_merchant_id }"
                  :before-upload="beforeEtcUpload"
                  :on-success="onSuccessEtcCertUpload"
                >
                  <el-button type="primary">点击上传P12证书</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC根证书">
                <el-link>{{ form.etc_cert_root_path }}</el-link>
                <el-upload
                  :limit="1"
                  :action="uploadEtcRootCertUrl"
                  :headers="headers"
                  :data="{ etcMerchantId: form.etc_merchant_id }"
                  :before-upload="beforeEtcUpload"
                  :on-success="onSuccessEtcRootCertUpload"
                >
                  <el-button type="primary">点击上传Root证书</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">微信找零商户信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="商户ID">
                <el-input v-model="form.wx_mch_id" placeholder="请输入微信商户ID" show-word-limit style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="平台证书序列号">
                <el-input v-model="form.wx_plat_cert_serial_no" placeholder="请输入平台证书序列号" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="AppId">
                <el-input v-model="form.wx_appid" placeholder="请输入AppId" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="apiV3key">
                <el-input v-model="form.wx_api_v3key" placeholder="请输入apiV3key" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="商户证书序列号">
                <el-input v-model="form.wx_mch_cert_serial_no" placeholder="请输入商户证书序列号" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="app密钥">
                <el-input v-model="form.wx_app_secret" placeholder="请输入app密钥" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="证书文件内容">
                <el-input v-model="form.wx_mch_certificate" type="textarea" :rows="4" placeholder="请输入证书文件内容" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="私钥文件内容">
                <el-input v-model="form.wx_mch_private_key" type="textarea" :rows="4" placeholder="请输入私钥文件内容" style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">备案信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="备案证编号">
                <el-input v-model="form.filing_no" placeholder="请输入备案编号" maxlength="30" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案车位数">
                <el-input-number v-model="form.filing_spaces" :min="0" :max="10000" placeholder="备案车位数" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案证到期时间">
                <el-date-picker
                  v-model="form.filing_expired_date"
                  type="date"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  placeholder="请选择备案证到期时间"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="收费标准">
                <el-space direction="vertical">
                  <el-link @click="getFiles(form.fee_stand_url)">{{ form.fee_stand_name }}</el-link>
                  <el-upload :limit="1" :action="uploadUrl" :headers="headers" :before-upload="beforeUpload" :on-success="onSuccessUpload">
                    <el-button type="primary">点击上传文件</el-button>
                  </el-upload>
                </el-space>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">增值服务</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="租车服务" prop="car_rent">
                <el-radio-group v-model="form.car_rent">
                  <el-radio v-for="item in carRent" :key="item.value" :label="item.value">
                    {{ item.key }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="洗车服务" prop="car_wash">
                <el-radio-group v-model="form.car_wash">
                  <el-radio v-for="item in carWash" :key="item.value" :label="item.value">
                    {{ item.key }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否云端值守" prop="cloud_watch">
                <el-radio-group v-model="form.cloud_watch">
                  <el-radio v-for="item in cloudWatch" :key="item.value" :label="item.value">
                    {{ item.key }}
                  </el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="充电桩快充数量">
                <el-input-number v-model="form.fast_charge_piles" :min="0" :max="10000" @blur="changeFast" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩慢充数量">
                <el-input-number v-model="form.slow_charge_piles" :min="0" :max="10000" @blur="changeSlow" style="width: 100%" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩总充数量">
                <el-input v-model="pile_count" readonly style="width: 100%" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="充电收费标准">
                <el-input
                  :rows="3"
                  type="textarea"
                  v-model="form.charge_fee_memo"
                  maxlength="100"
                  show-word-limit
                  placeholder="请输入充电收费标准"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="fixed-bottom">
        <el-button @click="cancelAndClose(editForm)"> 取消 </el-button>
        <el-button type="primary" @click="savePark(editForm)"> 保存 </el-button>
      </div>
    </el-form>

    <!-- 地图选点弹框 -->
    <el-dialog v-if="mapDialogVisible" draggable title="地图选点" v-model="mapDialogVisible" width="1000px">
      <map-punctuation
        ref="mapPunctuation"
        :province-id="form.province_code"
        :longitude="form.longitude"
        :latitude="form.latitude"
        @checkedPunctuation="checkedPunctuation"
        @closeMapDialogVisible="mapDialogVisible = false"
      />
    </el-dialog>
  </div>
</template>

<script name="ParkInfoEdit" setup>
import commonService from '@/service/common/CommonService';
import parkInfoService from '@/service/park/ParkInfoService';
import regionService from '@/service/region/RegionService';
import employeeService from '@/service/system/EmployeeService';
import propertyOwnerService from '@/service/system/PropertyOwnerService';
import { getToken } from '@/utils/common';
import { getMiniProgramMonthCardEnum } from '@/api/park/ParkInfoApi'; //api接口
import { closeCurrentTab } from '@/utils/tabKit';
import { ElMessage } from 'element-plus';
import { getCurrentInstance, nextTick, onActivated, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import MapPunctuation from './MapPunctuation.vue';

const { proxy } = getCurrentInstance();

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/park/uploadFeeStand');
const uploadEtcCertUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/park/uploadEtcCert');
const uploadEtcRootCertUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/park/uploadEtcRootCert');
const headers = reactive({
  Authorization: getToken()
});
const downdata = ref(); //获取的小程序月卡办理的枚举数据
const route = useRoute();
const editForm = ref();
const mapDialogVisible = ref(false);
const timeValue = ref([]);
const types = ref([]);
const states = ref([]);
const carRent = ref([]);
const carWash = ref([]);
const cloudWatch = ref([]);
const treeData = ref([]);
const properties = ref([]);
const localtionName = ref('');
const provinces = ref([]);
const cities = ref([]);
const areas = ref([]);
const pile_count = ref(0);
const params = reactive({
  parkInfoId: undefined
});
const form = reactive({
  allow_online_renew: null, //当前续费情况  默认为1
  id: '',
  code: '', //车场编号
  name: '', //车场名称
  address: '', //地址
  province_code: '', //省编码
  province_name: '', //省名称
  city_code: '', //市编码
  city_name: '', //市名称
  district_code: '', //区编码
  district_name: '', //区名称
  state: '1', //车场状态
  longitude: '', //经度
  latitude: '', //维度
  type: [], //车场类型
  org_department_id: '', //运营方ID
  org_department_name: '', //运营费名称
  property_id: '', //产权方ID
  property_name: '', //产权方名称
  total_spaces: 0, //总车位数
  compulsory_recovery: '',
  area: '', //面积
  open_start_time: '', //开放开始时间
  open_end_time: '', //开放结束时间
  limited_height: '', //限高
  terminal_by_date: '', //终端截至日期
  online_date: '', //车场上线时间
  wx_mch_id: '', //微信商户ID
  wx_plat_cert_serial_no: '', //微信平台证书序列号
  wx_appid: '', //微信appid
  wx_api_v3key: '', //微信apiV3key
  wx_mch_cert_serial_no: '', //微信商户证书序列号
  wx_mch_certificate: '', //微信商户证书文件内容
  wx_app_secret: '', //微信app密钥
  wx_mch_private_key: '', //微信商户私钥文件内容
  filing_no: '', //备案证编号
  filing_spaces: 0, //备案车位数
  filing_expired_date: '', //备案证到期时间
  car_rent: '1', //租车服务
  car_wash: '1', //洗车服务
  cloud_watch: '1', //云端值守
  fast_charge_piles: 0, //充电桩快充数量
  slow_charge_piles: 0, //充电桩慢充数量
  charge_fee_memo: '', //充电收费标准
  fee_stand: '',
  fee_stand_name: '',
  fee_stand_url: '',
  sub_mchid: '',
  sub_appid: '',
  app_auth_token: '', //子商户应用ID
  etc_merchant_id: '',
  etc_sub_merchant_id: '',
  etc_cert_pass: '',
  etc_cert_path: '',
  etc_cert_root_path: '',
  lease_term_start_date: '',
  lease_term_end_date: '',
  ownership_no: '',
  more_rent_switch: 1 //月卡一位多车
});
const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入停车场名称',
      trigger: 'blur'
    }
  ],
  address: [
    {
      required: true,
      message: '请输入停车场详细地址',
      trigger: 'blur'
    }
  ],
  state: [
    {
      required: true,
      message: '请选择停车场状态',
      trigger: 'change'
    }
  ],
  type: [
    {
      required: true,
      message: '请选择停车场类型',
      trigger: 'change'
    }
  ],
  org_department_id: [
    {
      required: true,
      message: '请选择运营方',
      trigger: 'change'
    }
  ],
  car_rent: [
    {
      required: true,
      message: '请选择租车服务',
      trigger: 'change'
    }
  ],
  car_wash: [
    {
      required: true,
      message: '请选择洗车服务',
      trigger: 'change'
    }
  ],
  cloud_watch: [
    {
      required: true,
      message: '请选择是否云端值守',
      trigger: 'change'
    }
  ],
  lease_term_start_date: [
    {
      required: true,
      message: '请选择租赁开始时间',
      trigger: 'change'
    }
  ],
  lease_term_end_date: [
    {
      required: true,
      message: '请选择租赁结束时间',
      trigger: 'change'
    }
  ]
});

onActivated(() => {
  initSelects();
  if ({} !== route.query && undefined !== route.query.parkInfoId) {
    params.parkInfoId = route.query.parkInfoId;
    getParkInfo(params.parkInfoId);
  }
  getmoncarddata();
});
//获取小程序月卡办理枚举
const getmoncarddata = async () => {
  try {
    const rudata = await getMiniProgramMonthCardEnum([{ enum_key: 'allowOnlineRenew', enum_value: 'EnumAllowOnlineRenew' }]);
    if (rudata.code == 200) {
      downdata.value = rudata.data.allowOnlineRenew;
    }
  } catch (error) {
    console.error('获取小程序月卡办理枚举失败', error);
  }
};
const initSelects = () => {
  const param = [
    {
      enum_key: 'types',
      enum_value: 'EnumParkType'
    },
    {
      enum_key: 'states',
      enum_value: 'EnumParkState'
    },
    {
      enum_key: 'carRent',
      enum_value: 'EnumCarRent'
    },
    {
      enum_key: 'carWash',
      enum_value: 'EnumCarWash'
    },
    {
      enum_key: 'cloudWatch',
      enum_value: 'EnumCloudWatch'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    types.value = response.data.types;
    states.value = response.data.states;
    carRent.value = response.data.carRent;
    carWash.value = response.data.carWash;
    cloudWatch.value = response.data.cloudWatch;
  });
  //产权方列表
  propertyOwnerService.listPropertyOwner().then((response) => {
    properties.value = response;
  });
  //运营方
  employeeService.departmentTree().then((response) => {
    treeData.value = response.data;
  });
  //省查询
  regionService.listProvince().then((response) => {
    provinces.value = response;
  });
};
const getParkInfo = (parkInfoId) => {
  parkInfoService.getParkById(parkInfoId).then((response) => {
    console.log('获取当前数据', response);
    form.allow_online_renew = response.allow_online_renew;
    form.id = response.id;
    form.code = response.code;
    form.name = response.name;
    form.address = response.address;
    form.province_code = response.province_code;
    form.province_name = response.province_name;
    form.city_code = response.city_code;
    form.city_name = response.city_name;
    form.district_code = response.district_code;
    form.district_name = response.district_name;
    form.state = response.state;
    form.longitude = response.longitude;
    form.latitude = response.latitude;
    const typeList = response.type;
    const arr = typeList.split(',');
    // arr去重
    const uniqueArr = [...new Set(arr)];
    form.type = []; //清空原有数据
    //将arr中的数据转换为数字类型
    uniqueArr.forEach(function (item) {
      form.type.push(parseInt(item));
    });
    form.org_department_id = response.org_department_id;
    form.org_department_name = response.org_department_name;
    form.property_id = response.property_id;
    form.property_name = response.property_name;
    form.total_spaces = response.total_spaces;
    form.area = response.area;
    form.open_start_time = response.open_start_time;
    form.open_end_time = response.open_end_time;
    form.limited_height = response.limited_height;
    form.terminal_by_date = response.terminal_by_date;
    form.online_date = response.online_date;
    form.wx_mch_id = response.wx_mch_id;
    form.wx_plat_cert_serial_no = response.wx_plat_cert_serial_no;
    form.wx_appid = response.wx_appid;
    form.wx_api_v3key = response.wx_api_v3key;
    form.wx_mch_cert_serial_no = response.wx_mch_cert_serial_no;
    form.wx_mch_certificate = response.wx_mch_certificate;
    form.wx_app_secret = response.wx_app_secret;
    form.wx_mch_private_key = response.wx_mch_private_key;
    form.filing_no = response.filing_no;
    form.filing_spaces = response.filing_spaces;
    form.filing_expired_date = response.filing_expired_date;
    form.car_rent = response.car_rent;
    form.car_wash = response.car_wash;
    form.cloud_watch = response.cloud_watch;
    form.fast_charge_piles = response.fast_charge_piles;
    form.slow_charge_piles = response.slow_charge_piles;
    form.charge_fee_memo = response.charge_fee_memo;
    form.fee_stand_name = response.fee_stand_name;
    form.fee_stand_url = response.fee_stand_url;
    form.sub_mchid = response.sub_mchid;
    form.sub_appid = response.sub_appid;
    form.app_auth_token = response.app_auth_token;
    form.etc_merchant_id = response.etc_merchant_id;
    form.etc_sub_merchant_id = response.etc_sub_merchant_id;
    form.etc_cert_pass = response.etc_cert_pass;
    form.etc_cert_path = response.etc_cert_path;
    form.etc_cert_root_path = response.etc_cert_root_path;
    form.lease_term_start_date = response.lease_term_start_date;
    form.lease_term_end_date = response.lease_term_end_date;
    form.ownership_no = response.ownership_no;
    form.more_rent_switch = response.more_rent_switch;
    form.compulsory_recovery = response.compulsory_recovery;
    pile_count.value = form.fast_charge_piles + form.slow_charge_piles;
    timeValue.value = [form.open_start_time, form.open_end_time];
    loadCities(form.province_code);
    loadAreas(form.city_code);
  });
};
const loadCities = (province_code) => {
  regionService.listCity(province_code).then((response) => {
    cities.value = response;
  });
};
const loadAreas = (city_code) => {
  regionService.listDistrict(city_code).then((response) => {
    areas.value = response;
  });
};
const changeProvince = (val) => {
  cities.value = [];
  areas.value = [];
  form.city_code = ''; //市编码
  form.city_name = ''; //市名称
  form.district_code = ''; //区编码
  form.district_name = ''; //区名称
  //获取省名称;
  form.province_code = val;
  nextTick(() => {
    form.province_name = proxy.$refs['province'].currentPlaceholder;
  });
  loadCities(val);
};
const changeCity = (val) => {
  areas.value = [];
  form.city_code = val;
  form.district_code = ''; //区编码
  //获取市名称
  nextTick(() => {
    form.city_name = proxy.$refs['city'].currentPlaceholder;
  });
  loadAreas(val);
};
const changeDistrict = (val) => {
  form.district_code = val;
  nextTick(() => {
    form.district_name = proxy.$refs['dist'].currentPlaceholder;
  });
};
const changeProperty = () => {
  nextTick(() => {
    form.property_name = proxy.$refs['property'].currentPlaceholder;
  });
};
const handleNodeClick = (node) => {
  form.org_department_id = node.id;
  form.org_department_name = node.label;
  localtionName.value = node.label;
  proxy.$refs.selectTree.blur();
};
const cancelAndClose = (editForm) => {
  editForm.resetFields();
  closeCurrentTab({
    path: '/park/parkInfo'
  });
};
//点击“保存”更新数据
const savePark = (editForm) => {
  console.log('form', form);
  editForm.validate().then(() => {
    if (
      form.province_code === undefined ||
      form.province_code === '' ||
      form.city_code === undefined ||
      form.city_code === '' ||
      form.district_code === undefined ||
      form.district_code === '' ||
      form.province_name === undefined ||
      form.province_name === '' ||
      form.city_name === undefined ||
      form.city_name === '' ||
      form.district_name === undefined ||
      form.district_name === ''
    ) {
      ElMessage({
        message: '省、市、区不能为空',
        type: 'warning'
      });
      return false;
    }
    if (form.longitude === '' || form.latitude === '') {
      ElMessage({
        message: '请打开地图选择坐标经纬度',
        type: 'warning'
      });
      return false;
    }
    if (form.terminal_by_date === '' || form.terminal_by_date === undefined) {
      ElMessage({
        message: '请选择终端截止日期',
        type: 'warning'
      });
      return false;
    }
    if (form.online_date === '' || form.online_date === undefined) {
      ElMessage({
        message: '请选择车场上线时间',
        type: 'warning'
      });
      return false;
    }
    if (timeValue.value.length <= 0) {
      ElMessage({
        message: '请选择车场经纬度',
        type: 'warning'
      });
      return false;
    }
    form.open_start_time = timeValue.value[0];
    form.open_end_time = timeValue.value[1];

    parkInfoService
      .updatePark(form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          cancelAndClose(editForm);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        cancelAndClose(editForm);
      });
  });
};
// 打开地图
const openMap = () => {
  mapDialogVisible.value = true;
};
// 选中的坐标
const checkedPunctuation = (data) => {
  form.address = data.address;
  form.longitude = data.lng;
  form.latitude = data.lat;
};
const changeFast = () => {
  pile_count.value = form.fast_charge_piles + form.slow_charge_piles;
};
const changeSlow = () => {
  pile_count.value = form.fast_charge_piles + form.slow_charge_piles;
};
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
const onSuccessUpload = (response) => {
  if (response.success == true) {
    form.fee_stand = response.data.fee_stand;
    form.fee_stand_name = response.data.fee_stand_name;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
const beforeEtcUpload = (file) => {
  //请先输入编号
  if (form.etc_merchant_id === undefined || form.etc_merchant_id === '') {
    ElMessage({
      message: '请先填写商户编号',
      type: 'error'
    });
    return false;
  }
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
const onSuccessEtcCertUpload = (response) => {
  if (response.success == true) {
    form.etc_cert_path = response.data.etc_cert_name;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
const onSuccessEtcRootCertUpload = (response) => {
  if (response.success == true) {
    form.etc_cert_root_path = response.data.etc_cert_name;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
const getFiles = (url) => {
  window.open(url, '_blank');
};
</script>

<style lang="scss" scoped>
.moncardbox {
  display: flex;
  align-items: center;
  padding-left: 10px;
  .moncardlabel {
    width: 122px;
    font-size: 13px;
    color: #606266;
    display: flex;
    align-items: center;
  }
}
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}

.el-input-number-append {
  display: inline;
  background-color: #f5f7fa;
  padding: 0px 10px;
  width: 31px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-radius: 0px 2px 2px 0px;
}
</style>
