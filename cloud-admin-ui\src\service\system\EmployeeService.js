import * as employee from '@/api/system/EmployeeApi';

/**
 * 员工
 */
export default {
  /**
   * 修改密码
   */
  changePasswd(param) {
    return new Promise((resolve, reject) => {
      try {
        employee.changePasswd(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分页查询
   */
  pagingEmployees(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.pagingEmployees(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 据部门ID分页查询员工
   */
  pagingEmployeesByDepartmentId(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.pagingEmployeesByDepartmentId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 保存员工
   */
  createEmployee(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.createEmployee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改员工
   */
  updateEmployee(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.updateEmployee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除员工
   */
  deleteEmployees(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.deleteEmployees(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 重置密码
   */
  resetPassword(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.resetPassword(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询员工
   */
  getEmployeeById(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.getEmployeeById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 部门树
   */
  departmentTree() {
    return new Promise((resolve, reject) => {
      try {
        employee.departmentTree().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取角色列表
   */
  findRoles() {
    return new Promise((resolve, reject) => {
      try {
        employee.findRoles().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用员工
   */
  enable(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.enable(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 禁用员工
   */
  disable(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.disable(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分管车场授权
   */
  parkingAuthority(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.parkingAuthority(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询车场信息
   */
  getParkList(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.getParkList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询员工授权车场信息
   */
  employeeParkList(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.employeeParkList(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 一键授权车场
   */
  authAllParks(data) {
    return new Promise((resolve, reject) => {
      try {
        employee.authAllParks(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
