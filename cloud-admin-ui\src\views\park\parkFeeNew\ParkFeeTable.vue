<template>
  <el-card style="margin-top: 10px; margin-bottom: -10px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加车场费率</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="180">
          <template v-slot="scope">
            <el-button
              link
              v-if="
                (scope.row.audit_state == 0 || scope.row.audit_state == 3 || scope.row.audit_state == 4) &&
                scope.row.valid_state == 0 &&
                !scope.row.valid_start_time
              "
              type="primary"
              @click="submitFeeAudit(scope.row.id)"
            >
              提交审核
            </el-button>
            <el-button
              link
              v-if="
                ((scope.row.valid_state == 0 && (!scope.row.valid_start_time || scope.row.valid_start_time == '')) || scope.row.valid_state == 2) &&
                scope.row.audit_state == 2
              "
              type="primary"
              @click="showSubmitAudit(scope.row.id, true)"
            >
              启用
            </el-button>
            <el-button
              link
              v-if="scope.row.valid_state == 1 && (!scope.row.valid_end_time || scope.row.valid_start_time)"
              type="danger"
              @click="showSubmitAudit(scope.row.id, false)"
            >
              停用
            </el-button>
            <el-button
              link
              type="primary"
              v-if="
                (scope.row.audit_state == 0 || scope.row.audit_state == 3 || scope.row.audit_state == 4) &&
                scope.row.valid_state == 0 &&
                !scope.row.valid_start_time
              "
              @click="handleEdit(scope.row.id, true)"
            >
              编辑
            </el-button>
            <el-button
              link
              type="primary"
              v-if="
                (scope.row.audit_state !== 0 && scope.row.audit_state !== 3 && scope.row.audit_state !== 4) ||
                (scope.row.audit_state == 3 && scope.row.valid_state == 1) ||
                (scope.row.audit_state == 4 && scope.row.valid_state == 1)
              "
              @click="handleDetail(scope.row.id)"
            >
              查看
            </el-button>
            <el-button link v-if="scope.row.audit_state == 1" type="primary" @click="cancel(scope.row.id)"> 撤销 </el-button>
            <el-button link type="primary" @click="testParkFee(scope.row.id, scope.row.region_name)">预览</el-button>
            <el-button
              link
              type="danger"
              v-if="
                (scope.row.valid_state == 0 && (scope.row.audit_state == 0 || scope.row.audit_state == 3 || scope.row.audit_state == 4)) ||
                scope.row.valid_state == 2
              "
              @click="handleDel(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="规则名称" align="center" min-width="200" />
        <el-table-column prop="region_name" label="关联子场/区域" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.region_name || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audit_state_desc" label="审核状态" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.audit_state_desc || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="valid_state_desc" label="生效状态" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.valid_state_desc || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="valid_start_time" label="生效时间" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.valid_start_time ? scope.row.valid_start_time + '点' : '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="valid_end_time" label="失效时间" align="center">
          <template v-slot="scope">
            <span>{{ scope.row.valid_end_time ? scope.row.valid_end_time + '点' : '-' }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="auditState ? '启用计费规则' : '停用计费规则'" width="620">
    <div>
      <div style="display: flex; align-items: center">
        <span class="required">{{ auditState ? '生效时间' : '失效时间' }}</span>
        <el-radio-group v-model="auditParams.type" style="margin-left: 10px">
          <el-radio v-for="item in parkFeeEnableTypes" :label="item.value" :key="item.value">{{
            auditState ? item.key : item.key.slice(0, 2) + '失效'
          }}</el-radio>
        </el-radio-group>
        <!-- <el-date-picker
          v-if="auditParams.type == 2"
          v-model="auditParams.timed_time"
          type="datetime"
          placeholder="请选择"
          format="YYYY-MM-DD H"
          value-format="YYYY-MM-DD H"
          date-format="YYYY-MMM-DD"
          time-format="H"
          style="width: 160px; margin-left: 10px"
        /> -->
        <el-date-picker
          v-if="auditParams.type == 2"
          v-model="auditParams.timed_time"
          type="date"
          placeholder="选择日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          style="width: 140px; margin-left: 10px"
        />

        <el-select v-if="auditParams.type == 2" v-model="timed_hour" placeholder="选择小时" style="width: 120px; margin-left: 10px">
          <el-option v-for="hour in hourList" :key="hour" :label="`${hour}点`" :value="hour" />
        </el-select>
      </div>
      <div style="display: flex; align-items: center; margin-top: 24px">
        <el-icon color="#f5222d">
          <QuestionFilled />
        </el-icon>
        <span style="color: #f5222d; margin-left: 10px">
          {{ auditState ? '请确认计费规则，提交后，到达生效时间时，规则自动生效' : '提交之后将进入审核流程，审核通过后才生效' }}
        </span>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="submitAudit">提交</el-button>
      </div>
    </template>
  </el-dialog>
  <el-drawer v-model="previewState.drawerVisible" :with-header="false">
    <div style="display: flex; margin-bottom: 10px">
      <span class="required" style="display: inline-block; height: 32px; line-height: 32px; width: 80px">子车场：</span>
      <el-select v-model="previewState.form.park_region_id" placeholder="请选择所属子场/区域" style="flex: 1" @change="changeRegion">
        <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </div>
    <div style="display: flex">
      <span style="display: inline-block; height: 32px; line-height: 32px; width: 80px">停车时间：</span>
      <el-date-picker
        v-model="previewState.dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="入场开始时间"
        end-placeholder="入场结束时间"
        style="width: 100px"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />&ensp;
      <el-button type="primary" @click="previewCalcModel">预 览</el-button>
    </div>
    <div class="title">基本信息</div>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">车场ID：</span>
          <span class="value">{{ previewState.data.park_id }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">车场名称：</span>
          <span class="value">{{ previewState.data.park_name }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">子场ID：</span>
          <span class="value">{{ previewState.data.park_region_id }}</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">子场名称：</span>
          <span class="value">{{ previewState.data.park_region_name }}</span>
        </div>
      </el-col>
    </el-row>
    <el-row :gutter="10">
      <el-col :span="12">
        <div class="desc">
          <span class="label">收费金额：</span>
          <span class="value" style="color: #fb001e">{{ previewState.data.total_money }}&ensp;元</span>
        </div>
      </el-col>
      <el-col :span="12">
        <div class="desc">
          <span class="label">停车时长：</span>
          <span class="value">{{ previewState.data.duration_text }}</span>
        </div>
      </el-col>
    </el-row>
    <div class="title">计费明细</div>
    <el-table :data="previewState.data.details" border style="margin-top: 10px; height: calc(100vh - 338px)">
      <el-table-column prop="start_time" label="开始时间" align="center"></el-table-column>
      <el-table-column prop="end_time" label="结束时间" align="center"></el-table-column>
      <el-table-column prop="money" label="计费金额" align="center">
        <template v-slot="scope">
          <span style="color: #fb001e">{{ scope.row.money }}</span>
        </template>
      </el-table-column>
    </el-table>
    <template #footer>
      <div style="flex: auto">
        <el-button type="primary" @click="handleDrawerClose">关 闭</el-button>
      </div>
    </template>
  </el-drawer>
</template>

<script name="ParkFeeTable" setup>
import commonService from '@/service/common/CommonService';
import parkFeeService from '@/service/park/ParkFeeService';
import parkRegionService from '@/service/park/ParkRegionService';
import { getToken } from '@/utils/common';
import { getIamAndNormal, getOpenUrl } from '@/utils/iamFlow';
import { activeRouteTab } from '@/utils/tabKit';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
// import { useUser } from '@/stores/user';
import { debounce } from 'lodash';
// const user = useUser();
const tableData = ref([]);
const park_id = ref('');
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: '',
    page: 1,
    limit: 30
  }
});
// 停用和启用标识
const dialogVisible = ref(false);
const auditState = ref(false);
const auditParams = reactive({
  id: undefined,
  type: 1,
  timed_time: undefined
});
const timed_hour = ref(0);
const hourList = ref([...Array(24).keys()]);
const parkFeeEnableTypes = ref([]);

// 预览计费
const previewState = reactive({
  drawerVisible: false,
  form: {
    park_id: undefined,
    park_region_id: undefined,
    fee_model_to_park_id: undefined,
    in_time: undefined,
    out_time: undefined
  },
  data: {
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    park_in_record_id: undefined,
    total_money: 0,
    duration: undefined,
    duration_text: undefined,
    details: []
  },
  dateRange: []
});
const parkRegions = ref([]);
onMounted(() => {
  window.addEventListener('message', handleMessage, false);

  const param = [
    {
      enum_key: 'parkFeeEnableTypes',
      enum_value: 'EnumParkFeeEnableType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    parkFeeEnableTypes.value = response.data.parkFeeEnableTypes;
  });
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  console.log(params);
  if (params.park_id != undefined && params.park_id != '') {
    park_id.value = params.park_id;
  } else {
    params.park_id = park_id.value;
  }
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkFeeService.pagingParkFeesNew(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  activeRouteTab({
    path: '/park/parkFeeNew/parkFeeAdd',
    query: {
      park_id: park_id.value
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleEdit = (id, edit) => {
  activeRouteTab({
    path: '/park/parkFeeNew/parkFeeEdit',
    query: {
      feeId: id,
      park_id: park_id.value,
      edit
    }
  });
};
const handleDel = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkFeeService
      .deleteParkFeeNew(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleDetail = (id) => {
  activeRouteTab({
    path: '/park/parkFeeNew/parkFeeDetail',
    query: {
      feeId: id,
      parkId: park_id.value
    }
  });
};

//提交费率审核
const submitFeeAudit = (id) => {
  // 确认后跳转至临时规则申请页面
  if (!getIamAndNormal(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/TemporaryRule?id=${id}&parkToken=${getToken()}`))) {
    ElMessageBox.confirm('请确认是否提交审核？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      parkFeeService
        .submitAuditParkFeeNew(id)
        .then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }
};

const showSubmitAudit = (id, state) => {
  auditState.value = state;
  dialogVisible.value = true;
  auditParams.id = id;
  auditParams.type = 1;
  auditParams.timed_time = undefined;
};
// 提交审核
const submitAudit = debounce(async () => {
  if (auditParams.type == 2 && auditParams.timed_time === undefined) {
    ElMessage({
      message: '请选择生效时间',
      type: 'warning'
    });
    return;
  }
  if (auditParams.type == 2 && auditParams.timed_time < new Date()) {
    ElMessage({
      message: '生效时间不能小于当前时间',
      type: 'warning'
    });
    return;
  }

  let response;
  let time = '';
  let params = { ...auditParams };
  if (auditParams.timed_time) {
    time = auditParams.timed_time + ' ' + timed_hour.value;
    params.timed_time = time;
  }

  try {
    if (auditState.value) {
      response = await parkFeeService.enableParkFee(params);
    } else {
      response = await parkFeeService.deactivateParkFee(params);
    }
    if (response.success === true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      dialogVisible.value = false;
      getList(data.queryParams);
    }
  } catch (e) {
    ElMessage.error(e.response.detail_message || e.response.message);
  }
}, 800); // 防抖延迟 1 秒

//撤销
const cancel = (id) => {
  ElMessageBox.confirm('请确认是否撤销？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkFeeService
      .cancelParkFeeNew(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//计费模型测试
const testParkFee = (val, region_name) => {
  const regionNameArr = region_name.split(',');
  previewState.form = {
    park_id: park_id.value,
    park_region_id: undefined,
    fee_model_to_park_id: val,
    in_time: undefined,
    out_time: undefined
  };
  previewState.data = {
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    park_region_name: undefined,
    park_in_record_id: undefined,
    total_money: 0,
    duration: undefined,
    duration_text: undefined,
    details: []
  };
  previewState.dateRange = [];
  previewState.drawerVisible = true;
  parkRegionService.listParkRegionAndArea(park_id.value).then((response) => {
    parkRegions.value = response.data.filter((item) => regionNameArr.includes(item.name));
  });
};

const previewCalcModel = () => {
  if (previewState.dateRange.length <= 0) {
    ElMessage({
      message: '请选择时间段',
      type: 'warning'
    });
    return false;
  }
  if (previewState.form.park_region_id === undefined || previewState.form.park_region_id === '') {
    ElMessage({
      message: '请选择子车场/区域',
      type: 'warning'
    });
    return false;
  }
  previewState.form.in_time = previewState.dateRange[0];
  previewState.form.out_time = previewState.dateRange[1];
  parkFeeService.previewCalcModelNew(previewState.form).then((response) => {
    console.log(response);
    if (response.success === true) {
      previewState.data = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const handleDrawerClose = () => {
  previewState.drawerVisible = false;
};

defineExpose({
  getList
});
</script>

<style lang="scss" scoped>
.desc {
  line-height: 36px;
}

.label {
  color: rgba(0, 0, 0, 0.65);
  padding-left: 10px;
  padding-right: 10px;
}

.value {
  color: rgba(0, 0, 0, 0.8);
}

.title {
  height: 32px;
  line-height: 32px;
  width: 100%;
  background-color: #eaeaea;
  padding-left: 8px;
  color: rgba(0, 0, 0, 0.7);
  margin-top: 10px;
}

:deep(.el-drawer__body) {
  padding: 10px !important;
}

.required::before {
  padding-right: 4px;
  content: '*  ';
  color: #f5222d;
}
</style>
