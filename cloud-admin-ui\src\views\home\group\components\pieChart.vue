<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-04-01 10:21:50
 * @LastEditors: 达万安 段世煜
 * @Description: 环形图组件
 * @FilePath: \cloud-admin-ui\src\views\home\group\components\pieChart.vue
-->
<template>
  <div ref="chartRef" class="chart-warp"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { markRaw, onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps({
  // 颜色数组
  color: {
    type: Array,
    default: () => ['#00dbf2', '#ff917c', '#1890ff', '#6dfacd', '#ffa800', '#ff5b00', '#ff3000', '#ea7ccc', '#DDD7C6', '#5470c6']
  },
  // 是否玫瑰图
  rose: {
    type: Boolean,
    default: false
  },
  // 是否环形图
  circle: {
    type: Boolean,
    default: false
  },
  // 图例布局方向
  legendOrient: {
    type: String,
    default: 'horizontal'
  },
  // 环形半径
  radius: {
    type: [String, Array, Object],
    default: () => ['45%', '65%']
  },
  // 环形半径
  center: {
    type: Array,
    default: () => ['50%', '50%']
  },
  border: {
    type: Number,
    default: 0
  },
  startAngle: {
    type: Number,
    default: 90
  },
  showLengend: {
    type: Boolean,
    default: true
  },
  labelFormatter: {
    type: String,
    default: '{percent|{d}%}\n'
  },
  size: {
    type: String,
    default: 'default'
  },
  labelRich: {
    type: Object,
    default: () => {
      return {
        percent: {
          padding: 4,
          fontSize: 12,
          color: '#fff',
          align: 'left'
        }
      };
    }
  },
  labelAlgin: {
    type: String,
    default: 'none'
  }
});
const chartRef = ref(null);
let chartInstance = null;
onMounted(() => {
  chartInstance = markRaw(echarts.init(chartRef.value));
  window.addEventListener('resize', resizeHandler);
});

const setData = (data) => {
  chartInstance.clear();
  const options = {
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {d}% <br/> {c}',
      confine: true
    },
    legend: {
      show: props.showLengend,
      orient: props.legendOrient,
      icon: 'roundRect',
      bottom: 0,
      left: 'center',
      // textStyle: {
      //   color: '#fff'
      // },
      itemWidth: 14,
      itemHeight: 8,
      formatter: ['{a|{name}}'].join('\n'),
      textStyle: {
        color: '#fff',
        rich: {
          a: {
            width: 60, // 每个图例的宽度，具体根据字数调整
            lineHeight: 12
          }
        }
      }
      // width: 5
    },
    color: props.color,
    series: [
      {
        type: 'pie',
        radius: props.radius,
        center: props.center,
        minAngle: 5,
        top: 0,
        roseType: props.rose ? 'radius' : '',
        itemStyle: {
          borderColor: 'rgb(11,36,92)',
          borderWidth: props.border
        },
        data: data,
        labelLine: {
          show: true,
          length: props.size === 'default' ? 3 : 50,
          length2: 0,
          lineStyle: {
            width: 2
          },
          minTurnAngle: 120,
          maxSurfaceAngle: 120
        },
        label: {
          alignTo: props.labelAlgin,
          formatter: props.labelFormatter,
          minMargin: 5,
          lineHeight: 15,
          bleedMargin: 5,
          rich: props.labelRich
        },
        labelLayout: function (params) {
          const isLeft = params.labelRect.x < chartInstance.getWidth() / 2;
          const points = params.labelLinePoints;
          if (points) {
            // Update the end point.
            points[2][0] = isLeft ? params.labelRect.x : params.labelRect.x + params.labelRect.width;
            return {
              labelLinePoints: points
            };
          }
        },
        startAngle: props.startAngle
      }
    ]
  };
  // 计算最长图例的长度
  // var getLongestTextLength = function (legendData) {
  //   var longest = 0;
  //   legendData.forEach(function (item) {
  //     longest = Math.max(longest, item.length);
  //   });
  //   // 将长度转换为像素值，可以根据实际字体大小调整系数
  //   return longest * 10;
  // };

  // 设置图例项的宽度
  // options.legend.itemWidth = getLongestTextLength(
  //   options.series[0].data.map((item) => {
  //     return item.name;
  //   })
  // );

  chartInstance.setOption(options, true);
};
const resizeHandler = () => {
  if (!chartInstance) return;
  chartInstance.resize();
};
const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
  window.removeEventListener('resize', resizeHandler);
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData
});
</script>

<style lang="scss" scoped>
.chart-warp {
  width: 100%;
  height: 100%;
}
</style>
