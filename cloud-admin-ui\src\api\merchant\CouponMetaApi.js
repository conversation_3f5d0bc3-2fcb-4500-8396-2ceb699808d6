/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 优惠券表格数据查询
export const pagingCouponMetas = (data) => {
  return $({
    url: '/console/coupon/meta/pagingCouponMetas',
    method: 'post',
    data
  });
};

//新增优惠券
export const createCouponMeta = (data) => {
  return $({
    url: '/console/coupon/meta/createCouponMeta',
    method: 'post',
    data
  });
};

// 修改商户
export const updateCouponMeta = (data) => {
  return $({
    url: '/console/coupon/meta/updateCouponMeta',
    method: 'post',
    data
  });
};

// 删除
export const deleteCouponMeta = (id) => {
  return $({
    url: '/console/coupon/meta/deleteCouponMeta/' + id,
    method: 'post'
  });
};

// 启用
export const enableCouponMeta = (id) => {
  return $({
    url: '/console/coupon/meta/enableCouponMeta/' + id,
    method: 'post'
  });
};

// 停用
export const disableCouponMeta = (id) => {
  return $({
    url: '/console/coupon/meta/disableCouponMeta/' + id,
    method: 'post'
  });
};

// 优惠券表格数据查询(查找带回)
export const findBackCouponMetas = (data) => {
  return $({
    url: '/console/coupon/meta/findBackCouponMetas',
    method: 'post',
    data
  });
};
