/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询退款管理
export const pagingRefundOrder = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/pagingRefundOrder',
    method: 'post',
    data
  });
};

// 查询长租退款订单详情
export const getRefundLeaveDetail = (id) => {
  return $({
    url: '/console/park/finance/refundOrder/getRefundLeaveDetail/' + id,
    method: 'post'
  });
};

// 查询临停退款订单详情
export const getRefundStopDetail = (id) => {
  return $({
    url: '/console/park/finance/refundOrder/getRefundStopDetail/' + id,
    method: 'post'
  });
};

// 长租打款完成
export const leavingComplete = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/leavingComplete',
    method: 'post',
    data
  });
};

// ETC退款申请
export const etcRefund = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/etcRefund',
    method: 'post',
    data
  });
};

// 临停打款完成
export const stopComplete = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/stopComplete',
    method: 'post',
    data
  });
};

// 长租取消退款
export const leavingCancel = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/leavingCancel',
    method: 'post',
    data
  });
};

// 临停取消退款
export const stopCancel = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/stopCancel',
    method: 'post',
    data
  });
};

// 易宝退款-原路返回
export const yibaoCancel = (data) => {
  return $({
    url: '/console/park/finance/refundOrder/refundYopOrder',
    method: 'post',
    data
  });
};
