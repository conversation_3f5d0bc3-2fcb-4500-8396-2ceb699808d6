<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick" class="h-full">
    <el-tab-pane label="日周转率" name="parkTurnover">
      <park-turnover type="date" />
    </el-tab-pane>
    <el-tab-pane label="周周转率" name="naturalWeekTurnover">
      <natural-week-turnover />
    </el-tab-pane>
    <el-tab-pane label="月周转率" name="monthparkTurnover">
      <park-turnover type="month" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="ParkSpaceTurnover" setup>
import parkTurnover from './ParkTurnover.vue';
import naturalWeekTurnover from './NaturalWeekTurnover.vue';
import { ref } from 'vue';

const activeName = ref('parkTurnover');
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
