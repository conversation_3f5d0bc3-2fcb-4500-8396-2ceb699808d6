<template>
  <div>
    <el-row :gutter="10" style="overflow: hidden">
      <el-col :span="16">
        <div>
          <p class="tips">选择</p>
          <div style="border: 1px solid #dcdfe6; padding: 20px; margin-top: 10px; height: 550px">
            <el-input v-model="filterText" placeholder="请输入部门名称查询" clearable @input="changeFilterText" />
            <div style="height: 490px; overflow: auto; margin-top: 10px">
              <el-tree ref="departmentTree" :data="treeData.departmentTree" :check-strictly="false" default-expand-all
                :expand-on-click-node="false" show-checkbox check-on-click-node="true" accordion node-key="id"
                highlight-current :default-checked-keys="treeSelectList" :filter-node-method="filterNode"
                @check-change="checkAllIn" />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="grid-content">
          <p class="tips">
            <span>已选</span>
            <el-button class="reset-checked" link type="primary" @click="resetChecked"> 清空 </el-button>
          </p>
          <div style="border: 1px solid #dcdfe6; padding: 20px; margin-top: 10px; height: 550px">
            <ul style="overflow-y: auto;height: 100%;">
              <li v-for="(item, key) in orignalData" :key="key" :data-id="item.id">
                <span>{{ item.label }}</span>
                <el-icon :size="16" class="icon" @click="removeItem(item.id)">
                  <RemoveFilled />
                </el-icon>
              </li>
            </ul>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="margin-top: 10px; text-align: right">
          <el-button @click="cancel"> 取 消 </el-button>
          <el-button type="primary" @click="submitTableData"> 确 定 </el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script name="OrgFindBack" setup>
import departmentService from '@/service/system/DepartmentService';
import { RemoveFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getCurrentInstance, onMounted, reactive, ref } from 'vue';
const data = reactive({
  queryParams: {}
});

const departmentTree = ref();
const treeSelectList = ref([]);

const treeData = reactive({
  departmentTree: []
});

const props = defineProps({
  title: {
    type: String,
    default: '表格型查找带回'
  },
  park_id: {
    type: String,
    required: true
  },
  organization_id: {
    type: String,
    required: true
  },
  department_name: {
    type: String,
    required: true
  },
  mode: {
    type: String,
    required: true
  }
});

const { proxy } = getCurrentInstance();
const filterText = ref('');
const orignalData = ref([]);

onMounted(() => {
  getDepartmentTree();
});

const getDepartmentTree = () => {
  initDepartmentTree();
  treeSelectList.value = [];
  if (props.organization_id === undefined || props.organization_id === '') {
    console.log('no');
  } else {
    const arr = [];
    const departmentIdArr = props.organization_id.split(',');
    treeSelectList.value = departmentIdArr;
    const departmentNameArr = props.department_name.split(',');
    for (var i = 0; i < departmentIdArr.length; i++) {
      const object = {
        id: departmentIdArr[i],
        label: departmentNameArr[i]
      };
      arr.push(object);
    }
    // 初始化右侧列表的值;
    orignalData.value = arr;
    // 勾选左侧部门树
    console.log(props.organization_id);
    departmentTree.value.setCheckedKeys([props.organization_id], true);
  }
};

const initDepartmentTree = () => {
  departmentService.departmentTree().then((res) => {
    if (res.success == true) {
      treeData.departmentTree = res.data;
    } else {
      ElMessage({
        message: res.detail_message,
        type: 'error'
      });
    }
  });
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.indexOf(value) !== -1;
};

const changeFilterText = (val) => {
  if (val !== undefined && val.trim() !== '') {
    data.queryParams.name = val;
  } else {
    delete data.queryParams.name;
  }
  initDepartmentTree();
};

const checkAllIn = (data, checked) => {
  if (checked === true) {
    // console.log('add data===', data);
    if (!data.children || data.children.length === 0) {
      let arr = {
        id: data.id,
        label: data.label
      };
      orignalData.value.push(arr);
    }
  } else {
    // console.log('del data===', data);
    if (!data.children || data.children.length === 0) {
      for (let i = 0; i < orignalData.value.length; i++) {
        if (orignalData.value[i].id === data.id) {
          orignalData.value.splice(i, 1);
        }
      }
    }
  }
};

const resetChecked = () => {
  if (orignalData.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning'
    });
  } else {
    ElMessageBox.confirm('确定要清空已选项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        departmentTree.value.setCheckedKeys([]);
        orignalData.value = [];
      })
      .catch(() => { });
  }
};

// 右侧删除
const removeItem = (id) => {
  departmentTree.value.setCheckedKeys(orignalData.value.map((item) => item.id).filter((item) => item !== id));
};
// 关闭弹框
const cancel = () => {
  proxy.$emit('orgCharge', 'false');
};
// 关闭弹框渲染input
const submitTableData = () => {
  if (orignalData.value.length < 0 || orignalData.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning'
    });
    return false;
  }
  proxy.$emit('renderOrgTableInput', orignalData.value);
  proxy.$emit('orgCharge', 'false');
};
</script>
<style scoped lang="scss">
.grid-conten {
  padding: 0 10px;
}

.tips {
  display: flex;
  justify-content: space-between;
  margin-top: 0;
  overflow: hidden;
}

.reset-checked {
  color: #1e9fff;
  cursor: pointer;
  padding: 0;
}

.reset-checked:hover {
  color: #ff6c65;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 564px;
  overflow: auto;
}

.icon {
  color: #999;
  cursor: pointer;
  margin-top: 5px;
}

.icon:hover {
  color: #ff6c65;
  text-shadow: #ff6c65 0px 0px 2px;
}

ul li {
  display: flex;
  justify-content: space-between;
  line-height: 28px;
  padding-left: 10px;
}

ul li:hover {
  background-color: #f5f7fa;
}

.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
