/* jshint esversion: 6 */
import $ from '@/utils/axios';

/*
 * 角色管理模块
 */
// 添加角色
export const createRole = (data) => {
  return $({
    url: '/console/role/createRole',
    method: 'post',
    data
  });
};

// 修改角色
export const updateRole = (data) => {
  return $({
    url: '/console/role/updateRole',
    method: 'post',
    data
  });
};

// 删除角色
export const deleteRoles = (data) => {
  return $({
    url: '/console/role/deleteRoles',
    method: 'post',
    data
  });
};

// 查询单条角色信息
export const getRoleById = (id) => {
  return $({
    url: '/console/role/getRoleById/' + id,
    method: 'post'
  });
};

// 启用角色
export const enableRole = (roleId) => {
  return $({
    url: '/console/role/enableRole/' + roleId,
    method: 'post'
  });
};

// 停用角色
export const disableRole = (roleId) => {
  return $({
    url: '/console/role/disableRole/' + roleId,
    method: 'post'
  });
};

// 分页查询
export const pagingRoles = (data) => {
  return $({
    url: '/console/role/pagingRoles',
    method: 'post',
    data
  });
};

// 查询角色
export const findRoles = () => {
  return $({
    url: '/console/role/findRoles',
    method: 'get'
  });
};

// 获得菜单树
export const findMenuTree = (data) => {
  return $({
    url: '/console/role/findMenuTree',
    method: 'post',
    data
  });
};

// 保存配置权限
export const configPermissions = (data) => {
  return $({
    url: '/console/role/configPermissions',
    method: 'post',
    data
  });
};

// 配置Api权限
export const configApiPermissions = (data) => {
  return $({
    url: '/console/role/configApiPermissions',
    method: 'post',
    data
  });
};

// 角色列表
export const listRoles = () => {
  return $({
    url: '/console/role/getRoleListByUserId',
    method: 'get'
  });
};

// 切换角色
export const switchRole = (roleId) => {
  return $({
    url: '/console/role/switchRole/' + roleId,
    method: 'post'
  });
};
