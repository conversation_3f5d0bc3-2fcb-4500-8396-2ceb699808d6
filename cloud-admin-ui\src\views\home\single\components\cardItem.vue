<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-06-27 09:01:11
 * @LastEditTime: 2024-06-27 16:23:32
 * @LastEditors: 达万安 段世煜
 * @Description: 通行效率卡片
 * @FilePath: \cloud-admin-ui\src\views\home\single\components\cardItem.vue
-->
<template>
  <div class="card-container">
    <div class="label">{{ props.label }}</div>
    <div class="value">
      <span>{{ props.value }}</span>
      <span>{{ props.unit }}</span>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  value: {
    type: [String, Number],
    default: ''
  },
  unit: {
    type: String,
    default: ''
  }
});
</script>
<style scoped lang="scss">
.card-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  height: 50px;
  background: #f4f7fd;
  padding: 10px;
  border-radius: 4px;
  .label {
    font-size: 14px;
    font-weight: 400;
    color: #333;
    line-height: 20px;
  }
  .value {
    font-size: 20px;
    color: #005bac;
  }
}
</style>
