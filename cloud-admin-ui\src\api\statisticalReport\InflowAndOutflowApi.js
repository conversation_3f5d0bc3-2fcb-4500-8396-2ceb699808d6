/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询进出流量
export const pagingInflowAndOutflow = (data) => {
  return $({
    url: '/console/statistics/car/in/out/flow/pagingCarInOutFlowByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/car/in/out/flow/exportCarInOutFlowByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportDataSum = (data) => {
  return $({
    url: '/console/statistics/car/in/out/flow/exportCarInOutFlowSummaryByPeriod',
    method: 'post',
    data
  });
}
