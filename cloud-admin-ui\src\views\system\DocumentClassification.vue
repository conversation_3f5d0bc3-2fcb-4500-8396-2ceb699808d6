<template>
  <el-card class="document_classification" shadow="never">
    <template #header>
      <div style="display: flex; justify-content: space-between; height: 30px">
        <div style="line-height: 30px"><span>文档分类</span></div>
        <div style="line-height: 30px"><el-button type="primary" @click="handleAdd()">添加一级分类</el-button></div>
      </div>
    </template>
    <el-tree
      ref="departmentTree"
      :data="treeData.departmentTree"
      node-key="id"
      default-expand-all
      :expand-on-click-node="false"
      @node-click="nodeClick"
    >
      <template #default="{ data }">
        <span class="custom-tree-node">
          <span>{{ data.label }}</span>
          <span>
            <a class="tree-item" v-if="data.parent_id !== '2'" @click.stop="add(data)"> + </a>
            <a class="tree-item" style="margin-left: 8px" @click.stop="remove(data)"> X </a></span
          >
        </span>
      </template>
    </el-tree>
    <el-dialog
      title="新建分类"
      v-model="createOneLevelDialogVisible"
      :close-on-click-modal="false"
      @close="closeAddOneLevelDialog(addOneLevelForm)"
      width="500px"
    >
      <el-form ref="addOneLevelForm" label-width="120px" :rules="data.rules" :model="data.oneLevelForm">
        <el-form-item prop="name" label="新增分类名称">
          <el-input v-model="data.oneLevelForm.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createOneLevelCancel(addOneLevelForm)">取 消</el-button>
          <el-button type="primary" @click="createOneLevel(addOneLevelForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="新建分类" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
      <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
        <el-form-item label="当前分类名称">{{ docTypeName }}</el-form-item>
        <el-form-item prop="name" label="新增分类名称">
          <el-input v-model="data.form.name" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createCancel(addForm)">取 消</el-button>
          <el-button type="primary" @click="createDocumentClassification(addForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script name="DocumentClassification" setup>
import documentService from '@/service/system/DocumentService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, reactive, onMounted } from 'vue';
const emits = defineEmits(['initData']);

const createDialogVisible = ref(false);
const createOneLevelDialogVisible = ref(false);
const addOneLevelForm = ref();
const docTypeName = ref('');
const addForm = ref();
const treeData = reactive({
  departmentTree: []
});
const documentList = reactive({
  parent_id: 0,
  name: '',
  parent_name: '',
  first_name: ''
});
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  oneLevelForm: {
    name: undefined
  },
  form: {
    parent_doc_type_id: undefined,
    name: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入分类名称',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initDepartmentTree();
});

const initDepartmentTree = () => {
  documentService.docTypeTree().then((res) => {
    if (res.success == true) {
      treeData.departmentTree = res.data;
    } else {
      ElMessage({
        message: res.detail_message,
        type: 'error'
      });
    }
  });
};

// 添加一级分类
const handleAdd = () => {
  createOneLevelDialogVisible.value = true;
};

// 提交并保存文档分类
const createOneLevel = (addOneLevelForm) => {
  addOneLevelForm.validate().then(() => {
    documentService.createDocType(data.oneLevelForm).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        createOneLevelDialogVisible.value = false;
        initDepartmentTree();
        addForm.value.resetFields();
      } else {
        ElMessage({
          message: response.detail_message,
          type: 'error'
        });
      }
    });
  });
};

// 取消一级分类
const createOneLevelCancel = (addOneLevelForm) => {
  addOneLevelForm.resetFields();
  createOneLevelDialogVisible.value = false;
};

// 添加节点
const add = (res) => {
  docTypeName.value = res.label;
  data.form.parent_doc_type_id = res.id;
  createDialogVisible.value = true;
};

// 提交并保存文档分类
const createDocumentClassification = (addForm) => {
  addForm.validate().then(() => {
    documentService.createDocType(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        createDialogVisible.value = false;
        initDepartmentTree();
        addForm.value.resetFields();
      } else {
        ElMessage({
          message: response.detail_message,
          type: 'error'
        });
      }
    });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};

// 删除节点
const remove = (res) => {
  ElMessageBox.confirm('确定要删除当前分类吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    documentService.deleteDocType(res.id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        initDepartmentTree();
      } else {
        ElMessage({
          message: response.detail_message,
          type: 'error'
        });
      }
    });
  });
};

// 获取文档分类名称
const getDocTypeName = (param) => {
  documentService.getDocTypeName(param).then((response) => {
    if (response.success === true) {
      documentList.parent_name = response.data.parent_name;
      documentList.first_name = response.data.first_name;
      emits('initData', documentList);
    } else {
      ElMessage({
        message: response.message,
        type: 'error'
      });
    }
  });
};

const nodeClick = (data) => {
  documentList.doc_type_id = data.id;
  documentList.name = data.label;
  // 获取文档分类名称
  const param = {
    id: data.id,
    parent_id: data.parent_id
  };
  getDocTypeName(param);
};

const closeAddOneLevelDialog = (addOneLevelForm) => {
  addOneLevelForm.resetFields();
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};

defineExpose({ initDepartmentTree });
</script>

<style lang="scss" scoped>
.document_classification {
  height: calc(100vh - 123px);
}

:deep(.el-card__header) {
  padding: 10px 10px;
}

.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.tree-item {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 1px solid rgb(9, 152, 215);
  border-radius: 50%;
  text-align: center;
  line-height: 12px;
  color: rgb(9, 152, 215);
  font-size: 16px;
}
</style>
