<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加支付渠道</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleDetail(scope.row.id)"> 详情 </el-button>
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="primary" @click="authCharge(true, scope.row.id)"> 授权车场 </el-button>
            <el-button link type="danger" v-if="scope.row.pay_channel_state == 1" @click="disable(scope.row)"> 禁用 </el-button>
            <el-button link type="success" v-if="scope.row.pay_channel_state == 2" @click="enable(scope.row)"> 启用 </el-button>
          </template>
        </el-table-column>
        <el-table-column label="授权停车场" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleParkList(scope.row.id)"> {{ scope.row.park_count }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="channel_name" label="支付渠道名称" align="center" />
        <el-table-column prop="pay_channel_state_display" label="支付渠道状态" align="center" />
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <el-dialog title="添加支付渠道" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="600px">
        <el-form ref="addForm" label-width="160px" :rules="data.rules" :model="data.form">
          <el-form-item label="支付渠道名称" prop="channel_name">
            <el-input v-model="data.form.channel_name" placeholder="请输入支付渠道名称" />
          </el-form-item>
          <el-form-item prop="pay_channel_state" label="支付渠道状态">
            <el-radio-group v-model="data.form.pay_channel_state">
              <el-radio v-for="item in states" :key="item.value" :label="item.value">
                {{ item.key }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="ali_app_id" label="阿里应用ID">
            <el-input v-model="data.form.ali_app_id" placeholder="请输入阿里应用ID" />
          </el-form-item>
          <el-form-item label="私钥" prop="ali_private_key">
            <el-input v-model="data.form.ali_private_key" placeholder="请输入私钥" />
          </el-form-item>
          <el-form-item prop="ali_public_key" label="公钥">
            <el-input v-model="data.form.ali_public_key" placeholder="请输入公钥" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createPayChannel(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改支付渠道" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="600px">
        <el-form ref="editForm" label-width="160px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="支付渠道名称" prop="channel_name">
            <el-input v-model="data.updateForm.channel_name" placeholder="请输入支付渠道名称" />
          </el-form-item>
          <el-form-item prop="pay_channel_state" label="支付渠道状态">
            <el-radio-group v-model="data.updateForm.pay_channel_state">
              <el-radio v-for="item in states" :key="item.value" :label="item.value">
                {{ item.key }}
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item prop="ali_app_id" label="阿里应用ID">
            <el-input v-model="data.updateForm.ali_app_id" placeholder="请输入阿里应用ID" />
          </el-form-item>
          <el-form-item prop="ali_private_key" label="私钥">
            <el-input v-model="data.updateForm.ali_private_key" placeholder="请输入私钥" />
          </el-form-item>
          <el-form-item prop="ali_public_key" label="公钥">
            <el-input v-model="data.updateForm.ali_public_key" placeholder="请输入公钥" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updatePayChannel(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 授权车场列表 -->
      <el-dialog title="授权车场列表" v-model="parkListDialogVisible" :close-on-click-modal="false" width="800px">
        <el-table :data="parkTableData" border>
          <el-table-column type="selection" style="text-align: center" width="40" />
          <el-table-column prop="prk_park_id" label="ID" align="center" width="100" />
          <el-table-column prop="prk_park_name" label="名称" align="center" />
          <el-table-column prop="prk_park_code" label="编号" align="center" />
          <el-table-column prop="app_auth_token" label="授权令牌" align="center" />
        </el-table>
        <el-pagination
          background
          :current-page="data.parkQueryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.parkQueryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="parkTotal"
          class="table-pagination"
          @size-change="handleParkSizeChange"
          @current-change="handleParkCurrentChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="parkListDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 支付渠道详情 -->
      <el-dialog title="支付渠道详情" v-model="detailDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.detailForm">
          <el-form-item label="支付渠道">支付宝</el-form-item>
          <el-form-item label="支付渠道名称"> {{ data.detailForm.channel_name }}</el-form-item>
          <el-form-item label="支付渠道状态"> {{ data.detailForm.pay_channel_state_display }}</el-form-item>
          <el-form-item label="阿里应用ID"> {{ data.detailForm.ali_app_id }}</el-form-item>
          <el-form-item label="私钥"> {{ data.detailForm.ali_private_key }}</el-form-item>
          <el-form-item label="公钥"> {{ data.detailForm.ali_public_key }}</el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="detailDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back @authCharge="authCharge(false, '')" :item_id="channel_id" mode="ali" @renderTableInput="renderTableInput" />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="AliPayChannelTable" setup>
import { reactive, ref, onMounted } from 'vue';
import payService from '@/service/finance/PayService';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '../ParkFindBack.vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const addForm = ref();
const editForm = ref();
const parkInfoDialogVisible = ref(false);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const parkListDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

const parkTableData = ref([]);
const parkTotal = ref(0);
const channel_id = ref('');
const states = ref([]);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  parkQueryParams: {
    pay_channel_id: '',
    page: 1,
    limit: 30
  },
  detailForm: {},
  form: {
    channel_name: '',
    ali_app_id: '',
    ali_private_key: '',
    ali_public_key: '',
    pay_channel_state: undefined
  },
  updateForm: {},
  rules: {
    channel_name: [
      {
        required: true,
        message: '请输入支付渠道名称',
        trigger: 'blur'
      }
    ],
    pay_channel_state: [
      {
        required: true,
        message: '请选择支付渠道状态',
        trigger: 'change'
      }
    ],
    ali_app_id: [
      {
        required: true,
        message: '请输入阿里应用ID',
        trigger: 'blur'
      }
    ],
    ali_private_key: [
      {
        required: true,
        message: '请输入私钥',
        trigger: 'blur'
      }
    ],
    ali_public_key: [
      {
        required: true,
        message: '请输入公钥',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumPayChannelState' }];
  commonService.findEnums('channel', param).then((response) => {
    states.value = response.data.states;
  });
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  payService.pageAliPayChannel(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleParkList = (val) => {
  data.parkQueryParams.pay_channel_id = val;
  parkList(data.parkQueryParams);
  parkListDialogVisible.value = true;
};

const parkList = (parkParams) => {
  parkParams.page === undefined ? (parkParams.page = 1) : (parkParams.page = data.parkQueryParams.page);
  parkParams.limit === undefined ? (parkParams.limit = 30) : (parkParams.limit = data.parkQueryParams.limit);
  data.parkQueryParams = parkParams;
  payService.pageAliPayChannelPark(parkParams).then((response) => {
    if (response.success === true) {
      parkTableData.value = response.data.rows;
      parkTotal.value = parseInt(response.data.total);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleParkSizeChange = (val) => {
  data.parkQueryParams.limit = val;
  parkList(data.parkQueryParams);
};
const handleParkCurrentChange = (val) => {
  data.parkQueryParams.page = val;
  parkList(data.parkQueryParams);
};

const handleCreate = () => {
  data.form = {
    channel_name: '',
    ali_app_id: '',
    ali_private_key: '',
    ali_public_key: '',
    pay_channel_state: '1'
  };
  createDialogVisible.value = true;
};

const createPayChannel = (addForm) => {
  addForm.validate().then(() => {
    payService
      .createAliPayChannel(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '支付宝支付渠道添加成功',
            type: 'success'
          });
          addForm.resetFields();
          getList(data.queryParams);
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleEdit = (val) => {
  data.updateForm = {
    id: val.id,
    channel_name: val.channel_name,
    ali_app_id: val.ali_app_id,
    ali_private_key: val.ali_private_key,
    ali_public_key: val.ali_public_key,
    pay_channel_state: val.pay_channel_state
  };
  updateDialogVisible.value = true;
};
const updatePayChannel = (editForm) => {
  editForm.validate().then(() => {
    payService
      .updateAliPayChannel(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '支付宝支付渠道修改成功',
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//启用
const enable = (row) => {
  ElMessageBox.confirm('是否要启用该支付渠道？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const parms = {
      id: row.id,
      pay_channel_state: 1
    };
    payService.updateAliPayChannelState(parms).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '启用支付渠道成功',
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 禁用
const disable = (row) => {
  ElMessageBox.confirm('是否要禁用该支付渠道？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const parms = {
      id: row.id,
      pay_channel_state: 2
    };
    payService.updateAliPayChannelState(parms).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '禁用支付渠道成功',
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

const handleDetail = (val) => {
  payService.getAliPayChannelDetail(val).then((response) => {
    if (response.success === true) {
      data.detailForm = response.data;
      detailDialogVisible.value = true;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, val) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    channel_id.value = val;
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  const params = {
    id: channel_id.value,
    prk_park_list: val
  };
  payService
    .authAliPayChannelPark(params)
    .then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '授权车场成功',
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    })
    .catch(() => {
      getList(data.queryParams);
    });
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
