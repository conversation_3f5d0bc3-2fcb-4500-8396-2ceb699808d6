<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-29 14:57:03
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\system\messagePush\MessagePushTab.vue
 * @Description: {}
-->
<template>
  <el-card class="tab" shadow="never">
    <el-row :gutter="10">
      <el-col :span="24" style="margin-top: 10px">
        <el-radio-group v-model="radio" size="large" @change="radioChange">
          <el-radio-button label="长租车到期提醒" value="1" />
          <el-radio-button label="车辆长期临停提醒" value="2" />
          <el-radio-button label="欠费催缴提醒" value="8" />
          <el-radio-button label="预警事件提醒" value="3" />
          <el-radio-button label="设备离线提醒" value="4" />
          <el-radio-button label="免费车入场提醒" value="5" />
          <el-radio-button label="访客车入场提醒" value="6" />
          <el-radio-button label="月卡审批提醒" value="7" />
        </el-radio-group>
      </el-col>
    </el-row>
  </el-card>
</template>

<script name="MessagePushTab" setup>
// import { onMounted } from 'vue';

const emit = defineEmits(['radioChange']);
const radio = defineModel('radio');
const radioChange = () => {
  emit('radioChange', radio.value);
};
</script>

<style lang="scss" scoped>
.tab {
  margin-top: 10px;
}
</style>
