<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" clearable /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.mbr_member_name" placeholder="车主姓名" clearable /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.mbr_member_mobile" placeholder="手机号" clearable /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.product_types" placeholder="产品类型" multiple clearable>
        <el-option v-for="item in productTypes" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.pay_states" placeholder="支付状态" multiple clearable>
        <el-option v-for="item in payStates" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" placeholder="审核状态" multiple clearable>
        <el-option v-for="item in auditStates" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.expiration_time" placeholder="到期时间" clearable>
        <el-option v-for="item in inTimeState" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.rent_states" placeholder="长租状态" multiple clearable>
        <el-option v-for="item in rentStates" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.prk_rent_rule_types" placeholder="长租类型" multiple clearable>
        <el-option v-for="item in rentTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.open_sign" style="width: 100%" placeholder="续费类型">
        <el-option v-for="item in openSignState" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="申请开始时间"
        end-placeholder="申请结束时间"
        :shortcuts="shortcuts"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="SpaceRentApplySearch" setup>
import { dayjs } from 'element-plus';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import commonService from '@/service/common/CommonService';
import dictService from '@/service/system/DictService';
import ParkFindBack from '@/views/car/ParkFindBack.vue';
import { reactive, ref, onActivated } from 'vue';

const shortcuts = [
  {
    text: '今天',
    value: () => {
      return [dayjs().format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '近三天',
    value: () => {
      return [dayjs().subtract(2, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '近七天',
    value: () => {
      return [dayjs().subtract(6, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  }
];
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    plate_no: '',
    mbr_member_name: '',
    mbr_member_mobile: '',
    prk_rent_product_name: '',
    pay_states: [],
    rent_states: [],
    audit_states: [],
    prk_rent_rule_types: [],
    valid_start_time: '',
    valid_end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});
const openSignState = ref([]);
const productTypes = ref([]);
const payStates = ref([]);
const rentStates = ref([]);
const auditStates = ref([]);
const rentTypes = ref([]);
const inTimeState = ref([]);

const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param1 = [
    {
      enum_key: 'openSignState',
      enum_value: 'EnumOpenSignState'
    },
    {
      enum_key: 'rentStates',
      enum_value: 'EnumRentSpaceApplyRentState'
    }
  ];
  const param2 = [
    {
      enum_key: 'auditStates',
      enum_value: 'EnumAuditState'
    }
  ];
  const param3 = [
    {
      enum_key: 'payStates',
      enum_value: 'EnumPayState'
    }
  ];
  const param4 = [
    {
      enum_key: 'productTypes',
      enum_value: 'EnumRentProductType'
    }
  ];
  commonService.findEnums('park', param1).then((response) => {
    rentStates.value = response.data.rentStates;
    openSignState.value = response.data.openSignState;
  });
  commonService.findEnums('audit', param2).then((response) => {
    auditStates.value = response.data.auditStates;
  });
  commonService.findEnums('order', param3).then((response) => {
    payStates.value = response.data.payStates;
  });
  commonService.findEnums('park', param4).then((response) => {
    productTypes.value = response.data.productTypes;
  });
  // 长租类型
  dictService.getDictsList('LONG_RENT_TYPE').then((response) => {
    rentTypes.value = response;
  });
  // 到期时间类型
  dictService.getExpirationTime().then((response) => {
    inTimeState.value = response.data;
  });
};
const handleDataSearch = () => {
  if (form.dateRange?.length > 0) {
    form.queryParams.valid_start_time = form.dateRange[0];
    form.queryParams.valid_end_time = form.dateRange[1];
  } else {
    form.queryParams.valid_start_time = undefined;
    form.queryParams.valid_end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_name: '',
    plate_no: '',
    mbr_member_name: '',
    prk_rent_product_name: '',
    pay_states: [],
    rent_states: [],
    audit_states: [],
    prk_rent_rule_types: [],
    valid_start_time: '',
    valid_end_time: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  console.log(val[0].park_name);
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
