<template>
  <el-button link type="primary" @click="openDialog"> 续期 </el-button>
  <el-dialog append-to-body destroy-on-close v-model="reRentDialogVisible" width="60%" title="续期操作">
    <div class="form-title">
      <span class="title">客户资料</span>
      <span class="status">长租状态：{{ formData.rent_state_desc }}</span>
    </div>
    <el-form label-width="100px" :model="formData" class="grid-form">
      <el-form-item label="车主姓名" prop="mbr_member_name">
        <el-input v-model="formData.mbr_member_name" maxlength="30" disabled />
      </el-form-item>
      <el-form-item label="手机号" prop="mbr_member_mobile">
        <el-input v-model="formData.mbr_member_mobile" disabled />
      </el-form-item>
      <el-form-item label="车牌号" prop="plate_nos">
        <el-input v-model="formData.plate_nos" disabled />
      </el-form-item>
      <el-form-item label="用户身份" prop="user_identity_desc">
        <el-input v-model="formData.user_identity_desc" disabled />
      </el-form-item>
      <el-form-item label="申请资料" prop="audit_urls">
        <div style="display: flex; gap: 0px 10px; flex-wrap: wrap">
          <el-link v-for="(url, index) in formData.audit_urls" :key="index" type="primary" @click="exportAuditData(url.audit_data_url)">{{
            url.audit_data_name
          }}</el-link>
        </div>
      </el-form-item>
    </el-form>
    <div class="form-title">
      <span class="title">当前套餐</span>
      <span></span>
    </div>
    <el-form label-width="100px" :model="currentFormData" class="grid-form">
      <el-form-item label="停车场名称" prop="park_name">
        <el-input v-model="currentFormData.park_name" disabled />
      </el-form-item>
      <el-form-item label="规则名称" prop="prk_rent_rule_name">
        <el-input v-model="currentFormData.prk_rent_rule_name" disabled />
      </el-form-item>
      <el-form-item label="车位编号" prop="space_code">
        <el-input v-model="currentFormData.space_code" disabled />
      </el-form-item>
      <el-form-item label="产品类型" prop="prk_rent_product_name">
        <el-input v-model="currentFormData.prk_rent_product_name" disabled />
      </el-form-item>
      <el-form-item label="产品金额" prop="order_money">
        <el-input v-model="currentFormData.order_money" :disabled="true" />
      </el-form-item>
      <el-form-item label="有效期" prop="valid_start_time">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-date-picker
              v-model="currentFormData.valid_start_time"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始时间"
              disabled
            />
          </el-col>
          <el-col :span="12">
            <el-input v-model="currentFormData.valid_end_time.split(' ')[0]" style="width: 100%" disabled placeholder="结束时间" />
          </el-col>
        </el-row>
      </el-form-item>
      <template v-if="currentFormData.product_range">
        <el-form-item label="产品周期" prop="product_range">
          <el-radio-group v-if="[5, 6, 7].includes(currentFormData.product_type)" v-model="currentFormData.product_range" disabled>
            <el-radio :label="item.label" v-for="item in rentProductRanges" :key="item.label">{{ item.text }}</el-radio>
          </el-radio-group>
          <el-input v-else-if="currentFormData.product_type == 8" :model-value="currentFormData.product_range + '天'" :disabled="true" />
          <el-input v-else-if="currentFormData.product_type == 9" :model-value="currentFormData.product_range + '周'" :disabled="true" />
        </el-form-item>
        <el-form-item label="长租时段">
          <!-- <div class="rime-range-picker" v-if="currentFormData.product_type == 5">
            <el-time-picker v-model="currentFormData.rent_time.split('-')[0]" value-format="HH:mm:ss" disabled />
            <span>至</span>
            <el-time-picker v-model="currentFormData.rent_time.split('-')[1]" value-format="HH:mm:ss" disabled />
          </div>
          <div style="width: 100%" v-else-if="currentFormData.product_type == 8 || currentFormData.product_type == 9">
            <el-input :model-value="'全时段'" :disabled="true" />
          </div> -->
          <el-input v-if="currentFormData.product_type == 5" v-model="currentFormData.rent_time" :disabled="true" />
          <el-input v-else-if="currentFormData.product_type == 8 || currentFormData.product_type == 9" :model-value="'全时段'" :disabled="true" />
        </el-form-item>
      </template>
    </el-form>
    <div class="form-title">
      <span style="color: #409eff">拟续费套餐:</span>
    </div>
    <el-form ref="editValidTimeForm" label-width="100px" :rules="rules" :model="formData" class="grid-form">
      <el-form-item label="规则名称" prop="prk_rent_rule_id">
        <el-select v-model="formData.prk_rent_rule_id" placeholder="规则名称" :disabled="!formData.park_id" @change="handleRentRuleChange">
          <el-option v-for="item in rentRules" :key="item.id + 'prk_rent_rule_id'" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品类型" prop="prk_rent_product_id">
        <el-select v-model="formData.prk_rent_product_id" placeholder="产品类型">
          <el-option v-for="item in products" :key="item.id + 'prk_rent_product_id'" :label="item.type_desc" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="产品金额" prop="prk_rent_product_money">
        <el-input v-model="formData.prk_rent_product_money" :disabled="true" />
      </el-form-item>
      <el-form-item label="有效期" prop="valid_start_time">
        <el-row :gutter="10">
          <el-col :span="12">
            <el-date-picker
              v-model="formData.valid_start_time"
              type="date"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD HH:mm:ss"
              placeholder="选择开始时间"
              :disabled-date="disabledDateFn"
              @change="changeEndData"
            />
          </el-col>
          <el-col :span="12">
            <el-input v-model="formData.valid_end_time.split(' ')[0]" style="width: 100%" disabled placeholder="结束时间" />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="产品周期" prop="product_range" v-if="showItem.product_range">
        <el-radio-group v-if="[5, 6, 7].includes(formData.product_type)" v-model="formData.product_range" disabled>
          <el-radio :label="item.label" v-for="item in rentProductRanges" :key="item.label">{{ item.text }}</el-radio>
        </el-radio-group>
        <el-input v-else-if="formData.product_type == 8" :model-value="formData.product_range + '天'" :disabled="true" />
        <el-input v-else-if="formData.product_type == 9" :model-value="formData.product_range + '周'" :disabled="true" />
      </el-form-item>
      <el-form-item label="长租时段" v-if="showItem.start_time">
        <el-input v-if="formData.product_type == 5" v-model="formData.rent_time" :disabled="true" />
        <el-input v-else-if="formData.product_type == 8 || formData.product_type == 9" :model-value="'全时段'" :disabled="true" />
      </el-form-item>
      <el-form-item label="付款方式" prop="pay_method">
        <el-select v-model="formData.pay_method" placeholder="付款方式" style="width: 100%">
          <el-option v-for="item in paymentMethods" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="实收金额" prop="payed_money" v-if="formData.pay_method !== undefined && formData.pay_method !== 4">
        <el-input v-model="formData.payed_money" style="width: 100%" placeholder="请输入实际收费金额" />
      </el-form-item>
      <div v-if="formData.pay_method !== undefined && formData.pay_method !== 4">
        <div class="el-form-item is-required asterisk-left">
          <label class="el-form-item__label" style="width: 100px"> 缴费凭证 </label>
          <span class="el-form-item__sublabel">（上传支付或合同文件凭证，支持JPG、JPEG、PNG、PDF格式，单张限10M）</span>
        </div>
        <el-form-item prop="payed_voucher_url1">
          <el-upload
            :action="uploadUrl"
            :headers="headers"
            ref="payedVoucherUploadRef1"
            accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
            v-model:file-list="defaultPayedVoucherFileList1"
            :on-preview="onPreview"
            list-type="picture-card"
            :before-upload="beforeUpload"
            :on-success="onSuccessUpload"
            :on-exceed="handlePayedVoucherUploadExceed1"
            :multiple="true"
          >
            <div class="upload-card">
              <el-icon><Plus /></el-icon>
              <div style="font-size: 12px">上传缴费凭证</div>
            </div>
            <template #file="{ file }">
              <div>
                <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="onPreview(file)">
                    <el-icon><zoom-in /></el-icon>
                  </span>
                  <span class="el-upload-list__item-delete" @click="handleRemove(file, defaultPayedVoucherFileList1)">
                    <el-icon><Delete /></el-icon>
                  </span>
                </span>
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </div>
      <el-form-item label="付款备注" prop="payed_memo" v-if="formData.pay_method !== undefined && formData.pay_method !== 4">
        <el-input
          v-model="formData.payed_memo"
          type="textarea"
          style="width: 100%"
          placeholder="请备注说明情况，限100字以内"
          maxlength="100"
          :rows="3"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <!-- <span class="warning">温馨提示：续期申请提交后，需经审核通过方会生效</span> -->
        <span></span>
        <div>
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        </div>
      </div>
    </template>
  </el-dialog>
  <el-dialog append-to-body destroy-on-close v-model="imgDialogVisible" :title="imgDialogTitle" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script setup>
import { computed, ref, reactive, watch } from 'vue';
import { getToken } from '@/utils/common';
import { ElMessage, dayjs } from 'element-plus';
import commonService from '@/service/common/CommonService';
import parkSpaceService from '@/service/park/ParkSpaceService';
import longRentRuleService from '@/service/park/LongRentRuleService';
import spaceRentApplyService from '@/service/car/SpaceRentApplyService';
import { rentProductRanges } from '@/views/park/longRentRule/enums';

const props = defineProps({
  user_info: {
    type: Object,
    default: () => {}
  }
});
const emits = defineEmits(['submit']);

const reRentDialogVisible = ref(false);
const openDialog = () => {
  spaceRentApplyService.whetherOrNotRenew(props.user_info.id, props.user_info.park_id).then((response) => {
    if (response.data.type == 0) {
      reRentDialogVisible.value = true;
    } else if (response.data.type == 1) {
      ElMessage.error('该用户已续过费，请勿重复申请');
    } else if (response.data.type == 2) {
      ElMessage.error('该用户续费申请已在流程中，请勿重复申请');
    }
  });
  // reRentDialogVisible.value = true;
};
const closeDialog = () => {
  reRentDialogVisible.value = false;
  emits('cancel');
};
const editValidTimeForm = ref();
const loading = ref(false);
// 表单数据
const formData = ref({
  park_id: '',
  park_name: '',
  prk_rent_rule_id: '',
  prk_rent_product_id: '',
  prk_rent_product_money: '',
  space_id: '',
  valid_start_time: '',
  valid_end_time: '',
  mbr_member_id: '',
  mbr_member_name: '',
  mbr_member_mobile: '',
  mbr_member_nickname: '',
  audit_url: '',
  payed_voucher_url: '',
  payed_voucher_url1: '',
  plate_nos: '',
  channel: 1
});
// 当前套餐数据
const currentFormData = ref({
  park_id: '',
  valid_end_time: '',
  order_money: 0
});
// 规则名称
const rentRules = ref([]);
// 产品列表
const products = ref([]);
// 车位列表
const parkSpaces = ref([]);
// 付款方式
const paymentMethods = ref([]);
// 缴费凭证组件实体
const payedVoucherUploadRef1 = ref();
// 默认缴费凭证
const defaultPayedVoucherFileList1 = ref([]);
const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadAuditData');
const headers = reactive({
  Authorization: getToken()
});
const initFormData = () => {
  // 初始化获取付款方式字典
  commonService.findEnums('order', [{ enum_key: 'rentPayMethod', enum_value: 'EnumRentPayMethod' }]).then((response) => {
    paymentMethods.value = response.data.rentPayMethod;
  });
  fetchCurrentRentData(props.user_info.id);
};

/**
 * @description: 点击文件列表中已上传的文件时的钩子
 */
const imgDialogTitle = ref('');
const imgDialogVisible = ref(false);
const dialogImageUrl = ref('');
const onPreview = (file) => {
  imgDialogVisible.value = true;
  imgDialogTitle.value = '图片预览';
  dialogImageUrl.value = file.url || file.response.data.audit_data_url;
};

// 删除已上传图片
const handleRemove = (file, fileList) => {
  const index = fileList.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.splice(index, 1);
  }
};
/**
 * @description: 上传文件前回调
 * @param {*} file
 * @return {*}
 */
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 10;
  if (!isLt25M) {
    ElMessage.warning('上传文件大小不能超过 10MB!');
    return false;
  }
  if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
    ElMessage.warning('上传文件格式错误!');
    return false;
  }
};

const handlePayedVoucherUploadExceed1 = (files) => {
  payedVoucherUploadRef1.value.clearFiles();
  payedVoucherUploadRef1.value.handleStart(files[0]);
  payedVoucherUploadRef1.value.submit();
};

const onSuccessUpload = (response, uploadFile) => {
  if (response.success == true) {
    uploadFile.url = response.data.audit_data_url;
    ElMessage.success(response.message);
  } else {
    ElMessage.error(response.message);
  }
};

const disabledDateFn = (date) => {
  if (currentFormData.value.rent_state === 1) {
    return date.getTime() < new Date(currentFormData.value.valid_end_time).getTime();
  } else {
    return date.getTime() < dayjs().startOf('day');
  }
};

/**
 * @description: 提交表单数据
 * @param {*}
 * @return {*}
 */
const handleSubmit = () => {
  const getFileExtension = (filename) => {
    const match = filename.match(/(\.[^.]+)$/);
    return match ? match[1] : '';
  };
  // 处理缴费凭证
  let defaultPayedVoucherFileList1copy = JSON.parse(JSON.stringify(defaultPayedVoucherFileList1.value));
  formData.value.payed_voucher_url1 = '';
  if (defaultPayedVoucherFileList1copy.length > 0) {
    formData.value.payed_voucher_url1 = 'true';
    defaultPayedVoucherFileList1copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('缴费凭证') != -1) {
        return;
      }
      item.name = '缴费凭证' + extension;
    });
    const auditPayedVoucherUrl = defaultPayedVoucherFileList1copy.map((item) => {
      return {
        audit_data_name: item.name,
        audit_data_url: item.response?.data.audit_data_url || item.url
      };
    });
    if (auditPayedVoucherUrl.length > 0) {
      formData.value.payed_voucher_url = JSON.stringify(auditPayedVoucherUrl);
    } else {
      formData.value.payed_voucher_url = undefined;
    }
  }

  editValidTimeForm.value.validate().then(async () => {
    loading.value = true;
    try {
      const submitFunc = spaceRentApplyService.webRenewRentSpace;
      const { success, message, detail_message, data } = await submitFunc({
        ...formData.value,
        plate_nos: formData.value.plate_nos?.split(','),
        rent_id: props.user_info.id
      });
      if (success) {
        if (formData.value.pay_method == 4) {
          ElMessage.success('保存成功');
          emits('submit');
          reRentDialogVisible.value = false;
          payedVoucherUploadRef1.value?.clearFiles();
        } else {
          const response = await spaceRentApplyService.submitAuditRenewRentSpaceApply(data.rent_space_apply_id);
          if (response.success) {
            ElMessage.success(response.message);
            emits('submit');
            reRentDialogVisible.value = false;
            payedVoucherUploadRef1.value?.clearFiles();
          } else {
            ElMessage.error(response.detail_message || response.message);
          }
        }
      } else {
        ElMessage.error(detail_message || message);
      }
    } catch (error) {
      console.error('保存失败', error);
    } finally {
      loading.value = false;
    }
  });
};

watch(products, () => {
  if (products.value && products.value.length > 0) {
    formData.value.prk_rent_product_id = products.value[0].id;
    formData.value.product_type = products.value[0].type;
    handleProductChange(formData.value.prk_rent_product_id);
  }
});

// 表单字段是否展示
const showItem = computed(() => {
  const selectedProductType = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  })?.type;
  const commonSet = [5, 6, 7, 8, 9].includes(selectedProductType);
  return {
    product_range: commonSet,
    start_time: selectedProductType === 5 || selectedProductType === 8 || selectedProductType === 9,
    time_type: commonSet,
    week_day: selectedProductType === 7 && formData.value?.week_day?.length,
    days: selectedProductType === 7 && formData.value?.days?.length,
    month_range: selectedProductType === 7
  };
});

/**
 * @description 获取车位列表
 * @param {*} park_id 停车场id
 */
const fetchParkSpaceList = (park_id) => {
  parkSpaces.value = [];
  parkSpaceService.listAvailableLongRentSpace(park_id).then((response) => {
    if (response.success === true) {
      parkSpaces.value = response.data;
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 获取长租规则列表
 * @param {*} park_id 停车场id
 */
const fetchRentRuleList = (park_id) => {
  rentRules.value = [];
  const params = {
    parkId: park_id,
    userIdentity: props.user_info.user_identity
  };
  longRentRuleService.listRentRule(params).then((response) => {
    if (response.success === true) {
      rentRules.value = response.data;
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 获取用户当前套餐
 * @param {*} id 停车场当前条目id
 */
const fetchCurrentRentData = (id) => {
  const userInfo = props.user_info;
  spaceRentApplyService.getNewRentProductDetails(id).then((response) => {
    if (response.success === true) {
      currentFormData.value = response.data;
      fetchParkSpaceList(currentFormData.value.park_id);
      fetchRentRuleList(currentFormData.value.park_id);
      formData.value = {
        ...formData.value,
        park_id: currentFormData.value.park_id,
        mbr_member_id: userInfo.mbr_member_id,
        mbr_member_name: userInfo.mbr_member_name,
        mbr_member_mobile: userInfo.mbr_member_mobile,
        mbr_member_nickname: userInfo.mbr_member_nickname,
        space_id: userInfo.space_id,
        plate_nos: userInfo.plate_nos,
        audit_url: userInfo.audit_url,
        audit_urls: userInfo.audit_urls,
        rent_state_desc: currentFormData.value.rent_state_desc,
        rent_state: currentFormData.value.rent_state,
        user_identity_desc: currentFormData.value.user_identity_desc
      };
      defaultPayedVoucherFileList1.value = [];
      if (currentFormData.value.rent_state === 1) {
        console.log('续费状态1');
        formData.value.valid_start_time = dayjs(currentFormData.value.valid_end_time).add(1, 'day').startOf('day').format('YYYY-MM-DD HH:mm:ss');
      } else {
        console.log('续费状态2');
        formData.value.valid_start_time = dayjs().format('YYYY-MM-DD HH:mm:ss');
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};

/**
 * @description 获取产品类型
 * @param {*} productparams
 */
const fetchProductList = (productparams) => {
  products.value = [];
  longRentRuleService.listProduct(productparams).then((response) => {
    if (response.success === true) {
      products.value = response.data;
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 选择规则名称
 */
const handleRentRuleChange = (val) => {
  const productparams = {
    rule_id: val,
    park_id: formData.value.park_id
  };
  formData.value.prk_rent_product_id = '';
  fetchProductList(productparams);
};
/**
 * @description 选择产品事件
 * @param {*} val 选择产品id
 */
const handleProductChange = (val) => {
  const selectedProduct = products.value.find((item) => {
    return item.id == val;
  });
  if (selectedProduct) {
    formData.value.prk_rent_product_money = selectedProduct.money;
    formData.value.product_range = selectedProduct.product_range;
    formData.value.rent_time = selectedProduct.rent_time;
    // formData.value.start_time = selectedProduct.start_day;
    // formData.value.end_time = selectedProduct.end_day;
    formData.value.time_type = selectedProduct.time_type;
    formData.value.week_day = selectedProduct.week_day;
    formData.value.days = selectedProduct.days;
    formData.value.month_range = selectedProduct.month_range;
    formData.value.prk_rent_product_detail_id = formData.value.prk_rent_product_id;
    changeEndData(formData.value.valid_start_time);
  }
};
/**
 * @description 改变结束时间
 * @param {*} val 起始时间
 */
const changeEndData = (val) => {
  if (!val) return;
  const selectedProduct = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  });
  if (!selectedProduct) {
    formData.value.valid_end_time = formData.value.valid_start_time;
    return;
  }
  if (selectedProduct.type === 8) {
    // 判断日卡的情况
    formData.value.valid_end_time = dayjs(val)
      .add(selectedProduct?.product_range - 1, 'day')
      .format('YYYY-MM-DD HH:mm:ss');
  } else if (selectedProduct.type === 9) {
    // 判断周卡的情况
    formData.value.valid_end_time = dayjs(val).add(selectedProduct?.product_range, 'week').subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
  } else {
    const timeRelaseBase = {
      1: 1,
      2: 3,
      3: 6,
      4: 12,
      0: 0
    };
    const flag = selectedProduct.type <= 4 ? selectedProduct.type : selectedProduct?.product_range || 0;
    formData.value.valid_end_time = dayjs(formData.value.valid_start_time).add(timeRelaseBase[flag], 'month').format('YYYY-MM-DD HH:mm:ss');
    if (dayjs(formData.value.valid_start_time).date() <= dayjs(formData.value.valid_end_time).date()) {
      formData.value.valid_end_time = dayjs(formData.value.valid_end_time).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
    }
  }
};

const exportAuditData = (val) => {
  window.open(val, '_blank');
};
watch(reRentDialogVisible, (val) => {
  if (val) {
    initFormData();
  } else {
    formData.value = {
      park_id: '',
      park_name: '',
      prk_rent_rule_id: '',
      prk_rent_product_id: '',
      prk_rent_product_money: '',
      space_id: '',
      valid_start_time: '',
      valid_end_time: '',
      mbr_member_id: '',
      mbr_member_name: '',
      mbr_member_mobile: '',
      mbr_member_nickname: '',
      audit_url: '',
      payed_voucher_url: '',
      payed_voucher_url1: '',
      plate_nos: '',
      channel: 1
    };
    defaultPayedVoucherFileList1.value = [];
  }
});

const validatePayedMoney = (rule, value, callback) => {
  const reg = /^[0-9]+.?[0-9]*$/;
  if (!reg.test(value)) {
    callback(new Error('请输入数字'));
  } else if (value < 0 || value > formData.value.prk_rent_product_money) {
    callback(new Error('实收金额数不能大于套餐的产品金额数且不能小于0'));
  } else {
    callback();
  }
};

const rules = {
  prk_rent_rule_id: [
    {
      required: true,
      message: '请选择长租规则',
      trigger: 'change'
    }
  ],
  prk_rent_product_id: [
    {
      required: true,
      message: '请选择产品名称',
      trigger: 'change'
    }
  ],
  valid_start_time: [
    {
      required: true,
      message: '请选择有效期',
      trigger: 'blur'
    }
  ],
  pay_method: [
    {
      required: true,
      message: '请选择付款方式',
      trigger: 'blur'
    }
  ],
  payed_money: [
    {
      required: true,
      message: '请输入实际收费金额',
      trigger: 'blur'
    },
    {
      trigger: 'blur',
      validator: validatePayedMoney
    }
  ],
  payed_voucher_url: [
    {
      required: false,
      message: '',
      trigger: ''
    }
  ],
  payed_voucher_url1: [
    {
      required: true,
      message: '请上传缴费凭证',
      trigger: 'blur'
    }
  ]
};
</script>

<style lang="scss" scoped>
.grid-form {
  display: grid;
  grid-template-columns: 1fr 1fr;

  .row {
    grid-column-start: 1;
    grid-column-end: 3;
  }
}

.form-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    font-size: 16px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 16px;
      margin-right: 4px;
      background-color: #409eff;
      vertical-align: sub;
    }
  }
  .status {
    color: #ffffff;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #409eff;
  }
}
.el-form-item__sublabel {
  color: #f56c6c;
  font-size: 12px;
  align-self: center;
}
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 80px;
  // width: 148px;
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;
}
.dialog-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .warning {
    color: #f56c6c;
    font-size: 12px;
  }
}
:deep(.el-form-item__content) {
  align-items: flex-start;
}
.rime-range-picker {
  width: 360px;
  display: flex;
  align-items: center;
  & > span {
    margin: 0 10px;
  }
}
</style>
