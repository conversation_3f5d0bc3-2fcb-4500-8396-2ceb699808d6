/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询月报表通过金额
export const pagingMonthReportByMoney = (data) => {
  return $({
    url: '/console/park/finance/monthReport/pagingMonthReports',
    method: 'post',
    data
  });
};

// 分页查询月报表通过次数
export const pagingMonthReportByTimes = (data) => {
  return $({
    url: '/console/park/finance/monthReport/pagingMonthReports',
    method: 'post',
    data
  });
};

// 卡片数据查询
export const searchBtnData = (data) => {
  return $({
    url: '/console/park/finance/monthReport/getTotal',
    method: 'post',
    data
  });
};

// 导出月报表（金额）
export const exportMonthReportsByMoney = (data) => {
  return $({
    url: '/console/park/finance/monthReport/exportMonthReports',
    method: 'post',
    data
  });
};

// 导出月报表（次数）
export const exportMonthReportsByCount = (data) => {
  return $({
    url: '/console/park/finance/monthReport/exportMonthReportCnt',
    method: 'post',
    data
  });
};
