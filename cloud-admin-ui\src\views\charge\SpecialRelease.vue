<template>
  <div class="container">
    <special-release-search @form-search="searchSpecialReleaseList" @reset="resetParamsAndData" />
    <special-release-table ref="table" />
  </div>
</template>

<script setup name="SpecialRelease">
import SpecialReleaseSearch from './specialRelease/SpecialReleaseSearch.vue';
import SpecialReleaseTable from './specialRelease/SpecialReleaseTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageSpecialRelease = (queryParams) => {
  table.value.getList(queryParams);
};

const searchSpecialReleaseList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageSpecialRelease
});
</script>
