/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 优免统计表格数据查询
export const pagingCouponStats = (data) => {
  return $({
    url: '/console/coupon/stat/pagingCouponStats',
    method: 'post',
    data
  });
};

// 导出优免卷核销报表
export const exportReports = (data) => {
  return $({
    url: '/console/coupon/stat/exportWriteOffCouponStats',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};
// 分页查询优惠券发放详情
export const pagingCouponGrant = (data) => {
  return $({
    url: '/console/coupon/stat/pagingCouponGrant',
    method: 'post',
    data
  });
};
// 导出优惠券发放详情
export const exportCouponGrant = (data) => {
  return $({
    url: '/console/coupon/stat/exportCouponGrant',
    method: 'post',
    data
  });
};
//分页查询优免劵售卖数据
export const maidata = (data) => {
  return $.post("/console/coupon/stat/pagingSaleCouponStatsList", data)
}
//导出优免卷售卖数据
export const exportSale = (data) => {
  return $.post("/console/coupon/stat/exportSaleCouponStatsList", data)
}
//获取优免劵核销数据
export const getWriteOffData = (data) => {
  return $.post("/console/coupon/stat/pagingWriteOffCouponStatsList", data)
}