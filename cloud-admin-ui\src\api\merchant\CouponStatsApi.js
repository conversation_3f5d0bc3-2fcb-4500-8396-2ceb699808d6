/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 优免统计表格数据查询
export const pagingCouponStats = (data) => {
  return $({
    url: '/console/coupon/stat/pagingCouponStats',
    method: 'post',
    data
  });
};

// 导出报表
export const exportReports = (data) => {
  return $({
    url: '/console/coupon/stat/exportCouponStats',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};
// 分页查询优惠券发放详情
export const pagingCouponGrant = (data) => {
  return $({
    url: '/console/coupon/stat/pagingCouponGrant',
    method: 'post',
    data
  });
};
// 导出优惠券发放详情
export const exportCouponGrant = (data) => {
  return $({
    url: '/console/coupon/stat/exportCouponGrant',
    method: 'post',
    data
  });
};
