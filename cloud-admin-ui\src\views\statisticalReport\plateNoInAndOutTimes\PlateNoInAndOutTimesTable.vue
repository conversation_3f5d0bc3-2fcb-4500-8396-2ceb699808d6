<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="plateNoInAndOutTimesService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="月份" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.shift_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.should_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="组织架构" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.payed_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="省市区" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.payed_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="有车型数据" align="center">
          <el-table-column label="车标" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ali_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车型" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车牌数" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ali_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排名" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="无车型数据" align="center">
          <el-table-column label="车标" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.cash_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车牌数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.special_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="排名" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.special_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="PlateNoInAndOutTimesTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import plateNoInAndOutTimesService from '@/service/statisticalReport/PlateNoInAndOutTimesService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  plateNoInAndOutTimesService.pagingPlateNoInAndOutTimes(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
