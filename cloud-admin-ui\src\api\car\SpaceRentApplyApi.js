/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询长租车申请
export const pagingRentSpaceApplies = (data) => {
  return $({
    url: '/console/park/rent/space/apply/pagingRentSpaceApplies',
    method: 'post',
    data
  });
};

// 新增长租车申请
export const createRentSpaceApply = (data) => {
  return $({
    url: '/console/park/rent/space/apply/createRentSpaceApply',
    method: 'post',
    data
  });
};

// 长租车申请续期前置判断
export const whetherOrNotRenew = (rentApplyId, parkId) => {
  return $({
    url: `/console/park/rent/space/apply/whetherOrNotRenew?rentApplyId=${rentApplyId}&parkId=${parkId}`,
    method: 'get'
  });
};

// 长租车申请续期
export const webRenewRentSpace = (data) => {
  return $({
    url: '/console/park/rent/space/apply/webRenewRentSpace',
    method: 'post',
    data
  });
};

// 提交长租车申请续期申请
export const submitAuditRenewRentSpaceApply = (data) => {
  return $({
    url: `/console/park/rent/space/apply/submitAuditRenewRentSpaceApply/${data}`,
    method: 'post',
    data
  });
};
// 撤回长租车申请续期申请
export const revokeAuditRenewRentSpaceApply = (data) => {
  return $({
    url: `/console/park/rent/space/apply/revokeAuditRenewRentSpaceApply/${data}`,
    method: 'post',
    data
  });
};

// 修改长租车申请
export const updateRentSpaceApply = (data) => {
  return $({
    url: '/console/park/rent/space/apply/updateRentSpaceApply',
    method: 'post',
    data
  });
};

// 获取用户当前套餐
export const getNewRentProductDetails = (data) => {
  return $({
    url: '/console/park/rent/space/apply/getNewRentProductDetails?id=' + data,
    method: 'post'
  });
};

// 删除长租车申请
export const deleteRentSpaceApply = (data) => {
  return $({
    url: '/console/park/rent/space/apply/deleteRentSpaceApply',
    method: 'post',
    data
  });
};

// 编辑车主信息
export const updateRentSpaceMember = (data) => {
  return $({
    url: '/console/park/rent/space/apply/updateRentSpaceMember',
    method: 'post',
    data
  });
};

// 编辑有效期
export const updateRentSpaceValidityTime = (data) => {
  return $({
    url: '/console/park/rent/space/apply/updateRentSpaceValidityTime',
    method: 'post',
    data
  });
};

// 提交审核
export const submitAuditRentSpaceApply = (id) => {
  return $({
    url: '/console/park/rent/space/apply/submitAuditRentSpaceApply/' + id,
    method: 'post'
  });
};

// 撤回
export const revokeAuditRentSpaceApply = (id) => {
  return $({
    url: '/console/park/rent/space/apply/revokeAuditRentSpaceApply/' + id,
    method: 'post'
  });
};

// 导出
export const exportRentSpaceApplies = (data) => {
  return $({
    url: '/console/park/rent/space/apply/exportRentSpaceApplies',
    method: 'post',
    data
  });
};

//  驳回
export const rejectRentSpaceApplies = (data) => {
  return $({
    url: '/console/park/rent/space/apply/rentPropertyReject',
    method: 'post',
    data
  });
};