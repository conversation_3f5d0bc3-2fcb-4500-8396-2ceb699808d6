import * as couponStats from '@/api/merchant/CouponStatsApi';

/**
 * 优免统计
 */
export default {
  /**
   *优免统计表格数据查询
   */
  pagingCouponStats(data) {
    return new Promise((resolve, reject) => {
      try {
        couponStats.pagingCouponStats(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出报表
   */
  exportReports(data) {
    return new Promise((resolve, reject) => {
      try {
        couponStats.exportReports(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *分页查询优惠券发放详情
   */
  pagingCouponGrant(data) {
    return new Promise((resolve, reject) => {
      try {
        couponStats.pagingCouponGrant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
