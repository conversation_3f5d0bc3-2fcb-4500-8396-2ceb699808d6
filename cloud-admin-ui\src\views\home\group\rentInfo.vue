<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-03-15 18:40:03
 * @LastEditors: 达万安 段世煜
 * @Description: 车场排名统计
 * @FilePath: \cloud-admin-ui\src\views\home\group\rentInfo.vue
-->
<template>
  <warp-card size="mini" title="临停/长租出入车次统计">
    <tab-button :options="tabOptions" v-model="activeKey" @change="handleTabChange" />
    <div class="main">
      <div class="chart">
        <pie-chart ref="circleChartRef" :radius="['35%', '48%']" circle />
      </div>
      <div class="chart">
        <bar-chart ref="barChartRef" :single="true" :show-label="false" :yName="false" :gridbottom="6" :legendBottom="'3%'" />
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import { reactive, ref } from 'vue';

import { carInOutFlowByInterval } from '@/api/home/<USER>';

import barChart from './components/barChart.vue';
import pieChart from './components/pieChart.vue';
import tabButton from './components/tabButton.vue';
import warpCard from './components/warpCard.vue';

// tab数据源
const tabOptions = [
  {
    value: 0,
    label: '入场车次'
  },
  {
    value: 1,
    label: '出场车次'
  }
];
// 当前激活tab
const activeKey = ref(0);
const globalParams = ref({});
/**
 * @description: tab切换 出场/入场
 * @param {*} value
 */
const handleTabChange = (value) => {
  fetchData();
};

const circleChartRef = ref(null);
const barChartRef = ref(null);
const data = reactive([
  {
    name: '临停（次）',
    value: 0
  },
  {
    name: '长租（次）',
    value: 0
  }
]);
/**
 * @description: 请求图表数据
 */
const fetchData = async (val) => {
  const yAxis = data.map((item) => item.name);
  let xName = [];
  if (val) globalParams.value = val;
  try {
    const { data: resData } = await carInOutFlowByInterval(globalParams.value);
    xName = resData.map((item) => item.time);
    if (activeKey.value == 0) {
      data[0].value = resData.map((item) => item.parking_car_in || 0);
      data[1].value = resData.map((item) => item.rent_car_in || 0);
      return;
    }
    data[0].value = resData.map((item) => item.parking_car_out || 0);
    data[1].value = resData.map((item) => item.rent_car_out || 0);
  } finally {
    // 累加饼图数据
    const circleData = data.map((item) => {
      return {
        ...item,
        value: item.value.reduce((pre, cur) => pre + cur, 0)
      };
    });
    circleChartRef.value && circleChartRef.value.setData(circleData);
    barChartRef.value &&
      barChartRef.value.setData(
        data,
        xName,
        // data[1].value.map((item) => {
        //   return item.name;
        // }),
        yAxis
      );
  }
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: calc(100% - 30px);
  display: flex;
  .chart {
    width: 50%;
    height: 100%;
  }
}
</style>
