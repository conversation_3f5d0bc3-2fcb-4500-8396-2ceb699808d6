<template>
  <el-card class="table" shadow="never">
    <el-radio-group v-model="tabPosition">
      <el-radio-button value="列表">列表</el-radio-button>
      <el-radio-button value="图表">图表</el-radio-button>
    </el-radio-group>

    <div v-if="tabPosition == '列表'" ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <DownloadButton btnType="default" :exportFunc="timedAccessService.exportData"
            :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
          </DownloadButton>
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="大区" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.region_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="城市分公司" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在省份" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.province_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在城市" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.city_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="所在区域" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.district_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="00:00-01:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.zero_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.zero_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="01:00-02:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.one_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.one_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="02:00-03:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.two_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.two_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="03:00-04:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.three_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.three_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="04:00-05:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.four_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.four_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="05:00-06:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.five_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.five_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="06:00-07:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.six_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.six_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="07:00-08:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.seven_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.seven_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="08:00-09:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eight_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eight_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="09:00-10:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.nine_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.nine_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="10:00-11:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ten_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.ten_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="11:00-12:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eleven_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eleven_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="12:00-13:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twelve_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twelve_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="13:00-14:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.thirteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.thirteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="14:00-15:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.fourteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fourteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="15:00-16:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.fifteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.fifteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="16:00-17:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.sixteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.sixteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="17:00-18:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.seventeen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.seventeen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="18:00-19:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.eighteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.eighteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="19:00-20:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.nineteen_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.nineteen_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="20:00-21:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="21:00-22:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_one_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_one_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="22:00-23:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_two_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_two_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="23:00-24:00" align="center">
          <el-table-column label="进" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.twenty_three_hour_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.twenty_three_hour_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar :tableData='tableData' :title="data.queryParams.park_name"></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%;height: 600px;background: #fff;line-height: 600px;text-align: center;">
        请选择停车场进行统计
      </div>
    </div>
  </el-card>
</template>

<script name="TimedAccessTable" setup>
import DownloadButton from '@/components/DownloadButton.vue';
import timedAccessService from '@/service/statisticalReport/TimedAccessService';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import echartbar from './barChart.vue';
const tableData = ref([]);
const loading = ref(false);
const tabPosition = ref('列表')
const data = reactive({
  queryParams: {}
});
const parkId = ref(null);
// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  timedAccessService.pagingTimedAccess(params).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id;
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell>.cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
