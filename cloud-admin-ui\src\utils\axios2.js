import { useUser } from '@/stores/user';
import { useDuty } from '@/stores/duty';
import axios from 'axios';
import { ElMessage, ElNotification } from 'element-plus';
import router from '../router';
import { getToken } from './common';

const $ = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
  timeout: 1000 * 60,
  headers: {
    'Content-Type': 'application/json;charset=UTF-8',
    'X-Requested-With': 'XMLHttpRequest'
  },
  withCredentials: true,
  responseType: 'json'
});

// 请求拦截
$.interceptors.request.use(
  (config) => {
    if (!config.headers?.Authorization) {
      if (config.headers && getToken()) {
        config.headers.Authorization = getToken();
      } else {
        router.push('/login');
      }
    }

    // 特殊处理文件上传请求
    if (config.data instanceof FormData) {
      config.headers['Content-Type'] = 'multipart/form-data';
    }

    return config;
  },
  (error) => {
    const errorInfo = error.response;

    if (errorInfo) {
      error = errorInfo.data;
      const code = errorInfo.code;

      router.push({
        path: `/error/${code}`
      });
    }

    return Promise.reject(error);
  }
);

// 响应拦截
$.interceptors.response.use(
  (response) => {
    let data;
    if (response.data && response.data.success === false) {
      const error = {
        response: {
          status: response.data.code,
          data: response.data.message,
          detail_message: response.data.detail_message
        },
        message: response.data.message
      };
      httpErrorStatusHandle(error);
      return Promise.reject(error);
    }

    if (response.data === undefined) {
      data = JSON.parse(response.request.responseText);
    } else if (response.headers['content-disposition'] && response.headers['content-disposition'].includes('attachment')) {
      data = response;
    } else {
      data = response.data;
    }

    return data;
  },
  (error) => {
    // 处理错误状态码
    httpErrorStatusHandle(error);
    return Promise.reject(error);
  }
);

/**
 * 自定义文件上传方法
 * @param {string} url 请求地址
 * @param {FormData} formData FormData对象
 * @param {Object} config 额外配置
 * @returns {Promise} 返回Promise对象
 */
export const uploadCustom = (url, formData, config = {}) => {
  // 合并headers，确保Content-Type正确
  const headers = {
    'Authorization': getToken(),
    ...config.headers
  };

  return $({
    url: url,
    method: 'post',
    data: formData,
    headers: headers,
    onUploadProgress: config.onUploadProgress || (() => { }),
    ...config
  });
};

/**
 * 处理错误状态码
 * @param {*} error
 * @returns
 */
function httpErrorStatusHandle(error) {
  // 处理被取消的请求
  if (axios.isCancel(error)) {
    return console.error('因请求重复被自动取消：' + error.message);
  }

  let title = '对不起,发生了错误,请联系管理员或稍后再试';
  let message = undefined;

  if (error && error.response) {
    switch (error.response.status) {
      case 302: title = '接口重定向！'; break;
      case 400: title = '参数不正确！'; break;
      case 401: title = '您未登录系统，或者登陆已经超时，请先登录！'; break;
      case 403: title = '您没有权限操作！'; break;
      case 404:
        title = '请求地址不存在！';
        message = error.response.config.url;
        break;
      case 408: title = '请求超时！'; break;
      case 409: title = '系统已存在相同数据！'; break;
      case 500: title = '服务器内部错误！'; break;
      case 501: title = '服务未实现！'; break;
      case 502:
        title = '网关错误！';
        message = '网关暂时无法访问，请稍后再试';
        break;
      case 503: title = '服务不可用！'; break;
      case 504: title = '服务暂时无法访问，请稍后再试'; break;
      case 505: title = 'Http 版本不受支持！'; break;
      default: title = '系统异常，请联系管理员！'; break;
    }
  }

  if (error.message.includes('timeout')) {
    message = '网络请求超时！';
  } else if (error.message.includes('Network')) {
    message = window.navigator.onLine ? '服务端异常！' : '网络异常！';
  } else if (!message) {
    message = error.response?.data || '未知错误';
  }

  if (error.response?.status === 401) {
    if (useUser().token === '') {
      return;
    }

    ElMessage({
      message: message ? message : title,
      type: 'error'
    });

    useUser().removeToken();
    useDuty().removeToken();
    router.push('/login');
  } else if (error.response?.status === 403) {
    ElMessage({
      message: message ? message : title,
      type: 'error'
    });
  } else {
    ElNotification({
      title: title,
      message: message,
      type: 'error'
    });
  }
}

// 导出默认实例和其他方法
export default uploadCustom