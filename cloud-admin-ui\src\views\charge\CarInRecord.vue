<template>
  <div class="container">
    <car-in-record-search @form-search="searchCarInRecordList" @reset="resetParamsAndData" />
    <car-in-record-table ref="table" />
  </div>
</template>

<script setup name="CarInRecord">
import CarInRecordSearch from './carInRecord/CarInRecordSearch.vue';
import CarInRecordTable from './carInRecord/CarInRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageCarInRecord = (queryParams) => {
  table.value.getList(queryParams);
};

const searchCarInRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageCarInRecord
});
</script>
