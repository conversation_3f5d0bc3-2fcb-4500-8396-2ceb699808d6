export const truncateDecimal = (num, precision = 3) => {
  if (num === null || num === undefined) {
    return num;
  }
  // 将数字转换为字符串
  const numStr = String(num);
  // 使用正则表达式查找小数点后是否有超过precision位的数字
  const regex = new RegExp(`(\\.\\d{${precision + 1},})$`);
  // 如果匹配，则截取到小数点后precision位
  if (regex.test(numStr)) {
    return Number(numStr).toFixed(precision);
  }
  // 如果没有匹配，则返回原数字
  return num;
};

export const saveToFile = (data, name) => {
  const url = window.URL.createObjectURL(new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
  const link = document.createElement('a');
  link.style.display = 'none';
  link.href = url;
  link.setAttribute('download', name);
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};
