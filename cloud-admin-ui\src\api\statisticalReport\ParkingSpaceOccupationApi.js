/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询分时段进出
export const getPlaceOccupyTablePage = (data) => {
  return $({
    url: '/console/statistics/park/place/occupy/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/park/place/occupy/exportByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportDataSummary = (data) => {
  return $({
    url: '/console/statistics/park/place/occupy/exportSummaryByPeriod',
    method: 'post',
    data
  })
}
