/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询分时段进出
export const getPlaceOccupyTablePage = (data) => {
  return $({
    url: '/console/statistics/park/place/occupy/getPlaceOccupyTablePage',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/park/place/occupy/exportPlaceOccupyExcel',
    method: 'post',
    data
  });
};
