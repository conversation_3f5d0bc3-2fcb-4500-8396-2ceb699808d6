<template>
  <div>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
      <form-search-item>
        <el-select v-model="queryParams.module_type" placeholder="功能模块" multiple>
          <el-option v-for="item in moduleTypes" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </form-search-item>
      <form-search-item>
        <ClearableChargeInput v-model="queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
      </form-search-item>
      <form-search-item>
        <el-select v-model="queryParams.operation_type" placeholder="操作类型" multiple>
          <el-option v-for="item in operationTypes" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </form-search-item>
      <form-search-item>
        <el-date-picker
          style="width: 100%"
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="操作开始时间"
          end-placeholder="操作结束时间"
          :shortcuts="shortcuts"
          @change="handleDateChange"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </form-search-item>
      <form-search-item>
        <el-input v-model="queryParams.operator_name" placeholder="操作人" />
      </form-search-item>
      <form-search-item>
        <el-input v-model="queryParams.business_name" placeholder="车位编号" />
      </form-search-item>
      <form-search-item>
        <el-input v-model="queryParams.business_extra" placeholder="设备序列号" />
      </form-search-item>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <div class="opers">
        <el-space><div></div></el-space>
        <el-space>
          <DownloadButton
            btnType="default"
            :exportFunc="actionLogService.exportOperationLogs"
            :params="queryParams.module_type.length == 0 ? { ...queryParams, module_type: [1, 2, 3] } : queryParams"
          ></DownloadButton>
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="module_type_desc" label="功能模块" align="center" width="100">
          <template #default="scope">
            <span> {{ scope.row.module_type_desc || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center">
          <template #default="scope">
            <span> {{ scope.row.park_name || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="business_number" label="车场编号" align="center">
          <template #default="scope">
            <span> {{ scope.row.business_number || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="business_name" label="车位编号" align="center">
          <template #default="scope">
            <span> {{ scope.row.business_name || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="business_extra" label="设备序列号" align="center">
          <template #default="scope">
            <span> {{ scope.row.business_extra || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="operation_type_desc" label="操作类型" align="center">
          <template #default="scope">
            <span> {{ scope.row.operation_type_desc || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="operation_desc" label="操作描述" align="left" width="500">
          <template #default="scope">
            <span v-if="scope.row.operation_desc && scope.row.operation_desc.length > 0" v-html="scope.row.operation_desc"> </span>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="operator_name" label="操作人" align="center" width="100">
          <template #default="scope">
            <span> {{ scope.row.operator_name || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="operator_account" label="操作人账号" align="center">
          <template #default="scope">
            <span> {{ scope.row.operator_account || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="operation_time" label="操作时间" align="center">
          <template #default="scope">
            <span> {{ scope.row.operation_time || '-' }} </span>
          </template>
        </el-table-column>
        <el-table-column prop="client_type_desc" label="操作客户端" align="center" width="100">
          <template #default="scope">
            <span> {{ scope.row.client_type_desc || '-' }} </span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="table-pagination"
        background
        :current-page="queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!-- 关联车场 -->
    <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
      <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" @renderTableInput="renderTableInput" />
    </el-dialog>
  </div>
</template>

<script setup>
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import DownloadButton from '@/components/DownloadButton.vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from '@/components/ParkFindBack.vue';
import actionLogService from '@/service/system/ActionLogService';
import { ElMessage, dayjs } from 'element-plus';
import { ref } from 'vue';

const queryParams = ref({
  module_type: [],
  park_id: undefined,
  operation_type: undefined,
  operator_name: undefined,
  business_name: undefined,
  business_extra: undefined,
  start_time: undefined,
  end_time: undefined,
  page: 1,
  limit: 30
});
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');

const moduleTypes = [
  {
    key: '车场管理',
    value: 1
  },
  {
    key: '车位管理',
    value: 2
  },
  {
    key: '车场设备管理',
    value: 3
  }
];
const operationTypes = [
  {
    key: '创建',
    value: 1
  },
  {
    key: '修改',
    value: 2
  },
  {
    key: '启用',
    value: 3
  },
  {
    key: '禁用',
    value: 4
  },
  {
    key: '删除',
    value: 5
  },
  {
    key: '批量导入',
    value: 7
  }
];
const shortcuts = [
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];
const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const startDate = new Date(val[0]);
    const endDate = new Date(val[1]);
    const diffInDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));

    if (diffInDays > 365) {
      ElMessage.warning('最大可选时间范围为一年');
      // Reset the selection or adjust it to one year
      // For example, you could automatically adjust the end date:
      // const newEndDate = new Date(startDate)
      // newEndDate.setFullYear(newEndDate.getFullYear() + 1)
      // this.dateRange = [val[0], newEndDate.toISOString().split('T')[0]]

      // Or simply clear the selection:
      dateRange.value = [];
    }
  }
};
const clearPark = () => {
  queryParams.value.park_id = undefined;
  queryParams.value.park_name = undefined;
};

const dateRange = ref([dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = queryParams.value.park_id;
    park_name.value = queryParams.value.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  queryParams.value.park_id = val[0].park_id;
  queryParams.value.park_name = val[0].park_name;
};

const handleDataSearch = (params) => {
  console.log('handleDataSearch', params);
  loading.value = true;
  if (dateRange.value?.length === 2) {
    queryParams.value.start_time = dateRange.value[0] + ' 00:00:00';
    queryParams.value.end_time = dateRange.value[1] + ' 23:59:59';
  } else {
    queryParams.value.start_time = undefined;
    queryParams.value.end_time = undefined;
  }
  const pageParams = { ...queryParams.value, ...params };
  if (queryParams.value.module_type?.length === 0) {
    pageParams.module_type = [1, 2, 3];
  }
  actionLogService.getLogOperationLogsPage(pageParams).then((response) => {
    if (response.success === true) {
      if (response.data.rows && response.data.rows.length > 0) {
        response.data.rows.forEach((item) => {
          item.operation_desc = item.operation_desc.replace(/\n/g, '<br />');
        });
      }
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
  dateRange.value = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  queryParams.value = {
    module_type: [],
    park_id: undefined,
    operation_type: undefined,
    operator_name: undefined,
    business_name: undefined,
    business_extra: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  handleDataSearch();
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};

defineExpose({
  handleDataSearch
});
</script>

<style lang="scss" scoped></style>
