<template>
  <div class="conbox">
<div class="contit">
      <!-- 标题 -->  
      <div class="conboxtit"> <div class="fill"></div> 版本更新记录</div>
    <!-- 添加版本按钮 仅管理员可见 -->
    <div class="adduotton">
      <el-button v-if="useUserStore.role_id == 1" icon="plus" type="primary" @click="addversition">新增版本</el-button>
    </div>
</div>
    <!-- 版本区域 -->
    <div class="VersionInfobox">
   <el-timeline>
    <el-timeline-item :timestamp="i.release_date" placement="top"  :icon="index?'Select':''" color="#2cadff" size="30" v-for="(i,index) in reverseVersition" :key="i" >
      <VersionInfocard  @editVersition="updateVersition" :data="i" />
    </el-timeline-item>
  </el-timeline>
    </div>
    <!-- 点击卡卡片编辑/新增弹出框 -->
    <el-dialog v-model="centerDialogVisible" :title="editOrAdd ? '修改版本' : '新增版本'" @open="addOrEditOpenDialog">
      <div class="dialog-content">
        <el-form>
          <el-form-item label="版本名称:">
            <el-input style="width: 30%" placeholder="请输入版本信息" v-model="versitiondata.version_name"><template #prepend>V</template></el-input>
          </el-form-item>
          <el-form-item :label="editOrAdd ? '更新时间' : '发布时间'">
            <el-date-picker
              value-format="YYYY-MM-DD HH:mm:ss"
              type="datetime"
              v-model="versitiondata.release_date"
              placeholder="选择日期"
              @change="changeDate"
            />
          </el-form-item>
          <el-form-item label="更新内容:">
            <div class="upddateVersition">
              <el-tooltip>
                <template #content> 功能更新:本次更新新增了[xxxx]<br />问题修复:修复了[xxxx]问题 </template>
                <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
              </el-tooltip>
              <el-input
                class="upddateVersitionInput"
                v-model="versitiondata.version_update_content"
                style="width: 80%"
                :rows="2"
                type="textarea"
                placeholder="请输入版本的简要描述，最多100字符"
                maxlength="100"
              />
            </div>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="verEditReset">重置</el-button>
          <el-button type="primary" @click="sendVersitionInfo">{{ editOrAdd ? '修改' : '发布' }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onActivated, computed } from 'vue'; //引入vue
import { useUser } from '@/stores/user';
import { getAllVersionInfo, addVersionInfo, editVersionInfo } from '@/api/VersionInfo/VersionInfo'; //引入api
import { ElMessage } from 'element-plus'; //引入element-plus
import VersionInfocard from './components/VersionInfocard.vue'; //引入版本信息卡片组件
const useUserStore = useUser(); //userStore
const editOrAdd = ref(0); //当前是修改or新增(0:新增 1:修改)
const centerDialogVisible = ref(false); //控制(编辑/新增)弹出框显示/隐藏
const versitionGetData = ref([]); //版本信息数据
//需要发送的版本信息数据
const versitiondata = ref({
  version_name: '',
  release_date: '',
  version_update_content: ''
});
//更新发送的数据
const updateVersitiondata = ref();
const reverseVersition = computed(() => {
  return versitionGetData.value.slice().reverse();
});
//start 生命周期钩子
onActivated(() => {
  //获取版本信息
  getAllVersionInfoData();
  console.log(useUserStore.role_id);
});
//end 生命周期钩子

//获取版本信息
const getAllVersionInfoData = async () => {
  try {
    const rudata = await getAllVersionInfo();
    if (rudata.code == 200) {
      versitionGetData.value = rudata.data;
    }
  } catch (error) {
    console.error('获取版本信息失败:', error);
  }
};
//初始化(versitiondata)数据
const resetinfo = () => {
  versitiondata.value = {
    version_name: '',
    release_date: '',
    version_update_content: ''
  };
};
//点击“添加版本”按钮
const addversition = () => {
  editOrAdd.value = 0;
  centerDialogVisible.value = true;
};
//点击弹窗的“重置”按钮
const verEditReset = () => {
  // 调用初始化(versitiondata)数据函数
  resetinfo();
};
//弹窗显示时触发
const addOrEditOpenDialog = () => {
  // 调用初始化(versitiondata)数据函数
  // 为新增的话清楚
  if (!editOrAdd.value) {
    resetinfo();
  }
};
//选择日期后
const changeDate = () => {
  // const date = new Date(versitiondata.value.release_date);
  // versitiondata.value.release_date = `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
};
//获取子组件传来的数据 (点击编辑按钮)
const updateVersition = (e) => {
  console.log('ximoya', e);
  versitiondata.value.version_name = e.version_name.slice(1);
  versitiondata.value.release_date = e.release_date;
  versitiondata.value.version_update_content = e.version_update_content;
  editOrAdd.value = 1;
  centerDialogVisible.value = true;
  updateVersitiondata.value = e;
};
//点击发布/更新按钮
const sendVersitionInfo = async () => {
  //判断是否为空
  if (versitiondata.value.version_name.trim() == '' || !versitiondata.value.release_date || versitiondata.value.version_update_content.trim() == '') {
    ElMessage({
      message: '请填写完整信息',
      type: 'warning'
    });
    return;
  }
  // 在版本名称前添加“v”
  versitiondata.value.version_name = `v${versitiondata.value.version_name}`;
  console.log(Object.values(versitiondata.value));
  //start当为新增
  if (!editOrAdd.value) {
    console.log(versitiondata.value);
    try {
      const rudata = await addVersionInfo(versitiondata.value);
      if (rudata.code == 200) {
        ElMessage({
          message: '添加成功',
          type: 'success'
        });
        centerDialogVisible.value = false;
        getAllVersionInfoData();
      } else {
        ElMessage({
          message: '添加失败',
          type: 'error'
        });
      }
    } catch (error) {
      console.error('添加版本信息失败:', error);
    }
    return;
  }
  //end当为新增
  //start当为修改
  try {
    const updataDate = {
      id: updateVersitiondata.value.id,
      version_name: versitiondata.value.version_name,
      release_date: versitiondata.value.release_date,
      version_update_content: versitiondata.value.version_update_content
    };
    console.log(updataDate);
    const rudata = await editVersionInfo(updataDate);
    if (rudata.code == 200) {
      ElMessage({ 
        message: '修改成功',
        type: 'success'
      });
      centerDialogVisible.value = false;
      getAllVersionInfoData();
    }
  } catch (error) {
    console.error('修改版本信息失败:', error);
  }
  console.log('修改');
  //end当为修改
};
</script>

<style scoped lang="scss">
.conbox {
  height: 100%;
  width: 100%;
  background-color: #fff;
  :deep(.el-dialog) {
    width: 70% !important;
  }
  .contit{
    height: 8%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #e6e7e8;
    .conboxtit {
    height: 50%;
    font-size: 16px;
    font-weight: 700;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
  .fill{
      width: 5px;
      height: 15px;
      border-radius: 5px;
      background-color: #1e9fff;
  }
    &::before{
      content: "";
      
    }
    }
  .adduotton {
    height: 50%;
  }
  }
 
  .VersionInfobox {
    height: calc(100% - 4% - 4% - 1%);
    padding:20px 20px 0 20px;
    overflow: auto;
  }
  .dialog-content {
    padding: 10px 0 0 20px;
  }
  .upddateVersition {
    margin-left: -10px;
    width: 100%;
    margin-top: 8px;
    display: flex;
    gap: 5px;
  }
  .upddateVersitionInput {
    width: 80%;
  }
}
</style>
