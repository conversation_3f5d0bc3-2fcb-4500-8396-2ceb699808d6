// 登录
export function login(param) {
  return {
    url: '/auth/login',
    type: 'post',
    data: {
      code: 200,
      success: true,
      message: '登录成功！',
      data: {
        id: 1,
        username: 'admin',
        nickname: 'Admin',
        avatar: 'https://demo.buildadmin.com/static/images/avatar.png',
        last_login_time: '2022-08-22 17:25:25',
        token:
          'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJhZG1pbiIsImV4cCI6MTY2MjE5MTA0MSwiaWF0IjoxNjYxNTg2MjQxfQ.ftOU6QKaCNGkB0fSuF-lWBm6X-L0ndCWSvOwmrd_eCY'
      }
    }
  };
}

// 登出
export function logout() {
  return {
    url: '/auth/logout',
    type: 'post',
    data: {
      code: 200,
      success: true
    }
  };
}
