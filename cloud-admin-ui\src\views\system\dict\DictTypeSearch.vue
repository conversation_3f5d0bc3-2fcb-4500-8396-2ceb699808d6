<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="字典名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.code" placeholder="字典编码" /></form-search-item>
  </FormSearch>
</template>

<script name="DictTypeSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: '',
    code: '',
    page: 1,
    limit: 30
  }
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    code: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
