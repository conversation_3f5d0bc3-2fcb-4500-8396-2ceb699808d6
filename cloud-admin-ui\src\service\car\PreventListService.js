import * as preventList from '@/api/car/PreventListApi';

/**
 * 疫情防控
 */
export default {
  /**
   * 分页查询
   */
  pagingPreventLists(data) {
    return new Promise((resolve, reject) => {
      try {
        preventList.pagingPreventLists(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增疫情防控
   */
  createPreventList(data) {
    return new Promise((resolve, reject) => {
      try {
        preventList.createPreventList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改疫情防控
   */
  updatePreventList(data) {
    return new Promise((resolve, reject) => {
      try {
        preventList.updatePreventList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除疫情防控
   */
  deletePreventList(data) {
    return new Promise((resolve, reject) => {
      try {
        preventList.deletePreventList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 导出疫情防控
   */
  exportPreventLists(data) {
    return new Promise((resolve, reject) => {
      try {
        preventList.exportPreventLists(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
