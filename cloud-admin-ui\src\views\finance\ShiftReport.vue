<template>
  <div class="container">
    <shift-report-search @form-search="searchShiftReportList" @reset="resetParamsAndData" />
    <shift-report-table ref="table" />
  </div>
</template>

<script name="ShiftReport" setup>
import ShiftReportSearch from './shiftReport/ShiftReportSearch.vue';
import ShiftReportTable from './shiftReport/ShiftReportTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchShiftReportList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
