<template>
  <div class="container">
    <el-card class="card">
      <div class="content">
        <div style="margin-top: 60px; margin-bottom: 100px; text-align: center">
          <el-form ref="addForm" label-width="200px" :rules="data.rules" :model="data.appForm">
            <template #header>
              <div style="display: inline-block; line-height: 32px">创建应用</div>
            </template>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="应用名称" class="required" prop="name">
                          <el-input v-model="data.appForm.name" maxlength="30" />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="app_key" class="required" prop="app_key">
                          <el-input v-model="data.appForm.app_key" maxlength="200" />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="应用类型" class="required" prop="app_type">
                          <el-select v-model="data.appForm.app_type" placeholder="应用类型" clearable>
                              <el-option v-for="item in appType" :key="item.value" :label="item.key" :value="item.value" />
                          </el-select>
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="应用标识" class="required" prop="app_code">
                          <el-input v-model="data.appForm.app_code" maxlength="200" />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="应用公钥" prop="app_public_key">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.app_public_key" maxlength="1000" show-word-limit />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="应用私钥" prop="app_private_key">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.app_private_key" maxlength="1000" show-word-limit />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="应用签名秘钥" prop="app_sign_key">
                          <el-input v-model="data.appForm.app_sign_key" maxlength="30" />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="应用Token有效期(分钟)" prop="app_token_expiry">
                          <el-input v-model="data.appForm.app_token_expiry" maxlength="200" />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="平台公钥"  prop="plat_public_key">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.plat_public_key" maxlength="1000" show-word-limit />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="平台私钥" prop="plat_private_key">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.plat_private_key" maxlength="1000" show-word-limit />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="平台签名秘钥"  prop="plat_sign_key">
                          <el-input v-model="data.appForm.plat_sign_key" maxlength="30" />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="平台Token有效期(分钟)" prop="plat_token_expiry">
                          <el-input v-model="data.appForm.plat_token_expiry" maxlength="200" />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="24">
                      <el-form-item label="其他参数（json）"  prop="app_params">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.app_params" maxlength="1000" show-word-limit />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5">
                  <el-col :span="12">
                      <el-form-item label="应用介绍" class="required" prop="features">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.features" maxlength="500" show-word-limit />
                      </el-form-item>
                  </el-col>
                  <el-col :span="12">
                      <el-form-item label="备注" prop="memo">
                          <el-input type="textarea" :rows="4" v-model="data.appForm.memo" maxlength="500" show-word-limit />
                      </el-form-item>
                  </el-col>
              </el-row>
              <el-row :gutter="5" class="btn-group">
                  <el-col :span="24">
                      <el-form-item style="text-align: center">
                          <el-button style="margin-top: 12px" @click="closeTab(addForm)">取消</el-button>
                          <el-button type="primary" style="margin-top: 12px" @click="save(addForm)">保存</el-button>
                      </el-form-item>
                  </el-col>
              </el-row>


          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="AppCreate">
import { reactive, ref, onActivated,onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import appAdminService from '@/service/system/AppAdminService';
import commonService from '@/service/common/CommonService';
import { closeCurrentTab } from '@/utils/tabKit';

const appType = ref([]);
const addForm = ref();
const data = reactive({
  active: 1,
  appForm: {
    name: undefined,
    app_key: undefined,
    features: undefined,
    memo: undefined,
    app_type: undefined,
    app_code: undefined,
    app_public_key: undefined,
    app_private_key: undefined,
    app_sign_key: undefined,
    app_token_expiry: undefined,
    plat_public_key: undefined,
    plat_private_key: undefined,
    plat_sign_key: undefined,
    plat_token_expiry: undefined,
    app_params: undefined
  },
  rules: {
    name: [
      {
        required: true,
        message: '请输入应用名称',
        trigger: 'blur'
      }
    ],
    app_key: [
      {
        required: true,
        message: '请输入App Key',
        trigger: 'blur'
      }
    ],
    features: [
      {
        required: true,
        message: '请输入应用介绍',
        trigger: 'blur'
      }
    ]
  }
});
onMounted(() => {
    // 数据初始化
    initSelects();
});
const initSelects = () => {
    const param = [{ enum_key: 'appType', enum_value: 'EnumAppType' }];
    commonService.findEnums('park', param).then((response) => {
        appType.value = response.data.appType;
    });
};

onActivated(() => {
  addForm.value.resetFields();
});

const closeTab = (addForm) => {
  addForm.resetFields();
  closeCurrentTab({
    path: '/system/appAdmin'
  });
};

const save = (addForm) => {
  addForm.validate().then(() => {
    appAdminService.createApp(data.appForm).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '创建应用成功',
          type: 'success'
        });
        closeTab(addForm);
      } else {
        ElMessage({
          message: response.message,
          type: 'error'
        });
      }
    });
  });
};
</script>

<style lang="scss" scoped>
.card {
  vertical-align: middle;
  height: 100%;
}

.content {
  //width: 1000px;
  margin: 50px auto;
}

.form {
  width: 600px;
  margin: 50px auto;
}

.desc {
  width: 100%;
  padding: 0 0px;
  color: rgba(0, 0, 0, 0.45);
}

.desc h3 {
  margin: 0 0 12px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  line-height: 32px;
  font-weight: 500;
}

.desc h4 {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
}

.desc p {
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 22px;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  content: '* ';
  color: red;
}

.el-upload-dragger {
  width: 500px;
}
.btn-group :deep(.el-form-item__content) {
  margin-left: 0;
  justify-content: center;
}
</style>
