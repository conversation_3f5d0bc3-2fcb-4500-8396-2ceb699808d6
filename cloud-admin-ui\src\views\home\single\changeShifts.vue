<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-04-03 09:09:41
 * @LastEditors: 达万安 段世煜
 * @Description: 交接班记录
 * @FilePath: \cloud-admin-ui\src\views\home\single\changeShifts.vue
-->
<template>
  <warp-card height="29.5%" title="交接班记录">
    <el-table :data="tableData" border style="width: 100%" height="100%">
      <el-table-column prop="park_name" label="岗亭名称" align="center" width="110px"></el-table-column>
      <el-table-column prop="shift_name" label="当班人" align="center" width="80px"></el-table-column>
      <el-table-column prop="on_time" label="上班时间" align="center"></el-table-column>
      <el-table-column prop="off_time" label="下班时间" align="center"></el-table-column>
      <el-table-column label="异常数据" align="center">
        <template #header>
          <div class="header">
            异常数据
            <el-tooltip content="如果当班时间大于等于7天，仅统计7天以内的数据" placement="bottom" effect="light">
              <el-icon class="icon"><QuestionFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        <template #default="scope">
          <el-button v-if="scope.row.loss_money > 0" type="primary" link @click="viewDetail('/statisticalReport/abnormalRecord')">点击查看</el-button>
          <span v-else>- -</span>
        </template>
      </el-table-column>
      <el-table-column label="当班详情" align="center">
        <template #default="scope">
          <el-button type="primary" link @click="viewDetail('/finance/shiftReport')">查看</el-button>
        </template>
      </el-table-column>
    </el-table>
  </warp-card>
</template>

<script setup>
import { ref } from 'vue';
import warpCard from './components/warpCard.vue';
import { activeRouteTab } from '@/utils/tabKit';

import { fetchShiftHandoverRecords } from '@/api/home/<USER>';
import { QuestionFilled } from '@element-plus/icons-vue';

const viewDetail = (path) => {
  activeRouteTab({
    path: path
  });
};

const tableData = ref([]);
const fetchData = async (params) => {
  const { data } = await fetchShiftHandoverRecords(params);
  tableData.value = data;
};
defineExpose({
  fetchData
});
</script>

<style scoped lang="scss">
.header {
  display: flex;
  align-items: center;
  .icon {
    cursor: pointer;
    margin-left: 2px;
  }
}
</style>
