<template>
    <div class="container">
        <el-card class="card">
            <div class="content">
                <el-steps :active="data.active" style="width: 100%" align-center>
                    <el-step title="上传EXE" icon="DocumentAdd"></el-step>
                    <el-step title="上传YML" icon="DocumentAdd"></el-step>
                    <el-step title="发布APP" icon="Setting"></el-step>
                    <el-step title="完成" icon="Star"></el-step>
                </el-steps>
                <div style="margin-top: 60px; margin-bottom: 100px; text-align: center">
                    <template v-if="data.active == 1" v-loading="loading">
                        <div style="display: inline-block">
                            <el-upload
                                    class="upload-demo"
                                    drag
                                    :auto-upload="true"
                                    :action="uploadExeUrl"
                                    accept=".exe"
                                    :data="data.form"
                                    :before-upload="exeBeforeUpload"
                                    :on-progress="exeOnProgressUpload"
                                    :on-success="exeOnSuccessUpload"
                                    :on-remove="exeOnRemoveFile"
                                    :headers="headers"
                                    :limit="1"
                            >
                                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            </el-upload>
                        </div>
                        <div class="desc">
                            <p>
                                点击按钮选择应用的安装包，或拖拽文件到此区域
                                <br />
                                仅支持exe文件，文件大小不超过100MB
                            </p>
                        </div>
                        <div style="width: 500px; margin: 0 auto">
                            <el-button type="primary" style="margin-top: 12px; width: 500px" @click="exeNext()">下一步</el-button>
                        </div>
                    </template>
                    <template v-if="data.active == 2" v-loading="loading">
                        <div style="display: inline-block">
                            <el-upload
                                    class="upload-demo"
                                    drag
                                    :auto-upload="true"
                                    :action="uploadYmlUrl"
                                    accept=".yml"
                                    :before-upload="ymlBeforeUpload"
                                    :on-progress="ymlOnProgressUpload"
                                    :on-success="ymlOnSuccessUpload"
                                    :on-remove="ymlOnRemoveFile"
                                    :headers="headers"
                                    :limit="1"
                            >
                                <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                                <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
                            </el-upload>
                        </div>
                        <div class="desc">
                            <p>
                                点击按钮选择应用的安装包，或拖拽文件到此区域
                                <br />
                                仅支持yml文件，文件大小不超过100MB
                            </p>
                        </div>
                        <div style="width: 500px; margin: 0 auto">
                            <el-button type="primary" style="margin-top: 12px; width: 500px" @click="ymlNext()">下一步</el-button>
                        </div>
                    </template>
                    <template v-if="data.active == 3">
                        <div style="margin-top: 60px; margin-bottom: 100px; text-align: center">
                            <el-form :model="data.appForm" :rules="data.rules" ref="addForm" label-width="110px" size="medium">
                                <el-form-item label="当前版本" class="required" prop="current_version">
                                    <el-input v-model="data.appForm.current_version"></el-input>
                                </el-form-item>
                                <el-form-item label="版本更新说明" class="required" prop="features">
                                    <el-input type="textarea" :rows="4" v-model="data.appForm.features" maxlength="500" show-word-limit></el-input>
                                </el-form-item>
                                <el-form-item label="备注" prop="memo">
                                    <el-input type="textarea" :rows="4" v-model="data.appForm.memo" maxlength="500" show-word-limit></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
                        <div style="width: 660px; margin-top: 12px; margin: 0 auto">
                            <el-row :gutter="10">
                                <el-col :span="12">
                                    <el-button style="width: 100%" @click="before()">上一步</el-button>
                                </el-col>
                                <el-col :span="12">
                                    <el-button type="primary" style="width: 100%" @click="finish(addForm)">完成</el-button>
                                </el-col>
                            </el-row>
                        </div>
                    </template>
                    <template v-if="data.active == 4">
                        <template v-if="data.result.success">
                            <i class="el-icon-success" style="font-size: 80pt; color: #67c23a"></i>
                            <br /><br />
                            <div style="font-size: 25pt">应用发布成功</div>
                            <div>
                                <el-button type="primary" style="margin-top: 12px" size="medium" @click="closeTab()">关&ensp;闭</el-button>
                            </div>
                        </template>
                        <template v-else>
                            <i class="el-icon-error" style="font-size: 80pt; color: #f56c6c"></i>
                            <br /><br />
                            <div style="font-size: 25pt">应用发布失败</div>
                            <div style="font-size: 14px; color: #999999; margin-top: 10px">
                                <i class="el-icon-warning" style="color: #f56c6c"></i>&ensp;{{ result.message }}
                            </div>
                            <div>
                                <el-button type="primary" style="margin-top: 12px" size="medium" @click="closeTab()">关&ensp;闭</el-button>
                            </div>
                        </template>
                    </template>
                </div>
            </div>
        </el-card>
    </div>
</template>

<script setup name="AppPublish">
    import { reactive, ref, onActivated } from 'vue';
    import appAdminService from '@/service/system/AppAdminService';
    import { closeCurrentTab } from '@/utils/tabKit';
    import { ElMessage } from 'element-plus';
    import { getToken } from '@/utils/common';
    import { useRoute } from 'vue-router';

    const uploadExeUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/app/uploadApp');
    const uploadYmlUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/app/uploadYml');
    const route = useRoute();
    const headers = reactive({
        Authorization: getToken()
    });
    const addForm = ref();
    const loading = ref(false);
    const data = reactive({
        active: 1,
        appForm: {
            app_id: undefined,
            appId: undefined,
            name: undefined,
            current_version: undefined,
            file_path: undefined,
            ymlFilePath: undefined,
            features: undefined,
            memo: undefined
        },
        form: {
            app_id: undefined
        },
        result: {
            success: true,
            message: ''
        },
        rules: {
            current_version: [
                {
                    required: true,
                    message: '版本号不能为空',
                    trigger: 'blur'
                }
            ],
            features: [
                {
                    required: true,
                    message: '版本更新说明不能为空',
                    trigger: 'blur'
                }
            ]
        }
    });

    onActivated(() => {
        if ({} !== route.query) {
            const param = route.query;
            data.appForm = {
                appId: param.id
            };
            data.active = param.active;
            // 查询单条记录
            appAdminService.getApp(param.id).then((response) => {
                if (response.success === true) {
                    const data = response.data;
                    data.appForm = {
                        appId: data.id,
                        logoUrl: data.logoUrl,
                        name: data.name
                    };
                } else {
                    ElMessage({
                        message: response.message,
                        type: 'error'
                    });
                }
            });
        }
    });

    const before = () => {
        if (data.active-- < 2) data.active = 0;
    };

    const exeNext = () => {
        if (data.appForm.file_path === undefined) {
            ElMessage({
                message: '请先上传EXE文件',
                type: 'warning'
            });
            return false;
        }
        data.active++;
    };

    const ymlNext = () => {
        if (data.appForm.ymlFilePath === undefined) {
            ElMessage({
                message: '请先上传yml文件',
                type: 'warning'
            });
            return false;
        }
        data.active++;
    };

    const closeTab = () => {
        closeCurrentTab({
            path: '/system/appAdmin'
        });
    };

    const finish = (addForm) => {
        // 版本号
        if (data.appForm.current_version === undefined || data.appForm.current_version === '') {
            ElMessage({
                message: '版本号不能为空',
                type: 'warning'
            });
            return false;
        }
        if (data.appForm.features === undefined || data.appForm.features === '') {
            ElMessage({
                message: '版本更新说明不能为空',
                type: 'warning'
            });
            return false;
        }
        data.appForm.app_id = data.appForm.appId;
        appAdminService.releaseApp(data.appForm).then((response) => {
            if (response.success === true) {
                ElMessage({
                    message: response.message,
                    type: 'success'
                });
                data.result = {
                    success: response.success,
                    message: response.message
                };
                console.log(response);
            } else {
                ElMessage({
                    message: response.detail_message,
                    type: 'warning'
                });
            }
        });

        data.active++;
    };

    const exeBeforeUpload = (file) => {
        data.form = data.appForm;
        const isLt100M = file.size / 1024 / 1024 < 100;
        if (!isLt100M) {
            ElMessage({
                message: '上传文件大小不能超过 100MB!',
                type: 'warning'
            });
        }
    };

    const exeOnProgressUpload = () => {
        loading.value = true;
    };

    const exeOnSuccessUpload = (response) => {
        if (response.success === true) {
            loading.value = false;
            data.appForm.file_path = response.data.file;
        } else {
            ElMessage({
                message: response.detail_message,
                type: 'warning'
            });
        }
    };

    const exeOnRemoveFile = () => {
        data.appForm.file_path = undefined;
    };

    const ymlBeforeUpload = (file) => {
        const isLt1M = file.size / 1024 / 1024 < 1;
        if (!isLt1M) {
            ElMessage({
                message: '上传文件大小不能超过 1MB!',
                type: 'warning'
            });
        }
    };

    const ymlOnProgressUpload = () => {
        loading.value = true;
    };

    const ymlOnSuccessUpload = (response) => {
        if (response.success === true) {
            loading.value = false;
            data.appForm.ymlFilePath = response.data.file;
        } else {
            ElMessage({
                message: response.detail_message,
                type: 'warning'
            });
        }
    };

    const ymlOnRemoveFile = () => {
        data.appForm.ymlFilePath = undefined;
    };
</script>

<style lang="scss" scoped>
  .card {
    vertical-align: middle;
    height: 100%;
  }

  .content {
    width: 1000px;
    margin: 50px auto;
  }

  .form {
    width: 600px;
    margin: 50px auto;
  }

  .desc {
    width: 100%;
    padding: 0 0px;
    color: rgba(0, 0, 0, 0.45);
  }

  .desc h3 {
    margin: 0 0 12px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 16px;
    line-height: 32px;
    font-weight: 500;
  }

  .desc h4 {
    margin: 0 0 4px;
    color: rgba(0, 0, 0, 0.45);
    font-size: 14px;
    line-height: 22px;
    font-weight: 500;
  }

  .desc p {
    margin-top: 0;
    margin-bottom: 12px;
    line-height: 22px;
  }

  .required > .el-form-item__label::before {
    padding-top: 5px;
    content: '* ';
    color: red;
  }

  .el-upload-dragger {
    width: 500px !important;
    margin: 0 auto !important;
  }
</style>
