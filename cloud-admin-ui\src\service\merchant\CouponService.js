import * as coupon from '@/api/merchant/CouponApi';

/**
 * 优免管理
 */
export default {
  /**
   *商家优免券表格数据查询
   */
  pagingCouponMerchants(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.pagingCouponMerchants(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增商家优免券
   */
  createCouponMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.createCouponMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改商家优免券
   */
  updateCouponMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.updateCouponMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除优惠券
   */
  deleteCouponMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.deleteCouponMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 提交审核
   */
  submitAuditCouponMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.submitAuditCouponMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 撤回
   */
  revokeAuditCouponMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.revokeAuditCouponMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
