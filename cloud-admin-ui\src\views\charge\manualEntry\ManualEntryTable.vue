<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <div><textModul ref="textRef"></textModul></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="sentryBoxRecordService.exportRepairInRecordRecords"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 384px)">
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="180" />
        <el-table-column prop="plate_no" label="车牌号码" align="center" min-width="180" />
        <el-table-column label="入口通道" align="center" min-width="100">
          <template #default="scope">
            <span style="margin-left: 10px">{{ scope.row.gateway_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="入场时间" align="center" min-width="180">
          <template #default="scope">
            <span style="margin-left: 10px">{{ scope.row.in_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="source" label="操作来源" align="center" min-width="120" />
        <el-table-column prop="operator" label="操作人" align="center" min-width="100" />
        <el-table-column prop="created_at" label="操作时间" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="ManualEntryTable" setup>
import DownloadButton from '@/components/DownloadButton.vue';
import sentryBoxRecordService from '@/service/charge/SentryBoxRecordService';
import { reactive, ref } from 'vue';
import textModul from '../textModul.vue';
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

const textRef = ref(null);
const getList = (params) => {
  console.log(params);
  textRef.value.getData(params);
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  sentryBoxRecordService.pagingRepairInRecordRecords(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
