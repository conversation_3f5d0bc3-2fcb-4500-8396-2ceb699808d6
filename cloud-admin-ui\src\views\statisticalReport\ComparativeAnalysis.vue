<template>
  <div class="container">
    <comparative-analysis-search @form-search="searchTimedAccessList" @reset="searchTimedAccessList" />
    <comparative-analysis-table ref="table" />
  </div>
</template>

<script name="TimedAccess" setup>
import { ref } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import ComparativeAnalysisSearch from './comparativeAnalysis/ComparativeAnalysisSearch.vue';
import ComparativeAnalysisTable from './comparativeAnalysis/ComparativeAnalysisTable.vue';

const table = ref(null);

const searchTimedAccessList = (queryParams) => {
  if (dayjs(queryParams.start_time).year() !== dayjs(queryParams.end_time).year()) {
    ElMessage({
      message: '仅支持选择同一年份的月份',
      type: 'warning'
    });
    return;
  }
  if (table.value.tabPosition === '列表') {
    table.value.getList(queryParams);
  } else {
    table.value.getChartData(queryParams);
  }
};
</script>
