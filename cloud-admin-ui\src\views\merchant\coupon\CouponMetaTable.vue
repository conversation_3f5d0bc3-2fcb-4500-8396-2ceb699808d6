<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加优免模板</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="130">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.enabled == 0" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" v-if="scope.row.enabled == 0" @click="handleDelete(scope.row.id)"> 删除 </el-button>
            <el-button link v-if="scope.row.enabled == 0" type="success" @click="enable(scope.row.id)"> 启用 </el-button>
            <el-button link v-if="scope.row.enabled == 1" type="danger" @click="disable(scope.row.id)"> 停用 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="name" label="优免券名称" align="center" />
        <el-table-column prop="type_desc" label="优免券类型" align="center" />
        <el-table-column label="优惠数" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.type == 1">{{ scope.row.coupon_meta_param.derate_hour }}</span>
            <span v-if="scope.row.type == 2">{{ scope.row.coupon_meta_param.derate_money }}</span>
            <span v-if="scope.row.type == 3">{{ scope.row.coupon_meta_param.discount_ratio }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="enabled_desc" label="优免券模板状态" align="center" />
        <el-table-column prop="creator" label="创建人" align="center" />
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加优免券" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择停车场" prop="park_id">
            <el-input v-model="data.form.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'add')" />
          </el-form-item>
          <el-form-item prop="name" label="优免券名称">
            <el-input v-model="data.form.name" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item prop="type" label="优免券类型">
            <el-select v-model="data.form.type" placeholder="优免券类型" style="width: 100%" clearable>
              <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="data.form.type == 1" class="required" label="优惠数">
            <el-input-number min="1" style="width: 90%" v-model="data.form.params.derateHour" />
            <div class="el-input-number-append">时</div>
          </el-form-item>
          <el-form-item v-if="data.form.type == 2" class="required" label="优惠数">
            <el-input-number min="1" style="width: 90%" v-model="data.form.params.derateMoney" />
            <div class="el-input-number-append">元</div>
          </el-form-item>
          <el-form-item v-if="data.form.type == 3" class="required" label="优惠数">
            <el-input-number max="10" min="1" :precision="2" :step="0.1" style="width: 90%" v-model="data.form.params.discountRatio" />
            <div class="el-input-number-append">折</div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createCoupon(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog title="修改优免券" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="选择停车场" prop="park_id">
            <el-input v-model="data.updateForm.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'edit')" />
          </el-form-item>
          <el-form-item prop="name" label="优免券名称">
            <el-input v-model="data.updateForm.name" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item prop="type" label="优免券类型">
            <el-select v-model="data.updateForm.type" placeholder="优免券类型" style="width: 100%" clearable>
              <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="data.updateForm.type == 1" label="优惠数" class="required">
            <el-input v-model="data.updateForm.params.derateHour">
              <template #append> 时 </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="data.updateForm.type == 2" label="优惠数" class="required">
            <el-input v-model="data.updateForm.params.derateMoney">
              <template #append> 元 </template>
            </el-input>
          </el-form-item>
          <el-form-item v-if="data.updateForm.type == 3" label="优惠数" class="required">
            <el-input v-model="data.updateForm.params.discountRatio">
              <template #append> 折 </template>
            </el-input>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateCoupon(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CouponMetaTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import commonService from '@/service/common/CommonService';
import couponMetaService from '@/service/merchant/CouponMetaService';
import ParkFindBack from '../ParkFindBack.vue';
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const types = ref([]);
const total = ref(0);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const parkInfoDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: '',
    park_name: '',
    name: '',
    type: '',
    params: {}
  },
  updateForm: {},
  rules: {
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入优免券名称',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择优免券类型',
        trigger: 'change'
      }
    ]
  }
});
onActivated(() => {
  // 数据初始化
  initSelects();
  getList(data.queryParams);
});

const initSelects = () => {
  const param = [{ enum_key: 'types', enum_value: 'EnumCouponMetaType' }];
  commonService.findEnums('coupon', param).then((response) => {
    types.value = response.data.types;
  });
};
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  couponMetaService.pagingCouponMetas(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: '',
    park_name: '',
    name: '',
    type: '',
    params: {}
  };
  createDialogVisible.value = true;
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createCoupon = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.type == 1) {
      if (data.form.params.derateHour == '' || data.form.params.derateHour == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    if (data.form.type == 2) {
      if (data.form.params.derateMoney == '' || data.form.params.derateMoney == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    if (data.form.type == 3) {
      if (data.form.params.discountRatio == '' || data.form.params.discountRatio == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    couponMetaService
      .createCouponMeta(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponMetaService
      .deleteCouponMeta(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (val) => {
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    park_name: val.park_name,
    name: val.name,
    type: val.type,
    params: {}
  };
  console.log(val);
  if (val.type == 1) {
    data.updateForm.params.derateHour = val.coupon_meta_param.derate_hour;
  }
  if (val.type == 2) {
    data.updateForm.params.derateMoney = val.coupon_meta_param.derate_money;
  }
  if (val.type == 3) {
    data.updateForm.params.discountRatio = val.coupon_meta_param.discount_ratio;
  }
  updateDialogVisible.value = true;
};
const updateCoupon = (editForm) => {
  editForm.validate().then(() => {
    if (data.updateForm.type == 1) {
      if (data.updateForm.params.derateHour == '' || data.updateForm.params.derateHour == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    if (data.updateForm.type == 2) {
      if (data.updateForm.params.derateMoney == '' || data.updateForm.params.derateMoney == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    if (data.updateForm.type == 3) {
      if (data.updateForm.params.discountRatio == '' || data.updateForm.params.discountRatio == undefined) {
        ElMessage({
          message: '优惠数不能为空',
          type: 'warning'
        });
        return false;
      }
    }
    couponMetaService
      .updateCouponMeta(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//启用
const enable = (val) => {
  ElMessageBox.confirm('是否要启用该优免券？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponMetaService.enableCouponMeta(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 禁用
const disable = (val) => {
  ElMessageBox.confirm('是否要停用该优免券？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponMetaService.disableCouponMeta(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
.el-input-number-append {
  display: inline;
  background-color: #f5f7fa;
  padding: 0px 10px;
  width: 31px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-radius: 0px 2px 2px 0px;
}
</style>
