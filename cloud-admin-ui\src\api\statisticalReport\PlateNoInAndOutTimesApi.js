/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询车牌进出频次
export const pagingPlateNoInAndOutTimes = (data) => {
  return $({
    url: '/console/statistics/stat/frequency/car/listStatFrequencyCar',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/stat/frequency/car/exportStatFrequencyCar',
    method: 'post',
    data
  });
};
