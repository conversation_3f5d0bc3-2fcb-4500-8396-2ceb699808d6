<template>
  <div class="container">
    <park-saturation-search @form-search="searchParkSaturationList" />
    <park-saturation-table ref="table" />
  </div>
</template>

<script name="ParkSaturation" setup>
import ParkSaturationSearch from './parkSaturation/ParkSaturationSearch.vue';
import ParkSaturationTable from './parkSaturation/ParkSaturationTable.vue';
import { ref } from 'vue';

const table = ref(null);

const searchParkSaturationList = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
