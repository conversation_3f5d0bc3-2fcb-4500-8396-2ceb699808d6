import * as dayReportA<PERSON> from '@/api/finance/DayReportApi';

/**
 * 日报表
 */
export default {
  /**
   * 分页查询日报表通过金额
   */
  pagingDayReportByMoney(data) {
    return new Promise((resolve, reject) => {
      try {
        dayReportApi.pagingDayReportByMoney(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询日报表通过次数
   */
  pagingDayReportByTimes(data) {
    return new Promise((resolve, reject) => {
      try {
        dayReportApi.pagingDayReportByTimes(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 卡片数据查询
   */
  searchBtnData(data) {
    return new Promise((resolve, reject) => {
      try {
        dayReportApi.searchBtnData(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出日报表（金额）
   */
  exportDayReports(data) {
    return new Promise((resolve, reject) => {
      try {
        dayReportApi.exportDayReports(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出日报表（次数）
   */
  exportDayReportCnt(data) {
    return new Promise((resolve, reject) => {
      try {
        dayReportApi.exportDayReportCnt(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
