<template>
  <div class="login-container">
    <div class="login-main">
      <div class="login-title">
        <p>您好！欢迎进入</p>
        <p>惠达云停车管理平台</p>
      </div>
      <div class="login-card">
        <div class="login-logo">
          <div class="image" />
        </div>
        <div class="login-form">
          <div class="content">
            <el-form @keyup.enter="login(formRef)" ref="formRef" :rules="rules" size="large" :model="form" hide-required-asterisk>
              <el-form-item prop="username">
                <el-input type="text" clearable v-model="form.username" placeholder="请输入您的账号/手机号"></el-input>
              </el-form-item>
              <el-form-item prop="password">
                <el-input v-model="form.password" type="password" placeholder="请输入您的密码"></el-input>
              </el-form-item>
              <el-form-item prop="captcha">
                <div class="captcha-item">
                  <el-input v-model="form.captcha" placeholder="请输入验证码" class="input"></el-input>
                  <div class="captcha-container" title="看不清，换一张" @click="generateCaptcha">
                    <img :src="state.captchaImg" style="height: 38px; width: " />
                  </div>
                </div>
              </el-form-item>
              <el-form-item>
                <div @click="login(formRef)" class="login-btn">登 录</div>
                <div class="login-more">
                  <el-divider>更多登录</el-divider>
                  <div class="iam-container">
                    <el-button plain color="#005bac" type="primary" @click="redirectToIAMCM" class="iam-btn">珠海万信用户</el-button>
                    <!-- <el-button plain color="#005bac" type="primary" @click="redirectToIAM" class="iam-btn">集团万信用户</el-button> -->
                  </div>
                </div>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </div>

    <!-- 强制修改密码 -->
    <el-dialog title="重新设置密码" v-model="passwdForm.passwdDialogVisible" :close-on-click-modal="false" width="400px">
      <el-alert :title="passwdForm.tip" type="info" center show-icon style="margin-bottom: 16px" />
      <el-form ref="passwdFormRef" :model="passwdForm.form" label-width="100px" :rules="passwdForm.rules">
        <el-form-item label="新密码" prop="new_passwd" style="margin-bottom: 30px">
          <el-input v-model="passwdForm.form.new_passwd" placeholder="新密码" type="password" />
        </el-form-item>
        <el-form-item label="重复新密码" prop="repeat_passwd">
          <el-input v-model="passwdForm.form.repeat_passwd" placeholder="重复新密码" type="password" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelChangePasswd(passwdFormRef)">取 消</el-button>
        <el-button type="primary" @click="submitChangePasswd(passwdFormRef)">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 版权信息 -->
    <div class="copyright">
      <div style="margin-right: 10px">Copyright @2022北京惠达万安智慧城市科技有限公司版权所有</div>
      <div>
        <img src="@/assets/copyright.png" style="background-position: -861px 0px; width: 16px; height: 16px" /> 京公网安备
        11010502050935号京ICP备2022028664号-1
      </div>
    </div>
  </div>
</template>

<script name="Login" setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import loginService from '@/service/login/LoginService';
import router from '@/router';
import { useUser } from '@/stores/user';

const validatePassword = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?\d)(?=.*?[!#@*&.])[a-zA-Z\d!#@*&.]{6,20}$/;
    if (!reg.test(value)) {
      callback(new Error('密码必须包含大小写字母，数字，符号组成，最少6位，最多20位'));
    }
  }
  callback();
};

const user = useUser();

const formRef = ref();
const form = reactive({
  username: undefined,
  password: undefined,
  captcha: undefined,
  key: undefined
});

const passwdFormRef = ref();
const passwdForm = reactive({
  passwdDialogVisible: false,
  tip: '',
  form: {
    user_id: undefined,
    new_passwd: undefined,
    repeat_passwd: undefined
  },
  rules: {
    new_passwd: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validatePassword
      }
    ],
    repeat_passwd: [
      { required: true, message: '请重复输入新密码', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validatePassword
      }
    ]
  }
});

const state = reactive({
  captchaImg: ''
});

const rules = reactive({
  username: [
    {
      required: true,
      message: '请输入手机号',
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: '请输入密码',
      trigger: 'blur'
    }
  ],
  captcha: [
    {
      required: true,
      message: '请输入验证码',
      trigger: 'blur'
    }
  ]
});

onMounted(() => {
  generateCaptcha();
});

const redirectToIAMCM = () => {
  const VITE_GLOB_MIDDLE_LOGIN_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_URL;
  user.user_cas_pid = 'parkingcm';
  // const u1 = location.href;
  const u1 = encodeURIComponent(location.href);
  // const u1 = encodeURIComponent(location.origin);
  // const u1 = location.origin;
  return location.replace(`${VITE_GLOB_MIDDLE_LOGIN_URL}/cas/authorize?pid=${user.user_cas_pid}&redirect_uri=${u1}`);
};
const redirectToIAM = () => {
  const VITE_GLOB_MIDDLE_LOGIN_WANDA_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_WANDA_URL;
  user.user_cas_pid = 'parking';
  // const u1 = location.href;
  const u1 = encodeURIComponent(location.href);
  // const u1 = encodeURIComponent(location.origin);
  // const u1 = location.origin;
  return location.replace(`${VITE_GLOB_MIDDLE_LOGIN_WANDA_URL}/cas/authorize?pid=${user.user_cas_pid}&redirect_uri=${u1}`);
};

const generateCaptcha = () => {
  loginService.getCaptcha().then((res) => {
    state.captchaImg = res.data.captcha;
    form.key = res.data.key;
  });
};

const login = (formRef) => {
  formRef.validate().then(() => {
    loginService.login(form).then((res) => {
      const user_detail = res.data.user_detail;
      if (user_detail.need_update_password === 0) {
        // 无需强制修改密码
        user.$state = {
          user_id: user_detail.user_id,
          username: user_detail.username,
          name: user_detail.name,
          role_id: user_detail.role_id
        };

        ElMessage({
          message: '登录成功',
          type: 'success'
        });
        user.token = res.data.token;
        user.park_ids = user_detail.park_ids;
        user.park_names = user_detail.park_names;

        router.push('/');
      } else if (user_detail.need_update_password === 1) {
        // 首次登录，强制修改密码
        passwdForm.tip = '首次登录系统，请先修改密码';
        passwdForm.form.user_id = user_detail.user_id;
        passwdForm.passwdDialogVisible = true;
      } else if (user_detail.need_update_password === 2) {
        // 密码过期，强制修改密码
        passwdForm.form.user_id = user_detail.user_id;
        passwdForm.tip = '密码已过期，请先修改密码';
        passwdForm.passwdDialogVisible = true;
      }
    });
  });
};

// 取消修改密码
const cancelChangePasswd = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();
  passwdForm.passwdDialogVisible = false;
};

// 修改密码
const submitChangePasswd = (formRef) => {
  formRef.validate().then(() => {
    loginService.forceUpdatePassword(passwdForm.form).then((res) => {
      if (res.success) {
        ElMessage({
          message: res.message,
          type: 'success'
        });
        passwdForm.passwdDialogVisible = false;
        form.password = undefined;
        form.captcha = undefined;
        generateCaptcha();
      } else {
        ElMessage({
          message: res.detail_message,
          type: 'error'
        });
      }
    });
  });
};
</script>

<style scoped lang="scss">
.login-container {
  width: 100vw;
  min-height: 100vh;
  background-image: url(../assets/login-bg.png);
  background-size: 100% 100%;
  font-family: 'SC-Medium';

  .login-title {
    font-size: 4vw;
    color: #fff;
    height: 24vh;
    margin-top: -11%;
    text-shadow: 0px 4px 6px rgba(0, 0, 0, 0.5);

    p {
      margin-bottom: 0;
    }
  }

  .login-main {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    height: calc(100% - 20px);
    padding: 11.5vh 7.3vw 0 9.5vw;

    .login-card {
      width: 30.75vw;
      min-height: 570px;
      height: 76.85vh;
      border-radius: 8px;
      background-color: #fff;
      padding: 4.95vh 5.2vw;
    }

    .login-logo {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 5.6vh;

      .image {
        height: 46.5px;
        width: 200px;
        background-image: url(../assets/login-logo.png);
        background-size: 100% 100%;
      }
    }

    .login-form {
      :deep(.el-form-item__label) {
        color: #333 !important;
      }

      :deep(.el-form-item--large) {
        margin-bottom: 3.5vh;
      }

      .captcha-item {
        display: flex;
        justify-content: space-between;
        width: 100%;

        .input {
          width: 100%;
          margin-right: 0.5vw;
        }

        .captcha-container {
          height: 40px;
          box-shadow: 0 0 0 1px #dcdfe6;
          border-radius: 4px;
          padding: 0 1vw;
          display: flex;
          justify-content: center;
          align-items: center;
          cursor: pointer;
        }
      }

      .iam-container {
        display: flex;
        justify-content: space-between;
      }
      .iam-btn {
        // width: calc(50% - 6px);
        width: 100%;
      }

      .login-btn {
        width: 100%;
        height: 46px;
        line-height: 46px;
        background-color: #005bac;
        border-radius: 2px;
        color: #fff;
        font-size: 16px;
        text-align: center;
        cursor: pointer;
        margin-top: 4vh;

        &:hover {
          background-color: #005bace6;
        }
      }

      .login-more {
        width: 100%;
        margin-top: 1.5rem;
        display: flex;
        flex-direction: column;
        justify-content: center;
        :deep(.el-divider__text.is-center) {
          transform: translateX(-50%) translateY(-50%);
          color: #9ca0af;
        }
      }
    }
  }
}

.copyright {
  white-space: nowrap;
  color: #b1b3b8;
  text-decoration: none;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  margin-bottom: 10px;
  height: 20px;
  position: absolute;
  bottom: 0;
  left: 50%;
  /*相对于可视区窗口，距离窗口左边50%个可视区窗口*/
  right: 50%;
  font-size: 16px;
}
</style>
