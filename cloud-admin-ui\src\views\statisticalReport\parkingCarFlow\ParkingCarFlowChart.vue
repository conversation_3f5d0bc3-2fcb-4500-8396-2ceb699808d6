<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-30 16:32:49
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\statisticalReport\parkingCarFlow\ParkingCarFlowChart.vue
 * @Description: {}
-->
<template>
  <el-card class="table" shadow="never">
    <div class="chart" ref="chartDom"></div>
    <div class="tool">
      <template v-if="radioType === '2'">
        <span>{{ curSelectInfo[curDataLevel].organizational_structure }}进出流量总数对比</span>
      </template>
      <template v-if="radioType === '3'">
        <span>{{ curSelectInfo[curDataLevel].organizational_structure }}流量趋势对比</span>
      </template>
      <template v-if="radioType === '4'">
        <span>{{ curSelectInfo[curDataLevel].organizational_structure }}每小时流量趋势对比</span>
      </template>
      <template v-if="radioType !== '2'">
        <el-radio-group v-model="inOrout" size="small" @change="changeInOrOut" style="margin-left: 10px">
          <el-radio-button label="进场流量" value="car_in_number" />
          <el-radio-button label="出场流量" value="car_out_number" />
        </el-radio-group>
      </template>
    </div>
  </el-card>
  <!-- <el-empty description="description"/> -->
</template>

<script name="ParkingCarFlowTable" setup>
import { onMounted, reactive, ref, watch, markRaw } from 'vue';
import * as echarts from 'echarts';
import { dayjs, ElMessage } from 'element-plus';
import parkingCarFlowService from '@/service/statisticalReport/ParkingCarFlowService';

const props = defineProps({ radioType: String });
const barData = ref([]);
const levelToKey = ['top', 'region', 'company', 'square'];
const curDataLevel = ref(0);
const curSelectInfo = ref([{ organizational_structure: '一级架构' }]);
const dataLevelMap = reactive({
  top: [],
  region: [],
  company: [],
  square: []
});
const trendData = ref([]);
const timeData = ref([]);
const loading = ref(false);
const chartDom = ref(null);
const chart = ref(null);
const data = reactive({
  queryParams: {},
  params: {}
});
const inOrout = ref('car_in_number');
const lineChartOption = ref({
  xAxis: {
    type: 'category',
    data: ['2021-01-01', '2021-01-02', '2021-01-03', '2021-01-04', '2021-01-05', '2021-01-06', '2021-01-07']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      data: [150, 230, 224, 200, 122, 147, 260],
      type: 'line',
      name: '11'
    },
    {
      data: [22, 33, 114, 218, 135, 44, 55],
      type: 'line',
      name: '22'
    }
  ]
});
const barChartOption = ref({
  title: {
    show: false,
    text: ''
  },
  tooltip: {
    trigger: 'axis'
  },
  grid: {
    top: '40',
    right: '10',
    left: '10',
    bottom: '30',
    containLabel: true
  },
  dataGroupId: '',
  animationDurationUpdate: 500,
  xAxis: {
    //坐标轴线
    axisLine: {
      show: false
    },
    //坐标轴刻度相关设置
    axisTick: {
      show: false,
      interval: 0
    },
    //坐标轴刻度标签的相关设置
    axisLabel: {
      interval: 0,
      align: 'center',
      rotate: '20',
      margin: '36'
    },
    triggerEvent: true,
    data: ['东区公司', '南区公司', '西区公司', '北区公司']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '进场流量',
      type: 'bar',
      barWidth: 35,
      //   itemStyle: {
      //     color: '#04e9ee'
      //   },
      universalTransition: {
        //开启全局动画过度策略
        enabled: true,
        divideShape: 'clone'
      },
      data: [
        {
          value: 1,
          groupId: '1'
        },
        {
          value: 2,
          groupId: '2'
        },
        {
          value: 3,
          groupId: '3'
        },
        {
          value: 4,
          groupId: '4'
        },
        {
          value: 5,
          groupId: '5'
        },
        {
          value: 6,
          groupId: '6'
        },
        {
          value: 7,
          groupId: '7'
        }
      ]
    },
    {
      name: '出场流量',
      type: 'bar',
      barWidth: 35,
      //   itemStyle: {
      //     color: '#04e9ee'
      //   },
      universalTransition: {
        //开启全局动画过度策略
        enabled: true,
        divideShape: 'clone'
      },
      data: [
        {
          value: 1,
          groupId: '1'
        },
        {
          value: 2,
          groupId: '2'
        },
        {
          value: 3,
          groupId: '3'
        },
        {
          value: 4,
          groupId: '4'
        },
        {
          value: 5,
          groupId: '5'
        },
        {
          value: 6,
          groupId: '6'
        },
        {
          value: 7,
          groupId: '7'
        }
      ]
    }
  ]
});

onMounted(() => {
  // setTimeout(() => {
  initChart();
  // }, 1000);
});

watch(
  () => props.radioType,
  () => {
    console.log('props.radioType', props.radioType);
    curDataLevel.value = 0;
    // if (props.radioType === '2') {
    //   getData(data.params);
    //   // chart.value && chart.value.setOption(barChartOption.value);
    // } else {
    //   chart.value && chart.value.setOption(lineChartOption.value);
    // }
  }
);
const initChart = () => {
  chart.value = markRaw(echarts.init(chartDom.value));

  chart.value.on('click', function (event) {
    if (event.data) {
      console.log(event.data);
      if (curDataLevel.value < 3) {
        curSelectInfo.value[curDataLevel.value + 1] = event.data;
        curDataLevel.value++;
        getData(data.params);
      }
    }
  });
};

const updateOption = (data) => {
  let option;
  if (props.radioType === '2') {
    option = {
      title: {
        show: false,
        text: ''
      },
      legend: {
        show: true
      },
      tooltip: {
        show: true
      },
      grid: {
        top: '40',
        right: '10',
        left: '10',
        // bottom: '30',
        containLabel: true
      },
      dataGroupId: '',
      animationDurationUpdate: 500,
      xAxis: {
        //坐标轴线
        axisLine: {
          show: false
        },
        //坐标轴刻度相关设置
        axisTick: {
          show: false,
          interval: 0
        },
        //坐标轴刻度标签的相关设置
        axisLabel: {
          interval: 0,
          // align: 'center',
          rotate: '20',
          margin: '20'
        },
        triggerEvent: true,
        data: data.map((i) => i.organizational_structure)
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '进场流量',
          type: 'bar',
          barWidth: 35,
          //   itemStyle: {
          //     color: '#04e9ee'
          //   },
          universalTransition: {
            //开启全局动画过度策略
            enabled: true,
            divideShape: 'clone'
          },
          data: data.map((i) => ({ ...i, value: i.car_in_number }))
        },
        {
          name: '出场流量',
          type: 'bar',
          barWidth: 35,
          //   itemStyle: {
          //     color: '#04e9ee'
          //   },
          universalTransition: {
            //开启全局动画过度策略
            enabled: true,
            divideShape: 'clone'
          },
          data: data.map((i) => ({ ...i, value: i.car_out_number }))
        }
      ]
    };
  } else if (props.radioType === '3') {
    let time = [];
    Object.keys(data)
      .map((key) => data[key].map((i) => i.day_time))
      .map((item) => {
        if (item.length > time.length) {
          time = item;
        }
      });
    const series = Object.keys(data).map((key) => ({
      name: key,
      type: 'line',
      data: data[key].map((i) => ({ ...i, value: i[inOrout.value] }))
    }));
    option = {
      legend: {
        show: true
      },
      tooltip: {
        trigger: 'axis',
        show: true
      },
      xAxis: {
        type: 'category',
        data: time
      },
      yAxis: {
        type: 'value'
      },
      series: series
    };
  } else if (props.radioType === '4') {
    const series = Object.keys(data).map((key) => {
      const ret = {
        name: key,
        type: 'line',
        data: new Array(24).fill(0)
      };
      data[key].map((i) => {
        ret.data[i.hour_period] = { ...i, value: i[inOrout.value] };
      });
      return ret;
    });

    option = {
      legend: {
        show: true
      },
      tooltip: {
        trigger: 'axis',
        show: true
      },
      xAxis: {
        type: 'category',
        data: [
          '00:00',
          '01:00',
          '02:00',
          '03:00',
          '04:00',
          '05:00',
          '06:00',
          '07:00',
          '08:00',
          '09:00',
          '10:00',
          '11:00',
          '12:00',
          '13:00',
          '14:00',
          '15:00',
          '16:00',
          '17:00',
          '18:00',
          '19:00',
          '20:00',
          '21:00',
          '22:00',
          '23:00'
        ]
      },
      yAxis: {
        type: 'value'
      },
      series: series
    };
  }

  if (curDataLevel.value !== 0) {
    option.graphic = [
      {
        type: 'text',
        left: 50,
        top: 30,
        style: {
          text: '返回上一级',
          fontSize: 14
        },
        onclick: function () {
          // chart.value.setOption(barChartOption.value, true);
          returnUpper();
        }
      }
    ];
  }
  return option;
};
// onMounted(() => {
// getList(data.queryParams);
// });

const getData = (params) => {
  loading.value = true;
  data.params = params;
  const query = {};
  if (curDataLevel.value > 0) query[levelToKey[curDataLevel.value] + '_id'] = curSelectInfo.value[curDataLevel.value].org_department_id;

  if (props.radioType === '2') {
    query.start_time = dayjs(params['datetimerange'][0]).format('YYYY-MM-DD');
    query.end_time = dayjs(params['datetimerange'][1]).format('YYYY-MM-DD');
    parkingCarFlowService.chartOrgParkTrafficFlows(query).then((response) => {
      if (response.success === true) {
        barData.value = response.data;
        dataLevelMap[levelToKey[curDataLevel.value]] = response.data;
        const option = updateOption(barData.value);
        chart.value && chart.value.setOption(option, true);
        barChartOption.value = option;
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    });
  } else if (props.radioType === '3') {
    query.start_time = dayjs(params['datetimerange'][0]).format('YYYY-MM-DD');
    query.end_time = dayjs(params['datetimerange'][1]).format('YYYY-MM-DD');
    query.hour_periods = params['hourPeriods'];
    parkingCarFlowService.chartTrendParkTrafficFlows(query).then((response) => {
      if (response.success === true) {
        trendData.value = response.data;
        dataLevelMap[levelToKey[curDataLevel.value]] = response.data;
        const option = updateOption(trendData.value);
        chart.value && chart.value.setOption(option, true);
        lineChartOption.value = option;
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    });
  } else if (props.radioType === '4') {
    // chart.value && chart.value.setOption(lineChartOption.value);
    query.start_time = dayjs(params['date']).format('YYYY-MM-DD');
    query.end_time = dayjs(params['date']).format('YYYY-MM-DD');
    parkingCarFlowService.chartPeriodParkTrafficFlows(query).then((response) => {
      if (response.success === true) {
        timeData.value = response.data;
        dataLevelMap[levelToKey[curDataLevel.value]] = response.data;
        const option = updateOption(timeData.value);
        chart.value && chart.value.setOption(option, true);
        lineChartOption.value = option;
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    });
  }
  data.queryParams = query;

  // parkingCarFlowService.pagingParkingCarFlow(params).then((response) => {
  //   if (response.success === true) {
  //     tableData.value = response.data;
  //     loading.value = false;
  //   } else {
  //     ElMessage({
  //       message: response.detail_message != '' ? response.detail_message : response.message,
  //       type: 'error'
  //     });
  //     loading.value = false;
  //   }
  // });
};

const returnUpper = () => {
  curDataLevel.value = curDataLevel.value - 1;
  const option = updateOption(dataLevelMap[levelToKey[curDataLevel.value]]);
  chart.value && chart.value.setOption(option, true);
};

const changeInOrOut = () => {
  if (props.radioType === '3') {
    const option = updateOption(trendData.value);
    chart.value && chart.value.setOption(option, true);
    lineChartOption.value = option;
  } else if (props.radioType === '4') {
    const option = updateOption(timeData.value);
    chart.value && chart.value.setOption(option, true);
    lineChartOption.value = option;
  }
};

defineExpose({
  getData
});
</script>
<style lang="scss" scoped>
.chart {
  width: 100%;
  height: calc(100vh - 300px);
  top: 30px;
}
.table {
  position: relative;
}
.tool {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
}
</style>
