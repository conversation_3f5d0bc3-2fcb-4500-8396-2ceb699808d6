<template>
  <div class="container">
    <car-out-record-search @form-search="searchCarOutRecordList" @reset="resetParamsAndData" />
    <car-out-record-table ref="table" />
  </div>
</template>

<script setup name="CarOutRecord">
import CarOutRecordSearch from './carOutRecord/CarOutRecordSearch.vue';
import CarOutRecordTable from './carOutRecord/CarOutRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchCarOutRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
