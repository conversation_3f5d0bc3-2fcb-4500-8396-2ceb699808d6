/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 修改单据并撤销商户转账
export const cancelTransfer = (orderNo) => {
  return $({
    url: '/console/pay/transfer/wx/cancelTransfer?orderNo=' + orderNo,
    method: 'post'
  });
};

// 导出微信找零列表
export const exportWxTransfer = (data) => {
  return $({
    url: '/console/pay/transfer/wx/exportWxTransfer',
    method: 'post',
    data
  });
};

// 查询微信找零列表(分页)
export const getWxTransferPage = (data) => {
  return $({
    url: '/console/pay/transfer/wx/getWxTransferPage',
    method: 'post',
    data
  });
};

// 根据找零状态统计找零
export const countTransferByState = (data) => {
  return $({
    url: '/console/pay/transfer/wx/countTransferByState',
    method: 'post',
    data
  });
};

// 重新发起商家转账
export const reTryTransfer = (transferId) => {
  return $({
    url: '/console/pay/transfer/wx/reTryTransfer?transferId=' + transferId,
    method: 'post'
  });
};

// 商户单号查询转账单
export const queryTransferByOutBillNo = (orderNo) => {
  return $({
    url: '/console/pay/transfer/wx/queryTransferByOutBillNo?orderNo=' + orderNo,
    method: 'get'
  });
};
