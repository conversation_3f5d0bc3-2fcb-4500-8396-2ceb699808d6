<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-06-26 11:31:05
 * @LastEditTime: 2024-06-27 16:18:28
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\home\single\trafficEfficiency.vue
-->
<template>
  <div class="container">
    <div class="tab-container">
      <tab-button :options="tabOptions" v-model="activeTab" @change="handleTabChange" type="simple" />
    </div>
    <div class="main-container" v-loading="loading">
      <div class="chart-warp center">
        <div class="turnover-rate-container center">
          <div class="value">{{ turnoverRate }}</div>
          <div class="title">平均周转率</div>
        </div>
      </div>
      <div class="card-warp">
        <card-item
          v-for="item in cardOptions[activeTab - 1]"
          :key="item.label"
          :label="(activeTab === 1 ? '临停' : '长租') + item.label"
          :value="truncateDecimal(resData[item.valueKey], 2) || 0"
          :unit="item.unit"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { homepageParkTrafficEfficiency } from '@/api/home/<USER>';
import { truncateDecimal } from '@/utils/utils';
import { ref } from 'vue';
import tabButton from './components/tabButton.vue';
import cardItem from './components/cardItem.vue';

const tabOptions = [
  {
    label: '临停数据',
    value: 1
  },
  {
    label: '长租数据',
    value: 2
  }
];

const cardOptions = [
  [
    {
      label: '车次',
      valueKey: 'parking_pass_amount',
      unit: '次'
    },
    {
      label: '通行时长',
      valueKey: 'parking_pass_time',
      unit: '秒'
    },
    {
      label: '平均通行效率',
      valueKey: 'parking_ratio',
      unit: '秒/次'
    }
  ],
  [
    {
      label: '车次',
      valueKey: 'rent_pass_amount',
      unit: '次'
    },
    {
      label: '通行时长',
      valueKey: 'rent_pass_time',
      unit: '秒'
    },
    {
      label: '平均通行效率',
      valueKey: 'rent_ratio',
      unit: '秒/次'
    }
  ]
];

const loading = ref(false);
const turnoverRate = ref(0);
const activeTab = ref(1);
const handleTabChange = () => {
  loading.value = true;
  turnoverRate.value = truncateDecimal(resData.value?.park_turnover_rate || 0, 2);
  setTimeout(() => {
    loading.value = false;
  }, 500);
};
const resData = ref([]);
const fetchData = async (params) => {
  loading.value = true;
  // 请求数据
  const { data: trafficData } = await homepageParkTrafficEfficiency(params);
  resData.value = trafficData;
  handleTabChange();
};

defineExpose({
  fetchData
});
</script>
<style scoped lang="scss">
.container {
  height: 100%;
  width: 100%;
  .tab-container {
    width: 60%;
    margin: 0 auto;
  }
  .main-container {
    width: 100%;
    height: calc(100% - 20px);
    display: flex;
    .chart-container {
      height: 100%;
      width: 50%;
    }
    .card-warp {
      height: 100%;
      width: 66%;
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      align-items: center;
      padding: 10px;
    }
    .chart-warp {
      width: 45%;
      height: 100%;
      .turnover-rate-container {
        width: 160px;
        height: 160px;
        background-image: url('@/assets/singleImage/turnover_bg.png');
        background-size: 100% 100%;
        color: #fff;
        flex-direction: column;
        .value {
          font-size: 24px;
          font-weight: bold;
          height: 35px;
        }
        .title {
          font-size: 14px;
        }
      }
    }
    .center {
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }
}
</style>
