import * as requestApi from '@/api/finance/TransactionReconciliation';

/**
 * 交易对账
 */
export default {
    /**
     * 停车场交易汇总
     */
    getParkTransactionSummary(data) {
        return new Promise((resolve, reject) => {
            try {
                requestApi.getParkTransactionSummary(data).then(function (res) {
                    resolve(res);
                });
            } catch (error) {
                reject(error);
            }
        });
    },
    /**
     * 各类汇总
     */
    getParkTypeSummary(data) {
        return new Promise((resolve, reject) => {
            try {
                requestApi.getParkTypeSummary(data).then(function (res) {
                    resolve(res);
                });
            } catch (error) {
                reject(error);
            }
        });
    }
};
