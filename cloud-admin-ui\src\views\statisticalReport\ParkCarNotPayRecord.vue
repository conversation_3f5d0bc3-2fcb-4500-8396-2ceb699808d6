<template>
  <div class="container">
    <park-car-not-pay-record-search @form-search="searchParkCarNotPayRecordList" @reset="resetParamsAndData" />
    <park-car-not-pay-record-table ref="table" />
  </div>
</template>

<script name="ParkCarNotPayRecord" setup>
import ParkCarNotPayRecordSearch from './parkCarNotPayRecord/ParkCarNotPayRecordSearch.vue';
import ParkCarNotPayRecordTable from './parkCarNotPayRecord/ParkCarNotPayRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkCarNotPayRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
