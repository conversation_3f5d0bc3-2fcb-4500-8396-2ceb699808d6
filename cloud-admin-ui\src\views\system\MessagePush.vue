<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-06-05 17:07:30
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\system\MessagePush.vue
 * @Description: {} 
-->
<template>
  <div class="container my-table-container">
    <MessagePushTab :radio="radio" @radioChange="radioChange" />
    <MessagePushCard :radioType="radio" />
  </div>
</template>

<script name="Menu" setup>
import MessagePushTab from './messagePush/MessagePushTab.vue';
import MessagePushCard from './messagePush/MessagePushCard.vue';
import { ref, onBeforeMount } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();

const radio = ref('1');
const routeKey = route.query.key;
const radioChange = (value) => {
  radio.value = value;
};
onBeforeMount(() => {
  if (routeKey) {
    radio.value = routeKey;
  }
});
</script>
<style lang="scss" scoped></style>
