/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找终端信息
export const pagingSentryTerminals = (data) => {
  return $({
    url: '/console/park/sentry/terminal/pagingSentryTerminals',
    method: 'post',
    data
  });
};

// 新增终端信息
export const createSentryTerminal = (data) => {
  return $({
    url: '/console/park/sentry/terminal/createSentryTerminal',
    method: 'post',
    data
  });
};

// 终端信息修改
export const updateSentryTerminal = (data) => {
  return $({
    url: '/console/park/sentry/terminal/updateSentryTerminal',
    method: 'post',
    data
  });
};

// 删除终端
export const deleteSentryTerminal = (id) => {
  return $({
    url: '/console/park/sentry/terminal/deleteSentryTerminal/' + id,
    method: 'post'
  });
};
