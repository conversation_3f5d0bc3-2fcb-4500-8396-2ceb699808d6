import{_ as $}from"./tslib-a8ebb986.js";var Jf=function(){function r(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1}return r}(),jf=function(){function r(){this.browser=new Jf,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow=typeof window<"u"}return r}(),qt=new jf;typeof wx=="object"&&typeof wx.getSystemInfoSync=="function"?(qt.wxa=!0,qt.touchEventsSupported=!0):typeof document>"u"&&typeof self<"u"?qt.worker=!0:!qt.hasGlobalWindow||"Deno"in window?(qt.node=!0,qt.svgSupported=!0):th(navigator.userAgent,qt);function th(r,t){var e=t.browser,i=r.match(/Firefox\/([\d.]+)/),n=r.match(/MSIE\s([\d.]+)/)||r.match(/Trident\/.+?rv:(([\d.]+))/),a=r.match(/Edge?\/([\d.]+)/),s=/micromessenger/i.test(r);i&&(e.firefox=!0,e.version=i[1]),n&&(e.ie=!0,e.version=n[1]),a&&(e.edge=!0,e.version=a[1],e.newEdge=+a[1].split(".")[0]>18),s&&(e.weChat=!0),t.svgSupported=typeof SVGRect<"u",t.touchEventsSupported="ontouchstart"in window&&!e.ie&&!e.edge,t.pointerEventsSupported="onpointerdown"in window&&(e.edge||e.ie&&+e.version>=11),t.domSupported=typeof document<"u";var o=document.documentElement.style;t.transform3dSupported=(e.ie&&"transition"in o||e.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in o)&&!("OTransition"in o),t.transformSupported=t.transform3dSupported||e.ie&&+e.version>=9}const nt=qt;var sa=12,lo="sans-serif",Jt=sa+"px "+lo,rh=20,eh=100,ih="007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N";function nh(r){var t={};if(typeof JSON>"u")return t;for(var e=0;e<r.length;e++){var i=String.fromCharCode(e+32),n=(r.charCodeAt(e)-rh)/eh;t[i]=n}return t}var ah=nh(ih),xe={createCanvas:function(){return typeof document<"u"&&document.createElement("canvas")},measureText:function(){var r,t;return function(e,i){if(!r){var n=xe.createCanvas();r=n&&n.getContext("2d")}if(r)return t!==i&&(t=r.font=i||Jt),r.measureText(e);e=e||"",i=i||Jt;var a=/((?:\d+)?\.?\d*)px/.exec(i),s=a&&+a[1]||sa,o=0;if(i.indexOf("mono")>=0)o=s*e.length;else for(var f=0;f<e.length;f++){var h=ah[e[f]];o+=h==null?s:h*s}return{width:o}}}(),loadImage:function(r,t,e){var i=new Image;return i.onload=t,i.onerror=e,i.src=r,i}},co=xi(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],function(r,t){return r["[object "+t+"]"]=!0,r},{}),po=xi(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],function(r,t){return r["[object "+t+"Array]"]=!0,r},{}),Qr=Object.prototype.toString,Li=Array.prototype,sh=Li.forEach,oh=Li.filter,oa=Li.slice,fh=Li.map,Pa=function(){}.constructor,ke=Pa?Pa.prototype:null,fa="__proto__",hh=2311;function go(){return hh++}function ha(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];typeof console<"u"&&console.error.apply(console,r)}function Pr(r){if(r==null||typeof r!="object")return r;var t=r,e=Qr.call(r);if(e==="[object Array]"){if(!de(r)){t=[];for(var i=0,n=r.length;i<n;i++)t[i]=Pr(r[i])}}else if(po[e]){if(!de(r)){var a=r.constructor;if(a.from)t=a.from(r);else{t=new a(r.length);for(var i=0,n=r.length;i<n;i++)t[i]=r[i]}}}else if(!co[e]&&!de(r)&&!xn(r)){t={};for(var s in r)r.hasOwnProperty(s)&&s!==fa&&(t[s]=Pr(r[s]))}return t}function zr(r,t,e){if(!Yt(t)||!Yt(r))return e?Pr(t):r;for(var i in t)if(t.hasOwnProperty(i)&&i!==fa){var n=r[i],a=t[i];Yt(a)&&Yt(n)&&!Kr(a)&&!Kr(n)&&!xn(a)&&!xn(n)&&!La(a)&&!La(n)&&!de(a)&&!de(n)?zr(n,a,e):(e||!(i in r))&&(r[i]=Pr(t[i]))}return r}function jc(r,t){for(var e=r[0],i=1,n=r.length;i<n;i++)e=zr(e,r[i],t);return e}function z(r,t){if(Object.assign)Object.assign(r,t);else for(var e in t)t.hasOwnProperty(e)&&e!==fa&&(r[e]=t[e]);return r}function Tt(r,t,e){for(var i=Y(t),n=0,a=i.length;n<a;n++){var s=i[n];(e?t[s]!=null:r[s]==null)&&(r[s]=t[s])}return r}xe.createCanvas;function Et(r,t){if(r){if(r.indexOf)return r.indexOf(t);for(var e=0,i=r.length;e<i;e++)if(r[e]===t)return e}return-1}function td(r,t){var e=r.prototype;function i(){}i.prototype=t.prototype,r.prototype=new i;for(var n in e)e.hasOwnProperty(n)&&(r.prototype[n]=e[n]);r.prototype.constructor=r,r.superClass=t}function _o(r,t,e){if(r="prototype"in r?r.prototype:r,t="prototype"in t?t.prototype:t,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(t),n=0;n<i.length;n++){var a=i[n];a!=="constructor"&&(e?t[a]!=null:r[a]==null)&&(r[a]=t[a])}else Tt(r,t,e)}function Ot(r){return!r||typeof r=="string"?!1:typeof r.length=="number"}function at(r,t,e){if(!!(r&&t))if(r.forEach&&r.forEach===sh)r.forEach(t,e);else if(r.length===+r.length)for(var i=0,n=r.length;i<n;i++)t.call(e,r[i],i,r);else for(var a in r)r.hasOwnProperty(a)&&t.call(e,r[a],a,r)}function Z(r,t,e){if(!r)return[];if(!t)return yo(r);if(r.map&&r.map===fh)return r.map(t,e);for(var i=[],n=0,a=r.length;n<a;n++)i.push(t.call(e,r[n],n,r));return i}function xi(r,t,e,i){if(!!(r&&t)){for(var n=0,a=r.length;n<a;n++)e=t.call(i,e,r[n],n,r);return e}}function Ln(r,t,e){if(!r)return[];if(!t)return yo(r);if(r.filter&&r.filter===oh)return r.filter(t,e);for(var i=[],n=0,a=r.length;n<a;n++)t.call(e,r[n],n,r)&&i.push(r[n]);return i}function rd(r,t,e){if(!!(r&&t)){for(var i=0,n=r.length;i<n;i++)if(t.call(e,r[i],i,r))return r[i]}}function Y(r){if(!r)return[];if(Object.keys)return Object.keys(r);var t=[];for(var e in r)r.hasOwnProperty(e)&&t.push(e);return t}function uh(r,t){for(var e=[],i=2;i<arguments.length;i++)e[i-2]=arguments[i];return function(){return r.apply(t,e.concat(oa.call(arguments)))}}var ed=ke&&Re(ke.bind)?ke.call.bind(ke.bind):uh;function id(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return function(){return r.apply(this,t.concat(oa.call(arguments)))}}function Kr(r){return Array.isArray?Array.isArray(r):Qr.call(r)==="[object Array]"}function Re(r){return typeof r=="function"}function kt(r){return typeof r=="string"}function nd(r){return Qr.call(r)==="[object String]"}function ce(r){return typeof r=="number"}function Yt(r){var t=typeof r;return t==="function"||!!r&&t==="object"}function La(r){return!!co[Qr.call(r)]}function vh(r){return!!po[Qr.call(r)]}function xn(r){return typeof r=="object"&&typeof r.nodeType=="number"&&typeof r.ownerDocument=="object"}function Ri(r){return r.colorStops!=null}function lh(r){return r.image!=null}function ad(r){return Qr.call(r)==="[object RegExp]"}function ch(r){return r!==r}function sd(){for(var r=[],t=0;t<arguments.length;t++)r[t]=arguments[t];for(var e=0,i=r.length;e<i;e++)if(r[e]!=null)return r[e]}function U(r,t){return r!=null?r:t}function ii(r,t,e){return r!=null?r:t!=null?t:e}function yo(r){for(var t=[],e=1;e<arguments.length;e++)t[e-1]=arguments[e];return oa.apply(r,t)}function dh(r){if(typeof r=="number")return[r,r,r,r];var t=r.length;return t===2?[r[0],r[1],r[0],r[1]]:t===3?[r[0],r[1],r[2],r[1]]:r}function xa(r,t){if(!r)throw new Error(t)}function $r(r){return r==null?null:typeof r.trim=="function"?r.trim():r.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var mo="__ec_primitive__";function od(r){r[mo]=!0}function de(r){return r[mo]}var ph=function(){function r(){this.data={}}return r.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},r.prototype.has=function(t){return this.data.hasOwnProperty(t)},r.prototype.get=function(t){return this.data[t]},r.prototype.set=function(t,e){return this.data[t]=e,this},r.prototype.keys=function(){return Y(this.data)},r.prototype.forEach=function(t){var e=this.data;for(var i in e)e.hasOwnProperty(i)&&t(e[i],i)},r}(),wo=typeof Map=="function";function gh(){return wo?new Map:new ph}var _h=function(){function r(t){var e=Kr(t);this.data=gh();var i=this;t instanceof r?t.each(n):t&&at(t,n);function n(a,s){e?i.set(a,s):i.set(s,a)}}return r.prototype.hasKey=function(t){return this.data.has(t)},r.prototype.get=function(t){return this.data.get(t)},r.prototype.set=function(t,e){return this.data.set(t,e),e},r.prototype.each=function(t,e){this.data.forEach(function(i,n){t.call(e,i,n)})},r.prototype.keys=function(){var t=this.data.keys();return wo?Array.from(t):t},r.prototype.removeKey=function(t){this.data.delete(t)},r}();function fd(r){return new _h(r)}function hd(r,t){for(var e=new r.constructor(r.length+t.length),i=0;i<r.length;i++)e[i]=r[i];for(var n=r.length,i=0;i<t.length;i++)e[i+n]=t[i];return e}function Di(r,t){var e;if(Object.create)e=Object.create(r);else{var i=function(){};i.prototype=r,e=new i}return t&&z(e,t),e}function To(r){var t=r.style;t.webkitUserSelect="none",t.userSelect="none",t.webkitTapHighlightColor="rgba(0,0,0,0)",t["-webkit-touch-callout"]="none"}function vi(r,t){return r.hasOwnProperty(t)}function br(){}var ni=180/Math.PI;function Jr(r,t){return r==null&&(r=0),t==null&&(t=0),[r,t]}function ud(r,t){return r[0]=t[0],r[1]=t[1],r}function yh(r){return[r[0],r[1]]}function vd(r,t,e){return r[0]=t,r[1]=e,r}function Ra(r,t,e){return r[0]=t[0]+e[0],r[1]=t[1]+e[1],r}function ld(r,t,e,i){return r[0]=t[0]+e[0]*i,r[1]=t[1]+e[1]*i,r}function mh(r,t,e){return r[0]=t[0]-e[0],r[1]=t[1]-e[1],r}function wh(r){return Math.sqrt(Th(r))}function Th(r){return r[0]*r[0]+r[1]*r[1]}function zi(r,t,e){return r[0]=t[0]*e,r[1]=t[1]*e,r}function bh(r,t){var e=wh(t);return e===0?(r[0]=0,r[1]=0):(r[0]=t[0]/e,r[1]=t[1]/e),r}function Rn(r,t){return Math.sqrt((r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1]))}var Sh=Rn;function Ch(r,t){return(r[0]-t[0])*(r[0]-t[0])+(r[1]-t[1])*(r[1]-t[1])}var Vr=Ch;function Mh(r,t,e,i){return r[0]=t[0]+i*(e[0]-t[0]),r[1]=t[1]+i*(e[1]-t[1]),r}function pe(r,t,e){var i=t[0],n=t[1];return r[0]=e[0]*i+e[2]*n+e[4],r[1]=e[1]*i+e[3]*n+e[5],r}function Gr(r,t,e){return r[0]=Math.min(t[0],e[0]),r[1]=Math.min(t[1],e[1]),r}function Nr(r,t,e){return r[0]=Math.max(t[0],e[0]),r[1]=Math.max(t[1],e[1]),r}var Lr=function(){function r(t,e){this.target=t,this.topTarget=e&&e.topTarget}return r}(),Ph=function(){function r(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return r.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Lr(e,t),"dragstart",t.event))},r.prototype._drag=function(t){var e=this._draggingTarget;if(e){var i=t.offsetX,n=t.offsetY,a=i-this._x,s=n-this._y;this._x=i,this._y=n,e.drift(a,s,t),this.handler.dispatchToElement(new Lr(e,t),"drag",t.event);var o=this.handler.findHover(i,n,e).target,f=this._dropTarget;this._dropTarget=o,e!==o&&(f&&o!==f&&this.handler.dispatchToElement(new Lr(f,t),"dragleave",t.event),o&&o!==f&&this.handler.dispatchToElement(new Lr(o,t),"dragenter",t.event))}},r.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Lr(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Lr(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},r}();const Lh=Ph;var xh=function(){function r(t){t&&(this._$eventProcessor=t)}return r.prototype.on=function(t,e,i,n){this._$handlers||(this._$handlers={});var a=this._$handlers;if(typeof e=="function"&&(n=i,i=e,e=null),!i||!t)return this;var s=this._$eventProcessor;e!=null&&s&&s.normalizeQuery&&(e=s.normalizeQuery(e)),a[t]||(a[t]=[]);for(var o=0;o<a[t].length;o++)if(a[t][o].h===i)return this;var f={h:i,query:e,ctx:n||this,callAtLast:i.zrEventfulCallAtLast},h=a[t].length-1,u=a[t][h];return u&&u.callAtLast?a[t].splice(h,0,f):a[t].push(f),this},r.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},r.prototype.off=function(t,e){var i=this._$handlers;if(!i)return this;if(!t)return this._$handlers={},this;if(e){if(i[t]){for(var n=[],a=0,s=i[t].length;a<s;a++)i[t][a].h!==e&&n.push(i[t][a]);i[t]=n}i[t]&&i[t].length===0&&delete i[t]}else delete i[t];return this},r.prototype.trigger=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var s=e.length,o=n.length,f=0;f<o;f++){var h=n[f];if(!(a&&a.filter&&h.query!=null&&!a.filter(t,h.query)))switch(s){case 0:h.h.call(h.ctx);break;case 1:h.h.call(h.ctx,e[0]);break;case 2:h.h.call(h.ctx,e[0],e[1]);break;default:h.h.apply(h.ctx,e);break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r.prototype.triggerWithContext=function(t){for(var e=[],i=1;i<arguments.length;i++)e[i-1]=arguments[i];if(!this._$handlers)return this;var n=this._$handlers[t],a=this._$eventProcessor;if(n)for(var s=e.length,o=e[s-1],f=n.length,h=0;h<f;h++){var u=n[h];if(!(a&&a.filter&&u.query!=null&&!a.filter(t,u.query)))switch(s){case 0:u.h.call(o);break;case 1:u.h.call(o,e[0]);break;case 2:u.h.call(o,e[0],e[1]);break;default:u.h.apply(o,e.slice(1,s-1));break}}return a&&a.afterTrigger&&a.afterTrigger(t),this},r}();const jr=xh;var Rh=Math.log(2);function Dn(r,t,e,i,n,a){var s=i+"-"+n,o=r.length;if(a.hasOwnProperty(s))return a[s];if(t===1){var f=Math.round(Math.log((1<<o)-1&~n)/Rh);return r[e][f]}for(var h=i|1<<e,u=e+1;i&1<<u;)u++;for(var v=0,l=0,c=0;l<o;l++){var p=1<<l;p&n||(v+=(c%2?-1:1)*r[e][l]*Dn(r,t-1,u,h,n|p,a),c++)}return a[s]=v,v}function Da(r,t){var e=[[r[0],r[1],1,0,0,0,-t[0]*r[0],-t[0]*r[1]],[0,0,0,r[0],r[1],1,-t[1]*r[0],-t[1]*r[1]],[r[2],r[3],1,0,0,0,-t[2]*r[2],-t[2]*r[3]],[0,0,0,r[2],r[3],1,-t[3]*r[2],-t[3]*r[3]],[r[4],r[5],1,0,0,0,-t[4]*r[4],-t[4]*r[5]],[0,0,0,r[4],r[5],1,-t[5]*r[4],-t[5]*r[5]],[r[6],r[7],1,0,0,0,-t[6]*r[6],-t[6]*r[7]],[0,0,0,r[6],r[7],1,-t[7]*r[6],-t[7]*r[7]]],i={},n=Dn(e,8,0,0,0,i);if(n!==0){for(var a=[],s=0;s<8;s++)for(var o=0;o<8;o++)a[o]==null&&(a[o]=0),a[o]+=((s+o)%2?-1:1)*Dn(e,7,s===0?1:0,1<<s,1<<o,i)/n*t[s];return function(f,h,u){var v=h*a[6]+u*a[7]+1;f[0]=(h*a[0]+u*a[1]+a[2])/v,f[1]=(h*a[3]+u*a[4]+a[5])/v}}}var Aa="___zrEVENTSAVED",$i=[];function cd(r,t,e,i,n){return An($i,t,i,n,!0)&&An(r,e,$i[0],$i[1])}function An(r,t,e,i,n){if(t.getBoundingClientRect&&nt.domSupported&&!bo(t)){var a=t[Aa]||(t[Aa]={}),s=Dh(t,a),o=Ah(s,a,n);if(o)return o(r,e,i),!0}return!1}function Dh(r,t){var e=t.markers;if(e)return e;e=t.markers=[];for(var i=["left","right"],n=["top","bottom"],a=0;a<4;a++){var s=document.createElement("div"),o=s.style,f=a%2,h=(a>>1)%2;o.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[f]+":0",n[h]+":0",i[1-f]+":auto",n[1-h]+":auto",""].join("!important;"),r.appendChild(s),e.push(s)}return e}function Ah(r,t,e){for(var i=e?"invTrans":"trans",n=t[i],a=t.srcCoords,s=[],o=[],f=!0,h=0;h<4;h++){var u=r[h].getBoundingClientRect(),v=2*h,l=u.left,c=u.top;s.push(l,c),f=f&&a&&l===a[v]&&c===a[v+1],o.push(r[h].offsetLeft,r[h].offsetTop)}return f&&n?n:(t.srcCoords=s,t[i]=e?Da(o,s):Da(s,o))}function bo(r){return r.nodeName.toUpperCase()==="CANVAS"}var Ih=/([&<>"'])/g,Eh={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Fh(r){return r==null?"":(r+"").replace(Ih,function(t,e){return Eh[e]})}var Oh=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Gi=[],kh=nt.browser.firefox&&+nt.browser.version.split(".")[0]<39;function In(r,t,e,i){return e=e||{},i?Ia(r,t,e):kh&&t.layerX!=null&&t.layerX!==t.offsetX?(e.zrX=t.layerX,e.zrY=t.layerY):t.offsetX!=null?(e.zrX=t.offsetX,e.zrY=t.offsetY):Ia(r,t,e),e}function Ia(r,t,e){if(nt.domSupported&&r.getBoundingClientRect){var i=t.clientX,n=t.clientY;if(bo(r)){var a=r.getBoundingClientRect();e.zrX=i-a.left,e.zrY=n-a.top;return}else if(An(Gi,r,i,n)){e.zrX=Gi[0],e.zrY=Gi[1];return}}e.zrX=e.zrY=0}function ua(r){return r||window.event}function St(r,t,e){if(t=ua(t),t.zrX!=null)return t;var i=t.type,n=i&&i.indexOf("touch")>=0;if(n){var s=i!=="touchend"?t.targetTouches[0]:t.changedTouches[0];s&&In(r,s,t,e)}else{In(r,t,t,e);var a=Bh(t);t.zrDelta=a?a/120:-(t.detail||0)/3}var o=t.button;return t.which==null&&o!==void 0&&Oh.test(t.type)&&(t.which=o&1?1:o&2?3:o&4?2:0),t}function Bh(r){var t=r.wheelDelta;if(t)return t;var e=r.deltaX,i=r.deltaY;if(e==null||i==null)return t;var n=Math.abs(i!==0?i:e),a=i>0?-1:i<0?1:e>0?-1:1;return 3*n*a}function Hh(r,t,e,i){r.addEventListener(t,e,i)}function zh(r,t,e,i){r.removeEventListener(t,e,i)}var $h=function(r){r.preventDefault(),r.stopPropagation(),r.cancelBubble=!0};function dd(r){return r.which===2||r.which===3}var Gh=function(){function r(){this._track=[]}return r.prototype.recognize=function(t,e,i){return this._doTrack(t,e,i),this._recognize(t)},r.prototype.clear=function(){return this._track.length=0,this},r.prototype._doTrack=function(t,e,i){var n=t.touches;if(!!n){for(var a={points:[],touches:[],target:e,event:t},s=0,o=n.length;s<o;s++){var f=n[s],h=In(i,f,{});a.points.push([h.zrX,h.zrY]),a.touches.push(f)}this._track.push(a)}},r.prototype._recognize=function(t){for(var e in Ni)if(Ni.hasOwnProperty(e)){var i=Ni[e](this._track,t);if(i)return i}},r}();function Ea(r){var t=r[1][0]-r[0][0],e=r[1][1]-r[0][1];return Math.sqrt(t*t+e*e)}function Nh(r){return[(r[0][0]+r[1][0])/2,(r[0][1]+r[1][1])/2]}var Ni={pinch:function(r,t){var e=r.length;if(!!e){var i=(r[e-1]||{}).points,n=(r[e-2]||{}).points||i;if(n&&n.length>1&&i&&i.length>1){var a=Ea(i)/Ea(n);!isFinite(a)&&(a=1),t.pinchScale=a;var s=Nh(i);return t.pinchX=s[0],t.pinchY=s[1],{type:"pinch",target:r[0].target,event:t}}}}};function Sr(){return[1,0,0,1,0,0]}function Yh(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=1,r[4]=0,r[5]=0,r}function So(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4],r[5]=t[5],r}function ge(r,t,e){var i=t[0]*e[0]+t[2]*e[1],n=t[1]*e[0]+t[3]*e[1],a=t[0]*e[2]+t[2]*e[3],s=t[1]*e[2]+t[3]*e[3],o=t[0]*e[4]+t[2]*e[5]+t[4],f=t[1]*e[4]+t[3]*e[5]+t[5];return r[0]=i,r[1]=n,r[2]=a,r[3]=s,r[4]=o,r[5]=f,r}function En(r,t,e){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r[4]=t[4]+e[0],r[5]=t[5]+e[1],r}function Co(r,t,e,i){i===void 0&&(i=[0,0]);var n=t[0],a=t[2],s=t[4],o=t[1],f=t[3],h=t[5],u=Math.sin(e),v=Math.cos(e);return r[0]=n*v+o*u,r[1]=-n*u+o*v,r[2]=a*v+f*u,r[3]=-a*u+v*f,r[4]=v*(s-i[0])+u*(h-i[1])+i[0],r[5]=v*(h-i[1])-u*(s-i[0])+i[1],r}function Mo(r,t,e){var i=e[0],n=e[1];return r[0]=t[0]*i,r[1]=t[1]*n,r[2]=t[2]*i,r[3]=t[3]*n,r[4]=t[4]*i,r[5]=t[5]*n,r}function Xh(r,t){var e=t[0],i=t[2],n=t[4],a=t[1],s=t[3],o=t[5],f=e*s-a*i;return f?(f=1/f,r[0]=s*f,r[1]=-a*f,r[2]=-i*f,r[3]=e*f,r[4]=(i*o-s*n)*f,r[5]=(a*n-e*o)*f,r):null}function pd(r){var t=Sr();return So(t,r),t}var Wh=function(){function r(t,e){this.x=t||0,this.y=e||0}return r.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},r.prototype.clone=function(){return new r(this.x,this.y)},r.prototype.set=function(t,e){return this.x=t,this.y=e,this},r.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},r.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},r.prototype.scale=function(t){this.x*=t,this.y*=t},r.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},r.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},r.prototype.dot=function(t){return this.x*t.x+this.y*t.y},r.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},r.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},r.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},r.prototype.distance=function(t){var e=this.x-t.x,i=this.y-t.y;return Math.sqrt(e*e+i*i)},r.prototype.distanceSquare=function(t){var e=this.x-t.x,i=this.y-t.y;return e*e+i*i},r.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},r.prototype.transform=function(t){if(!!t){var e=this.x,i=this.y;return this.x=t[0]*e+t[2]*i+t[4],this.y=t[1]*e+t[3]*i+t[5],this}},r.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},r.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},r.set=function(t,e,i){t.x=e,t.y=i},r.copy=function(t,e){t.x=e.x,t.y=e.y},r.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},r.lenSquare=function(t){return t.x*t.x+t.y*t.y},r.dot=function(t,e){return t.x*e.x+t.y*e.y},r.add=function(t,e,i){t.x=e.x+i.x,t.y=e.y+i.y},r.sub=function(t,e,i){t.x=e.x-i.x,t.y=e.y-i.y},r.scale=function(t,e,i){t.x=e.x*i,t.y=e.y*i},r.scaleAndAdd=function(t,e,i,n){t.x=e.x+i.x*n,t.y=e.y+i.y*n},r.lerp=function(t,e,i,n){var a=1-n;t.x=a*e.x+n*i.x,t.y=a*e.y+n*i.y},r}();const k=Wh;var Be=Math.min,He=Math.max,rr=new k,er=new k,ir=new k,nr=new k,te=new k,re=new k,qh=function(){function r(t,e,i,n){i<0&&(t=t+i,i=-i),n<0&&(e=e+n,n=-n),this.x=t,this.y=e,this.width=i,this.height=n}return r.prototype.union=function(t){var e=Be(t.x,this.x),i=Be(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=He(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=He(t.y+t.height,this.y+this.height)-i:this.height=t.height,this.x=e,this.y=i},r.prototype.applyTransform=function(t){r.applyTransform(this,this,t)},r.prototype.calculateTransform=function(t){var e=this,i=t.width/e.width,n=t.height/e.height,a=Sr();return En(a,a,[-e.x,-e.y]),Mo(a,a,[i,n]),En(a,a,[t.x,t.y]),a},r.prototype.intersect=function(t,e){if(!t)return!1;t instanceof r||(t=r.create(t));var i=this,n=i.x,a=i.x+i.width,s=i.y,o=i.y+i.height,f=t.x,h=t.x+t.width,u=t.y,v=t.y+t.height,l=!(a<f||h<n||o<u||v<s);if(e){var c=1/0,p=0,_=Math.abs(a-f),d=Math.abs(h-n),g=Math.abs(o-u),y=Math.abs(v-s),m=Math.min(_,d),w=Math.min(g,y);a<f||h<n?m>p&&(p=m,_<d?k.set(re,-_,0):k.set(re,d,0)):m<c&&(c=m,_<d?k.set(te,_,0):k.set(te,-d,0)),o<u||v<s?w>p&&(p=w,g<y?k.set(re,0,-g):k.set(re,0,y)):m<c&&(c=m,g<y?k.set(te,0,g):k.set(te,0,-y))}return e&&k.copy(e,l?te:re),l},r.prototype.contain=function(t,e){var i=this;return t>=i.x&&t<=i.x+i.width&&e>=i.y&&e<=i.y+i.height},r.prototype.clone=function(){return new r(this.x,this.y,this.width,this.height)},r.prototype.copy=function(t){r.copy(this,t)},r.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},r.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},r.prototype.isZero=function(){return this.width===0||this.height===0},r.create=function(t){return new r(t.x,t.y,t.width,t.height)},r.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},r.applyTransform=function(t,e,i){if(!i){t!==e&&r.copy(t,e);return}if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var n=i[0],a=i[3],s=i[4],o=i[5];t.x=e.x*n+s,t.y=e.y*a+o,t.width=e.width*n,t.height=e.height*a,t.width<0&&(t.x+=t.width,t.width=-t.width),t.height<0&&(t.y+=t.height,t.height=-t.height);return}rr.x=ir.x=e.x,rr.y=nr.y=e.y,er.x=nr.x=e.x+e.width,er.y=ir.y=e.y+e.height,rr.transform(i),nr.transform(i),er.transform(i),ir.transform(i),t.x=Be(rr.x,er.x,ir.x,nr.x),t.y=Be(rr.y,er.y,ir.y,nr.y);var f=He(rr.x,er.x,ir.x,nr.x),h=He(rr.y,er.y,ir.y,nr.y);t.width=f-t.x,t.height=h-t.y},r}();const X=qh;var Po="silent";function Uh(r,t,e){return{type:r,event:e,target:t.target,topTarget:t.topTarget,cancelBubble:!1,offsetX:e.zrX,offsetY:e.zrY,gestureEvent:e.gestureEvent,pinchX:e.pinchX,pinchY:e.pinchY,pinchScale:e.pinchScale,wheelDelta:e.zrDelta,zrByTouch:e.zrByTouch,which:e.which,stop:Vh}}function Vh(){$h(this.event)}var Zh=function(r){$(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.handler=null,e}return t.prototype.dispose=function(){},t.prototype.setCursor=function(){},t}(jr),ee=function(){function r(t,e){this.x=t,this.y=e}return r}(),Kh=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Yi=new X(0,0,0,0),Lo=function(r){$(t,r);function t(e,i,n,a,s){var o=r.call(this)||this;return o._hovered=new ee(0,0),o.storage=e,o.painter=i,o.painterRoot=a,o._pointerSize=s,n=n||new Zh,o.proxy=null,o.setHandlerProxy(n),o._draggingMgr=new Lh(o),o}return t.prototype.setHandlerProxy=function(e){this.proxy&&this.proxy.dispose(),e&&(at(Kh,function(i){e.on&&e.on(i,this[i],this)},this),e.handler=this),this.proxy=e},t.prototype.mousemove=function(e){var i=e.zrX,n=e.zrY,a=xo(this,i,n),s=this._hovered,o=s.target;o&&!o.__zr&&(s=this.findHover(s.x,s.y),o=s.target);var f=this._hovered=a?new ee(i,n):this.findHover(i,n),h=f.target,u=this.proxy;u.setCursor&&u.setCursor(h?h.cursor:"default"),o&&h!==o&&this.dispatchToElement(s,"mouseout",e),this.dispatchToElement(f,"mousemove",e),h&&h!==o&&this.dispatchToElement(f,"mouseover",e)},t.prototype.mouseout=function(e){var i=e.zrEventControl;i!=="only_globalout"&&this.dispatchToElement(this._hovered,"mouseout",e),i!=="no_globalout"&&this.trigger("globalout",{type:"globalout",event:e})},t.prototype.resize=function(){this._hovered=new ee(0,0)},t.prototype.dispatch=function(e,i){var n=this[e];n&&n.call(this,i)},t.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},t.prototype.setCursorStyle=function(e){var i=this.proxy;i.setCursor&&i.setCursor(e)},t.prototype.dispatchToElement=function(e,i,n){e=e||{};var a=e.target;if(!(a&&a.silent)){for(var s="on"+i,o=Uh(i,e,n);a&&(a[s]&&(o.cancelBubble=!!a[s].call(a,o)),a.trigger(i,o),a=a.__hostTarget?a.__hostTarget:a.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(i,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer(function(f){typeof f[s]=="function"&&f[s].call(f,o),f.trigger&&f.trigger(i,o)}))}},t.prototype.findHover=function(e,i,n){var a=this.storage.getDisplayList(),s=new ee(e,i);if(Fa(a,s,e,i,n),this._pointerSize&&!s.target){for(var o=[],f=this._pointerSize,h=f/2,u=new X(e-h,i-h,f,f),v=a.length-1;v>=0;v--){var l=a[v];l!==n&&!l.ignore&&!l.ignoreCoarsePointer&&(!l.parent||!l.parent.ignoreCoarsePointer)&&(Yi.copy(l.getBoundingRect()),l.transform&&Yi.applyTransform(l.transform),Yi.intersect(u)&&o.push(l))}if(o.length)for(var c=4,p=Math.PI/12,_=Math.PI*2,d=0;d<h;d+=c)for(var g=0;g<_;g+=p){var y=e+d*Math.cos(g),m=i+d*Math.sin(g);if(Fa(o,s,y,m,n),s.target)return s}}return s},t.prototype.processGesture=function(e,i){this._gestureMgr||(this._gestureMgr=new Gh);var n=this._gestureMgr;i==="start"&&n.clear();var a=n.recognize(e,this.findHover(e.zrX,e.zrY,null).target,this.proxy.dom);if(i==="end"&&n.clear(),a){var s=a.type;e.gestureEvent=s;var o=new ee;o.target=a.target,this.dispatchToElement(o,s,a.event)}},t}(jr);at(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],function(r){Lo.prototype[r]=function(t){var e=t.zrX,i=t.zrY,n=xo(this,e,i),a,s;if((r!=="mouseup"||!n)&&(a=this.findHover(e,i),s=a.target),r==="mousedown")this._downEl=s,this._downPoint=[t.zrX,t.zrY],this._upEl=s;else if(r==="mouseup")this._upEl=s;else if(r==="click"){if(this._downEl!==this._upEl||!this._downPoint||Sh(this._downPoint,[t.zrX,t.zrY])>4)return;this._downPoint=null}this.dispatchToElement(a,r,t)}});function Qh(r,t,e){if(r[r.rectHover?"rectContain":"contain"](t,e)){for(var i=r,n=void 0,a=!1;i;){if(i.ignoreClip&&(a=!0),!a){var s=i.getClipPath();if(s&&!s.contain(t,e))return!1}i.silent&&(n=!0);var o=i.__hostTarget;i=o||i.parent}return n?Po:!0}return!1}function Fa(r,t,e,i,n){for(var a=r.length-1;a>=0;a--){var s=r[a],o=void 0;if(s!==n&&!s.ignore&&(o=Qh(s,e,i))&&(!t.topTarget&&(t.topTarget=s),o!==Po)){t.target=s;break}}}function xo(r,t,e){var i=r.painter;return t<0||t>i.getWidth()||e<0||e>i.getHeight()}const Jh=Lo;var Ro=32,ie=7;function jh(r){for(var t=0;r>=Ro;)t|=r&1,r>>=1;return r+t}function Oa(r,t,e,i){var n=t+1;if(n===e)return 1;if(i(r[n++],r[t])<0){for(;n<e&&i(r[n],r[n-1])<0;)n++;tu(r,t,n)}else for(;n<e&&i(r[n],r[n-1])>=0;)n++;return n-t}function tu(r,t,e){for(e--;t<e;){var i=r[t];r[t++]=r[e],r[e--]=i}}function ka(r,t,e,i,n){for(i===t&&i++;i<e;i++){for(var a=r[i],s=t,o=i,f;s<o;)f=s+o>>>1,n(a,r[f])<0?o=f:s=f+1;var h=i-s;switch(h){case 3:r[s+3]=r[s+2];case 2:r[s+2]=r[s+1];case 1:r[s+1]=r[s];break;default:for(;h>0;)r[s+h]=r[s+h-1],h--}r[s]=a}}function Xi(r,t,e,i,n,a){var s=0,o=0,f=1;if(a(r,t[e+n])>0){for(o=i-n;f<o&&a(r,t[e+n+f])>0;)s=f,f=(f<<1)+1,f<=0&&(f=o);f>o&&(f=o),s+=n,f+=n}else{for(o=n+1;f<o&&a(r,t[e+n-f])<=0;)s=f,f=(f<<1)+1,f<=0&&(f=o);f>o&&(f=o);var h=s;s=n-f,f=n-h}for(s++;s<f;){var u=s+(f-s>>>1);a(r,t[e+u])>0?s=u+1:f=u}return f}function Wi(r,t,e,i,n,a){var s=0,o=0,f=1;if(a(r,t[e+n])<0){for(o=n+1;f<o&&a(r,t[e+n-f])<0;)s=f,f=(f<<1)+1,f<=0&&(f=o);f>o&&(f=o);var h=s;s=n-f,f=n-h}else{for(o=i-n;f<o&&a(r,t[e+n+f])>=0;)s=f,f=(f<<1)+1,f<=0&&(f=o);f>o&&(f=o),s+=n,f+=n}for(s++;s<f;){var u=s+(f-s>>>1);a(r,t[e+u])<0?f=u:s=u+1}return f}function ru(r,t){var e=ie,i,n,a=0,s=[];i=[],n=[];function o(c,p){i[a]=c,n[a]=p,a+=1}function f(){for(;a>1;){var c=a-2;if(c>=1&&n[c-1]<=n[c]+n[c+1]||c>=2&&n[c-2]<=n[c]+n[c-1])n[c-1]<n[c+1]&&c--;else if(n[c]>n[c+1])break;u(c)}}function h(){for(;a>1;){var c=a-2;c>0&&n[c-1]<n[c+1]&&c--,u(c)}}function u(c){var p=i[c],_=n[c],d=i[c+1],g=n[c+1];n[c]=_+g,c===a-3&&(i[c+1]=i[c+2],n[c+1]=n[c+2]),a--;var y=Wi(r[d],r,p,_,0,t);p+=y,_-=y,_!==0&&(g=Xi(r[p+_-1],r,d,g,g-1,t),g!==0&&(_<=g?v(p,_,d,g):l(p,_,d,g)))}function v(c,p,_,d){var g=0;for(g=0;g<p;g++)s[g]=r[c+g];var y=0,m=_,w=c;if(r[w++]=r[m++],--d===0){for(g=0;g<p;g++)r[w+g]=s[y+g];return}if(p===1){for(g=0;g<d;g++)r[w+g]=r[m+g];r[w+d]=s[y];return}for(var S=e,b,T,C;;){b=0,T=0,C=!1;do if(t(r[m],s[y])<0){if(r[w++]=r[m++],T++,b=0,--d===0){C=!0;break}}else if(r[w++]=s[y++],b++,T=0,--p===1){C=!0;break}while((b|T)<S);if(C)break;do{if(b=Wi(r[m],s,y,p,0,t),b!==0){for(g=0;g<b;g++)r[w+g]=s[y+g];if(w+=b,y+=b,p-=b,p<=1){C=!0;break}}if(r[w++]=r[m++],--d===0){C=!0;break}if(T=Xi(s[y],r,m,d,0,t),T!==0){for(g=0;g<T;g++)r[w+g]=r[m+g];if(w+=T,m+=T,d-=T,d===0){C=!0;break}}if(r[w++]=s[y++],--p===1){C=!0;break}S--}while(b>=ie||T>=ie);if(C)break;S<0&&(S=0),S+=2}if(e=S,e<1&&(e=1),p===1){for(g=0;g<d;g++)r[w+g]=r[m+g];r[w+d]=s[y]}else{if(p===0)throw new Error;for(g=0;g<p;g++)r[w+g]=s[y+g]}}function l(c,p,_,d){var g=0;for(g=0;g<d;g++)s[g]=r[_+g];var y=c+p-1,m=d-1,w=_+d-1,S=0,b=0;if(r[w--]=r[y--],--p===0){for(S=w-(d-1),g=0;g<d;g++)r[S+g]=s[g];return}if(d===1){for(w-=p,y-=p,b=w+1,S=y+1,g=p-1;g>=0;g--)r[b+g]=r[S+g];r[w]=s[m];return}for(var T=e;;){var C=0,M=0,P=!1;do if(t(s[m],r[y])<0){if(r[w--]=r[y--],C++,M=0,--p===0){P=!0;break}}else if(r[w--]=s[m--],M++,C=0,--d===1){P=!0;break}while((C|M)<T);if(P)break;do{if(C=p-Wi(s[m],r,c,p,p-1,t),C!==0){for(w-=C,y-=C,p-=C,b=w+1,S=y+1,g=C-1;g>=0;g--)r[b+g]=r[S+g];if(p===0){P=!0;break}}if(r[w--]=s[m--],--d===1){P=!0;break}if(M=d-Xi(r[y],s,0,d,d-1,t),M!==0){for(w-=M,m-=M,d-=M,b=w+1,S=m+1,g=0;g<M;g++)r[b+g]=s[S+g];if(d<=1){P=!0;break}}if(r[w--]=r[y--],--p===0){P=!0;break}T--}while(C>=ie||M>=ie);if(P)break;T<0&&(T=0),T+=2}if(e=T,e<1&&(e=1),d===1){for(w-=p,y-=p,b=w+1,S=y+1,g=p-1;g>=0;g--)r[b+g]=r[S+g];r[w]=s[m]}else{if(d===0)throw new Error;for(S=w-(d-1),g=0;g<d;g++)r[S+g]=s[g]}}return{mergeRuns:f,forceMergeRuns:h,pushRun:o}}function eu(r,t,e,i){e||(e=0),i||(i=r.length);var n=i-e;if(!(n<2)){var a=0;if(n<Ro){a=Oa(r,e,i,t),ka(r,e,i,e+a,t);return}var s=ru(r,t),o=jh(n);do{if(a=Oa(r,e,i,t),a<o){var f=n;f>o&&(f=o),ka(r,e,e+f,e+a,t),a=f}s.pushRun(e,a),s.mergeRuns(),n-=a,e+=a}while(n!==0);s.forceMergeRuns()}}var ct=1,fe=2,Or=4,Ba=!1;function qi(){Ba||(Ba=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Ha(r,t){return r.zlevel===t.zlevel?r.z===t.z?r.z2-t.z2:r.z-t.z:r.zlevel-t.zlevel}var iu=function(){function r(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Ha}return r.prototype.traverse=function(t,e){for(var i=0;i<this._roots.length;i++)this._roots[i].traverse(t,e)},r.prototype.getDisplayList=function(t,e){e=e||!1;var i=this._displayList;return(t||!i.length)&&this.updateDisplayList(e),i},r.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,i=this._displayList,n=0,a=e.length;n<a;n++)this._updateAndAddDisplayable(e[n],null,t);i.length=this._displayListLen,eu(i,Ha)},r.prototype._updateAndAddDisplayable=function(t,e,i){if(!(t.ignore&&!i)){t.beforeUpdate(),t.update(),t.afterUpdate();var n=t.getClipPath();if(t.ignoreClip)e=null;else if(n){e?e=e.slice():e=[];for(var a=n,s=t;a;)a.parent=s,a.updateTransform(),e.push(a),s=a,a=a.getClipPath()}if(t.childrenRef){for(var o=t.childrenRef(),f=0;f<o.length;f++){var h=o[f];t.__dirty&&(h.__dirty|=ct),this._updateAndAddDisplayable(h,e,i)}t.__dirty=0}else{var u=t;e&&e.length?u.__clipPaths=e:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(qi(),u.z=0),isNaN(u.z2)&&(qi(),u.z2=0),isNaN(u.zlevel)&&(qi(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var v=t.getDecalElement&&t.getDecalElement();v&&this._updateAndAddDisplayable(v,e,i);var l=t.getTextGuideLine();l&&this._updateAndAddDisplayable(l,e,i);var c=t.getTextContent();c&&this._updateAndAddDisplayable(c,e,i)}},r.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},r.prototype.delRoot=function(t){if(t instanceof Array){for(var e=0,i=t.length;e<i;e++)this.delRoot(t[e]);return}var n=Et(this._roots,t);n>=0&&this._roots.splice(n,1)},r.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},r.prototype.getRoots=function(){return this._roots},r.prototype.dispose=function(){this._displayList=null,this._roots=null},r}();const nu=iu;var Do;Do=nt.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(r){return setTimeout(r,16)};const Fn=Do;var ai={linear:function(r){return r},quadraticIn:function(r){return r*r},quadraticOut:function(r){return r*(2-r)},quadraticInOut:function(r){return(r*=2)<1?.5*r*r:-.5*(--r*(r-2)-1)},cubicIn:function(r){return r*r*r},cubicOut:function(r){return--r*r*r+1},cubicInOut:function(r){return(r*=2)<1?.5*r*r*r:.5*((r-=2)*r*r+2)},quarticIn:function(r){return r*r*r*r},quarticOut:function(r){return 1- --r*r*r*r},quarticInOut:function(r){return(r*=2)<1?.5*r*r*r*r:-.5*((r-=2)*r*r*r-2)},quinticIn:function(r){return r*r*r*r*r},quinticOut:function(r){return--r*r*r*r*r+1},quinticInOut:function(r){return(r*=2)<1?.5*r*r*r*r*r:.5*((r-=2)*r*r*r*r+2)},sinusoidalIn:function(r){return 1-Math.cos(r*Math.PI/2)},sinusoidalOut:function(r){return Math.sin(r*Math.PI/2)},sinusoidalInOut:function(r){return .5*(1-Math.cos(Math.PI*r))},exponentialIn:function(r){return r===0?0:Math.pow(1024,r-1)},exponentialOut:function(r){return r===1?1:1-Math.pow(2,-10*r)},exponentialInOut:function(r){return r===0?0:r===1?1:(r*=2)<1?.5*Math.pow(1024,r-1):.5*(-Math.pow(2,-10*(r-1))+2)},circularIn:function(r){return 1-Math.sqrt(1-r*r)},circularOut:function(r){return Math.sqrt(1- --r*r)},circularInOut:function(r){return(r*=2)<1?-.5*(Math.sqrt(1-r*r)-1):.5*(Math.sqrt(1-(r-=2)*r)+1)},elasticIn:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),-(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)))},elasticOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),e*Math.pow(2,-10*r)*Math.sin((r-t)*(2*Math.PI)/i)+1)},elasticInOut:function(r){var t,e=.1,i=.4;return r===0?0:r===1?1:(!e||e<1?(e=1,t=i/4):t=i*Math.asin(1/e)/(2*Math.PI),(r*=2)<1?-.5*(e*Math.pow(2,10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)):e*Math.pow(2,-10*(r-=1))*Math.sin((r-t)*(2*Math.PI)/i)*.5+1)},backIn:function(r){var t=1.70158;return r*r*((t+1)*r-t)},backOut:function(r){var t=1.70158;return--r*r*((t+1)*r+t)+1},backInOut:function(r){var t=2.5949095;return(r*=2)<1?.5*(r*r*((t+1)*r-t)):.5*((r-=2)*r*((t+1)*r+t)+2)},bounceIn:function(r){return 1-ai.bounceOut(1-r)},bounceOut:function(r){return r<1/2.75?7.5625*r*r:r<2/2.75?7.5625*(r-=1.5/2.75)*r+.75:r<2.5/2.75?7.5625*(r-=2.25/2.75)*r+.9375:7.5625*(r-=2.625/2.75)*r+.984375},bounceInOut:function(r){return r<.5?ai.bounceIn(r*2)*.5:ai.bounceOut(r*2-1)*.5+.5}};const Ao=ai;var ze=Math.pow,Qt=Math.sqrt,li=1e-8,Io=1e-4,za=Qt(3),$e=1/3,Ft=Jr(),wt=Jr(),Zr=Jr();function Vt(r){return r>-li&&r<li}function Eo(r){return r>li||r<-li}function tt(r,t,e,i,n){var a=1-n;return a*a*(a*r+3*n*t)+n*n*(n*i+3*a*e)}function $a(r,t,e,i,n){var a=1-n;return 3*(((t-r)*a+2*(e-t)*n)*a+(i-e)*n*n)}function Fo(r,t,e,i,n,a){var s=i+3*(t-e)-r,o=3*(e-t*2+r),f=3*(t-r),h=r-n,u=o*o-3*s*f,v=o*f-9*s*h,l=f*f-3*o*h,c=0;if(Vt(u)&&Vt(v))if(Vt(o))a[0]=0;else{var p=-f/o;p>=0&&p<=1&&(a[c++]=p)}else{var _=v*v-4*u*l;if(Vt(_)){var d=v/u,p=-o/s+d,g=-d/2;p>=0&&p<=1&&(a[c++]=p),g>=0&&g<=1&&(a[c++]=g)}else if(_>0){var y=Qt(_),m=u*o+1.5*s*(-v+y),w=u*o+1.5*s*(-v-y);m<0?m=-ze(-m,$e):m=ze(m,$e),w<0?w=-ze(-w,$e):w=ze(w,$e);var p=(-o-(m+w))/(3*s);p>=0&&p<=1&&(a[c++]=p)}else{var S=(2*u*o-3*s*v)/(2*Qt(u*u*u)),b=Math.acos(S)/3,T=Qt(u),C=Math.cos(b),p=(-o-2*T*C)/(3*s),g=(-o+T*(C+za*Math.sin(b)))/(3*s),M=(-o+T*(C-za*Math.sin(b)))/(3*s);p>=0&&p<=1&&(a[c++]=p),g>=0&&g<=1&&(a[c++]=g),M>=0&&M<=1&&(a[c++]=M)}}return c}function Oo(r,t,e,i,n){var a=6*e-12*t+6*r,s=9*t+3*i-3*r-9*e,o=3*t-3*r,f=0;if(Vt(s)){if(Eo(a)){var h=-o/a;h>=0&&h<=1&&(n[f++]=h)}}else{var u=a*a-4*s*o;if(Vt(u))n[0]=-a/(2*s);else if(u>0){var v=Qt(u),h=(-a+v)/(2*s),l=(-a-v)/(2*s);h>=0&&h<=1&&(n[f++]=h),l>=0&&l<=1&&(n[f++]=l)}}return f}function jt(r,t,e,i,n,a){var s=(t-r)*n+r,o=(e-t)*n+t,f=(i-e)*n+e,h=(o-s)*n+s,u=(f-o)*n+o,v=(u-h)*n+h;a[0]=r,a[1]=s,a[2]=h,a[3]=v,a[4]=v,a[5]=u,a[6]=f,a[7]=i}function au(r,t,e,i,n,a,s,o,f,h,u){var v,l=.005,c=1/0,p,_,d,g;Ft[0]=f,Ft[1]=h;for(var y=0;y<1;y+=.05)wt[0]=tt(r,e,n,s,y),wt[1]=tt(t,i,a,o,y),d=Vr(Ft,wt),d<c&&(v=y,c=d);c=1/0;for(var m=0;m<32&&!(l<Io);m++)p=v-l,_=v+l,wt[0]=tt(r,e,n,s,p),wt[1]=tt(t,i,a,o,p),d=Vr(wt,Ft),p>=0&&d<c?(v=p,c=d):(Zr[0]=tt(r,e,n,s,_),Zr[1]=tt(t,i,a,o,_),g=Vr(Zr,Ft),_<=1&&g<c?(v=_,c=g):l*=.5);return u&&(u[0]=tt(r,e,n,s,v),u[1]=tt(t,i,a,o,v)),Qt(c)}function su(r,t,e,i,n,a,s,o,f){for(var h=r,u=t,v=0,l=1/f,c=1;c<=f;c++){var p=c*l,_=tt(r,e,n,s,p),d=tt(t,i,a,o,p),g=_-h,y=d-u;v+=Math.sqrt(g*g+y*y),h=_,u=d}return v}function it(r,t,e,i){var n=1-i;return n*(n*r+2*i*t)+i*i*e}function Ga(r,t,e,i){return 2*((1-i)*(t-r)+i*(e-t))}function ou(r,t,e,i,n){var a=r-2*t+e,s=2*(t-r),o=r-i,f=0;if(Vt(a)){if(Eo(s)){var h=-o/s;h>=0&&h<=1&&(n[f++]=h)}}else{var u=s*s-4*a*o;if(Vt(u)){var h=-s/(2*a);h>=0&&h<=1&&(n[f++]=h)}else if(u>0){var v=Qt(u),h=(-s+v)/(2*a),l=(-s-v)/(2*a);h>=0&&h<=1&&(n[f++]=h),l>=0&&l<=1&&(n[f++]=l)}}return f}function ko(r,t,e){var i=r+e-2*t;return i===0?.5:(r-t)/i}function ci(r,t,e,i,n){var a=(t-r)*i+r,s=(e-t)*i+t,o=(s-a)*i+a;n[0]=r,n[1]=a,n[2]=o,n[3]=o,n[4]=s,n[5]=e}function fu(r,t,e,i,n,a,s,o,f){var h,u=.005,v=1/0;Ft[0]=s,Ft[1]=o;for(var l=0;l<1;l+=.05){wt[0]=it(r,e,n,l),wt[1]=it(t,i,a,l);var c=Vr(Ft,wt);c<v&&(h=l,v=c)}v=1/0;for(var p=0;p<32&&!(u<Io);p++){var _=h-u,d=h+u;wt[0]=it(r,e,n,_),wt[1]=it(t,i,a,_);var c=Vr(wt,Ft);if(_>=0&&c<v)h=_,v=c;else{Zr[0]=it(r,e,n,d),Zr[1]=it(t,i,a,d);var g=Vr(Zr,Ft);d<=1&&g<v?(h=d,v=g):u*=.5}}return f&&(f[0]=it(r,e,n,h),f[1]=it(t,i,a,h)),Qt(v)}function hu(r,t,e,i,n,a,s){for(var o=r,f=t,h=0,u=1/s,v=1;v<=s;v++){var l=v*u,c=it(r,e,n,l),p=it(t,i,a,l),_=c-o,d=p-f;h+=Math.sqrt(_*_+d*d),o=c,f=p}return h}var uu=/cubic-bezier\(([0-9,\.e ]+)\)/;function va(r){var t=r&&uu.exec(r);if(t){var e=t[1].split(","),i=+$r(e[0]),n=+$r(e[1]),a=+$r(e[2]),s=+$r(e[3]);if(isNaN(i+n+a+s))return;var o=[];return function(f){return f<=0?0:f>=1?1:Fo(0,i,a,1,f,o)&&tt(0,n,s,1,o[0])}}}var vu=function(){function r(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||br,this.ondestroy=t.ondestroy||br,this.onrestart=t.onrestart||br,t.easing&&this.setEasing(t.easing)}return r.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),this._paused){this._pausedTime+=e;return}var i=this._life,n=t-this._startTime-this._pausedTime,a=n/i;a<0&&(a=0),a=Math.min(a,1);var s=this.easingFunc,o=s?s(a):a;if(this.onframe(o),a===1)if(this.loop){var f=n%i;this._startTime=t-f,this._pausedTime=0,this.onrestart()}else return!0;return!1},r.prototype.pause=function(){this._paused=!0},r.prototype.resume=function(){this._paused=!1},r.prototype.setEasing=function(t){this.easing=t,this.easingFunc=Re(t)?t:Ao[t]||va(t)},r}();const lu=vu;var Bo=function(){function r(t){this.value=t}return r}(),cu=function(){function r(){this._len=0}return r.prototype.insert=function(t){var e=new Bo(t);return this.insertEntry(e),e},r.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},r.prototype.remove=function(t){var e=t.prev,i=t.next;e?e.next=i:this.head=i,i?i.prev=e:this.tail=e,t.next=t.prev=null,this._len--},r.prototype.len=function(){return this._len},r.prototype.clear=function(){this.head=this.tail=null,this._len=0},r}(),du=function(){function r(t){this._list=new cu,this._maxSize=10,this._map={},this._maxSize=t}return r.prototype.put=function(t,e){var i=this._list,n=this._map,a=null;if(n[t]==null){var s=i.len(),o=this._lastRemovedEntry;if(s>=this._maxSize&&s>0){var f=i.head;i.remove(f),delete n[f.key],a=f.value,this._lastRemovedEntry=f}o?o.value=e:o=new Bo(e),o.key=t,i.insertEntry(o),n[t]=o}return a},r.prototype.get=function(t){var e=this._map[t],i=this._list;if(e!=null)return e!==i.tail&&(i.remove(e),i.insertEntry(e)),e.value},r.prototype.clear=function(){this._list.clear(),this._map={}},r.prototype.len=function(){return this._list.len()},r}();const Ai=du;var Na={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Mt(r){return r=Math.round(r),r<0?0:r>255?255:r}function pu(r){return r=Math.round(r),r<0?0:r>360?360:r}function we(r){return r<0?0:r>1?1:r}function Ui(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?Mt(parseFloat(t)/100*255):Mt(parseInt(t,10))}function Cr(r){var t=r;return t.length&&t.charAt(t.length-1)==="%"?we(parseFloat(t)/100):we(parseFloat(t))}function Vi(r,t,e){return e<0?e+=1:e>1&&(e-=1),e*6<1?r+(t-r)*e*6:e*2<1?t:e*3<2?r+(t-r)*(2/3-e)*6:r}function Zt(r,t,e){return r+(t-r)*e}function yt(r,t,e,i,n){return r[0]=t,r[1]=e,r[2]=i,r[3]=n,r}function On(r,t){return r[0]=t[0],r[1]=t[1],r[2]=t[2],r[3]=t[3],r}var Ho=new Ai(20),Ge=null;function xr(r,t){Ge&&On(Ge,t),Ge=Ho.put(r,Ge||t.slice())}function Pt(r,t){if(!!r){t=t||[];var e=Ho.get(r);if(e)return On(t,e);r=r+"";var i=r.replace(/ /g,"").toLowerCase();if(i in Na)return On(t,Na[i]),xr(r,t),t;var n=i.length;if(i.charAt(0)==="#"){if(n===4||n===5){var a=parseInt(i.slice(1,4),16);if(!(a>=0&&a<=4095)){yt(t,0,0,0,1);return}return yt(t,(a&3840)>>4|(a&3840)>>8,a&240|(a&240)>>4,a&15|(a&15)<<4,n===5?parseInt(i.slice(4),16)/15:1),xr(r,t),t}else if(n===7||n===9){var a=parseInt(i.slice(1,7),16);if(!(a>=0&&a<=16777215)){yt(t,0,0,0,1);return}return yt(t,(a&16711680)>>16,(a&65280)>>8,a&255,n===9?parseInt(i.slice(7),16)/255:1),xr(r,t),t}return}var s=i.indexOf("("),o=i.indexOf(")");if(s!==-1&&o+1===n){var f=i.substr(0,s),h=i.substr(s+1,o-(s+1)).split(","),u=1;switch(f){case"rgba":if(h.length!==4)return h.length===3?yt(t,+h[0],+h[1],+h[2],1):yt(t,0,0,0,1);u=Cr(h.pop());case"rgb":if(h.length>=3)return yt(t,Ui(h[0]),Ui(h[1]),Ui(h[2]),h.length===3?u:Cr(h[3])),xr(r,t),t;yt(t,0,0,0,1);return;case"hsla":if(h.length!==4){yt(t,0,0,0,1);return}return h[3]=Cr(h[3]),kn(h,t),xr(r,t),t;case"hsl":if(h.length!==3){yt(t,0,0,0,1);return}return kn(h,t),xr(r,t),t;default:return}}yt(t,0,0,0,1)}}function kn(r,t){var e=(parseFloat(r[0])%360+360)%360/360,i=Cr(r[1]),n=Cr(r[2]),a=n<=.5?n*(i+1):n+i-n*i,s=n*2-a;return t=t||[],yt(t,Mt(Vi(s,a,e+1/3)*255),Mt(Vi(s,a,e)*255),Mt(Vi(s,a,e-1/3)*255),1),r.length===4&&(t[3]=r[3]),t}function gu(r){if(!!r){var t=r[0]/255,e=r[1]/255,i=r[2]/255,n=Math.min(t,e,i),a=Math.max(t,e,i),s=a-n,o=(a+n)/2,f,h;if(s===0)f=0,h=0;else{o<.5?h=s/(a+n):h=s/(2-a-n);var u=((a-t)/6+s/2)/s,v=((a-e)/6+s/2)/s,l=((a-i)/6+s/2)/s;t===a?f=l-v:e===a?f=1/3+u-l:i===a&&(f=2/3+v-u),f<0&&(f+=1),f>1&&(f-=1)}var c=[f*360,h,o];return r[3]!=null&&c.push(r[3]),c}}function Ya(r,t){var e=Pt(r);if(e){for(var i=0;i<3;i++)t<0?e[i]=e[i]*(1-t)|0:e[i]=(255-e[i])*t+e[i]|0,e[i]>255?e[i]=255:e[i]<0&&(e[i]=0);return De(e,e.length===4?"rgba":"rgb")}}function gd(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){e=e||[];var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),s=t[n],o=t[a],f=i-n;return e[0]=Mt(Zt(s[0],o[0],f)),e[1]=Mt(Zt(s[1],o[1],f)),e[2]=Mt(Zt(s[2],o[2],f)),e[3]=we(Zt(s[3],o[3],f)),e}}function _d(r,t,e){if(!(!(t&&t.length)||!(r>=0&&r<=1))){var i=r*(t.length-1),n=Math.floor(i),a=Math.ceil(i),s=Pt(t[n]),o=Pt(t[a]),f=i-n,h=De([Mt(Zt(s[0],o[0],f)),Mt(Zt(s[1],o[1],f)),Mt(Zt(s[2],o[2],f)),we(Zt(s[3],o[3],f))],"rgba");return e?{color:h,leftIndex:n,rightIndex:a,value:i}:h}}function yd(r,t,e,i){var n=Pt(r);if(r)return n=gu(n),t!=null&&(n[0]=pu(t)),e!=null&&(n[1]=Cr(e)),i!=null&&(n[2]=Cr(i)),De(kn(n),"rgba")}function md(r,t){var e=Pt(r);if(e&&t!=null)return e[3]=we(t),De(e,"rgba")}function De(r,t){if(!(!r||!r.length)){var e=r[0]+","+r[1]+","+r[2];return(t==="rgba"||t==="hsva"||t==="hsla")&&(e+=","+r[3]),t+"("+e+")"}}function di(r,t){var e=Pt(r);return e?(.299*e[0]+.587*e[1]+.114*e[2])*e[3]/255+(1-e[3])*t:0}var Xa=new Ai(100);function _u(r){if(kt(r)){var t=Xa.get(r);return t||(t=Ya(r,-.1),Xa.put(r,t)),t}else if(Ri(r)){var e=z({},r);return e.colorStops=Z(r.colorStops,function(i){return{offset:i.offset,color:Ya(i.color,-.1)}}),e}return r}var pi=Math.round;function Te(r){var t;if(!r||r==="transparent")r="none";else if(typeof r=="string"&&r.indexOf("rgba")>-1){var e=Pt(r);e&&(r="rgb("+e[0]+","+e[1]+","+e[2]+")",t=e[3])}return{color:r,opacity:t==null?1:t}}var Wa=1e-4;function Kt(r){return r<Wa&&r>-Wa}function Ne(r){return pi(r*1e3)/1e3}function Bn(r){return pi(r*1e4)/1e4}function yu(r){return"matrix("+Ne(r[0])+","+Ne(r[1])+","+Ne(r[2])+","+Ne(r[3])+","+Bn(r[4])+","+Bn(r[5])+")"}var mu={left:"start",right:"end",center:"middle",middle:"middle"};function wu(r,t,e){return e==="top"?r+=t/2:e==="bottom"&&(r-=t/2),r}function Tu(r){return r&&(r.shadowBlur||r.shadowOffsetX||r.shadowOffsetY)}function bu(r){var t=r.style,e=r.getGlobalScale();return[t.shadowColor,(t.shadowBlur||0).toFixed(2),(t.shadowOffsetX||0).toFixed(2),(t.shadowOffsetY||0).toFixed(2),e[0],e[1]].join(",")}function zo(r){return r&&!!r.image}function Su(r){return r&&!!r.svgElement}function la(r){return zo(r)||Su(r)}function $o(r){return r.type==="linear"}function Go(r){return r.type==="radial"}function No(r){return r&&(r.type==="linear"||r.type==="radial")}function Ii(r){return"url(#"+r+")"}function Yo(r){var t=r.getGlobalScale(),e=Math.max(t[0],t[1]);return Math.max(Math.ceil(Math.log(e)/Math.log(10)),1)}function Xo(r){var t=r.x||0,e=r.y||0,i=(r.rotation||0)*ni,n=U(r.scaleX,1),a=U(r.scaleY,1),s=r.skewX||0,o=r.skewY||0,f=[];return(t||e)&&f.push("translate("+t+"px,"+e+"px)"),i&&f.push("rotate("+i+")"),(n!==1||a!==1)&&f.push("scale("+n+","+a+")"),(s||o)&&f.push("skew("+pi(s*ni)+"deg, "+pi(o*ni)+"deg)"),f.join(" ")}var Cu=function(){return nt.hasGlobalWindow&&Re(window.btoa)?function(r){return window.btoa(unescape(encodeURIComponent(r)))}:typeof Buffer<"u"?function(r){return Buffer.from(r).toString("base64")}:function(r){return null}}(),Hn=Array.prototype.slice;function Gt(r,t,e){return(t-r)*e+r}function Zi(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=Gt(t[a],e[a],i);return r}function Mu(r,t,e,i){for(var n=t.length,a=n&&t[0].length,s=0;s<n;s++){r[s]||(r[s]=[]);for(var o=0;o<a;o++)r[s][o]=Gt(t[s][o],e[s][o],i)}return r}function Ye(r,t,e,i){for(var n=t.length,a=0;a<n;a++)r[a]=t[a]+e[a]*i;return r}function qa(r,t,e,i){for(var n=t.length,a=n&&t[0].length,s=0;s<n;s++){r[s]||(r[s]=[]);for(var o=0;o<a;o++)r[s][o]=t[s][o]+e[s][o]*i}return r}function Pu(r,t){for(var e=r.length,i=t.length,n=e>i?t:r,a=Math.min(e,i),s=n[a-1]||{color:[0,0,0,0],offset:0},o=a;o<Math.max(e,i);o++)n.push({offset:s.offset,color:s.color.slice()})}function Lu(r,t,e){var i=r,n=t;if(!(!i.push||!n.push)){var a=i.length,s=n.length;if(a!==s){var o=a>s;if(o)i.length=s;else for(var f=a;f<s;f++)i.push(e===1?n[f]:Hn.call(n[f]))}for(var h=i[0]&&i[0].length,f=0;f<i.length;f++)if(e===1)isNaN(i[f])&&(i[f]=n[f]);else for(var u=0;u<h;u++)isNaN(i[f][u])&&(i[f][u]=n[f][u])}}function si(r){if(Ot(r)){var t=r.length;if(Ot(r[0])){for(var e=[],i=0;i<t;i++)e.push(Hn.call(r[i]));return e}return Hn.call(r)}return r}function oi(r){return r[0]=Math.floor(r[0])||0,r[1]=Math.floor(r[1])||0,r[2]=Math.floor(r[2])||0,r[3]=r[3]==null?1:r[3],"rgba("+r.join(",")+")"}function xu(r){return Ot(r&&r[0])?2:1}var Xe=0,fi=1,Wo=2,he=3,zn=4,$n=5,Ua=6;function Va(r){return r===zn||r===$n}function We(r){return r===fi||r===Wo}var ne=[0,0,0,0],Ru=function(){function r(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return r.prototype.isFinished=function(){return this._finished},r.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},r.prototype.needsAnimate=function(){return this.keyframes.length>=1},r.prototype.getAdditiveTrack=function(){return this._additiveTrack},r.prototype.addKeyframe=function(t,e,i){this._needsSort=!0;var n=this.keyframes,a=n.length,s=!1,o=Ua,f=e;if(Ot(e)){var h=xu(e);o=h,(h===1&&!ce(e[0])||h===2&&!ce(e[0][0]))&&(s=!0)}else if(ce(e)&&!ch(e))o=Xe;else if(kt(e))if(!isNaN(+e))o=Xe;else{var u=Pt(e);u&&(f=u,o=he)}else if(Ri(e)){var v=z({},f);v.colorStops=Z(e.colorStops,function(c){return{offset:c.offset,color:Pt(c.color)}}),$o(e)?o=zn:Go(e)&&(o=$n),f=v}a===0?this.valType=o:(o!==this.valType||o===Ua)&&(s=!0),this.discrete=this.discrete||s;var l={time:t,value:f,rawValue:e,percent:0};return i&&(l.easing=i,l.easingFunc=Re(i)?i:Ao[i]||va(i)),n.push(l),l},r.prototype.prepare=function(t,e){var i=this.keyframes;this._needsSort&&i.sort(function(_,d){return _.time-d.time});for(var n=this.valType,a=i.length,s=i[a-1],o=this.discrete,f=We(n),h=Va(n),u=0;u<a;u++){var v=i[u],l=v.value,c=s.value;v.percent=v.time/t,o||(f&&u!==a-1?Lu(l,c,n):h&&Pu(l.colorStops,c.colorStops))}if(!o&&n!==$n&&e&&this.needsAnimate()&&e.needsAnimate()&&n===e.valType&&!e._finished){this._additiveTrack=e;for(var p=i[0].value,u=0;u<a;u++)n===Xe?i[u].additiveValue=i[u].value-p:n===he?i[u].additiveValue=Ye([],i[u].value,p,-1):We(n)&&(i[u].additiveValue=n===fi?Ye([],i[u].value,p,-1):qa([],i[u].value,p,-1))}},r.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var i=this._additiveTrack!=null,n=i?"additiveValue":"value",a=this.valType,s=this.keyframes,o=s.length,f=this.propName,h=a===he,u,v=this._lastFr,l=Math.min,c,p;if(o===1)c=p=s[0];else{if(e<0)u=0;else if(e<this._lastFrP){var _=l(v+1,o-1);for(u=_;u>=0&&!(s[u].percent<=e);u--);u=l(u,o-2)}else{for(u=v;u<o&&!(s[u].percent>e);u++);u=l(u-1,o-2)}p=s[u+1],c=s[u]}if(!!(c&&p)){this._lastFr=u,this._lastFrP=e;var d=p.percent-c.percent,g=d===0?1:l((e-c.percent)/d,1);p.easingFunc&&(g=p.easingFunc(g));var y=i?this._additiveValue:h?ne:t[f];if((We(a)||h)&&!y&&(y=this._additiveValue=[]),this.discrete)t[f]=g<1?c.rawValue:p.rawValue;else if(We(a))a===fi?Zi(y,c[n],p[n],g):Mu(y,c[n],p[n],g);else if(Va(a)){var m=c[n],w=p[n],S=a===zn;t[f]={type:S?"linear":"radial",x:Gt(m.x,w.x,g),y:Gt(m.y,w.y,g),colorStops:Z(m.colorStops,function(T,C){var M=w.colorStops[C];return{offset:Gt(T.offset,M.offset,g),color:oi(Zi([],T.color,M.color,g))}}),global:w.global},S?(t[f].x2=Gt(m.x2,w.x2,g),t[f].y2=Gt(m.y2,w.y2,g)):t[f].r=Gt(m.r,w.r,g)}else if(h)Zi(y,c[n],p[n],g),i||(t[f]=oi(y));else{var b=Gt(c[n],p[n],g);i?this._additiveValue=b:t[f]=b}i&&this._addToTarget(t)}}},r.prototype._addToTarget=function(t){var e=this.valType,i=this.propName,n=this._additiveValue;e===Xe?t[i]=t[i]+n:e===he?(Pt(t[i],ne),Ye(ne,ne,n,1),t[i]=oi(ne)):e===fi?Ye(t[i],t[i],n,1):e===Wo&&qa(t[i],t[i],n,1)},r}(),ca=function(){function r(t,e,i,n){if(this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&n){ha("Can' use additive animation on looped animation.");return}this._additiveAnimators=n,this._allowDiscrete=i}return r.prototype.getMaxTime=function(){return this._maxTime},r.prototype.getDelay=function(){return this._delay},r.prototype.getLoop=function(){return this._loop},r.prototype.getTarget=function(){return this._target},r.prototype.changeTarget=function(t){this._target=t},r.prototype.when=function(t,e,i){return this.whenWithKeys(t,e,Y(e),i)},r.prototype.whenWithKeys=function(t,e,i,n){for(var a=this._tracks,s=0;s<i.length;s++){var o=i[s],f=a[o];if(!f){f=a[o]=new Ru(o);var h=void 0,u=this._getAdditiveTrack(o);if(u){var v=u.keyframes,l=v[v.length-1];h=l&&l.value,u.valType===he&&h&&(h=oi(h))}else h=this._target[o];if(h==null)continue;t>0&&f.addKeyframe(0,si(h),n),this._trackKeys.push(o)}f.addKeyframe(t,si(e[o]),n)}return this._maxTime=Math.max(this._maxTime,t),this},r.prototype.pause=function(){this._clip.pause(),this._paused=!0},r.prototype.resume=function(){this._clip.resume(),this._paused=!1},r.prototype.isPaused=function(){return!!this._paused},r.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},r.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,i=0;i<e;i++)t[i].call(this)},r.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var i=0;i<e.length;i++)e[i].call(this)},r.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,i=0;i<e.length;i++)t[e[i]].setFinished()},r.prototype._getAdditiveTrack=function(t){var e,i=this._additiveAnimators;if(i)for(var n=0;n<i.length;n++){var a=i[n].getTrack(t);a&&(e=a)}return e},r.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,i=[],n=this._maxTime||0,a=0;a<this._trackKeys.length;a++){var s=this._trackKeys[a],o=this._tracks[s],f=this._getAdditiveTrack(s),h=o.keyframes,u=h.length;if(o.prepare(n,f),o.needsAnimate())if(!this._allowDiscrete&&o.discrete){var v=h[u-1];v&&(e._target[o.propName]=v.rawValue),o.setFinished()}else i.push(o)}if(i.length||this._force){var l=new lu({life:n,loop:this._loop,delay:this._delay||0,onframe:function(c){e._started=2;var p=e._additiveAnimators;if(p){for(var _=!1,d=0;d<p.length;d++)if(p[d]._clip){_=!0;break}_||(e._additiveAnimators=null)}for(var d=0;d<i.length;d++)i[d].step(e._target,c);var g=e._onframeCbs;if(g)for(var d=0;d<g.length;d++)g[d](e._target,c)},ondestroy:function(){e._doneCallback()}});this._clip=l,this.animation&&this.animation.addClip(l),t&&l.setEasing(t)}else this._doneCallback();return this}},r.prototype.stop=function(t){if(!!this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},r.prototype.delay=function(t){return this._delay=t,this},r.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},r.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},r.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},r.prototype.getClip=function(){return this._clip},r.prototype.getTrack=function(t){return this._tracks[t]},r.prototype.getTracks=function(){var t=this;return Z(this._trackKeys,function(e){return t._tracks[e]})},r.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var i=this._tracks,n=this._trackKeys,a=0;a<t.length;a++){var s=i[t[a]];s&&!s.isFinished()&&(e?s.step(this._target,1):this._started===1&&s.step(this._target,0),s.setFinished())}for(var o=!0,a=0;a<n.length;a++)if(!i[n[a]].isFinished()){o=!1;break}return o&&this._abortedCallback(),o},r.prototype.saveTo=function(t,e,i){if(!!t){e=e||this._trackKeys;for(var n=0;n<e.length;n++){var a=e[n],s=this._tracks[a];if(!(!s||s.isFinished())){var o=s.keyframes,f=o[i?0:o.length-1];f&&(t[a]=si(f.rawValue))}}}},r.prototype.__changeFinalValue=function(t,e){e=e||Y(t);for(var i=0;i<e.length;i++){var n=e[i],a=this._tracks[n];if(!!a){var s=a.keyframes;if(s.length>1){var o=s.pop();a.addKeyframe(o.time,t[n]),a.prepare(this._maxTime,a.getAdditiveTrack())}}}},r}();function Yr(){return new Date().getTime()}var Du=function(r){$(t,r);function t(e){var i=r.call(this)||this;return i._running=!1,i._time=0,i._pausedTime=0,i._pauseStart=0,i._paused=!1,e=e||{},i.stage=e.stage||{},i}return t.prototype.addClip=function(e){e.animation&&this.removeClip(e),this._head?(this._tail.next=e,e.prev=this._tail,e.next=null,this._tail=e):this._head=this._tail=e,e.animation=this},t.prototype.addAnimator=function(e){e.animation=this;var i=e.getClip();i&&this.addClip(i)},t.prototype.removeClip=function(e){if(!!e.animation){var i=e.prev,n=e.next;i?i.next=n:this._head=n,n?n.prev=i:this._tail=i,e.next=e.prev=e.animation=null}},t.prototype.removeAnimator=function(e){var i=e.getClip();i&&this.removeClip(i),e.animation=null},t.prototype.update=function(e){for(var i=Yr()-this._pausedTime,n=i-this._time,a=this._head;a;){var s=a.next,o=a.step(i,n);o&&(a.ondestroy(),this.removeClip(a)),a=s}this._time=i,e||(this.trigger("frame",n),this.stage.update&&this.stage.update())},t.prototype._startLoop=function(){var e=this;this._running=!0;function i(){e._running&&(Fn(i),!e._paused&&e.update())}Fn(i)},t.prototype.start=function(){this._running||(this._time=Yr(),this._pausedTime=0,this._startLoop())},t.prototype.stop=function(){this._running=!1},t.prototype.pause=function(){this._paused||(this._pauseStart=Yr(),this._paused=!0)},t.prototype.resume=function(){this._paused&&(this._pausedTime+=Yr()-this._pauseStart,this._paused=!1)},t.prototype.clear=function(){for(var e=this._head;e;){var i=e.next;e.prev=e.next=e.animation=null,e=i}this._head=this._tail=null},t.prototype.isFinished=function(){return this._head==null},t.prototype.animate=function(e,i){i=i||{},this.start();var n=new ca(e,i.loop);return this.addAnimator(n),n},t}(jr);const Au=Du;var Iu=300,Ki=nt.domSupported,Qi=function(){var r=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],t=["touchstart","touchend","touchmove"],e={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},i=Z(r,function(n){var a=n.replace("mouse","pointer");return e.hasOwnProperty(a)?a:n});return{mouse:r,touch:t,pointer:i}}(),Za={mouse:["mousemove","mouseup"],pointer:["pointermove","pointerup"]},Ka=!1;function Gn(r){var t=r.pointerType;return t==="pen"||t==="touch"}function Eu(r){r.touching=!0,r.touchTimer!=null&&(clearTimeout(r.touchTimer),r.touchTimer=null),r.touchTimer=setTimeout(function(){r.touching=!1,r.touchTimer=null},700)}function Ji(r){r&&(r.zrByTouch=!0)}function Fu(r,t){return St(r.dom,new Ou(r,t),!0)}function qo(r,t){for(var e=t,i=!1;e&&e.nodeType!==9&&!(i=e.domBelongToZr||e!==t&&e===r.painterRoot);)e=e.parentNode;return i}var Ou=function(){function r(t,e){this.stopPropagation=br,this.stopImmediatePropagation=br,this.preventDefault=br,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY}return r}(),Ct={mousedown:function(r){r=St(this.dom,r),this.__mayPointerCapture=[r.zrX,r.zrY],this.trigger("mousedown",r)},mousemove:function(r){r=St(this.dom,r);var t=this.__mayPointerCapture;t&&(r.zrX!==t[0]||r.zrY!==t[1])&&this.__togglePointerCapture(!0),this.trigger("mousemove",r)},mouseup:function(r){r=St(this.dom,r),this.__togglePointerCapture(!1),this.trigger("mouseup",r)},mouseout:function(r){r=St(this.dom,r);var t=r.toElement||r.relatedTarget;qo(this,t)||(this.__pointerCapturing&&(r.zrEventControl="no_globalout"),this.trigger("mouseout",r))},wheel:function(r){Ka=!0,r=St(this.dom,r),this.trigger("mousewheel",r)},mousewheel:function(r){Ka||(r=St(this.dom,r),this.trigger("mousewheel",r))},touchstart:function(r){r=St(this.dom,r),Ji(r),this.__lastTouchMoment=new Date,this.handler.processGesture(r,"start"),Ct.mousemove.call(this,r),Ct.mousedown.call(this,r)},touchmove:function(r){r=St(this.dom,r),Ji(r),this.handler.processGesture(r,"change"),Ct.mousemove.call(this,r)},touchend:function(r){r=St(this.dom,r),Ji(r),this.handler.processGesture(r,"end"),Ct.mouseup.call(this,r),+new Date-+this.__lastTouchMoment<Iu&&Ct.click.call(this,r)},pointerdown:function(r){Ct.mousedown.call(this,r)},pointermove:function(r){Gn(r)||Ct.mousemove.call(this,r)},pointerup:function(r){Ct.mouseup.call(this,r)},pointerout:function(r){Gn(r)||Ct.mouseout.call(this,r)}};at(["click","dblclick","contextmenu"],function(r){Ct[r]=function(t){t=St(this.dom,t),this.trigger(r,t)}});var Nn={pointermove:function(r){Gn(r)||Nn.mousemove.call(this,r)},pointerup:function(r){Nn.mouseup.call(this,r)},mousemove:function(r){this.trigger("mousemove",r)},mouseup:function(r){var t=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",r),t&&(r.zrEventControl="only_globalout",this.trigger("mouseout",r))}};function ku(r,t){var e=t.domHandlers;nt.pointerEventsSupported?at(Qi.pointer,function(i){hi(t,i,function(n){e[i].call(r,n)})}):(nt.touchEventsSupported&&at(Qi.touch,function(i){hi(t,i,function(n){e[i].call(r,n),Eu(t)})}),at(Qi.mouse,function(i){hi(t,i,function(n){n=ua(n),t.touching||e[i].call(r,n)})}))}function Bu(r,t){nt.pointerEventsSupported?at(Za.pointer,e):nt.touchEventsSupported||at(Za.mouse,e);function e(i){function n(a){a=ua(a),qo(r,a.target)||(a=Fu(r,a),t.domHandlers[i].call(r,a))}hi(t,i,n,{capture:!0})}}function hi(r,t,e,i){r.mounted[t]=e,r.listenerOpts[t]=i,Hh(r.domTarget,t,e,i)}function ji(r){var t=r.mounted;for(var e in t)t.hasOwnProperty(e)&&zh(r.domTarget,e,t[e],r.listenerOpts[e]);r.mounted={}}var Qa=function(){function r(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e}return r}(),Hu=function(r){$(t,r);function t(e,i){var n=r.call(this)||this;return n.__pointerCapturing=!1,n.dom=e,n.painterRoot=i,n._localHandlerScope=new Qa(e,Ct),Ki&&(n._globalHandlerScope=new Qa(document,Nn)),ku(n,n._localHandlerScope),n}return t.prototype.dispose=function(){ji(this._localHandlerScope),Ki&&ji(this._globalHandlerScope)},t.prototype.setCursor=function(e){this.dom.style&&(this.dom.style.cursor=e||"default")},t.prototype.__togglePointerCapture=function(e){if(this.__mayPointerCapture=null,Ki&&+this.__pointerCapturing^+e){this.__pointerCapturing=e;var i=this._globalHandlerScope;e?Bu(this,i):ji(i)}},t}(jr);const zu=Hu;var Uo=1;nt.hasGlobalWindow&&(Uo=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var gi=Uo,Yn=.4,Xn="#333",Wn="#ccc",$u="#eee",Ja=Yh,ja=5e-5;function ar(r){return r>ja||r<-ja}var sr=[],Rr=[],tn=Sr(),rn=Math.abs,Gu=function(){function r(){}return r.prototype.getLocalTransform=function(t){return r.getLocalTransform(this,t)},r.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},r.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},r.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},r.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},r.prototype.needLocalTransform=function(){return ar(this.rotation)||ar(this.x)||ar(this.y)||ar(this.scaleX-1)||ar(this.scaleY-1)||ar(this.skewX)||ar(this.skewY)},r.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),i=this.transform;if(!(e||t)){i&&(Ja(i),this.invTransform=null);return}i=i||Sr(),e?this.getLocalTransform(i):Ja(i),t&&(e?ge(i,t,i):So(i,t)),this.transform=i,this._resolveGlobalScaleRatio(i)},r.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(e!=null&&e!==1){this.getGlobalScale(sr);var i=sr[0]<0?-1:1,n=sr[1]<0?-1:1,a=((sr[0]-i)*e+i)/sr[0]||0,s=((sr[1]-n)*e+n)/sr[1]||0;t[0]*=a,t[1]*=a,t[2]*=s,t[3]*=s}this.invTransform=this.invTransform||Sr(),Xh(this.invTransform,t)},r.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},r.prototype.setLocalTransform=function(t){if(!!t){var e=t[0]*t[0]+t[1]*t[1],i=t[2]*t[2]+t[3]*t[3],n=Math.atan2(t[1],t[0]),a=Math.PI/2+n-Math.atan2(t[3],t[2]);i=Math.sqrt(i)*Math.cos(a),e=Math.sqrt(e),this.skewX=a,this.skewY=0,this.rotation=-n,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=i,this.originX=0,this.originY=0}},r.prototype.decomposeTransform=function(){if(!!this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||Sr(),ge(Rr,t.invTransform,e),e=Rr);var i=this.originX,n=this.originY;(i||n)&&(tn[4]=i,tn[5]=n,ge(Rr,e,tn),Rr[4]-=i,Rr[5]-=n,e=Rr),this.setLocalTransform(e)}},r.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},r.prototype.transformCoordToLocal=function(t,e){var i=[t,e],n=this.invTransform;return n&&pe(i,i,n),i},r.prototype.transformCoordToGlobal=function(t,e){var i=[t,e],n=this.transform;return n&&pe(i,i,n),i},r.prototype.getLineScale=function(){var t=this.transform;return t&&rn(t[0]-1)>1e-10&&rn(t[3]-1)>1e-10?Math.sqrt(rn(t[0]*t[3]-t[2]*t[1])):1},r.prototype.copyTransform=function(t){Vo(this,t)},r.getLocalTransform=function(t,e){e=e||[];var i=t.originX||0,n=t.originY||0,a=t.scaleX,s=t.scaleY,o=t.anchorX,f=t.anchorY,h=t.rotation||0,u=t.x,v=t.y,l=t.skewX?Math.tan(t.skewX):0,c=t.skewY?Math.tan(-t.skewY):0;if(i||n||o||f){var p=i+o,_=n+f;e[4]=-p*a-l*_*s,e[5]=-_*s-c*p*a}else e[4]=e[5]=0;return e[0]=a,e[3]=s,e[1]=c*a,e[2]=l*s,h&&Co(e,e,h),e[4]+=i+u,e[5]+=n+v,e},r.initDefaultProps=function(){var t=r.prototype;t.scaleX=t.scaleY=t.globalScaleRatio=1,t.x=t.y=t.originX=t.originY=t.skewX=t.skewY=t.rotation=t.anchorX=t.anchorY=0}(),r}(),be=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];function Vo(r,t){for(var e=0;e<be.length;e++){var i=be[e];r[i]=t[i]}}const da=Gu;var ts={};function dt(r,t){t=t||Jt;var e=ts[t];e||(e=ts[t]=new Ai(500));var i=e.get(r);return i==null&&(i=xe.measureText(r,t).width,e.put(r,i)),i}function rs(r,t,e,i){var n=dt(r,t),a=Ei(t),s=ue(0,n,e),o=kr(0,a,i),f=new X(s,o,n,a);return f}function Nu(r,t,e,i){var n=((r||"")+"").split(`
`),a=n.length;if(a===1)return rs(n[0],t,e,i);for(var s=new X(0,0,0,0),o=0;o<n.length;o++){var f=rs(n[o],t,e,i);o===0?s.copy(f):s.union(f)}return s}function ue(r,t,e){return e==="right"?r-=t:e==="center"&&(r-=t/2),r}function kr(r,t,e){return e==="middle"?r-=t/2:e==="bottom"&&(r-=t),r}function Ei(r){return dt("\u56FD",r)}function Se(r,t){return typeof r=="string"?r.lastIndexOf("%")>=0?parseFloat(r)/100*t:parseFloat(r):r}function Yu(r,t,e){var i=t.position||"inside",n=t.distance!=null?t.distance:5,a=e.height,s=e.width,o=a/2,f=e.x,h=e.y,u="left",v="top";if(i instanceof Array)f+=Se(i[0],e.width),h+=Se(i[1],e.height),u=null,v=null;else switch(i){case"left":f-=n,h+=o,u="right",v="middle";break;case"right":f+=n+s,h+=o,v="middle";break;case"top":f+=s/2,h-=n,u="center",v="bottom";break;case"bottom":f+=s/2,h+=a+n,u="center";break;case"inside":f+=s/2,h+=o,u="center",v="middle";break;case"insideLeft":f+=n,h+=o,v="middle";break;case"insideRight":f+=s-n,h+=o,u="right",v="middle";break;case"insideTop":f+=s/2,h+=n,u="center";break;case"insideBottom":f+=s/2,h+=a-n,u="center",v="bottom";break;case"insideTopLeft":f+=n,h+=n;break;case"insideTopRight":f+=s-n,h+=n,u="right";break;case"insideBottomLeft":f+=n,h+=a-n,v="bottom";break;case"insideBottomRight":f+=s-n,h+=a-n,u="right",v="bottom";break}return r=r||{},r.x=f,r.y=h,r.align=u,r.verticalAlign=v,r}var en="__zr_normal__",nn=be.concat(["ignore"]),Xu=xi(be,function(r,t){return r[t]=!0,r},{ignore:!1}),Dr={},Wu=new X(0,0,0,0),pa=function(){function r(t){this.id=go(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return r.prototype._init=function(t){this.attr(t)},r.prototype.drift=function(t,e,i){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0;break}var n=this.transform;n||(n=this.transform=[1,0,0,1,0,0]),n[4]+=t,n[5]+=e,this.decomposeTransform(),this.markRedraw()},r.prototype.beforeUpdate=function(){},r.prototype.afterUpdate=function(){},r.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},r.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var i=this.textConfig,n=i.local,a=e.innerTransformable,s=void 0,o=void 0,f=!1;a.parent=n?this:null;var h=!1;if(a.copyTransform(e),i.position!=null){var u=Wu;i.layoutRect?u.copy(i.layoutRect):u.copy(this.getBoundingRect()),n||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(Dr,i,u):Yu(Dr,i,u),a.x=Dr.x,a.y=Dr.y,s=Dr.align,o=Dr.verticalAlign;var v=i.origin;if(v&&i.rotation!=null){var l=void 0,c=void 0;v==="center"?(l=u.width*.5,c=u.height*.5):(l=Se(v[0],u.width),c=Se(v[1],u.height)),h=!0,a.originX=-a.x+l+(n?0:u.x),a.originY=-a.y+c+(n?0:u.y)}}i.rotation!=null&&(a.rotation=i.rotation);var p=i.offset;p&&(a.x+=p[0],a.y+=p[1],h||(a.originX=-p[0],a.originY=-p[1]));var _=i.inside==null?typeof i.position=="string"&&i.position.indexOf("inside")>=0:i.inside,d=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),g=void 0,y=void 0,m=void 0;_&&this.canBeInsideText()?(g=i.insideFill,y=i.insideStroke,(g==null||g==="auto")&&(g=this.getInsideTextFill()),(y==null||y==="auto")&&(y=this.getInsideTextStroke(g),m=!0)):(g=i.outsideFill,y=i.outsideStroke,(g==null||g==="auto")&&(g=this.getOutsideFill()),(y==null||y==="auto")&&(y=this.getOutsideStroke(g),m=!0)),g=g||"#000",(g!==d.fill||y!==d.stroke||m!==d.autoStroke||s!==d.align||o!==d.verticalAlign)&&(f=!0,d.fill=g,d.stroke=y,d.autoStroke=m,d.align=s,d.verticalAlign=o,e.setDefaultTextStyle(d)),e.__dirty|=ct,f&&e.dirtyStyle(!0)}},r.prototype.canBeInsideText=function(){return!0},r.prototype.getInsideTextFill=function(){return"#fff"},r.prototype.getInsideTextStroke=function(t){return"#000"},r.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Wn:Xn},r.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),i=typeof e=="string"&&Pt(e);i||(i=[255,255,255,1]);for(var n=i[3],a=this.__zr.isDarkMode(),s=0;s<3;s++)i[s]=i[s]*n+(a?0:255)*(1-n);return i[3]=1,De(i,"rgba")},r.prototype.traverse=function(t,e){},r.prototype.attrKV=function(t,e){t==="textConfig"?this.setTextConfig(e):t==="textContent"?this.setTextContent(e):t==="clipPath"?this.setClipPath(e):t==="extra"?(this.extra=this.extra||{},z(this.extra,e)):this[t]=e},r.prototype.hide=function(){this.ignore=!0,this.markRedraw()},r.prototype.show=function(){this.ignore=!1,this.markRedraw()},r.prototype.attr=function(t,e){if(typeof t=="string")this.attrKV(t,e);else if(Yt(t))for(var i=t,n=Y(i),a=0;a<n.length;a++){var s=n[a];this.attrKV(s,t[s])}return this.markRedraw(),this},r.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,i=0;i<this.animators.length;i++){var n=this.animators[i],a=n.__fromStateTransition;if(!(n.getLoop()||a&&a!==en)){var s=n.targetName,o=s?e[s]:e;n.saveTo(o)}}},r.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,nn)},r.prototype._savePrimaryToNormal=function(t,e,i){for(var n=0;n<i.length;n++){var a=i[n];t[a]!=null&&!(a in e)&&(e[a]=this[a])}},r.prototype.hasState=function(){return this.currentStates.length>0},r.prototype.getState=function(t){return this.states[t]},r.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},r.prototype.clearStates=function(t){this.useState(en,!1,t)},r.prototype.useState=function(t,e,i,n){var a=t===en,s=this.hasState();if(!(!s&&a)){var o=this.currentStates,f=this.stateTransition;if(!(Et(o,t)>=0&&(e||o.length===1))){var h;if(this.stateProxy&&!a&&(h=this.stateProxy(t)),h||(h=this.states&&this.states[t]),!h&&!a){ha("State "+t+" not exists.");return}a||this.saveCurrentToNormalState(h);var u=!!(h&&h.hoverLayer||n);u&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,h,this._normalState,e,!i&&!this.__inHover&&f&&f.duration>0,f);var v=this._textContent,l=this._textGuide;return v&&v.useState(t,e,i,u),l&&l.useState(t,e,i,u),a?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!u&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ct),h}}},r.prototype.useStates=function(t,e,i){if(!t.length)this.clearStates();else{var n=[],a=this.currentStates,s=t.length,o=s===a.length;if(o){for(var f=0;f<s;f++)if(t[f]!==a[f]){o=!1;break}}if(o)return;for(var f=0;f<s;f++){var h=t[f],u=void 0;this.stateProxy&&(u=this.stateProxy(h,t)),u||(u=this.states[h]),u&&n.push(u)}var v=n[s-1],l=!!(v&&v.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0);var c=this._mergeStates(n),p=this.stateTransition;this.saveCurrentToNormalState(c),this._applyStateObj(t.join(","),c,this._normalState,!1,!e&&!this.__inHover&&p&&p.duration>0,p);var _=this._textContent,d=this._textGuide;_&&_.useStates(t,e,l),d&&d.useStates(t,e,l),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=~ct)}},r.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},r.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},r.prototype.removeState=function(t){var e=Et(this.currentStates,t);if(e>=0){var i=this.currentStates.slice();i.splice(e,1),this.useStates(i)}},r.prototype.replaceState=function(t,e,i){var n=this.currentStates.slice(),a=Et(n,t),s=Et(n,e)>=0;a>=0?s?n.splice(a,1):n[a]=e:i&&!s&&n.push(e),this.useStates(n)},r.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},r.prototype._mergeStates=function(t){for(var e={},i,n=0;n<t.length;n++){var a=t[n];z(e,a),a.textConfig&&(i=i||{},z(i,a.textConfig))}return i&&(e.textConfig=i),e},r.prototype._applyStateObj=function(t,e,i,n,a,s){var o=!(e&&n);e&&e.textConfig?(this.textConfig=z({},n?this.textConfig:i.textConfig),z(this.textConfig,e.textConfig)):o&&i.textConfig&&(this.textConfig=i.textConfig);for(var f={},h=!1,u=0;u<nn.length;u++){var v=nn[u],l=a&&Xu[v];e&&e[v]!=null?l?(h=!0,f[v]=e[v]):this[v]=e[v]:o&&i[v]!=null&&(l?(h=!0,f[v]=i[v]):this[v]=i[v])}if(!a)for(var u=0;u<this.animators.length;u++){var c=this.animators[u],p=c.targetName;c.getLoop()||c.__changeFinalValue(p?(e||i)[p]:e||i)}h&&this._transitionState(t,f,s)},r.prototype._attachComponent=function(t){if(!(t.__zr&&!t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},r.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},r.prototype.getClipPath=function(){return this._clipPath},r.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},r.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},r.prototype.getTextContent=function(){return this._textContent},r.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new da,this._attachComponent(t),this._textContent=t,this.markRedraw())},r.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),z(this.textConfig,t),this.markRedraw()},r.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},r.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},r.prototype.getTextGuideLine=function(){return this._textGuide},r.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},r.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},r.prototype.markRedraw=function(){this.__dirty|=ct;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},r.prototype.dirty=function(){this.markRedraw()},r.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,i=this._textGuide;e&&(e.__inHover=t),i&&(i.__inHover=t)},r.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.addAnimator(e[i]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},r.prototype.removeSelfFromZr=function(t){if(!!this.__zr){this.__zr=null;var e=this.animators;if(e)for(var i=0;i<e.length;i++)t.animation.removeAnimator(e[i]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},r.prototype.animate=function(t,e,i){var n=t?this[t]:this,a=new ca(n,e,i);return t&&(a.targetName=t),this.addAnimator(a,t),a},r.prototype.addAnimator=function(t,e){var i=this.__zr,n=this;t.during(function(){n.updateDuringAnimation(e)}).done(function(){var a=n.animators,s=Et(a,t);s>=0&&a.splice(s,1)}),this.animators.push(t),i&&i.animation.addAnimator(t),i&&i.wakeUp()},r.prototype.updateDuringAnimation=function(t){this.markRedraw()},r.prototype.stopAnimation=function(t,e){for(var i=this.animators,n=i.length,a=[],s=0;s<n;s++){var o=i[s];!t||t===o.scope?o.stop(e):a.push(o)}return this.animators=a,this},r.prototype.animateTo=function(t,e,i){an(this,t,e,i)},r.prototype.animateFrom=function(t,e,i){an(this,t,e,i,!0)},r.prototype._transitionState=function(t,e,i,n){for(var a=an(this,e,i,n),s=0;s<a.length;s++)a[s].__fromStateTransition=t},r.prototype.getBoundingRect=function(){return null},r.prototype.getPaintRect=function(){return null},r.initDefaultProps=function(){var t=r.prototype;t.type="element",t.name="",t.ignore=t.silent=t.isGroup=t.draggable=t.dragging=t.ignoreClip=t.__inHover=!1,t.__dirty=ct;function e(i,n,a,s){Object.defineProperty(t,i,{get:function(){if(!this[n]){var f=this[n]=[];o(this,f)}return this[n]},set:function(f){this[a]=f[0],this[s]=f[1],this[n]=f,o(this,f)}});function o(f,h){Object.defineProperty(h,0,{get:function(){return f[a]},set:function(u){f[a]=u}}),Object.defineProperty(h,1,{get:function(){return f[s]},set:function(u){f[s]=u}})}}Object.defineProperty&&(e("position","_legacyPos","x","y"),e("scale","_legacyScale","scaleX","scaleY"),e("origin","_legacyOrigin","originX","originY"))}(),r}();_o(pa,jr);_o(pa,da);function an(r,t,e,i,n){e=e||{};var a=[];Zo(r,"",r,t,e,i,a,n);var s=a.length,o=!1,f=e.done,h=e.aborted,u=function(){o=!0,s--,s<=0&&(o?f&&f():h&&h())},v=function(){s--,s<=0&&(o?f&&f():h&&h())};s||f&&f(),a.length>0&&e.during&&a[0].during(function(p,_){e.during(_)});for(var l=0;l<a.length;l++){var c=a[l];u&&c.done(u),v&&c.aborted(v),e.force&&c.duration(e.duration),c.start(e.easing)}return a}function sn(r,t,e){for(var i=0;i<e;i++)r[i]=t[i]}function qu(r){return Ot(r[0])}function Uu(r,t,e){if(Ot(t[e]))if(Ot(r[e])||(r[e]=[]),vh(t[e])){var i=t[e].length;r[e].length!==i&&(r[e]=new t[e].constructor(i),sn(r[e],t[e],i))}else{var n=t[e],a=r[e],s=n.length;if(qu(n))for(var o=n[0].length,f=0;f<s;f++)a[f]?sn(a[f],n[f],o):a[f]=Array.prototype.slice.call(n[f]);else sn(a,n,s);a.length=n.length}else r[e]=t[e]}function Vu(r,t){return r===t||Ot(r)&&Ot(t)&&Zu(r,t)}function Zu(r,t){var e=r.length;if(e!==t.length)return!1;for(var i=0;i<e;i++)if(r[i]!==t[i])return!1;return!0}function Zo(r,t,e,i,n,a,s,o){for(var f=Y(i),h=n.duration,u=n.delay,v=n.additive,l=n.setToFinal,c=!Yt(a),p=r.animators,_=[],d=0;d<f.length;d++){var g=f[d],y=i[g];if(y!=null&&e[g]!=null&&(c||a[g]))if(Yt(y)&&!Ot(y)&&!Ri(y)){if(t){o||(e[g]=y,r.updateDuringAnimation(t));continue}Zo(r,g,e[g],y,n,a&&a[g],s,o)}else _.push(g);else o||(e[g]=y,r.updateDuringAnimation(t),_.push(g))}var m=_.length;if(!v&&m)for(var w=0;w<p.length;w++){var S=p[w];if(S.targetName===t){var b=S.stopTracks(_);if(b){var T=Et(p,S);p.splice(T,1)}}}if(n.force||(_=Ln(_,function(L){return!Vu(i[L],e[L])}),m=_.length),m>0||n.force&&!s.length){var C=void 0,M=void 0,P=void 0;if(o){M={},l&&(C={});for(var w=0;w<m;w++){var g=_[w];M[g]=e[g],l?C[g]=i[g]:e[g]=i[g]}}else if(l){P={};for(var w=0;w<m;w++){var g=_[w];P[g]=si(e[g]),Uu(e,i,g)}}var S=new ca(e,!1,!1,v?Ln(p,function(R){return R.targetName===t}):null);S.targetName=t,n.scope&&(S.scope=n.scope),l&&C&&S.whenWithKeys(0,C,_),P&&S.whenWithKeys(0,P,_),S.whenWithKeys(h==null?500:h,o?M:i,_).delay(u||0),r.addAnimator(S,t),s.push(S)}}const Ko=pa;var Qo=function(r){$(t,r);function t(e){var i=r.call(this)||this;return i.isGroup=!0,i._children=[],i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.children=function(){return this._children.slice()},t.prototype.childAt=function(e){return this._children[e]},t.prototype.childOfName=function(e){for(var i=this._children,n=0;n<i.length;n++)if(i[n].name===e)return i[n]},t.prototype.childCount=function(){return this._children.length},t.prototype.add=function(e){return e&&e!==this&&e.parent!==this&&(this._children.push(e),this._doAdd(e)),this},t.prototype.addBefore=function(e,i){if(e&&e!==this&&e.parent!==this&&i&&i.parent===this){var n=this._children,a=n.indexOf(i);a>=0&&(n.splice(a,0,e),this._doAdd(e))}return this},t.prototype.replace=function(e,i){var n=Et(this._children,e);return n>=0&&this.replaceAt(i,n),this},t.prototype.replaceAt=function(e,i){var n=this._children,a=n[i];if(e&&e!==this&&e.parent!==this&&e!==a){n[i]=e,a.parent=null;var s=this.__zr;s&&a.removeSelfFromZr(s),this._doAdd(e)}return this},t.prototype._doAdd=function(e){e.parent&&e.parent.remove(e),e.parent=this;var i=this.__zr;i&&i!==e.__zr&&e.addSelfToZr(i),i&&i.refresh()},t.prototype.remove=function(e){var i=this.__zr,n=this._children,a=Et(n,e);return a<0?this:(n.splice(a,1),e.parent=null,i&&e.removeSelfFromZr(i),i&&i.refresh(),this)},t.prototype.removeAll=function(){for(var e=this._children,i=this.__zr,n=0;n<e.length;n++){var a=e[n];i&&a.removeSelfFromZr(i),a.parent=null}return e.length=0,this},t.prototype.eachChild=function(e,i){for(var n=this._children,a=0;a<n.length;a++){var s=n[a];e.call(i,s,a)}return this},t.prototype.traverse=function(e,i){for(var n=0;n<this._children.length;n++){var a=this._children[n],s=e.call(i,a);a.isGroup&&!s&&a.traverse(e,i)}return this},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.addSelfToZr(e)}},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++){var n=this._children[i];n.removeSelfFromZr(e)}},t.prototype.getBoundingRect=function(e){for(var i=new X(0,0,0,0),n=e||this._children,a=[],s=null,o=0;o<n.length;o++){var f=n[o];if(!(f.ignore||f.invisible)){var h=f.getBoundingRect(),u=f.getLocalTransform(a);u?(X.applyTransform(i,h,u),s=s||i.clone(),s.union(i)):(s=s||h.clone(),s.union(h))}}return s||i},t}(Ko);Qo.prototype.type="group";const Br=Qo;/*!
* ZRender, a high performance 2d drawing library.
*
* Copyright (c) 2013, Baidu Inc.
* All rights reserved.
*
* LICENSE
* https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
*/var ui={},Jo={};function Ku(r){delete Jo[r]}function Qu(r){if(!r)return!1;if(typeof r=="string")return di(r,1)<Yn;if(r.colorStops){for(var t=r.colorStops,e=0,i=t.length,n=0;n<i;n++)e+=di(t[n].color,1);return e/=i,e<Yn}return!1}var Ju=function(){function r(t,e,i){var n=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,i=i||{},this.dom=e,this.id=t;var a=new nu,s=i.renderer||"canvas";ui[s]||(s=Y(ui)[0]),i.useDirtyRect=i.useDirtyRect==null?!1:i.useDirtyRect;var o=new ui[s](e,a,i,t),f=i.ssr||o.ssrOnly;this.storage=a,this.painter=o;var h=!nt.node&&!nt.worker&&!f?new zu(o.getViewportRoot(),o.root):null,u=i.useCoarsePointer,v=u==null||u==="auto"?nt.touchEventsSupported:!!u,l=44,c;v&&(c=U(i.pointerSize,l)),this.handler=new Jh(a,o,h,o.root,c),this.animation=new Au({stage:{update:f?null:function(){return n._flush(!0)}}}),f||this.animation.start()}return r.prototype.add=function(t){this._disposed||!t||(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},r.prototype.remove=function(t){this._disposed||!t||(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},r.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},r.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=Qu(t))},r.prototype.getBackgroundColor=function(){return this._backgroundColor},r.prototype.setDarkMode=function(t){this._darkMode=t},r.prototype.isDarkMode=function(){return this._darkMode},r.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},r.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},r.prototype.flush=function(){this._disposed||this._flush(!1)},r.prototype._flush=function(t){var e,i=Yr();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var n=Yr();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:n-i})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},r.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},r.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},r.prototype.refreshHover=function(){this._needsRefreshHover=!0},r.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&this.painter.getType()==="canvas"&&this.painter.refreshHover())},r.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},r.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},r.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},r.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},r.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},r.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},r.prototype.on=function(t,e,i){return this._disposed||this.handler.on(t,e,i),this},r.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},r.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},r.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof Br&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},r.prototype.dispose=function(){this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,Ku(this.id))},r}();function wd(r,t){var e=new Ju(go(),r,t);return Jo[e.id]=e,e}function Td(r,t){ui[r]=t}var qn;function ju(r){if(typeof qn=="function")return qn(r)}function bd(r){qn=r}var Un=new Ai(50);function tv(r){if(typeof r=="string"){var t=Un.get(r);return t&&t.image}else return r}function ga(r,t,e,i,n){if(r)if(typeof r=="string"){if(t&&t.__zrImageSrc===r||!e)return t;var a=Un.get(r),s={hostEl:e,cb:i,cbPayload:n};return a?(t=a.image,!Fi(t)&&a.pending.push(s)):(t=xe.loadImage(r,es,es),t.__zrImageSrc=r,Un.put(r,t.__cachedImgObj={image:t,pending:[s]})),t}else return r;else return t}function es(){var r=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var t=0;t<r.pending.length;t++){var e=r.pending[t],i=e.cb;i&&i(this,e.cbPayload),e.hostEl.dirty()}r.pending.length=0}function Fi(r){return r&&r.width&&r.height}var on=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function rv(r,t,e,i,n,a){if(!e){r.text="",r.isTruncated=!1;return}var s=(t+"").split(`
`);a=jo(e,i,n,a);for(var o=!1,f={},h=0,u=s.length;h<u;h++)tf(f,s[h],a),s[h]=f.textLine,o=o||f.isTruncated;r.text=s.join(`
`),r.isTruncated=o}function jo(r,t,e,i){i=i||{};var n=z({},i);n.font=t,e=U(e,"..."),n.maxIterations=U(i.maxIterations,2);var a=n.minChar=U(i.minChar,0);n.cnCharWidth=dt("\u56FD",t);var s=n.ascCharWidth=dt("a",t);n.placeholder=U(i.placeholder,"");for(var o=r=Math.max(0,r-1),f=0;f<a&&o>=s;f++)o-=s;var h=dt(e,t);return h>o&&(e="",h=0),o=r-h,n.ellipsis=e,n.ellipsisWidth=h,n.contentWidth=o,n.containerWidth=r,n}function tf(r,t,e){var i=e.containerWidth,n=e.font,a=e.contentWidth;if(!i){r.textLine="",r.isTruncated=!1;return}var s=dt(t,n);if(s<=i){r.textLine=t,r.isTruncated=!1;return}for(var o=0;;o++){if(s<=a||o>=e.maxIterations){t+=e.ellipsis;break}var f=o===0?ev(t,a,e.ascCharWidth,e.cnCharWidth):s>0?Math.floor(t.length*a/s):0;t=t.substr(0,f),s=dt(t,n)}t===""&&(t=e.placeholder),r.textLine=t,r.isTruncated=!0}function ev(r,t,e,i){for(var n=0,a=0,s=r.length;a<s&&n<t;a++){var o=r.charCodeAt(a);n+=0<=o&&o<=127?e:i}return a}function iv(r,t){r!=null&&(r+="");var e=t.overflow,i=t.padding,n=t.font,a=e==="truncate",s=Ei(n),o=U(t.lineHeight,s),f=!!t.backgroundColor,h=t.lineOverflow==="truncate",u=!1,v=t.width,l;v!=null&&(e==="break"||e==="breakAll")?l=r?rf(r,t.font,v,e==="breakAll",0).lines:[]:l=r?r.split(`
`):[];var c=l.length*o,p=U(t.height,c);if(c>p&&h){var _=Math.floor(p/o);u=u||l.length>_,l=l.slice(0,_)}if(r&&a&&v!=null)for(var d=jo(v,n,t.ellipsis,{minChar:t.truncateMinChar,placeholder:t.placeholder}),g={},y=0;y<l.length;y++)tf(g,l[y],d),l[y]=g.textLine,u=u||g.isTruncated;for(var m=p,w=0,y=0;y<l.length;y++)w=Math.max(dt(l[y],n),w);v==null&&(v=w);var S=w;return i&&(m+=i[0]+i[2],S+=i[1]+i[3],v+=i[1]+i[3]),f&&(S=v),{lines:l,height:p,outerWidth:S,outerHeight:m,lineHeight:o,calculatedLineHeight:s,contentWidth:w,contentHeight:c,width:v,isTruncated:u}}var nv=function(){function r(){}return r}(),is=function(){function r(t){this.tokens=[],t&&(this.tokens=t)}return r}(),av=function(){function r(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1}return r}();function sv(r,t){var e=new av;if(r!=null&&(r+=""),!r)return e;for(var i=t.width,n=t.height,a=t.overflow,s=(a==="break"||a==="breakAll")&&i!=null?{width:i,accumWidth:0,breakAll:a==="breakAll"}:null,o=on.lastIndex=0,f;(f=on.exec(r))!=null;){var h=f.index;h>o&&fn(e,r.substring(o,h),t,s),fn(e,f[2],t,s,f[1]),o=on.lastIndex}o<r.length&&fn(e,r.substring(o,r.length),t,s);var u=[],v=0,l=0,c=t.padding,p=a==="truncate",_=t.lineOverflow==="truncate",d={};function g(V,W,G){V.width=W,V.lineHeight=G,v+=G,l=Math.max(l,W)}t:for(var y=0;y<e.lines.length;y++){for(var m=e.lines[y],w=0,S=0,b=0;b<m.tokens.length;b++){var T=m.tokens[b],C=T.styleName&&t.rich[T.styleName]||{},M=T.textPadding=C.padding,P=M?M[1]+M[3]:0,L=T.font=C.font||t.font;T.contentHeight=Ei(L);var R=U(C.height,T.contentHeight);if(T.innerHeight=R,M&&(R+=M[0]+M[2]),T.height=R,T.lineHeight=ii(C.lineHeight,t.lineHeight,R),T.align=C&&C.align||t.align,T.verticalAlign=C&&C.verticalAlign||"middle",_&&n!=null&&v+T.lineHeight>n){var D=e.lines.length;b>0?(m.tokens=m.tokens.slice(0,b),g(m,S,w),e.lines=e.lines.slice(0,y+1)):e.lines=e.lines.slice(0,y),e.isTruncated=e.isTruncated||e.lines.length<D;break t}var x=C.width,E=x==null||x==="auto";if(typeof x=="string"&&x.charAt(x.length-1)==="%")T.percentWidth=x,u.push(T),T.contentWidth=dt(T.text,L);else{if(E){var A=C.backgroundColor,F=A&&A.image;F&&(F=tv(F),Fi(F)&&(T.width=Math.max(T.width,F.width*R/F.height)))}var H=p&&i!=null?i-S:null;H!=null&&H<T.width?!E||H<P?(T.text="",T.width=T.contentWidth=0):(rv(d,T.text,H-P,L,t.ellipsis,{minChar:t.truncateMinChar}),T.text=d.text,e.isTruncated=e.isTruncated||d.isTruncated,T.width=T.contentWidth=dt(T.text,L)):T.contentWidth=dt(T.text,L)}T.width+=P,S+=T.width,C&&(w=Math.max(w,T.lineHeight))}g(m,S,w)}e.outerWidth=e.width=U(i,l),e.outerHeight=e.height=U(n,v),e.contentHeight=v,e.contentWidth=l,c&&(e.outerWidth+=c[1]+c[3],e.outerHeight+=c[0]+c[2]);for(var y=0;y<u.length;y++){var T=u[y],N=T.percentWidth;T.width=parseInt(N,10)/100*e.width}return e}function fn(r,t,e,i,n){var a=t==="",s=n&&e.rich[n]||{},o=r.lines,f=s.font||e.font,h=!1,u,v;if(i){var l=s.padding,c=l?l[1]+l[3]:0;if(s.width!=null&&s.width!=="auto"){var p=Se(s.width,i.width)+c;o.length>0&&p+i.accumWidth>i.width&&(u=t.split(`
`),h=!0),i.accumWidth=p}else{var _=rf(t,f,i.width,i.breakAll,i.accumWidth);i.accumWidth=_.accumWidth+c,v=_.linesWidths,u=_.lines}}else u=t.split(`
`);for(var d=0;d<u.length;d++){var g=u[d],y=new nv;if(y.styleName=n,y.text=g,y.isLineHolder=!g&&!a,typeof s.width=="number"?y.width=s.width:y.width=v?v[d]:dt(g,f),!d&&!h){var m=(o[o.length-1]||(o[0]=new is)).tokens,w=m.length;w===1&&m[0].isLineHolder?m[0]=y:(g||!w||a)&&m.push(y)}else o.push(new is([y]))}}function ov(r){var t=r.charCodeAt(0);return t>=32&&t<=591||t>=880&&t<=4351||t>=4608&&t<=5119||t>=7680&&t<=8303}var fv=xi(",&?/;] ".split(""),function(r,t){return r[t]=!0,r},{});function hv(r){return ov(r)?!!fv[r]:!0}function rf(r,t,e,i,n){for(var a=[],s=[],o="",f="",h=0,u=0,v=0;v<r.length;v++){var l=r.charAt(v);if(l===`
`){f&&(o+=f,u+=h),a.push(o),s.push(u),o="",f="",h=0,u=0;continue}var c=dt(l,t),p=i?!1:!hv(l);if(a.length?u+c>e:n+u+c>e){u?(o||f)&&(p?(o||(o=f,f="",h=0,u=h),a.push(o),s.push(u-h),f+=l,h+=c,o="",u=h):(f&&(o+=f,f="",h=0),a.push(o),s.push(u),o=l,u=c)):p?(a.push(f),s.push(h),f=l,h=c):(a.push(l),s.push(c));continue}u+=c,p?(f+=l,h+=c):(f&&(o+=f,f="",h=0),o+=l)}return!a.length&&!o&&(o=r,f="",h=0),f&&(o+=f),o&&(a.push(o),s.push(u)),a.length===1&&(u+=n),{accumWidth:u,lines:a,linesWidths:s}}var Vn="__zr_style_"+Math.round(Math.random()*10),Mr={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},Oi={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Mr[Vn]=!0;var ns=["z","z2","invisible"],uv=["invisible"],vv=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype._init=function(e){for(var i=Y(e),n=0;n<i.length;n++){var a=i[n];a==="style"?this.useStyle(e[a]):r.prototype.attrKV.call(this,a,e[a])}this.style||this.useStyle({})},t.prototype.beforeBrush=function(){},t.prototype.afterBrush=function(){},t.prototype.innerBeforeBrush=function(){},t.prototype.innerAfterBrush=function(){},t.prototype.shouldBePainted=function(e,i,n,a){var s=this.transform;if(this.ignore||this.invisible||this.style.opacity===0||this.culling&&lv(this,e,i)||s&&!s[0]&&!s[3])return!1;if(n&&this.__clipPaths){for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1}if(a&&this.parent)for(var f=this.parent;f;){if(f.ignore)return!1;f=f.parent}return!0},t.prototype.contain=function(e,i){return this.rectContain(e,i)},t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.rectContain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();return a.contain(n[0],n[1])},t.prototype.getPaintRect=function(){var e=this._paintRect;if(!this._paintRect||this.__dirty){var i=this.transform,n=this.getBoundingRect(),a=this.style,s=a.shadowBlur||0,o=a.shadowOffsetX||0,f=a.shadowOffsetY||0;e=this._paintRect||(this._paintRect=new X(0,0,0,0)),i?X.applyTransform(e,n,i):e.copy(n),(s||o||f)&&(e.width+=s*2+Math.abs(o),e.height+=s*2+Math.abs(f),e.x=Math.min(e.x,e.x+o-s),e.y=Math.min(e.y,e.y+f-s));var h=this.dirtyRectTolerance;e.isZero()||(e.x=Math.floor(e.x-h),e.y=Math.floor(e.y-h),e.width=Math.ceil(e.width+1+h*2),e.height=Math.ceil(e.height+1+h*2))}return e},t.prototype.setPrevPaintRect=function(e){e?(this._prevPaintRect=this._prevPaintRect||new X(0,0,0,0),this._prevPaintRect.copy(e)):this._prevPaintRect=null},t.prototype.getPrevPaintRect=function(){return this._prevPaintRect},t.prototype.animateStyle=function(e){return this.animate("style",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():this.markRedraw()},t.prototype.attrKV=function(e,i){e!=="style"?r.prototype.attrKV.call(this,e,i):this.style?this.setStyle(i):this.useStyle(i)},t.prototype.setStyle=function(e,i){return typeof e=="string"?this.style[e]=i:z(this.style,e),this.dirtyStyle(),this},t.prototype.dirtyStyle=function(e){e||this.markRedraw(),this.__dirty|=fe,this._rect&&(this._rect=null)},t.prototype.dirty=function(){this.dirtyStyle()},t.prototype.styleChanged=function(){return!!(this.__dirty&fe)},t.prototype.styleUpdated=function(){this.__dirty&=~fe},t.prototype.createStyle=function(e){return Di(Mr,e)},t.prototype.useStyle=function(e){e[Vn]||(e=this.createStyle(e)),this.__inHover?this.__hoverStyle=e:this.style=e,this.dirtyStyle()},t.prototype.isStyleObject=function(e){return e[Vn]},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.style&&!i.style&&(i.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,i,ns)},t.prototype._applyStateObj=function(e,i,n,a,s,o){r.prototype._applyStateObj.call(this,e,i,n,a,s,o);var f=!(i&&a),h;if(i&&i.style?s?a?h=i.style:(h=this._mergeStyle(this.createStyle(),n.style),this._mergeStyle(h,i.style)):(h=this._mergeStyle(this.createStyle(),a?this.style:n.style),this._mergeStyle(h,i.style)):f&&(h=n.style),h)if(s){var u=this.style;if(this.style=this.createStyle(f?{}:u),f)for(var v=Y(u),l=0;l<v.length;l++){var c=v[l];c in h&&(h[c]=h[c],this.style[c]=u[c])}for(var p=Y(h),l=0;l<p.length;l++){var c=p[l];this.style[c]=this.style[c]}this._transitionState(e,{style:h},o,this.getAnimationStyleProps())}else this.useStyle(h);for(var _=this.__inHover?uv:ns,l=0;l<_.length;l++){var c=_[l];i&&i[c]!=null?this[c]=i[c]:f&&n[c]!=null&&(this[c]=n[c])}},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var s=e[a];s.style&&(n=n||{},this._mergeStyle(n,s.style))}return n&&(i.style=n),i},t.prototype._mergeStyle=function(e,i){return z(e,i),e},t.prototype.getAnimationStyleProps=function(){return Oi},t.initDefaultProps=function(){var e=t.prototype;e.type="displayable",e.invisible=!1,e.z=0,e.z2=0,e.zlevel=0,e.culling=!1,e.cursor="pointer",e.rectHover=!1,e.incremental=!1,e._rect=null,e.dirtyRectTolerance=0,e.__dirty=ct|fe}(),t}(Ko),hn=new X(0,0,0,0),un=new X(0,0,0,0);function lv(r,t,e){return hn.copy(r.getBoundingRect()),r.transform&&hn.applyTransform(r.transform),un.width=t,un.height=e,!hn.intersect(un)}const Ae=vv;var ht=Math.min,ut=Math.max,vn=Math.sin,ln=Math.cos,or=Math.PI*2,qe=Jr(),Ue=Jr(),Ve=Jr();function ef(r,t,e){if(r.length!==0){for(var i=r[0],n=i[0],a=i[0],s=i[1],o=i[1],f=1;f<r.length;f++)i=r[f],n=ht(n,i[0]),a=ut(a,i[0]),s=ht(s,i[1]),o=ut(o,i[1]);t[0]=n,t[1]=s,e[0]=a,e[1]=o}}function as(r,t,e,i,n,a){n[0]=ht(r,e),n[1]=ht(t,i),a[0]=ut(r,e),a[1]=ut(t,i)}var ss=[],os=[];function cv(r,t,e,i,n,a,s,o,f,h){var u=Oo,v=tt,l=u(r,e,n,s,ss);f[0]=1/0,f[1]=1/0,h[0]=-1/0,h[1]=-1/0;for(var c=0;c<l;c++){var p=v(r,e,n,s,ss[c]);f[0]=ht(p,f[0]),h[0]=ut(p,h[0])}l=u(t,i,a,o,os);for(var c=0;c<l;c++){var _=v(t,i,a,o,os[c]);f[1]=ht(_,f[1]),h[1]=ut(_,h[1])}f[0]=ht(r,f[0]),h[0]=ut(r,h[0]),f[0]=ht(s,f[0]),h[0]=ut(s,h[0]),f[1]=ht(t,f[1]),h[1]=ut(t,h[1]),f[1]=ht(o,f[1]),h[1]=ut(o,h[1])}function dv(r,t,e,i,n,a,s,o){var f=ko,h=it,u=ut(ht(f(r,e,n),1),0),v=ut(ht(f(t,i,a),1),0),l=h(r,e,n,u),c=h(t,i,a,v);s[0]=ht(r,n,l),s[1]=ht(t,a,c),o[0]=ut(r,n,l),o[1]=ut(t,a,c)}function pv(r,t,e,i,n,a,s,o,f){var h=Gr,u=Nr,v=Math.abs(n-a);if(v%or<1e-4&&v>1e-4){o[0]=r-e,o[1]=t-i,f[0]=r+e,f[1]=t+i;return}if(qe[0]=ln(n)*e+r,qe[1]=vn(n)*i+t,Ue[0]=ln(a)*e+r,Ue[1]=vn(a)*i+t,h(o,qe,Ue),u(f,qe,Ue),n=n%or,n<0&&(n=n+or),a=a%or,a<0&&(a=a+or),n>a&&!s?a+=or:n<a&&s&&(n+=or),s){var l=a;a=n,n=l}for(var c=0;c<a;c+=Math.PI/2)c>n&&(Ve[0]=ln(c)*e+r,Ve[1]=vn(c)*i+t,h(o,Ve,o),u(f,Ve,f))}var B={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},fr=[],hr=[],Lt=[],Xt=[],xt=[],Rt=[],cn=Math.min,dn=Math.max,ur=Math.cos,vr=Math.sin,$t=Math.abs,Zn=Math.PI,Ut=Zn*2,pn=typeof Float32Array<"u",ae=[];function gn(r){var t=Math.round(r/Zn*1e8)/1e8;return t%2*Zn}function gv(r,t){var e=gn(r[0]);e<0&&(e+=Ut);var i=e-r[0],n=r[1];n+=i,!t&&n-e>=Ut?n=e+Ut:t&&e-n>=Ut?n=e-Ut:!t&&e>n?n=e+(Ut-gn(e-n)):t&&e<n&&(n=e-(Ut-gn(n-e))),r[0]=e,r[1]=n}var tr=function(){function r(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return r.prototype.increaseVersion=function(){this._version++},r.prototype.getVersion=function(){return this._version},r.prototype.setScale=function(t,e,i){i=i||0,i>0&&(this._ux=$t(i/gi/t)||0,this._uy=$t(i/gi/e)||0)},r.prototype.setDPR=function(t){this.dpr=t},r.prototype.setContext=function(t){this._ctx=t},r.prototype.getContext=function(){return this._ctx},r.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},r.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},r.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(B.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},r.prototype.lineTo=function(t,e){var i=$t(t-this._xi),n=$t(e-this._yi),a=i>this._ux||n>this._uy;if(this.addData(B.L,t,e),this._ctx&&a&&this._ctx.lineTo(t,e),a)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var s=i*i+n*n;s>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=s)}return this},r.prototype.bezierCurveTo=function(t,e,i,n,a,s){return this._drawPendingPt(),this.addData(B.C,t,e,i,n,a,s),this._ctx&&this._ctx.bezierCurveTo(t,e,i,n,a,s),this._xi=a,this._yi=s,this},r.prototype.quadraticCurveTo=function(t,e,i,n){return this._drawPendingPt(),this.addData(B.Q,t,e,i,n),this._ctx&&this._ctx.quadraticCurveTo(t,e,i,n),this._xi=i,this._yi=n,this},r.prototype.arc=function(t,e,i,n,a,s){this._drawPendingPt(),ae[0]=n,ae[1]=a,gv(ae,s),n=ae[0],a=ae[1];var o=a-n;return this.addData(B.A,t,e,i,i,n,o,0,s?0:1),this._ctx&&this._ctx.arc(t,e,i,n,a,s),this._xi=ur(a)*i+t,this._yi=vr(a)*i+e,this},r.prototype.arcTo=function(t,e,i,n,a){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,i,n,a),this},r.prototype.rect=function(t,e,i,n){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,i,n),this.addData(B.R,t,e,i,n),this},r.prototype.closePath=function(){this._drawPendingPt(),this.addData(B.Z);var t=this._ctx,e=this._x0,i=this._y0;return t&&t.closePath(),this._xi=e,this._yi=i,this},r.prototype.fill=function(t){t&&t.fill(),this.toStatic()},r.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},r.prototype.len=function(){return this._len},r.prototype.setData=function(t){var e=t.length;!(this.data&&this.data.length===e)&&pn&&(this.data=new Float32Array(e));for(var i=0;i<e;i++)this.data[i]=t[i];this._len=e},r.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,i=0,n=this._len,a=0;a<e;a++)i+=t[a].len();pn&&this.data instanceof Float32Array&&(this.data=new Float32Array(n+i));for(var a=0;a<e;a++)for(var s=t[a].data,o=0;o<s.length;o++)this.data[n++]=s[o];this._len=n},r.prototype.addData=function(t,e,i,n,a,s,o,f,h){if(!!this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var v=0;v<arguments.length;v++)u[this._len++]=arguments[v]}},r.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},r.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},r.prototype.toStatic=function(){if(!!this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,pn&&this._len>11&&(this.data=new Float32Array(t)))}},r.prototype.getBoundingRect=function(){Lt[0]=Lt[1]=xt[0]=xt[1]=Number.MAX_VALUE,Xt[0]=Xt[1]=Rt[0]=Rt[1]=-Number.MAX_VALUE;var t=this.data,e=0,i=0,n=0,a=0,s;for(s=0;s<this._len;){var o=t[s++],f=s===1;switch(f&&(e=t[s],i=t[s+1],n=e,a=i),o){case B.M:e=n=t[s++],i=a=t[s++],xt[0]=n,xt[1]=a,Rt[0]=n,Rt[1]=a;break;case B.L:as(e,i,t[s],t[s+1],xt,Rt),e=t[s++],i=t[s++];break;case B.C:cv(e,i,t[s++],t[s++],t[s++],t[s++],t[s],t[s+1],xt,Rt),e=t[s++],i=t[s++];break;case B.Q:dv(e,i,t[s++],t[s++],t[s],t[s+1],xt,Rt),e=t[s++],i=t[s++];break;case B.A:var h=t[s++],u=t[s++],v=t[s++],l=t[s++],c=t[s++],p=t[s++]+c;s+=1;var _=!t[s++];f&&(n=ur(c)*v+h,a=vr(c)*l+u),pv(h,u,v,l,c,p,_,xt,Rt),e=ur(p)*v+h,i=vr(p)*l+u;break;case B.R:n=e=t[s++],a=i=t[s++];var d=t[s++],g=t[s++];as(n,a,n+d,a+g,xt,Rt);break;case B.Z:e=n,i=a;break}Gr(Lt,Lt,xt),Nr(Xt,Xt,Rt)}return s===0&&(Lt[0]=Lt[1]=Xt[0]=Xt[1]=0),new X(Lt[0],Lt[1],Xt[0]-Lt[0],Xt[1]-Lt[1])},r.prototype._calculateLength=function(){var t=this.data,e=this._len,i=this._ux,n=this._uy,a=0,s=0,o=0,f=0;this._pathSegLen||(this._pathSegLen=[]);for(var h=this._pathSegLen,u=0,v=0,l=0;l<e;){var c=t[l++],p=l===1;p&&(a=t[l],s=t[l+1],o=a,f=s);var _=-1;switch(c){case B.M:a=o=t[l++],s=f=t[l++];break;case B.L:{var d=t[l++],g=t[l++],y=d-a,m=g-s;($t(y)>i||$t(m)>n||l===e-1)&&(_=Math.sqrt(y*y+m*m),a=d,s=g);break}case B.C:{var w=t[l++],S=t[l++],d=t[l++],g=t[l++],b=t[l++],T=t[l++];_=su(a,s,w,S,d,g,b,T,10),a=b,s=T;break}case B.Q:{var w=t[l++],S=t[l++],d=t[l++],g=t[l++];_=hu(a,s,w,S,d,g,10),a=d,s=g;break}case B.A:var C=t[l++],M=t[l++],P=t[l++],L=t[l++],R=t[l++],D=t[l++],x=D+R;l+=1,p&&(o=ur(R)*P+C,f=vr(R)*L+M),_=dn(P,L)*cn(Ut,Math.abs(D)),a=ur(x)*P+C,s=vr(x)*L+M;break;case B.R:{o=a=t[l++],f=s=t[l++];var E=t[l++],A=t[l++];_=E*2+A*2;break}case B.Z:{var y=o-a,m=f-s;_=Math.sqrt(y*y+m*m),a=o,s=f;break}}_>=0&&(h[v++]=_,u+=_)}return this._pathLen=u,u},r.prototype.rebuildPath=function(t,e){var i=this.data,n=this._ux,a=this._uy,s=this._len,o,f,h,u,v,l,c=e<1,p,_,d=0,g=0,y,m=0,w,S;if(c&&(this._pathSegLen||this._calculateLength(),p=this._pathSegLen,_=this._pathLen,y=e*_,!y))return;t:for(var b=0;b<s;){var T=i[b++],C=b===1;switch(C&&(h=i[b],u=i[b+1],o=h,f=u),T!==B.L&&m>0&&(t.lineTo(w,S),m=0),T){case B.M:o=h=i[b++],f=u=i[b++],t.moveTo(h,u);break;case B.L:{v=i[b++],l=i[b++];var M=$t(v-h),P=$t(l-u);if(M>n||P>a){if(c){var L=p[g++];if(d+L>y){var R=(y-d)/L;t.lineTo(h*(1-R)+v*R,u*(1-R)+l*R);break t}d+=L}t.lineTo(v,l),h=v,u=l,m=0}else{var D=M*M+P*P;D>m&&(w=v,S=l,m=D)}break}case B.C:{var x=i[b++],E=i[b++],A=i[b++],F=i[b++],H=i[b++],N=i[b++];if(c){var L=p[g++];if(d+L>y){var R=(y-d)/L;jt(h,x,A,H,R,fr),jt(u,E,F,N,R,hr),t.bezierCurveTo(fr[1],hr[1],fr[2],hr[2],fr[3],hr[3]);break t}d+=L}t.bezierCurveTo(x,E,A,F,H,N),h=H,u=N;break}case B.Q:{var x=i[b++],E=i[b++],A=i[b++],F=i[b++];if(c){var L=p[g++];if(d+L>y){var R=(y-d)/L;ci(h,x,A,R,fr),ci(u,E,F,R,hr),t.quadraticCurveTo(fr[1],hr[1],fr[2],hr[2]);break t}d+=L}t.quadraticCurveTo(x,E,A,F),h=A,u=F;break}case B.A:var V=i[b++],W=i[b++],G=i[b++],rt=i[b++],j=i[b++],pt=i[b++],Bt=i[b++],Ht=!i[b++],gt=G>rt?G:rt,Q=$t(G-rt)>.001,q=j+pt,I=!1;if(c){var L=p[g++];d+L>y&&(q=j+pt*(y-d)/L,I=!0),d+=L}if(Q&&t.ellipse?t.ellipse(V,W,G,rt,Bt,j,q,Ht):t.arc(V,W,gt,j,q,Ht),I)break t;C&&(o=ur(j)*G+V,f=vr(j)*rt+W),h=ur(q)*G+V,u=vr(q)*rt+W;break;case B.R:o=h=i[b],f=u=i[b+1],v=i[b++],l=i[b++];var O=i[b++],zt=i[b++];if(c){var L=p[g++];if(d+L>y){var ot=y-d;t.moveTo(v,l),t.lineTo(v+cn(ot,O),l),ot-=O,ot>0&&t.lineTo(v+O,l+cn(ot,zt)),ot-=zt,ot>0&&t.lineTo(v+dn(O-ot,0),l+zt),ot-=O,ot>0&&t.lineTo(v,l+dn(zt-ot,0));break t}d+=L}t.rect(v,l,O,zt);break;case B.Z:if(c){var L=p[g++];if(d+L>y){var R=(y-d)/L;t.lineTo(h*(1-R)+o*R,u*(1-R)+f*R);break t}d+=L}t.closePath(),h=o,u=f}}},r.prototype.clone=function(){var t=new r,e=this.data;return t.data=e.slice?e.slice():Array.prototype.slice.call(e),t._len=this._len,t},r.CMD=B,r.initDefaultProps=function(){var t=r.prototype;t._saveData=!0,t._ux=0,t._uy=0,t._pendingPtDist=0,t._version=0}(),r}();function Ar(r,t,e,i,n,a,s){if(n===0)return!1;var o=n,f=0,h=r;if(s>t+o&&s>i+o||s<t-o&&s<i-o||a>r+o&&a>e+o||a<r-o&&a<e-o)return!1;if(r!==e)f=(t-i)/(r-e),h=(r*i-e*t)/(r-e);else return Math.abs(a-r)<=o/2;var u=f*a-s+h,v=u*u/(f*f+1);return v<=o/2*o/2}function _v(r,t,e,i,n,a,s,o,f,h,u){if(f===0)return!1;var v=f;if(u>t+v&&u>i+v&&u>a+v&&u>o+v||u<t-v&&u<i-v&&u<a-v&&u<o-v||h>r+v&&h>e+v&&h>n+v&&h>s+v||h<r-v&&h<e-v&&h<n-v&&h<s-v)return!1;var l=au(r,t,e,i,n,a,s,o,h,u,null);return l<=v/2}function yv(r,t,e,i,n,a,s,o,f){if(s===0)return!1;var h=s;if(f>t+h&&f>i+h&&f>a+h||f<t-h&&f<i-h&&f<a-h||o>r+h&&o>e+h&&o>n+h||o<r-h&&o<e-h&&o<n-h)return!1;var u=fu(r,t,e,i,n,a,o,f,null);return u<=h/2}var fs=Math.PI*2;function Ze(r){return r%=fs,r<0&&(r+=fs),r}var se=Math.PI*2;function mv(r,t,e,i,n,a,s,o,f){if(s===0)return!1;var h=s;o-=r,f-=t;var u=Math.sqrt(o*o+f*f);if(u-h>e||u+h<e)return!1;if(Math.abs(i-n)%se<1e-4)return!0;if(a){var v=i;i=Ze(n),n=Ze(v)}else i=Ze(i),n=Ze(n);i>n&&(n+=se);var l=Math.atan2(f,o);return l<0&&(l+=se),l>=i&&l<=n||l+se>=i&&l+se<=n}function Nt(r,t,e,i,n,a){if(a>t&&a>i||a<t&&a<i||i===t)return 0;var s=(a-t)/(i-t),o=i<t?1:-1;(s===1||s===0)&&(o=i<t?.5:-.5);var f=s*(e-r)+r;return f===n?1/0:f>n?o:0}var Wt=tr.CMD,lr=Math.PI*2,wv=1e-4;function Tv(r,t){return Math.abs(r-t)<wv}var st=[-1,-1,-1],mt=[-1,-1];function bv(){var r=mt[0];mt[0]=mt[1],mt[1]=r}function Sv(r,t,e,i,n,a,s,o,f,h){if(h>t&&h>i&&h>a&&h>o||h<t&&h<i&&h<a&&h<o)return 0;var u=Fo(t,i,a,o,h,st);if(u===0)return 0;for(var v=0,l=-1,c=void 0,p=void 0,_=0;_<u;_++){var d=st[_],g=d===0||d===1?.5:1,y=tt(r,e,n,s,d);y<f||(l<0&&(l=Oo(t,i,a,o,mt),mt[1]<mt[0]&&l>1&&bv(),c=tt(t,i,a,o,mt[0]),l>1&&(p=tt(t,i,a,o,mt[1]))),l===2?d<mt[0]?v+=c<t?g:-g:d<mt[1]?v+=p<c?g:-g:v+=o<p?g:-g:d<mt[0]?v+=c<t?g:-g:v+=o<c?g:-g)}return v}function Cv(r,t,e,i,n,a,s,o){if(o>t&&o>i&&o>a||o<t&&o<i&&o<a)return 0;var f=ou(t,i,a,o,st);if(f===0)return 0;var h=ko(t,i,a);if(h>=0&&h<=1){for(var u=0,v=it(t,i,a,h),l=0;l<f;l++){var c=st[l]===0||st[l]===1?.5:1,p=it(r,e,n,st[l]);p<s||(st[l]<h?u+=v<t?c:-c:u+=a<v?c:-c)}return u}else{var c=st[0]===0||st[0]===1?.5:1,p=it(r,e,n,st[0]);return p<s?0:a<t?c:-c}}function Mv(r,t,e,i,n,a,s,o){if(o-=t,o>e||o<-e)return 0;var f=Math.sqrt(e*e-o*o);st[0]=-f,st[1]=f;var h=Math.abs(i-n);if(h<1e-4)return 0;if(h>=lr-1e-4){i=0,n=lr;var u=a?1:-1;return s>=st[0]+r&&s<=st[1]+r?u:0}if(i>n){var v=i;i=n,n=v}i<0&&(i+=lr,n+=lr);for(var l=0,c=0;c<2;c++){var p=st[c];if(p+r>s){var _=Math.atan2(o,p),u=a?1:-1;_<0&&(_=lr+_),(_>=i&&_<=n||_+lr>=i&&_+lr<=n)&&(_>Math.PI/2&&_<Math.PI*1.5&&(u=-u),l+=u)}}return l}function nf(r,t,e,i,n){for(var a=r.data,s=r.len(),o=0,f=0,h=0,u=0,v=0,l,c,p=0;p<s;){var _=a[p++],d=p===1;switch(_===Wt.M&&p>1&&(e||(o+=Nt(f,h,u,v,i,n))),d&&(f=a[p],h=a[p+1],u=f,v=h),_){case Wt.M:u=a[p++],v=a[p++],f=u,h=v;break;case Wt.L:if(e){if(Ar(f,h,a[p],a[p+1],t,i,n))return!0}else o+=Nt(f,h,a[p],a[p+1],i,n)||0;f=a[p++],h=a[p++];break;case Wt.C:if(e){if(_v(f,h,a[p++],a[p++],a[p++],a[p++],a[p],a[p+1],t,i,n))return!0}else o+=Sv(f,h,a[p++],a[p++],a[p++],a[p++],a[p],a[p+1],i,n)||0;f=a[p++],h=a[p++];break;case Wt.Q:if(e){if(yv(f,h,a[p++],a[p++],a[p],a[p+1],t,i,n))return!0}else o+=Cv(f,h,a[p++],a[p++],a[p],a[p+1],i,n)||0;f=a[p++],h=a[p++];break;case Wt.A:var g=a[p++],y=a[p++],m=a[p++],w=a[p++],S=a[p++],b=a[p++];p+=1;var T=!!(1-a[p++]);l=Math.cos(S)*m+g,c=Math.sin(S)*w+y,d?(u=l,v=c):o+=Nt(f,h,l,c,i,n);var C=(i-g)*w/m+g;if(e){if(mv(g,y,w,S,S+b,T,t,C,n))return!0}else o+=Mv(g,y,w,S,S+b,T,C,n);f=Math.cos(S+b)*m+g,h=Math.sin(S+b)*w+y;break;case Wt.R:u=f=a[p++],v=h=a[p++];var M=a[p++],P=a[p++];if(l=u+M,c=v+P,e){if(Ar(u,v,l,v,t,i,n)||Ar(l,v,l,c,t,i,n)||Ar(l,c,u,c,t,i,n)||Ar(u,c,u,v,t,i,n))return!0}else o+=Nt(l,v,l,c,i,n),o+=Nt(u,c,u,v,i,n);break;case Wt.Z:if(e){if(Ar(f,h,u,v,t,i,n))return!0}else o+=Nt(f,h,u,v,i,n);f=u,h=v;break}}return!e&&!Tv(h,v)&&(o+=Nt(f,h,u,v,i,n)||0),o!==0}function Pv(r,t,e){return nf(r,0,!1,t,e)}function Lv(r,t,e,i){return nf(r,t,!0,e,i)}var _i=Tt({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Mr),xv={style:Tt({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},Oi.style)},_n=be.concat(["invisible","culling","z","z2","zlevel","parent"]),Rv=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.update=function(){var e=this;r.prototype.update.call(this);var i=this.style;if(i.decal){var n=this._decalEl=this._decalEl||new t;n.buildPath===t.prototype.buildPath&&(n.buildPath=function(f){e.buildPath(f,e.shape)}),n.silent=!0;var a=n.style;for(var s in i)a[s]!==i[s]&&(a[s]=i[s]);a.fill=i.fill?i.decal:null,a.decal=null,a.shadowColor=null,i.strokeFirst&&(a.stroke=null);for(var o=0;o<_n.length;++o)n[_n[o]]=this[_n[o]];n.__dirty|=ct}else this._decalEl&&(this._decalEl=null)},t.prototype.getDecalElement=function(){return this._decalEl},t.prototype._init=function(e){var i=Y(e);this.shape=this.getDefaultShape();var n=this.getDefaultStyle();n&&this.useStyle(n);for(var a=0;a<i.length;a++){var s=i[a],o=e[s];s==="style"?this.style?z(this.style,o):this.useStyle(o):s==="shape"?z(this.shape,o):r.prototype.attrKV.call(this,s,o)}this.style||this.useStyle({})},t.prototype.getDefaultStyle=function(){return null},t.prototype.getDefaultShape=function(){return{}},t.prototype.canBeInsideText=function(){return this.hasFill()},t.prototype.getInsideTextFill=function(){var e=this.style.fill;if(e!=="none"){if(kt(e)){var i=di(e,0);return i>.5?Xn:i>.2?$u:Wn}else if(e)return Wn}return Xn},t.prototype.getInsideTextStroke=function(e){var i=this.style.fill;if(kt(i)){var n=this.__zr,a=!!(n&&n.isDarkMode()),s=di(e,0)<Yn;if(a===s)return i}},t.prototype.buildPath=function(e,i,n){},t.prototype.pathUpdated=function(){this.__dirty&=~Or},t.prototype.getUpdatedPathProxy=function(e){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,e),this.path},t.prototype.createPathProxy=function(){this.path=new tr(!1)},t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return!(i==null||i==="none"||!(e.lineWidth>0))},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.getBoundingRect=function(){var e=this._rect,i=this.style,n=!e;if(n){var a=!1;this.path||(a=!0,this.createPathProxy());var s=this.path;(a||this.__dirty&Or)&&(s.beginPath(),this.buildPath(s,this.shape,!1),this.pathUpdated()),e=s.getBoundingRect()}if(this._rect=e,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=e.clone());if(this.__dirty||n){o.copy(e);var f=i.strokeNoScale?this.getLineScale():1,h=i.lineWidth;if(!this.hasFill()){var u=this.strokeContainThreshold;h=Math.max(h,u==null?4:u)}f>1e-10&&(o.width+=h/f,o.height+=h/f,o.x-=h/f/2,o.y-=h/f/2)}return o}return e},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect(),s=this.style;if(e=n[0],i=n[1],a.contain(e,i)){var o=this.path;if(this.hasStroke()){var f=s.lineWidth,h=s.strokeNoScale?this.getLineScale():1;if(h>1e-10&&(this.hasFill()||(f=Math.max(f,this.strokeContainThreshold)),Lv(o,f/h,e,i)))return!0}if(this.hasFill())return Pv(o,e,i)}return!1},t.prototype.dirtyShape=function(){this.__dirty|=Or,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},t.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},t.prototype.animateShape=function(e){return this.animate("shape",e)},t.prototype.updateDuringAnimation=function(e){e==="style"?this.dirtyStyle():e==="shape"?this.dirtyShape():this.markRedraw()},t.prototype.attrKV=function(e,i){e==="shape"?this.setShape(i):r.prototype.attrKV.call(this,e,i)},t.prototype.setShape=function(e,i){var n=this.shape;return n||(n=this.shape={}),typeof e=="string"?n[e]=i:z(n,e),this.dirtyShape(),this},t.prototype.shapeChanged=function(){return!!(this.__dirty&Or)},t.prototype.createStyle=function(e){return Di(_i,e)},t.prototype._innerSaveToNormal=function(e){r.prototype._innerSaveToNormal.call(this,e);var i=this._normalState;e.shape&&!i.shape&&(i.shape=z({},this.shape))},t.prototype._applyStateObj=function(e,i,n,a,s,o){r.prototype._applyStateObj.call(this,e,i,n,a,s,o);var f=!(i&&a),h;if(i&&i.shape?s?a?h=i.shape:(h=z({},n.shape),z(h,i.shape)):(h=z({},a?this.shape:n.shape),z(h,i.shape)):f&&(h=n.shape),h)if(s){this.shape=z({},this.shape);for(var u={},v=Y(h),l=0;l<v.length;l++){var c=v[l];typeof h[c]=="object"?this.shape[c]=h[c]:u[c]=h[c]}this._transitionState(e,{shape:u},o)}else this.shape=h,this.dirtyShape()},t.prototype._mergeStates=function(e){for(var i=r.prototype._mergeStates.call(this,e),n,a=0;a<e.length;a++){var s=e[a];s.shape&&(n=n||{},this._mergeStyle(n,s.shape))}return n&&(i.shape=n),i},t.prototype.getAnimationStyleProps=function(){return xv},t.prototype.isZeroArea=function(){return!1},t.extend=function(e){var i=function(a){$(s,a);function s(o){var f=a.call(this,o)||this;return e.init&&e.init.call(f,o),f}return s.prototype.getDefaultStyle=function(){return Pr(e.style)},s.prototype.getDefaultShape=function(){return Pr(e.shape)},s}(t);for(var n in e)typeof e[n]=="function"&&(i.prototype[n]=e[n]);return i},t.initDefaultProps=function(){var e=t.prototype;e.type="path",e.strokeContainThreshold=5,e.segmentIgnoreThreshold=0,e.subPixelOptimize=!1,e.autoBatch=!1,e.__dirty=ct|fe|Or}(),t}(Ae);const K=Rv;var Dv=Tt({strokeFirst:!0,font:Jt,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},_i),af=function(r){$(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.hasStroke=function(){var e=this.style,i=e.stroke;return i!=null&&i!=="none"&&e.lineWidth>0},t.prototype.hasFill=function(){var e=this.style,i=e.fill;return i!=null&&i!=="none"},t.prototype.createStyle=function(e){return Di(Dv,e)},t.prototype.setBoundingRect=function(e){this._rect=e},t.prototype.getBoundingRect=function(){var e=this.style;if(!this._rect){var i=e.text;i!=null?i+="":i="";var n=Nu(i,e.font,e.textAlign,e.textBaseline);if(n.x+=e.x||0,n.y+=e.y||0,this.hasStroke()){var a=e.lineWidth;n.x-=a/2,n.y-=a/2,n.width+=a,n.height+=a}this._rect=n}return this._rect},t.initDefaultProps=function(){var e=t.prototype;e.dirtyRectTolerance=10}(),t}(Ae);af.prototype.type="tspan";const Ce=af;var Av=Tt({x:0,y:0},Mr),Iv={style:Tt({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},Oi.style)};function Ev(r){return!!(r&&typeof r!="string"&&r.width&&r.height)}var sf=function(r){$(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.createStyle=function(e){return Di(Av,e)},t.prototype._getSize=function(e){var i=this.style,n=i[e];if(n!=null)return n;var a=Ev(i.image)?i.image:this.__image;if(!a)return 0;var s=e==="width"?"height":"width",o=i[s];return o==null?a[e]:a[e]/a[s]*o},t.prototype.getWidth=function(){return this._getSize("width")},t.prototype.getHeight=function(){return this._getSize("height")},t.prototype.getAnimationStyleProps=function(){return Iv},t.prototype.getBoundingRect=function(){var e=this.style;return this._rect||(this._rect=new X(e.x||0,e.y||0,this.getWidth(),this.getHeight())),this._rect},t}(Ae);sf.prototype.type="image";const Ie=sf;function Fv(r,t){var e=t.x,i=t.y,n=t.width,a=t.height,s=t.r,o,f,h,u;n<0&&(e=e+n,n=-n),a<0&&(i=i+a,a=-a),typeof s=="number"?o=f=h=u=s:s instanceof Array?s.length===1?o=f=h=u=s[0]:s.length===2?(o=h=s[0],f=u=s[1]):s.length===3?(o=s[0],f=u=s[1],h=s[2]):(o=s[0],f=s[1],h=s[2],u=s[3]):o=f=h=u=0;var v;o+f>n&&(v=o+f,o*=n/v,f*=n/v),h+u>n&&(v=h+u,h*=n/v,u*=n/v),f+h>a&&(v=f+h,f*=a/v,h*=a/v),o+u>a&&(v=o+u,o*=a/v,u*=a/v),r.moveTo(e+o,i),r.lineTo(e+n-f,i),f!==0&&r.arc(e+n-f,i+f,f,-Math.PI/2,0),r.lineTo(e+n,i+a-h),h!==0&&r.arc(e+n-h,i+a-h,h,0,Math.PI/2),r.lineTo(e+u,i+a),u!==0&&r.arc(e+u,i+a-u,u,Math.PI/2,Math.PI),r.lineTo(e,i+o),o!==0&&r.arc(e+o,i+o,o,Math.PI,Math.PI*1.5)}var Xr=Math.round;function Ov(r,t,e){if(!!t){var i=t.x1,n=t.x2,a=t.y1,s=t.y2;r.x1=i,r.x2=n,r.y1=a,r.y2=s;var o=e&&e.lineWidth;return o&&(Xr(i*2)===Xr(n*2)&&(r.x1=r.x2=Wr(i,o,!0)),Xr(a*2)===Xr(s*2)&&(r.y1=r.y2=Wr(a,o,!0))),r}}function kv(r,t,e){if(!!t){var i=t.x,n=t.y,a=t.width,s=t.height;r.x=i,r.y=n,r.width=a,r.height=s;var o=e&&e.lineWidth;return o&&(r.x=Wr(i,o,!0),r.y=Wr(n,o,!0),r.width=Math.max(Wr(i+a,o,!1)-r.x,a===0?0:1),r.height=Math.max(Wr(n+s,o,!1)-r.y,s===0?0:1)),r}}function Wr(r,t,e){if(!t)return r;var i=Xr(r*2);return(i+Xr(t))%2===0?i/2:(i+(e?1:-1))/2}var Bv=function(){function r(){this.x=0,this.y=0,this.width=0,this.height=0}return r}(),Hv={},of=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Bv},t.prototype.buildPath=function(e,i){var n,a,s,o;if(this.subPixelOptimize){var f=kv(Hv,i,this.style);n=f.x,a=f.y,s=f.width,o=f.height,f.r=i.r,i=f}else n=i.x,a=i.y,s=i.width,o=i.height;i.r?Fv(e,i):e.rect(n,a,s,o)},t.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},t}(K);of.prototype.type="rect";const yi=of;var hs={fill:"#000"},us=2,zv={style:Tt({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},Oi.style)},ff=function(r){$(t,r);function t(e){var i=r.call(this)||this;return i.type="text",i._children=[],i._defaultStyle=hs,i.attr(e),i}return t.prototype.childrenRef=function(){return this._children},t.prototype.update=function(){r.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var i=this._children[e];i.zlevel=this.zlevel,i.z=this.z,i.z2=this.z2,i.culling=this.culling,i.cursor=this.cursor,i.invisible=this.invisible}},t.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):r.prototype.updateTransform.call(this)},t.prototype.getLocalTransform=function(e){var i=this.innerTransformable;return i?i.getLocalTransform(e):r.prototype.getLocalTransform.call(this,e)},t.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),r.prototype.getComputedTransform.call(this)},t.prototype._updateSubTexts=function(){this._childCursor=0,Nv(this.style),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},t.prototype.addSelfToZr=function(e){r.prototype.addSelfToZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=e},t.prototype.removeSelfFromZr=function(e){r.prototype.removeSelfFromZr.call(this,e);for(var i=0;i<this._children.length;i++)this._children[i].__zr=null},t.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var e=new X(0,0,0,0),i=this._children,n=[],a=null,s=0;s<i.length;s++){var o=i[s],f=o.getBoundingRect(),h=o.getLocalTransform(n);h?(e.copy(f),e.applyTransform(h),a=a||e.clone(),a.union(e)):(a=a||f.clone(),a.union(f))}this._rect=a||e}return this._rect},t.prototype.setDefaultTextStyle=function(e){this._defaultStyle=e||hs},t.prototype.setTextContent=function(e){},t.prototype._mergeStyle=function(e,i){if(!i)return e;var n=i.rich,a=e.rich||n&&{};return z(e,i),n&&a?(this._mergeRich(a,n),e.rich=a):a&&(e.rich=a),e},t.prototype._mergeRich=function(e,i){for(var n=Y(i),a=0;a<n.length;a++){var s=n[a];e[s]=e[s]||{},z(e[s],i[s])}},t.prototype.getAnimationStyleProps=function(){return zv},t.prototype._getOrCreateChild=function(e){var i=this._children[this._childCursor];return(!i||!(i instanceof e))&&(i=new e),this._children[this._childCursor++]=i,i.__zr=this.__zr,i.parent=this,i},t.prototype._updatePlainTexts=function(){var e=this.style,i=e.font||Jt,n=e.padding,a=_s(e),s=iv(a,e),o=yn(e),f=!!e.backgroundColor,h=s.outerHeight,u=s.outerWidth,v=s.contentWidth,l=s.lines,c=s.lineHeight,p=this._defaultStyle;this.isTruncated=!!s.isTruncated;var _=e.x||0,d=e.y||0,g=e.align||p.align||"left",y=e.verticalAlign||p.verticalAlign||"top",m=_,w=kr(d,s.contentHeight,y);if(o||n){var S=ue(_,u,g),b=kr(d,h,y);o&&this._renderBackground(e,e,S,b,u,h)}w+=c/2,n&&(m=gs(_,g,n),y==="top"?w+=n[0]:y==="bottom"&&(w-=n[2]));for(var T=0,C=!1,M=ps("fill"in e?e.fill:(C=!0,p.fill)),P=ds("stroke"in e?e.stroke:!f&&(!p.autoStroke||C)?(T=us,p.stroke):null),L=e.textShadowBlur>0,R=e.width!=null&&(e.overflow==="truncate"||e.overflow==="break"||e.overflow==="breakAll"),D=s.calculatedLineHeight,x=0;x<l.length;x++){var E=this._getOrCreateChild(Ce),A=E.createStyle();E.useStyle(A),A.text=l[x],A.x=m,A.y=w,g&&(A.textAlign=g),A.textBaseline="middle",A.opacity=e.opacity,A.strokeFirst=!0,L&&(A.shadowBlur=e.textShadowBlur||0,A.shadowColor=e.textShadowColor||"transparent",A.shadowOffsetX=e.textShadowOffsetX||0,A.shadowOffsetY=e.textShadowOffsetY||0),A.stroke=P,A.fill=M,P&&(A.lineWidth=e.lineWidth||T,A.lineDash=e.lineDash,A.lineDashOffset=e.lineDashOffset||0),A.font=i,ls(A,e),w+=c,R&&E.setBoundingRect(new X(ue(A.x,v,A.textAlign),kr(A.y,D,A.textBaseline),v,D))}},t.prototype._updateRichTexts=function(){var e=this.style,i=_s(e),n=sv(i,e),a=n.width,s=n.outerWidth,o=n.outerHeight,f=e.padding,h=e.x||0,u=e.y||0,v=this._defaultStyle,l=e.align||v.align,c=e.verticalAlign||v.verticalAlign;this.isTruncated=!!n.isTruncated;var p=ue(h,s,l),_=kr(u,o,c),d=p,g=_;f&&(d+=f[3],g+=f[0]);var y=d+a;yn(e)&&this._renderBackground(e,e,p,_,s,o);for(var m=!!e.backgroundColor,w=0;w<n.lines.length;w++){for(var S=n.lines[w],b=S.tokens,T=b.length,C=S.lineHeight,M=S.width,P=0,L=d,R=y,D=T-1,x=void 0;P<T&&(x=b[P],!x.align||x.align==="left");)this._placeToken(x,e,C,g,L,"left",m),M-=x.width,L+=x.width,P++;for(;D>=0&&(x=b[D],x.align==="right");)this._placeToken(x,e,C,g,R,"right",m),M-=x.width,R-=x.width,D--;for(L+=(a-(L-d)-(y-R)-M)/2;P<=D;)x=b[P],this._placeToken(x,e,C,g,L+x.width/2,"center",m),L+=x.width,P++;g+=C}},t.prototype._placeToken=function(e,i,n,a,s,o,f){var h=i.rich[e.styleName]||{};h.text=e.text;var u=e.verticalAlign,v=a+n/2;u==="top"?v=a+e.height/2:u==="bottom"&&(v=a+n-e.height/2);var l=!e.isLineHolder&&yn(h);l&&this._renderBackground(h,i,o==="right"?s-e.width:o==="center"?s-e.width/2:s,v-e.height/2,e.width,e.height);var c=!!h.backgroundColor,p=e.textPadding;p&&(s=gs(s,o,p),v-=e.height/2-p[0]-e.innerHeight/2);var _=this._getOrCreateChild(Ce),d=_.createStyle();_.useStyle(d);var g=this._defaultStyle,y=!1,m=0,w=ps("fill"in h?h.fill:"fill"in i?i.fill:(y=!0,g.fill)),S=ds("stroke"in h?h.stroke:"stroke"in i?i.stroke:!c&&!f&&(!g.autoStroke||y)?(m=us,g.stroke):null),b=h.textShadowBlur>0||i.textShadowBlur>0;d.text=e.text,d.x=s,d.y=v,b&&(d.shadowBlur=h.textShadowBlur||i.textShadowBlur||0,d.shadowColor=h.textShadowColor||i.textShadowColor||"transparent",d.shadowOffsetX=h.textShadowOffsetX||i.textShadowOffsetX||0,d.shadowOffsetY=h.textShadowOffsetY||i.textShadowOffsetY||0),d.textAlign=o,d.textBaseline="middle",d.font=e.font||Jt,d.opacity=ii(h.opacity,i.opacity,1),ls(d,h),S&&(d.lineWidth=ii(h.lineWidth,i.lineWidth,m),d.lineDash=U(h.lineDash,i.lineDash),d.lineDashOffset=i.lineDashOffset||0,d.stroke=S),w&&(d.fill=w);var T=e.contentWidth,C=e.contentHeight;_.setBoundingRect(new X(ue(d.x,T,d.textAlign),kr(d.y,C,d.textBaseline),T,C))},t.prototype._renderBackground=function(e,i,n,a,s,o){var f=e.backgroundColor,h=e.borderWidth,u=e.borderColor,v=f&&f.image,l=f&&!v,c=e.borderRadius,p=this,_,d;if(l||e.lineHeight||h&&u){_=this._getOrCreateChild(yi),_.useStyle(_.createStyle()),_.style.fill=null;var g=_.shape;g.x=n,g.y=a,g.width=s,g.height=o,g.r=c,_.dirtyShape()}if(l){var y=_.style;y.fill=f||null,y.fillOpacity=U(e.fillOpacity,1)}else if(v){d=this._getOrCreateChild(Ie),d.onload=function(){p.dirtyStyle()};var m=d.style;m.image=f.image,m.x=n,m.y=a,m.width=s,m.height=o}if(h&&u){var y=_.style;y.lineWidth=h,y.stroke=u,y.strokeOpacity=U(e.strokeOpacity,1),y.lineDash=e.borderDash,y.lineDashOffset=e.borderDashOffset||0,_.strokeContainThreshold=0,_.hasFill()&&_.hasStroke()&&(y.strokeFirst=!0,y.lineWidth*=2)}var w=(_||d).style;w.shadowBlur=e.shadowBlur||0,w.shadowColor=e.shadowColor||"transparent",w.shadowOffsetX=e.shadowOffsetX||0,w.shadowOffsetY=e.shadowOffsetY||0,w.opacity=ii(e.opacity,i.opacity,1)},t.makeFont=function(e){var i="";return uf(e)&&(i=[e.fontStyle,e.fontWeight,hf(e.fontSize),e.fontFamily||"sans-serif"].join(" ")),i&&$r(i)||e.textFont||e.font},t}(Ae),$v={left:!0,right:1,center:1},Gv={top:1,bottom:1,middle:1},vs=["fontStyle","fontWeight","fontSize","fontFamily"];function hf(r){return typeof r=="string"&&(r.indexOf("px")!==-1||r.indexOf("rem")!==-1||r.indexOf("em")!==-1)?r:isNaN(+r)?sa+"px":r+"px"}function ls(r,t){for(var e=0;e<vs.length;e++){var i=vs[e],n=t[i];n!=null&&(r[i]=n)}}function uf(r){return r.fontSize!=null||r.fontFamily||r.fontWeight}function Nv(r){return cs(r),at(r.rich,cs),r}function cs(r){if(r){r.font=ff.makeFont(r);var t=r.align;t==="middle"&&(t="center"),r.align=t==null||$v[t]?t:"left";var e=r.verticalAlign;e==="center"&&(e="middle"),r.verticalAlign=e==null||Gv[e]?e:"top";var i=r.padding;i&&(r.padding=dh(r.padding))}}function ds(r,t){return r==null||t<=0||r==="transparent"||r==="none"?null:r.image||r.colorStops?"#000":r}function ps(r){return r==null||r==="none"?null:r.image||r.colorStops?"#000":r}function gs(r,t,e){return t==="right"?r-e[1]:t==="center"?r+e[3]/2-e[1]/2:r+e[3]}function _s(r){var t=r.text;return t!=null&&(t+=""),t}function yn(r){return!!(r.backgroundColor||r.lineHeight||r.borderWidth&&r.borderColor)}const Sd=ff;var Ir=tr.CMD,Yv=[[],[],[]],ys=Math.sqrt,Xv=Math.atan2;function vf(r,t){if(!!t){var e=r.data,i=r.len(),n,a,s,o,f,h,u=Ir.M,v=Ir.C,l=Ir.L,c=Ir.R,p=Ir.A,_=Ir.Q;for(s=0,o=0;s<i;){switch(n=e[s++],o=s,a=0,n){case u:a=1;break;case l:a=1;break;case v:a=3;break;case _:a=2;break;case p:var d=t[4],g=t[5],y=ys(t[0]*t[0]+t[1]*t[1]),m=ys(t[2]*t[2]+t[3]*t[3]),w=Xv(-t[1]/m,t[0]/y);e[s]*=y,e[s++]+=d,e[s]*=m,e[s++]+=g,e[s++]*=y,e[s++]*=m,e[s++]+=w,e[s++]+=w,s+=2,o=s;break;case c:h[0]=e[s++],h[1]=e[s++],pe(h,h,t),e[o++]=h[0],e[o++]=h[1],h[0]+=e[s++],h[1]+=e[s++],pe(h,h,t),e[o++]=h[0],e[o++]=h[1]}for(f=0;f<a;f++){var S=Yv[f];S[0]=e[s++],S[1]=e[s++],pe(S,S,t),e[o++]=S[0],e[o++]=S[1]}}r.increaseVersion()}}var mn=Math.sqrt,Ke=Math.sin,Qe=Math.cos,oe=Math.PI;function ms(r){return Math.sqrt(r[0]*r[0]+r[1]*r[1])}function Kn(r,t){return(r[0]*t[0]+r[1]*t[1])/(ms(r)*ms(t))}function ws(r,t){return(r[0]*t[1]<r[1]*t[0]?-1:1)*Math.acos(Kn(r,t))}function Ts(r,t,e,i,n,a,s,o,f,h,u){var v=f*(oe/180),l=Qe(v)*(r-e)/2+Ke(v)*(t-i)/2,c=-1*Ke(v)*(r-e)/2+Qe(v)*(t-i)/2,p=l*l/(s*s)+c*c/(o*o);p>1&&(s*=mn(p),o*=mn(p));var _=(n===a?-1:1)*mn((s*s*(o*o)-s*s*(c*c)-o*o*(l*l))/(s*s*(c*c)+o*o*(l*l)))||0,d=_*s*c/o,g=_*-o*l/s,y=(r+e)/2+Qe(v)*d-Ke(v)*g,m=(t+i)/2+Ke(v)*d+Qe(v)*g,w=ws([1,0],[(l-d)/s,(c-g)/o]),S=[(l-d)/s,(c-g)/o],b=[(-1*l-d)/s,(-1*c-g)/o],T=ws(S,b);if(Kn(S,b)<=-1&&(T=oe),Kn(S,b)>=1&&(T=0),T<0){var C=Math.round(T/oe*1e6)/1e6;T=oe*2+C%2*oe}u.addData(h,y,m,s,o,w,T,v,a)}var Wv=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/ig,qv=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Uv(r){var t=new tr;if(!r)return t;var e=0,i=0,n=e,a=i,s,o=tr.CMD,f=r.match(Wv);if(!f)return t;for(var h=0;h<f.length;h++){for(var u=f[h],v=u.charAt(0),l=void 0,c=u.match(qv)||[],p=c.length,_=0;_<p;_++)c[_]=parseFloat(c[_]);for(var d=0;d<p;){var g=void 0,y=void 0,m=void 0,w=void 0,S=void 0,b=void 0,T=void 0,C=e,M=i,P=void 0,L=void 0;switch(v){case"l":e+=c[d++],i+=c[d++],l=o.L,t.addData(l,e,i);break;case"L":e=c[d++],i=c[d++],l=o.L,t.addData(l,e,i);break;case"m":e+=c[d++],i+=c[d++],l=o.M,t.addData(l,e,i),n=e,a=i,v="l";break;case"M":e=c[d++],i=c[d++],l=o.M,t.addData(l,e,i),n=e,a=i,v="L";break;case"h":e+=c[d++],l=o.L,t.addData(l,e,i);break;case"H":e=c[d++],l=o.L,t.addData(l,e,i);break;case"v":i+=c[d++],l=o.L,t.addData(l,e,i);break;case"V":i=c[d++],l=o.L,t.addData(l,e,i);break;case"C":l=o.C,t.addData(l,c[d++],c[d++],c[d++],c[d++],c[d++],c[d++]),e=c[d-2],i=c[d-1];break;case"c":l=o.C,t.addData(l,c[d++]+e,c[d++]+i,c[d++]+e,c[d++]+i,c[d++]+e,c[d++]+i),e+=c[d-2],i+=c[d-1];break;case"S":g=e,y=i,P=t.len(),L=t.data,s===o.C&&(g+=e-L[P-4],y+=i-L[P-3]),l=o.C,C=c[d++],M=c[d++],e=c[d++],i=c[d++],t.addData(l,g,y,C,M,e,i);break;case"s":g=e,y=i,P=t.len(),L=t.data,s===o.C&&(g+=e-L[P-4],y+=i-L[P-3]),l=o.C,C=e+c[d++],M=i+c[d++],e+=c[d++],i+=c[d++],t.addData(l,g,y,C,M,e,i);break;case"Q":C=c[d++],M=c[d++],e=c[d++],i=c[d++],l=o.Q,t.addData(l,C,M,e,i);break;case"q":C=c[d++]+e,M=c[d++]+i,e+=c[d++],i+=c[d++],l=o.Q,t.addData(l,C,M,e,i);break;case"T":g=e,y=i,P=t.len(),L=t.data,s===o.Q&&(g+=e-L[P-4],y+=i-L[P-3]),e=c[d++],i=c[d++],l=o.Q,t.addData(l,g,y,e,i);break;case"t":g=e,y=i,P=t.len(),L=t.data,s===o.Q&&(g+=e-L[P-4],y+=i-L[P-3]),e+=c[d++],i+=c[d++],l=o.Q,t.addData(l,g,y,e,i);break;case"A":m=c[d++],w=c[d++],S=c[d++],b=c[d++],T=c[d++],C=e,M=i,e=c[d++],i=c[d++],l=o.A,Ts(C,M,e,i,b,T,m,w,S,l,t);break;case"a":m=c[d++],w=c[d++],S=c[d++],b=c[d++],T=c[d++],C=e,M=i,e+=c[d++],i+=c[d++],l=o.A,Ts(C,M,e,i,b,T,m,w,S,l,t);break}}(v==="z"||v==="Z")&&(l=o.Z,t.addData(l),e=n,i=a),s=l}return t.toStatic(),t}var lf=function(r){$(t,r);function t(){return r!==null&&r.apply(this,arguments)||this}return t.prototype.applyTransform=function(e){},t}(K);function cf(r){return r.setData!=null}function df(r,t){var e=Uv(r),i=z({},t);return i.buildPath=function(n){if(cf(n)){n.setData(e.data);var a=n.getContext();a&&n.rebuildPath(a,1)}else{var a=n;e.rebuildPath(a,1)}},i.applyTransform=function(n){vf(e,n),this.dirtyShape()},i}function Vv(r,t){return new lf(df(r,t))}function Cd(r,t){var e=df(r,t),i=function(n){$(a,n);function a(s){var o=n.call(this,s)||this;return o.applyTransform=e.applyTransform,o.buildPath=e.buildPath,o}return a}(lf);return i}function Md(r,t){for(var e=[],i=r.length,n=0;n<i;n++){var a=r[n];e.push(a.getUpdatedPathProxy(!0))}var s=new K(t);return s.createPathProxy(),s.buildPath=function(o){if(cf(o)){o.appendPath(e);var f=o.getContext();f&&o.rebuildPath(f,1)}},s}function pf(r,t){t=t||{};var e=new K;return r.shape&&e.setShape(r.shape),e.setStyle(r.style),t.bakeTransform?vf(e.path,r.getComputedTransform()):t.toLocal?e.setLocalTransform(r.getComputedTransform()):e.copyTransform(r),e.buildPath=r.buildPath,e.applyTransform=e.applyTransform,e.z=r.z,e.z2=r.z2,e.zlevel=r.zlevel,e}var Zv=function(){function r(){this.cx=0,this.cy=0,this.r=0}return r}(),gf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Zv},t.prototype.buildPath=function(e,i){e.moveTo(i.cx+i.r,i.cy),e.arc(i.cx,i.cy,i.r,0,Math.PI*2)},t}(K);gf.prototype.type="circle";const Kv=gf;var Qv=function(){function r(){this.cx=0,this.cy=0,this.rx=0,this.ry=0}return r}(),_f=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new Qv},t.prototype.buildPath=function(e,i){var n=.5522848,a=i.cx,s=i.cy,o=i.rx,f=i.ry,h=o*n,u=f*n;e.moveTo(a-o,s),e.bezierCurveTo(a-o,s-u,a-h,s-f,a,s-f),e.bezierCurveTo(a+h,s-f,a+o,s-u,a+o,s),e.bezierCurveTo(a+o,s+u,a+h,s+f,a,s+f),e.bezierCurveTo(a-h,s+f,a-o,s+u,a-o,s),e.closePath()},t}(K);_f.prototype.type="ellipse";const Jv=_f;var yf=Math.PI,wn=yf*2,cr=Math.sin,Er=Math.cos,jv=Math.acos,et=Math.atan2,bs=Math.abs,_e=Math.sqrt,ve=Math.max,Dt=Math.min,bt=1e-4;function tl(r,t,e,i,n,a,s,o){var f=e-r,h=i-t,u=s-n,v=o-a,l=v*f-u*h;if(!(l*l<bt))return l=(u*(t-a)-v*(r-n))/l,[r+l*f,t+l*h]}function Je(r,t,e,i,n,a,s){var o=r-e,f=t-i,h=(s?a:-a)/_e(o*o+f*f),u=h*f,v=-h*o,l=r+u,c=t+v,p=e+u,_=i+v,d=(l+p)/2,g=(c+_)/2,y=p-l,m=_-c,w=y*y+m*m,S=n-a,b=l*_-p*c,T=(m<0?-1:1)*_e(ve(0,S*S*w-b*b)),C=(b*m-y*T)/w,M=(-b*y-m*T)/w,P=(b*m+y*T)/w,L=(-b*y+m*T)/w,R=C-d,D=M-g,x=P-d,E=L-g;return R*R+D*D>x*x+E*E&&(C=P,M=L),{cx:C,cy:M,x0:-u,y0:-v,x1:C*(n/S-1),y1:M*(n/S-1)}}function rl(r){var t;if(Kr(r)){var e=r.length;if(!e)return r;e===1?t=[r[0],r[0],0,0]:e===2?t=[r[0],r[0],r[1],r[1]]:e===3?t=r.concat(r[2]):t=r}else t=[r,r,r,r];return t}function el(r,t){var e,i=ve(t.r,0),n=ve(t.r0||0,0),a=i>0,s=n>0;if(!(!a&&!s)){if(a||(i=n,n=0),n>i){var o=i;i=n,n=o}var f=t.startAngle,h=t.endAngle;if(!(isNaN(f)||isNaN(h))){var u=t.cx,v=t.cy,l=!!t.clockwise,c=bs(h-f),p=c>wn&&c%wn;if(p>bt&&(c=p),!(i>bt))r.moveTo(u,v);else if(c>wn-bt)r.moveTo(u+i*Er(f),v+i*cr(f)),r.arc(u,v,i,f,h,!l),n>bt&&(r.moveTo(u+n*Er(h),v+n*cr(h)),r.arc(u,v,n,h,f,l));else{var _=void 0,d=void 0,g=void 0,y=void 0,m=void 0,w=void 0,S=void 0,b=void 0,T=void 0,C=void 0,M=void 0,P=void 0,L=void 0,R=void 0,D=void 0,x=void 0,E=i*Er(f),A=i*cr(f),F=n*Er(h),H=n*cr(h),N=c>bt;if(N){var V=t.cornerRadius;V&&(e=rl(V),_=e[0],d=e[1],g=e[2],y=e[3]);var W=bs(i-n)/2;if(m=Dt(W,g),w=Dt(W,y),S=Dt(W,_),b=Dt(W,d),M=T=ve(m,w),P=C=ve(S,b),(T>bt||C>bt)&&(L=i*Er(h),R=i*cr(h),D=n*Er(f),x=n*cr(f),c<yf)){var G=tl(E,A,D,x,L,R,F,H);if(G){var rt=E-G[0],j=A-G[1],pt=L-G[0],Bt=R-G[1],Ht=1/cr(jv((rt*pt+j*Bt)/(_e(rt*rt+j*j)*_e(pt*pt+Bt*Bt)))/2),gt=_e(G[0]*G[0]+G[1]*G[1]);M=Dt(T,(i-gt)/(Ht+1)),P=Dt(C,(n-gt)/(Ht-1))}}}if(!N)r.moveTo(u+E,v+A);else if(M>bt){var Q=Dt(g,M),q=Dt(y,M),I=Je(D,x,E,A,i,Q,l),O=Je(L,R,F,H,i,q,l);r.moveTo(u+I.cx+I.x0,v+I.cy+I.y0),M<T&&Q===q?r.arc(u+I.cx,v+I.cy,M,et(I.y0,I.x0),et(O.y0,O.x0),!l):(Q>0&&r.arc(u+I.cx,v+I.cy,Q,et(I.y0,I.x0),et(I.y1,I.x1),!l),r.arc(u,v,i,et(I.cy+I.y1,I.cx+I.x1),et(O.cy+O.y1,O.cx+O.x1),!l),q>0&&r.arc(u+O.cx,v+O.cy,q,et(O.y1,O.x1),et(O.y0,O.x0),!l))}else r.moveTo(u+E,v+A),r.arc(u,v,i,f,h,!l);if(!(n>bt)||!N)r.lineTo(u+F,v+H);else if(P>bt){var Q=Dt(_,P),q=Dt(d,P),I=Je(F,H,L,R,n,-q,l),O=Je(E,A,D,x,n,-Q,l);r.lineTo(u+I.cx+I.x0,v+I.cy+I.y0),P<C&&Q===q?r.arc(u+I.cx,v+I.cy,P,et(I.y0,I.x0),et(O.y0,O.x0),!l):(q>0&&r.arc(u+I.cx,v+I.cy,q,et(I.y0,I.x0),et(I.y1,I.x1),!l),r.arc(u,v,n,et(I.cy+I.y1,I.cx+I.x1),et(O.cy+O.y1,O.cx+O.x1),l),Q>0&&r.arc(u+O.cx,v+O.cy,Q,et(O.y1,O.x1),et(O.y0,O.x0),!l))}else r.lineTo(u+F,v+H),r.arc(u,v,n,h,f,l)}r.closePath()}}}var il=function(){function r(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0,this.cornerRadius=0}return r}(),mf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new il},t.prototype.buildPath=function(e,i){el(e,i)},t.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},t}(K);mf.prototype.type="sector";const Ss=mf;var nl=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.r0=0}return r}(),wf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new nl},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,s=Math.PI*2;e.moveTo(n+i.r,a),e.arc(n,a,i.r,0,s,!1),e.moveTo(n+i.r0,a),e.arc(n,a,i.r0,0,s,!0)},t}(K);wf.prototype.type="ring";const Pd=wf;function al(r,t,e,i){var n=[],a=[],s=[],o=[],f,h,u,v;if(i){u=[1/0,1/0],v=[-1/0,-1/0];for(var l=0,c=r.length;l<c;l++)Gr(u,u,r[l]),Nr(v,v,r[l]);Gr(u,u,i[0]),Nr(v,v,i[1])}for(var l=0,c=r.length;l<c;l++){var p=r[l];if(e)f=r[l?l-1:c-1],h=r[(l+1)%c];else if(l===0||l===c-1){n.push(yh(r[l]));continue}else f=r[l-1],h=r[l+1];mh(a,h,f),zi(a,a,t);var _=Rn(p,f),d=Rn(p,h),g=_+d;g!==0&&(_/=g,d/=g),zi(s,a,-_),zi(o,a,d);var y=Ra([],p,s),m=Ra([],p,o);i&&(Nr(y,y,u),Gr(y,y,v),Nr(m,m,u),Gr(m,m,v)),n.push(y),n.push(m)}return e&&n.push(n.shift()),n}function Tf(r,t,e){var i=t.smooth,n=t.points;if(n&&n.length>=2){if(i){var a=al(n,i,e,t.smoothConstraint);r.moveTo(n[0][0],n[0][1]);for(var s=n.length,o=0;o<(e?s:s-1);o++){var f=a[o*2],h=a[o*2+1],u=n[(o+1)%s];r.bezierCurveTo(f[0],f[1],h[0],h[1],u[0],u[1])}}else{r.moveTo(n[0][0],n[0][1]);for(var o=1,v=n.length;o<v;o++)r.lineTo(n[o][0],n[o][1])}e&&r.closePath()}}var sl=function(){function r(){this.points=null,this.smooth=0,this.smoothConstraint=null}return r}(),bf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultShape=function(){return new sl},t.prototype.buildPath=function(e,i){Tf(e,i,!0)},t}(K);bf.prototype.type="polygon";const Sf=bf;var ol=function(){function r(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null}return r}(),Cf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new ol},t.prototype.buildPath=function(e,i){Tf(e,i,!1)},t}(K);Cf.prototype.type="polyline";const fl=Cf;var hl={},ul=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1}return r}(),Mf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new ul},t.prototype.buildPath=function(e,i){var n,a,s,o;if(this.subPixelOptimize){var f=Ov(hl,i,this.style);n=f.x1,a=f.y1,s=f.x2,o=f.y2}else n=i.x1,a=i.y1,s=i.x2,o=i.y2;var h=i.percent;h!==0&&(e.moveTo(n,a),h<1&&(s=n*(1-h)+s*h,o=a*(1-h)+o*h),e.lineTo(s,o))},t.prototype.pointAt=function(e){var i=this.shape;return[i.x1*(1-e)+i.x2*e,i.y1*(1-e)+i.y2*e]},t}(K);Mf.prototype.type="line";const vl=Mf;var ft=[],ll=function(){function r(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1}return r}();function Cs(r,t,e){var i=r.cpx2,n=r.cpy2;return i!=null||n!=null?[(e?$a:tt)(r.x1,r.cpx1,r.cpx2,r.x2,t),(e?$a:tt)(r.y1,r.cpy1,r.cpy2,r.y2,t)]:[(e?Ga:it)(r.x1,r.cpx1,r.x2,t),(e?Ga:it)(r.y1,r.cpy1,r.y2,t)]}var Pf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new ll},t.prototype.buildPath=function(e,i){var n=i.x1,a=i.y1,s=i.x2,o=i.y2,f=i.cpx1,h=i.cpy1,u=i.cpx2,v=i.cpy2,l=i.percent;l!==0&&(e.moveTo(n,a),u==null||v==null?(l<1&&(ci(n,f,s,l,ft),f=ft[1],s=ft[2],ci(a,h,o,l,ft),h=ft[1],o=ft[2]),e.quadraticCurveTo(f,h,s,o)):(l<1&&(jt(n,f,u,s,l,ft),f=ft[1],u=ft[2],s=ft[3],jt(a,h,v,o,l,ft),h=ft[1],v=ft[2],o=ft[3]),e.bezierCurveTo(f,h,u,v,s,o)))},t.prototype.pointAt=function(e){return Cs(this.shape,e,!1)},t.prototype.tangentAt=function(e){var i=Cs(this.shape,e,!0);return bh(i,i)},t}(K);Pf.prototype.type="bezier-curve";const Ld=Pf;var cl=function(){function r(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=Math.PI*2,this.clockwise=!0}return r}(),Lf=function(r){$(t,r);function t(e){return r.call(this,e)||this}return t.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},t.prototype.getDefaultShape=function(){return new cl},t.prototype.buildPath=function(e,i){var n=i.cx,a=i.cy,s=Math.max(i.r,0),o=i.startAngle,f=i.endAngle,h=i.clockwise,u=Math.cos(o),v=Math.sin(o);e.moveTo(u*s+n,v*s+a),e.arc(n,a,s,o,f,!h)},t}(K);Lf.prototype.type="arc";const xd=Lf;var dl=function(r){$(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.type="compound",e}return t.prototype._updatePathDirty=function(){for(var e=this.shape.paths,i=this.shapeChanged(),n=0;n<e.length;n++)i=i||e[n].shapeChanged();i&&this.dirtyShape()},t.prototype.beforeBrush=function(){this._updatePathDirty();for(var e=this.shape.paths||[],i=this.getGlobalScale(),n=0;n<e.length;n++)e[n].path||e[n].createPathProxy(),e[n].path.setScale(i[0],i[1],e[n].segmentIgnoreThreshold)},t.prototype.buildPath=function(e,i){for(var n=i.paths||[],a=0;a<n.length;a++)n[a].buildPath(e,n[a].shape,!0)},t.prototype.afterBrush=function(){for(var e=this.shape.paths||[],i=0;i<e.length;i++)e[i].pathUpdated()},t.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),K.prototype.getBoundingRect.call(this)},t}(K);const pl=dl;var gl=function(){function r(t){this.colorStops=t||[]}return r.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},r}();const xf=gl;var _l=function(r){$(t,r);function t(e,i,n,a,s,o){var f=r.call(this,s)||this;return f.x=e==null?0:e,f.y=i==null?0:i,f.x2=n==null?1:n,f.y2=a==null?0:a,f.type="linear",f.global=o||!1,f}return t}(xf);const yl=_l;var ml=function(r){$(t,r);function t(e,i,n,a,s){var o=r.call(this,a)||this;return o.x=e==null?.5:e,o.y=i==null?.5:i,o.r=n==null?.5:n,o.type="radial",o.global=s||!1,o}return t}(xf);const wl=ml;var dr=[0,0],pr=[0,0],je=new k,ti=new k,Tl=function(){function r(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var i=0;i<4;i++)this._corners[i]=new k;for(var i=0;i<2;i++)this._axes[i]=new k;t&&this.fromBoundingRect(t,e)}return r.prototype.fromBoundingRect=function(t,e){var i=this._corners,n=this._axes,a=t.x,s=t.y,o=a+t.width,f=s+t.height;if(i[0].set(a,s),i[1].set(o,s),i[2].set(o,f),i[3].set(a,f),e)for(var h=0;h<4;h++)i[h].transform(e);k.sub(n[0],i[1],i[0]),k.sub(n[1],i[3],i[0]),n[0].normalize(),n[1].normalize();for(var h=0;h<2;h++)this._origin[h]=n[h].dot(i[0])},r.prototype.intersect=function(t,e){var i=!0,n=!e;return je.set(1/0,1/0),ti.set(0,0),!this._intersectCheckOneSide(this,t,je,ti,n,1)&&(i=!1,n)||!this._intersectCheckOneSide(t,this,je,ti,n,-1)&&(i=!1,n)||n||k.copy(e,i?je:ti),i},r.prototype._intersectCheckOneSide=function(t,e,i,n,a,s){for(var o=!0,f=0;f<2;f++){var h=this._axes[f];if(this._getProjMinMaxOnAxis(f,t._corners,dr),this._getProjMinMaxOnAxis(f,e._corners,pr),dr[1]<pr[0]||dr[0]>pr[1]){if(o=!1,a)return o;var u=Math.abs(pr[0]-dr[1]),v=Math.abs(dr[0]-pr[1]);Math.min(u,v)>n.len()&&(u<v?k.scale(n,h,-u*s):k.scale(n,h,v*s))}else if(i){var u=Math.abs(pr[0]-dr[1]),v=Math.abs(dr[0]-pr[1]);Math.min(u,v)<i.len()&&(u<v?k.scale(i,h,u*s):k.scale(i,h,-v*s))}}return o},r.prototype._getProjMinMaxOnAxis=function(t,e,i){for(var n=this._axes[t],a=this._origin,s=e[0].dot(n)+a[t],o=s,f=s,h=1;h<e.length;h++){var u=e[h].dot(n)+a[t];o=Math.min(u,o),f=Math.max(u,f)}i[0]=o,i[1]=f},r}();const Rd=Tl;var bl=[],Sl=function(r){$(t,r);function t(){var e=r!==null&&r.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return t.prototype.traverse=function(e,i){e.call(i,this)},t.prototype.useStyle=function(){this.style={}},t.prototype.getCursor=function(){return this._cursor},t.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},t.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},t.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},t.prototype.addDisplayable=function(e,i){i?this._temporaryDisplayables.push(e):this._displayables.push(e),this.markRedraw()},t.prototype.addDisplayables=function(e,i){i=i||!1;for(var n=0;n<e.length;n++)this.addDisplayable(e[n],i)},t.prototype.getDisplayables=function(){return this._displayables},t.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},t.prototype.eachPendingDisplayable=function(e){for(var i=this._cursor;i<this._displayables.length;i++)e&&e(this._displayables[i]);for(var i=0;i<this._temporaryDisplayables.length;i++)e&&e(this._temporaryDisplayables[i])},t.prototype.update=function(){this.updateTransform();for(var e=this._cursor;e<this._displayables.length;e++){var i=this._displayables[e];i.parent=this,i.update(),i.parent=null}for(var e=0;e<this._temporaryDisplayables.length;e++){var i=this._temporaryDisplayables[e];i.parent=this,i.update(),i.parent=null}},t.prototype.getBoundingRect=function(){if(!this._rect){for(var e=new X(1/0,1/0,-1/0,-1/0),i=0;i<this._displayables.length;i++){var n=this._displayables[i],a=n.getBoundingRect().clone();n.needLocalTransform()&&a.applyTransform(n.getLocalTransform(bl)),e.union(a)}this._rect=e}return this._rect},t.prototype.contain=function(e,i){var n=this.transformCoordToLocal(e,i),a=this.getBoundingRect();if(a.contain(n[0],n[1]))for(var s=0;s<this._displayables.length;s++){var o=this._displayables[s];if(o.contain(e,i))return!0}return!1},t}(Ae);const Dd=Sl;var Cl=Math.round(Math.random()*9),Ml=typeof Object.defineProperty=="function",Pl=function(){function r(){this._id="__ec_inner_"+Cl++}return r.prototype.get=function(t){return this._guard(t)[this._id]},r.prototype.set=function(t,e){var i=this._guard(t);return Ml?Object.defineProperty(i,this._id,{value:e,enumerable:!1,configurable:!0}):i[this._id]=e,this},r.prototype.delete=function(t){return this.has(t)?(delete this._guard(t)[this._id],!0):!1},r.prototype.has=function(t){return!!this._guard(t)[this._id]},r.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},r}();const Ad=Pl;function wr(r){return isFinite(r)}function Ll(r,t,e){var i=t.x==null?0:t.x,n=t.x2==null?1:t.x2,a=t.y==null?0:t.y,s=t.y2==null?0:t.y2;t.global||(i=i*e.width+e.x,n=n*e.width+e.x,a=a*e.height+e.y,s=s*e.height+e.y),i=wr(i)?i:0,n=wr(n)?n:1,a=wr(a)?a:0,s=wr(s)?s:0;var o=r.createLinearGradient(i,a,n,s);return o}function xl(r,t,e){var i=e.width,n=e.height,a=Math.min(i,n),s=t.x==null?.5:t.x,o=t.y==null?.5:t.y,f=t.r==null?.5:t.r;t.global||(s=s*i+e.x,o=o*n+e.y,f=f*a),s=wr(s)?s:.5,o=wr(o)?o:.5,f=f>=0&&wr(f)?f:.5;var h=r.createRadialGradient(s,o,0,s,o,f);return h}function Qn(r,t,e){for(var i=t.type==="radial"?xl(r,t,e):Ll(r,t,e),n=t.colorStops,a=0;a<n.length;a++)i.addColorStop(n[a].offset,n[a].color);return i}function Rl(r,t){if(r===t||!r&&!t)return!1;if(!r||!t||r.length!==t.length)return!0;for(var e=0;e<r.length;e++)if(r[e]!==t[e])return!0;return!1}function ri(r){return parseInt(r,10)}function qr(r,t,e){var i=["width","height"][t],n=["clientWidth","clientHeight"][t],a=["paddingLeft","paddingTop"][t],s=["paddingRight","paddingBottom"][t];if(e[i]!=null&&e[i]!=="auto")return parseFloat(e[i]);var o=document.defaultView.getComputedStyle(r);return(r[n]||ri(o[i])||ri(r.style[i]))-(ri(o[a])||0)-(ri(o[s])||0)|0}function Dl(r,t){return!r||r==="solid"||!(t>0)?null:r==="dashed"?[4*t,2*t]:r==="dotted"?[t]:ce(r)?[r]:Kr(r)?r:null}function _a(r){var t=r.style,e=t.lineDash&&t.lineWidth>0&&Dl(t.lineDash,t.lineWidth),i=t.lineDashOffset;if(e){var n=t.strokeNoScale&&r.getLineScale?r.getLineScale():1;n&&n!==1&&(e=Z(e,function(a){return a/n}),i/=n)}return[e,i]}var Al=new tr(!0);function mi(r){var t=r.stroke;return!(t==null||t==="none"||!(r.lineWidth>0))}function Ms(r){return typeof r=="string"&&r!=="none"}function wi(r){var t=r.fill;return t!=null&&t!=="none"}function Ps(r,t){if(t.fillOpacity!=null&&t.fillOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.fillOpacity*t.opacity,r.fill(),r.globalAlpha=e}else r.fill()}function Ls(r,t){if(t.strokeOpacity!=null&&t.strokeOpacity!==1){var e=r.globalAlpha;r.globalAlpha=t.strokeOpacity*t.opacity,r.stroke(),r.globalAlpha=e}else r.stroke()}function Jn(r,t,e){var i=ga(t.image,t.__image,e);if(Fi(i)){var n=r.createPattern(i,t.repeat||"repeat");if(typeof DOMMatrix=="function"&&n&&n.setTransform){var a=new DOMMatrix;a.translateSelf(t.x||0,t.y||0),a.rotateSelf(0,0,(t.rotation||0)*ni),a.scaleSelf(t.scaleX||1,t.scaleY||1),n.setTransform(a)}return n}}function Il(r,t,e,i){var n,a=mi(e),s=wi(e),o=e.strokePercent,f=o<1,h=!t.path;(!t.silent||f)&&h&&t.createPathProxy();var u=t.path||Al,v=t.__dirty;if(!i){var l=e.fill,c=e.stroke,p=s&&!!l.colorStops,_=a&&!!c.colorStops,d=s&&!!l.image,g=a&&!!c.image,y=void 0,m=void 0,w=void 0,S=void 0,b=void 0;(p||_)&&(b=t.getBoundingRect()),p&&(y=v?Qn(r,l,b):t.__canvasFillGradient,t.__canvasFillGradient=y),_&&(m=v?Qn(r,c,b):t.__canvasStrokeGradient,t.__canvasStrokeGradient=m),d&&(w=v||!t.__canvasFillPattern?Jn(r,l,t):t.__canvasFillPattern,t.__canvasFillPattern=w),g&&(S=v||!t.__canvasStrokePattern?Jn(r,c,t):t.__canvasStrokePattern,t.__canvasStrokePattern=w),p?r.fillStyle=y:d&&(w?r.fillStyle=w:s=!1),_?r.strokeStyle=m:g&&(S?r.strokeStyle=S:a=!1)}var T=t.getGlobalScale();u.setScale(T[0],T[1],t.segmentIgnoreThreshold);var C,M;r.setLineDash&&e.lineDash&&(n=_a(t),C=n[0],M=n[1]);var P=!0;(h||v&Or)&&(u.setDPR(r.dpr),f?u.setContext(null):(u.setContext(r),P=!1),u.reset(),t.buildPath(u,t.shape,i),u.toStatic(),t.pathUpdated()),P&&u.rebuildPath(r,f?o:1),C&&(r.setLineDash(C),r.lineDashOffset=M),i||(e.strokeFirst?(a&&Ls(r,e),s&&Ps(r,e)):(s&&Ps(r,e),a&&Ls(r,e))),C&&r.setLineDash([])}function El(r,t,e){var i=t.__image=ga(e.image,t.__image,t,t.onload);if(!(!i||!Fi(i))){var n=e.x||0,a=e.y||0,s=t.getWidth(),o=t.getHeight(),f=i.width/i.height;if(s==null&&o!=null?s=o*f:o==null&&s!=null?o=s/f:s==null&&o==null&&(s=i.width,o=i.height),e.sWidth&&e.sHeight){var h=e.sx||0,u=e.sy||0;r.drawImage(i,h,u,e.sWidth,e.sHeight,n,a,s,o)}else if(e.sx&&e.sy){var h=e.sx,u=e.sy,v=s-h,l=o-u;r.drawImage(i,h,u,v,l,n,a,s,o)}else r.drawImage(i,n,a,s,o)}}function Fl(r,t,e){var i,n=e.text;if(n!=null&&(n+=""),n){r.font=e.font||Jt,r.textAlign=e.textAlign,r.textBaseline=e.textBaseline;var a=void 0,s=void 0;r.setLineDash&&e.lineDash&&(i=_a(t),a=i[0],s=i[1]),a&&(r.setLineDash(a),r.lineDashOffset=s),e.strokeFirst?(mi(e)&&r.strokeText(n,e.x,e.y),wi(e)&&r.fillText(n,e.x,e.y)):(wi(e)&&r.fillText(n,e.x,e.y),mi(e)&&r.strokeText(n,e.x,e.y)),a&&r.setLineDash([])}}var xs=["shadowBlur","shadowOffsetX","shadowOffsetY"],Rs=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function Rf(r,t,e,i,n){var a=!1;if(!i&&(e=e||{},t===e))return!1;if(i||t.opacity!==e.opacity){vt(r,n),a=!0;var s=Math.max(Math.min(t.opacity,1),0);r.globalAlpha=isNaN(s)?Mr.opacity:s}(i||t.blend!==e.blend)&&(a||(vt(r,n),a=!0),r.globalCompositeOperation=t.blend||Mr.blend);for(var o=0;o<xs.length;o++){var f=xs[o];(i||t[f]!==e[f])&&(a||(vt(r,n),a=!0),r[f]=r.dpr*(t[f]||0))}return(i||t.shadowColor!==e.shadowColor)&&(a||(vt(r,n),a=!0),r.shadowColor=t.shadowColor||Mr.shadowColor),a}function Ds(r,t,e,i,n){var a=Me(t,n.inHover),s=i?null:e&&Me(e,n.inHover)||{};if(a===s)return!1;var o=Rf(r,a,s,i,n);if((i||a.fill!==s.fill)&&(o||(vt(r,n),o=!0),Ms(a.fill)&&(r.fillStyle=a.fill)),(i||a.stroke!==s.stroke)&&(o||(vt(r,n),o=!0),Ms(a.stroke)&&(r.strokeStyle=a.stroke)),(i||a.opacity!==s.opacity)&&(o||(vt(r,n),o=!0),r.globalAlpha=a.opacity==null?1:a.opacity),t.hasStroke()){var f=a.lineWidth,h=f/(a.strokeNoScale&&t.getLineScale?t.getLineScale():1);r.lineWidth!==h&&(o||(vt(r,n),o=!0),r.lineWidth=h)}for(var u=0;u<Rs.length;u++){var v=Rs[u],l=v[0];(i||a[l]!==s[l])&&(o||(vt(r,n),o=!0),r[l]=a[l]||v[1])}return o}function Ol(r,t,e,i,n){return Rf(r,Me(t,n.inHover),e&&Me(e,n.inHover),i,n)}function Df(r,t){var e=t.transform,i=r.dpr||1;e?r.setTransform(i*e[0],i*e[1],i*e[2],i*e[3],i*e[4],i*e[5]):r.setTransform(i,0,0,i,0,0)}function kl(r,t,e){for(var i=!1,n=0;n<r.length;n++){var a=r[n];i=i||a.isZeroArea(),Df(t,a),t.beginPath(),a.buildPath(t,a.shape),t.clip()}e.allClipped=i}function Bl(r,t){return r&&t?r[0]!==t[0]||r[1]!==t[1]||r[2]!==t[2]||r[3]!==t[3]||r[4]!==t[4]||r[5]!==t[5]:!(!r&&!t)}var As=1,Is=2,Es=3,Fs=4;function Hl(r){var t=wi(r),e=mi(r);return!(r.lineDash||!(+t^+e)||t&&typeof r.fill!="string"||e&&typeof r.stroke!="string"||r.strokePercent<1||r.strokeOpacity<1||r.fillOpacity<1)}function vt(r,t){t.batchFill&&r.fill(),t.batchStroke&&r.stroke(),t.batchFill="",t.batchStroke=""}function Me(r,t){return t&&r.__hoverStyle||r.style}function zl(r,t){Tr(r,t,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function Tr(r,t,e,i){var n=t.transform;if(!t.shouldBePainted(e.viewWidth,e.viewHeight,!1,!1)){t.__dirty&=~ct,t.__isRendered=!1;return}var a=t.__clipPaths,s=e.prevElClipPaths,o=!1,f=!1;if((!s||Rl(a,s))&&(s&&s.length&&(vt(r,e),r.restore(),f=o=!0,e.prevElClipPaths=null,e.allClipped=!1,e.prevEl=null),a&&a.length&&(vt(r,e),r.save(),kl(a,r,e),o=!0),e.prevElClipPaths=a),e.allClipped){t.__isRendered=!1;return}t.beforeBrush&&t.beforeBrush(),t.innerBeforeBrush();var h=e.prevEl;h||(f=o=!0);var u=t instanceof K&&t.autoBatch&&Hl(t.style);o||Bl(n,h.transform)?(vt(r,e),Df(r,t)):u||vt(r,e);var v=Me(t,e.inHover);t instanceof K?(e.lastDrawType!==As&&(f=!0,e.lastDrawType=As),Ds(r,t,h,f,e),(!u||!e.batchFill&&!e.batchStroke)&&r.beginPath(),Il(r,t,v,u),u&&(e.batchFill=v.fill||"",e.batchStroke=v.stroke||"")):t instanceof Ce?(e.lastDrawType!==Es&&(f=!0,e.lastDrawType=Es),Ds(r,t,h,f,e),Fl(r,t,v)):t instanceof Ie?(e.lastDrawType!==Is&&(f=!0,e.lastDrawType=Is),Ol(r,t,h,f,e),El(r,t,v)):t.getTemporalDisplayables&&(e.lastDrawType!==Fs&&(f=!0,e.lastDrawType=Fs),$l(r,t,e)),u&&i&&vt(r,e),t.innerAfterBrush(),t.afterBrush&&t.afterBrush(),e.prevEl=t,t.__dirty=0,t.__isRendered=!0}function $l(r,t,e){var i=t.getDisplayables(),n=t.getTemporalDisplayables();r.save();var a={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:e.viewWidth,viewHeight:e.viewHeight,inHover:e.inHover},s,o;for(s=t.getCursor(),o=i.length;s<o;s++){var f=i[s];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),Tr(r,f,a,s===o-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),a.prevEl=f}for(var h=0,u=n.length;h<u;h++){var f=n[h];f.beforeBrush&&f.beforeBrush(),f.innerBeforeBrush(),Tr(r,f,a,h===u-1),f.innerAfterBrush(),f.afterBrush&&f.afterBrush(),a.prevEl=f}t.clearTemporalDisplayables(),t.notClear=!0,r.restore()}var Gl=1e-8;function Os(r,t){return Math.abs(r-t)<Gl}function Id(r,t,e){var i=0,n=r[0];if(!n)return!1;for(var a=1;a<r.length;a++){var s=r[a];i+=Nt(n[0],n[1],s[0],s[1],t,e),n=s}var o=r[0];return(!Os(n[0],o[0])||!Os(n[1],o[1]))&&(i+=Nt(n[0],n[1],o[0],o[1],t,e)),i!==0}var Tn=Math.sin,bn=Math.cos,Af=Math.PI,gr=Math.PI*2,Nl=180/Af,Yl=function(){function r(){}return r.prototype.reset=function(t){this._start=!0,this._d=[],this._str="",this._p=Math.pow(10,t||4)},r.prototype.moveTo=function(t,e){this._add("M",t,e)},r.prototype.lineTo=function(t,e){this._add("L",t,e)},r.prototype.bezierCurveTo=function(t,e,i,n,a,s){this._add("C",t,e,i,n,a,s)},r.prototype.quadraticCurveTo=function(t,e,i,n){this._add("Q",t,e,i,n)},r.prototype.arc=function(t,e,i,n,a,s){this.ellipse(t,e,i,i,0,n,a,s)},r.prototype.ellipse=function(t,e,i,n,a,s,o,f){var h=o-s,u=!f,v=Math.abs(h),l=Kt(v-gr)||(u?h>=gr:-h>=gr),c=h>0?h%gr:h%gr+gr,p=!1;l?p=!0:Kt(v)?p=!1:p=c>=Af==!!u;var _=t+i*bn(s),d=e+n*Tn(s);this._start&&this._add("M",_,d);var g=Math.round(a*Nl);if(l){var y=1/this._p,m=(u?1:-1)*(gr-y);this._add("A",i,n,g,1,+u,t+i*bn(s+m),e+n*Tn(s+m)),y>.01&&this._add("A",i,n,g,0,+u,_,d)}else{var w=t+i*bn(o),S=e+n*Tn(o);this._add("A",i,n,g,+p,+u,w,S)}},r.prototype.rect=function(t,e,i,n){this._add("M",t,e),this._add("l",i,0),this._add("l",0,n),this._add("l",-i,0),this._add("Z")},r.prototype.closePath=function(){this._d.length>0&&this._add("Z")},r.prototype._add=function(t,e,i,n,a,s,o,f,h){for(var u=[],v=this._p,l=1;l<arguments.length;l++){var c=arguments[l];if(isNaN(c)){this._invalid=!0;return}u.push(Math.round(c*v)/v)}this._d.push(t+u.join(" ")),this._start=t==="Z"},r.prototype.generateStr=function(){this._str=this._invalid?"":this._d.join(""),this._d=[]},r.prototype.getStr=function(){return this._str},r}();const If=Yl;var ye="none",Xl=Math.round;function Wl(r){var t=r.fill;return t!=null&&t!==ye}function ql(r){var t=r.stroke;return t!=null&&t!==ye}var jn=["lineCap","miterLimit","lineJoin"],Ul=Z(jn,function(r){return"stroke-"+r.toLowerCase()});function Vl(r,t,e,i){var n=t.opacity==null?1:t.opacity;if(e instanceof Ie){r("opacity",n);return}if(Wl(t)){var a=Te(t.fill);r("fill",a.color);var s=t.fillOpacity!=null?t.fillOpacity*a.opacity*n:a.opacity*n;(i||s<1)&&r("fill-opacity",s)}else r("fill",ye);if(ql(t)){var o=Te(t.stroke);r("stroke",o.color);var f=t.strokeNoScale?e.getLineScale():1,h=f?(t.lineWidth||0)/f:0,u=t.strokeOpacity!=null?t.strokeOpacity*o.opacity*n:o.opacity*n,v=t.strokeFirst;if((i||h!==1)&&r("stroke-width",h),(i||v)&&r("paint-order",v?"stroke":"fill"),(i||u<1)&&r("stroke-opacity",u),t.lineDash){var l=_a(e),c=l[0],p=l[1];c&&(p=Xl(p||0),r("stroke-dasharray",c.join(",")),(p||i)&&r("stroke-dashoffset",p))}else i&&r("stroke-dasharray",ye);for(var _=0;_<jn.length;_++){var d=jn[_];if(i||t[d]!==_i[d]){var g=t[d]||_i[d];g&&r(Ul[_],g)}}}else i&&r("stroke",ye)}var Ef="http://www.w3.org/2000/svg",Ff="http://www.w3.org/1999/xlink",Zl="http://www.w3.org/2000/xmlns/",Kl="http://www.w3.org/XML/1998/namespace",ks="ecmeta_";function Of(r){return document.createElementNS(Ef,r)}function J(r,t,e,i,n){return{tag:r,attrs:e||{},children:i,text:n,key:t}}function Ql(r,t){var e=[];if(t)for(var i in t){var n=t[i],a=i;n!==!1&&(n!==!0&&n!=null&&(a+='="'+n+'"'),e.push(a))}return"<"+r+" "+e.join(" ")+">"}function Jl(r){return"</"+r+">"}function ya(r,t){t=t||{};var e=t.newline?`
`:"";function i(n){var a=n.children,s=n.tag,o=n.attrs,f=n.text;return Ql(s,o)+(s!=="style"?Fh(f):f||"")+(a?""+e+Z(a,function(h){return i(h)}).join(e)+e:"")+Jl(s)}return i(r)}function jl(r,t,e){e=e||{};var i=e.newline?`
`:"",n=" {"+i,a=i+"}",s=Z(Y(r),function(f){return f+n+Z(Y(r[f]),function(h){return h+":"+r[f][h]+";"}).join(i)+a}).join(i),o=Z(Y(t),function(f){return"@keyframes "+f+n+Z(Y(t[f]),function(h){return h+n+Z(Y(t[f][h]),function(u){var v=t[f][h][u];return u==="d"&&(v='path("'+v+'")'),u+":"+v+";"}).join(i)+a}).join(i)+a}).join(i);return!s&&!o?"":["<![CDATA[",s,o,"]]>"].join(i)}function ta(r){return{zrId:r,shadowCache:{},patternCache:{},gradientCache:{},clipPathCache:{},defs:{},cssNodes:{},cssAnims:{},cssStyleCache:{},cssAnimIdx:0,shadowIdx:0,gradientIdx:0,patternIdx:0,clipPathIdx:0}}function Bs(r,t,e,i){return J("svg","root",{width:r,height:t,xmlns:Ef,"xmlns:xlink":Ff,version:"1.1",baseProfile:"full",viewBox:i?"0 0 "+r+" "+t:!1},e)}var tc=0;function kf(){return tc++}var Hs={cubicIn:"0.32,0,0.67,0",cubicOut:"0.33,1,0.68,1",cubicInOut:"0.65,0,0.35,1",quadraticIn:"0.11,0,0.5,0",quadraticOut:"0.5,1,0.89,1",quadraticInOut:"0.45,0,0.55,1",quarticIn:"0.5,0,0.75,0",quarticOut:"0.25,1,0.5,1",quarticInOut:"0.76,0,0.24,1",quinticIn:"0.64,0,0.78,0",quinticOut:"0.22,1,0.36,1",quinticInOut:"0.83,0,0.17,1",sinusoidalIn:"0.12,0,0.39,0",sinusoidalOut:"0.61,1,0.88,1",sinusoidalInOut:"0.37,0,0.63,1",exponentialIn:"0.7,0,0.84,0",exponentialOut:"0.16,1,0.3,1",exponentialInOut:"0.87,0,0.13,1",circularIn:"0.55,0,1,0.45",circularOut:"0,0.55,0.45,1",circularInOut:"0.85,0,0.15,1"},yr="transform-origin";function rc(r,t,e){var i=z({},r.shape);z(i,t),r.buildPath(e,i);var n=new If;return n.reset(Yo(r)),e.rebuildPath(n,1),n.generateStr(),n.getStr()}function ec(r,t){var e=t.originX,i=t.originY;(e||i)&&(r[yr]=e+"px "+i+"px")}var ic={fill:"fill",opacity:"opacity",lineWidth:"stroke-width",lineDashOffset:"stroke-dashoffset"};function Bf(r,t){var e=t.zrId+"-ani-"+t.cssAnimIdx++;return t.cssAnims[e]=r,e}function nc(r,t,e){var i=r.shape.paths,n={},a,s;if(at(i,function(f){var h=ta(e.zrId);h.animation=!0,ki(f,{},h,!0);var u=h.cssAnims,v=h.cssNodes,l=Y(u),c=l.length;if(!!c){s=l[c-1];var p=u[s];for(var _ in p){var d=p[_];n[_]=n[_]||{d:""},n[_].d+=d.d||""}for(var g in v){var y=v[g].animation;y.indexOf(s)>=0&&(a=y)}}}),!!a){t.d=!1;var o=Bf(n,e);return a.replace(s,o)}}function zs(r){return kt(r)?Hs[r]?"cubic-bezier("+Hs[r]+")":va(r)?r:"":""}function ki(r,t,e,i){var n=r.animators,a=n.length,s=[];if(r instanceof pl){var o=nc(r,t,e);if(o)s.push(o);else if(!a)return}else if(!a)return;for(var f={},h=0;h<a;h++){var u=n[h],v=[u.getMaxTime()/1e3+"s"],l=zs(u.getClip().easing),c=u.getDelay();l?v.push(l):v.push("linear"),c&&v.push(c/1e3+"s"),u.getLoop()&&v.push("infinite");var p=v.join(" ");f[p]=f[p]||[p,[]],f[p][1].push(u)}function _(y){var m=y[1],w=m.length,S={},b={},T={},C="animation-timing-function";function M(gt,Q,q){for(var I=gt.getTracks(),O=gt.getMaxTime(),zt=0;zt<I.length;zt++){var ot=I[zt];if(ot.needsAnimate()){var Sa=ot.keyframes,Ee=ot.propName;if(q&&(Ee=q(Ee)),Ee)for(var Hi=0;Hi<Sa.length;Hi++){var Fe=Sa[Hi],Oe=Math.round(Fe.time/O*100)+"%",Ca=zs(Fe.easing),Ma=Fe.rawValue;(kt(Ma)||ce(Ma))&&(Q[Oe]=Q[Oe]||{},Q[Oe][Ee]=Fe.rawValue,Ca&&(Q[Oe][C]=Ca))}}}}for(var P=0;P<w;P++){var L=m[P],R=L.targetName;R?R==="shape"&&M(L,b):!i&&M(L,S)}for(var D in S){var x={};Vo(x,r),z(x,S[D]);var E=Xo(x),A=S[D][C];T[D]=E?{transform:E}:{},ec(T[D],x),A&&(T[D][C]=A)}var F,H=!0;for(var D in b){T[D]=T[D]||{};var N=!F,A=b[D][C];N&&(F=new tr);var V=F.len();F.reset(),T[D].d=rc(r,b[D],F);var W=F.len();if(!N&&V!==W){H=!1;break}A&&(T[D][C]=A)}if(!H)for(var D in T)delete T[D].d;if(!i)for(var P=0;P<w;P++){var L=m[P],R=L.targetName;R==="style"&&M(L,T,function(I){return ic[I]})}for(var G=Y(T),rt=!0,j,P=1;P<G.length;P++){var pt=G[P-1],Bt=G[P];if(T[pt][yr]!==T[Bt][yr]){rt=!1;break}j=T[pt][yr]}if(rt&&j){for(var D in T)T[D][yr]&&delete T[D][yr];t[yr]=j}if(Ln(G,function(gt){return Y(T[gt]).length>0}).length){var Ht=Bf(T,e);return Ht+" "+y[0]+" both"}}for(var d in f){var o=_(f[d]);o&&s.push(o)}if(s.length){var g=e.zrId+"-cls-"+kf();e.cssNodes["."+g]={animation:s.join(",")},t.class=g}}function ac(r,t,e){if(!r.ignore)if(r.isSilent()){var i={"pointer-events":"none"};$s(i,t,e,!0)}else{var n=r.states.emphasis&&r.states.emphasis.style?r.states.emphasis.style:{},a=n.fill;if(!a){var s=r.style&&r.style.fill,o=r.states.select&&r.states.select.style&&r.states.select.style.fill,f=r.currentStates.indexOf("select")>=0&&o||s;f&&(a=_u(f))}var h=n.lineWidth;if(h){var u=!n.strokeNoScale&&r.transform?r.transform[0]:1;h=h/u}var i={cursor:"pointer"};a&&(i.fill=a),n.stroke&&(i.stroke=n.stroke),h&&(i["stroke-width"]=h),$s(i,t,e,!0)}}function $s(r,t,e,i){var n=JSON.stringify(r),a=e.cssStyleCache[n];a||(a=e.zrId+"-cls-"+kf(),e.cssStyleCache[n]=a,e.cssNodes["."+a+(i?":hover":"")]=r),t.class=t.class?t.class+" "+a:a}var Pe=Math.round;function Hf(r){return r&&kt(r.src)}function zf(r){return r&&Re(r.toDataURL)}function ma(r,t,e,i){Vl(function(n,a){var s=n==="fill"||n==="stroke";s&&No(a)?Gf(t,r,n,i):s&&la(a)?Nf(e,r,n,i):r[n]=a,s&&i.ssr&&a==="none"&&(r["pointer-events"]="visible")},t,e,!1),lc(e,r,i)}function wa(r,t){var e=ju(t);e&&(e.each(function(i,n){i!=null&&(r[(ks+n).toLowerCase()]=i+"")}),t.isSilent()&&(r[ks+"silent"]="true"))}function Gs(r){return Kt(r[0]-1)&&Kt(r[1])&&Kt(r[2])&&Kt(r[3]-1)}function sc(r){return Kt(r[4])&&Kt(r[5])}function Ta(r,t,e){if(t&&!(sc(t)&&Gs(t))){var i=e?10:1e4;r.transform=Gs(t)?"translate("+Pe(t[4]*i)/i+" "+Pe(t[5]*i)/i+")":yu(t)}}function Ns(r,t,e){for(var i=r.points,n=[],a=0;a<i.length;a++)n.push(Pe(i[a][0]*e)/e),n.push(Pe(i[a][1]*e)/e);t.points=n.join(" ")}function Ys(r){return!r.smooth}function oc(r){var t=Z(r,function(e){return typeof e=="string"?[e,e]:e});return function(e,i,n){for(var a=0;a<t.length;a++){var s=t[a],o=e[s[0]];o!=null&&(i[s[1]]=Pe(o*n)/n)}}}var fc={circle:[oc(["cx","cy","r"])],polyline:[Ns,Ys],polygon:[Ns,Ys]};function hc(r){for(var t=r.animators,e=0;e<t.length;e++)if(t[e].targetName==="shape")return!0;return!1}function $f(r,t){var e=r.style,i=r.shape,n=fc[r.type],a={},s=t.animation,o="path",f=r.style.strokePercent,h=t.compress&&Yo(r)||4;if(n&&!t.willUpdate&&!(n[1]&&!n[1](i))&&!(s&&hc(r))&&!(f<1)){o=r.type;var u=Math.pow(10,h);n[0](i,a,u)}else{var v=!r.path||r.shapeChanged();r.path||r.createPathProxy();var l=r.path;v&&(l.beginPath(),r.buildPath(l,r.shape),r.pathUpdated());var c=l.getVersion(),p=r,_=p.__svgPathBuilder;(p.__svgPathVersion!==c||!_||f!==p.__svgPathStrokePercent)&&(_||(_=p.__svgPathBuilder=new If),_.reset(h),l.rebuildPath(_,f),_.generateStr(),p.__svgPathVersion=c,p.__svgPathStrokePercent=f),a.d=_.getStr()}return Ta(a,r.transform),ma(a,e,r,t),wa(a,r),t.animation&&ki(r,a,t),t.emphasis&&ac(r,a,t),J(o,r.id+"",a)}function uc(r,t){var e=r.style,i=e.image;if(i&&!kt(i)&&(Hf(i)?i=i.src:zf(i)&&(i=i.toDataURL())),!!i){var n=e.x||0,a=e.y||0,s=e.width,o=e.height,f={href:i,width:s,height:o};return n&&(f.x=n),a&&(f.y=a),Ta(f,r.transform),ma(f,e,r,t),wa(f,r),t.animation&&ki(r,f,t),J("image",r.id+"",f)}}function vc(r,t){var e=r.style,i=e.text;if(i!=null&&(i+=""),!(!i||isNaN(e.x)||isNaN(e.y))){var n=e.font||Jt,a=e.x||0,s=wu(e.y||0,Ei(n),e.textBaseline),o=mu[e.textAlign]||e.textAlign,f={"dominant-baseline":"central","text-anchor":o};if(uf(e)){var h="",u=e.fontStyle,v=hf(e.fontSize);if(!parseFloat(v))return;var l=e.fontFamily||lo,c=e.fontWeight;h+="font-size:"+v+";font-family:"+l+";",u&&u!=="normal"&&(h+="font-style:"+u+";"),c&&c!=="normal"&&(h+="font-weight:"+c+";"),f.style=h}else f.style="font: "+n;return i.match(/\s/)&&(f["xml:space"]="preserve"),a&&(f.x=a),s&&(f.y=s),Ta(f,r.transform),ma(f,e,r,t),wa(f,r),t.animation&&ki(r,f,t),J("text",r.id+"",f,void 0,i)}}function Xs(r,t){if(r instanceof K)return $f(r,t);if(r instanceof Ie)return uc(r,t);if(r instanceof Ce)return vc(r,t)}function lc(r,t,e){var i=r.style;if(Tu(i)){var n=bu(r),a=e.shadowCache,s=a[n];if(!s){var o=r.getGlobalScale(),f=o[0],h=o[1];if(!f||!h)return;var u=i.shadowOffsetX||0,v=i.shadowOffsetY||0,l=i.shadowBlur,c=Te(i.shadowColor),p=c.opacity,_=c.color,d=l/2/f,g=l/2/h,y=d+" "+g;s=e.zrId+"-s"+e.shadowIdx++,e.defs[s]=J("filter",s,{id:s,x:"-100%",y:"-100%",width:"300%",height:"300%"},[J("feDropShadow","",{dx:u/f,dy:v/h,stdDeviation:y,"flood-color":_,"flood-opacity":p})]),a[n]=s}t.filter=Ii(s)}}function Gf(r,t,e,i){var n=r[e],a,s={gradientUnits:n.global?"userSpaceOnUse":"objectBoundingBox"};if($o(n))a="linearGradient",s.x1=n.x,s.y1=n.y,s.x2=n.x2,s.y2=n.y2;else if(Go(n))a="radialGradient",s.cx=U(n.x,.5),s.cy=U(n.y,.5),s.r=U(n.r,.5);else return;for(var o=n.colorStops,f=[],h=0,u=o.length;h<u;++h){var v=Bn(o[h].offset)*100+"%",l=o[h].color,c=Te(l),p=c.color,_=c.opacity,d={offset:v};d["stop-color"]=p,_<1&&(d["stop-opacity"]=_),f.push(J("stop",h+"",d))}var g=J(a,"",s,f),y=ya(g),m=i.gradientCache,w=m[y];w||(w=i.zrId+"-g"+i.gradientIdx++,m[y]=w,s.id=w,i.defs[w]=J(a,w,s,f)),t[e]=Ii(w)}function Nf(r,t,e,i){var n=r.style[e],a=r.getBoundingRect(),s={},o=n.repeat,f=o==="no-repeat",h=o==="repeat-x",u=o==="repeat-y",v;if(zo(n)){var l=n.imageWidth,c=n.imageHeight,p=void 0,_=n.image;if(kt(_)?p=_:Hf(_)?p=_.src:zf(_)&&(p=_.toDataURL()),typeof Image>"u"){var d="Image width/height must been given explictly in svg-ssr renderer.";xa(l,d),xa(c,d)}else if(l==null||c==null){var g=function(P,L){if(P){var R=P.elm,D=l||L.width,x=c||L.height;P.tag==="pattern"&&(h?(x=1,D/=a.width):u&&(D=1,x/=a.height)),P.attrs.width=D,P.attrs.height=x,R&&(R.setAttribute("width",D),R.setAttribute("height",x))}},y=ga(p,null,r,function(P){f||g(b,P),g(v,P)});y&&y.width&&y.height&&(l=l||y.width,c=c||y.height)}v=J("image","img",{href:p,width:l,height:c}),s.width=l,s.height=c}else n.svgElement&&(v=Pr(n.svgElement),s.width=n.svgWidth,s.height=n.svgHeight);if(!!v){var m,w;f?m=w=1:h?(w=1,m=s.width/a.width):u?(m=1,w=s.height/a.height):s.patternUnits="userSpaceOnUse",m!=null&&!isNaN(m)&&(s.width=m),w!=null&&!isNaN(w)&&(s.height=w);var S=Xo(n);S&&(s.patternTransform=S);var b=J("pattern","",s,[v]),T=ya(b),C=i.patternCache,M=C[T];M||(M=i.zrId+"-p"+i.patternIdx++,C[T]=M,s.id=M,b=i.defs[M]=J("pattern",M,s,[v])),t[e]=Ii(M)}}function cc(r,t,e){var i=e.clipPathCache,n=e.defs,a=i[r.id];if(!a){a=e.zrId+"-c"+e.clipPathIdx++;var s={id:a};i[r.id]=a,n[a]=J("clipPath",a,s,[$f(r,e)])}t["clip-path"]=Ii(a)}function Ws(r){return document.createTextNode(r)}function mr(r,t,e){r.insertBefore(t,e)}function qs(r,t){r.removeChild(t)}function Us(r,t){r.appendChild(t)}function Yf(r){return r.parentNode}function Xf(r){return r.nextSibling}function Sn(r,t){r.textContent=t}var Vs=58,dc=120,pc=J("","");function ra(r){return r===void 0}function It(r){return r!==void 0}function gc(r,t,e){for(var i={},n=t;n<=e;++n){var a=r[n].key;a!==void 0&&(i[a]=n)}return i}function le(r,t){var e=r.key===t.key,i=r.tag===t.tag;return i&&e}function Le(r){var t,e=r.children,i=r.tag;if(It(i)){var n=r.elm=Of(i);if(ba(pc,r),Kr(e))for(t=0;t<e.length;++t){var a=e[t];a!=null&&Us(n,Le(a))}else It(r.text)&&!Yt(r.text)&&Us(n,Ws(r.text))}else r.elm=Ws(r.text);return r.elm}function Wf(r,t,e,i,n){for(;i<=n;++i){var a=e[i];a!=null&&mr(r,Le(a),t)}}function Ti(r,t,e,i){for(;e<=i;++e){var n=t[e];if(n!=null)if(It(n.tag)){var a=Yf(n.elm);qs(a,n.elm)}else qs(r,n.elm)}}function ba(r,t){var e,i=t.elm,n=r&&r.attrs||{},a=t.attrs||{};if(n!==a){for(e in a){var s=a[e],o=n[e];o!==s&&(s===!0?i.setAttribute(e,""):s===!1?i.removeAttribute(e):e==="style"?i.style.cssText=s:e.charCodeAt(0)!==dc?i.setAttribute(e,s):e==="xmlns:xlink"||e==="xmlns"?i.setAttributeNS(Zl,e,s):e.charCodeAt(3)===Vs?i.setAttributeNS(Kl,e,s):e.charCodeAt(5)===Vs?i.setAttributeNS(Ff,e,s):i.setAttribute(e,s))}for(e in n)e in a||i.removeAttribute(e)}}function _c(r,t,e){for(var i=0,n=0,a=t.length-1,s=t[0],o=t[a],f=e.length-1,h=e[0],u=e[f],v,l,c,p;i<=a&&n<=f;)s==null?s=t[++i]:o==null?o=t[--a]:h==null?h=e[++n]:u==null?u=e[--f]:le(s,h)?(Hr(s,h),s=t[++i],h=e[++n]):le(o,u)?(Hr(o,u),o=t[--a],u=e[--f]):le(s,u)?(Hr(s,u),mr(r,s.elm,Xf(o.elm)),s=t[++i],u=e[--f]):le(o,h)?(Hr(o,h),mr(r,o.elm,s.elm),o=t[--a],h=e[++n]):(ra(v)&&(v=gc(t,i,a)),l=v[h.key],ra(l)?mr(r,Le(h),s.elm):(c=t[l],c.tag!==h.tag?mr(r,Le(h),s.elm):(Hr(c,h),t[l]=void 0,mr(r,c.elm,s.elm))),h=e[++n]);(i<=a||n<=f)&&(i>a?(p=e[f+1]==null?null:e[f+1].elm,Wf(r,p,e,n,f)):Ti(r,t,i,a))}function Hr(r,t){var e=t.elm=r.elm,i=r.children,n=t.children;r!==t&&(ba(r,t),ra(t.text)?It(i)&&It(n)?i!==n&&_c(e,i,n):It(n)?(It(r.text)&&Sn(e,""),Wf(e,null,n,0,n.length-1)):It(i)?Ti(e,i,0,i.length-1):It(r.text)&&Sn(e,""):r.text!==t.text&&(It(i)&&Ti(e,i,0,i.length-1),Sn(e,t.text)))}function yc(r,t){if(le(r,t))Hr(r,t);else{var e=r.elm,i=Yf(e);Le(t),i!==null&&(mr(i,t.elm,Xf(e)),Ti(i,[r],0,0))}return t}var mc=0,wc=function(){function r(t,e,i){if(this.type="svg",this.refreshHover=Zs(),this.configLayer=Zs(),this.storage=e,this._opts=i=z({},i),this.root=t,this._id="zr"+mc++,this._oldVNode=Bs(i.width,i.height),t&&!i.ssr){var n=this._viewport=document.createElement("div");n.style.cssText="position:relative;overflow:hidden";var a=this._svgDom=this._oldVNode.elm=Of("svg");ba(null,this._oldVNode),n.appendChild(a),t.appendChild(n)}this.resize(i.width,i.height)}return r.prototype.getType=function(){return this.type},r.prototype.getViewportRoot=function(){return this._viewport},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.getSvgDom=function(){return this._svgDom},r.prototype.refresh=function(){if(this.root){var t=this.renderToVNode({willUpdate:!0});t.attrs.style="position:absolute;left:0;top:0;user-select:none",yc(this._oldVNode,t),this._oldVNode=t}},r.prototype.renderOneToVNode=function(t){return Xs(t,ta(this._id))},r.prototype.renderToVNode=function(t){t=t||{};var e=this.storage.getDisplayList(!0),i=this._width,n=this._height,a=ta(this._id);a.animation=t.animation,a.willUpdate=t.willUpdate,a.compress=t.compress,a.emphasis=t.emphasis,a.ssr=this._opts.ssr;var s=[],o=this._bgVNode=Tc(i,n,this._backgroundColor,a);o&&s.push(o);var f=t.compress?null:this._mainVNode=J("g","main",{},[]);this._paintList(e,a,f?f.children:s),f&&s.push(f);var h=Z(Y(a.defs),function(l){return a.defs[l]});if(h.length&&s.push(J("defs","defs",{},h)),t.animation){var u=jl(a.cssNodes,a.cssAnims,{newline:!0});if(u){var v=J("style","stl",{},[],u);s.push(v)}}return Bs(i,n,s,t.useViewBox)},r.prototype.renderToString=function(t){return t=t||{},ya(this.renderToVNode({animation:U(t.cssAnimation,!0),emphasis:U(t.cssEmphasis,!0),willUpdate:!1,compress:!0,useViewBox:U(t.useViewBox,!0)}),{newline:!0})},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t},r.prototype.getSvgRoot=function(){return this._mainVNode&&this._mainVNode.elm},r.prototype._paintList=function(t,e,i){for(var n=t.length,a=[],s=0,o,f,h=0,u=0;u<n;u++){var v=t[u];if(!v.invisible){var l=v.__clipPaths,c=l&&l.length||0,p=f&&f.length||0,_=void 0;for(_=Math.max(c-1,p-1);_>=0&&!(l&&f&&l[_]===f[_]);_--);for(var d=p-1;d>_;d--)s--,o=a[s-1];for(var g=_+1;g<c;g++){var y={};cc(l[g],y,e);var m=J("g","clip-g-"+h++,y,[]);(o?o.children:i).push(m),a[s++]=m,o=m}f=l;var w=Xs(v,e);w&&(o?o.children:i).push(w)}}},r.prototype.resize=function(t,e){var i=this._opts,n=this.root,a=this._viewport;if(t!=null&&(i.width=t),e!=null&&(i.height=e),n&&a&&(a.style.display="none",t=qr(n,0,i),e=qr(n,1,i),a.style.display=""),this._width!==t||this._height!==e){if(this._width=t,this._height=e,a){var s=a.style;s.width=t+"px",s.height=e+"px"}if(la(this._backgroundColor))this.refresh();else{var o=this._svgDom;o&&(o.setAttribute("width",t),o.setAttribute("height",e));var f=this._bgVNode&&this._bgVNode.elm;f&&(f.setAttribute("width",t),f.setAttribute("height",e))}}},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r.prototype.dispose=function(){this.root&&(this.root.innerHTML=""),this._svgDom=this._viewport=this.storage=this._oldVNode=this._bgVNode=this._mainVNode=null},r.prototype.clear=function(){this._svgDom&&(this._svgDom.innerHTML=null),this._oldVNode=null},r.prototype.toDataURL=function(t){var e=this.renderToString(),i="data:image/svg+xml;";return t?(e=Cu(e),e&&i+"base64,"+e):i+"charset=UTF-8,"+encodeURIComponent(e)},r}();function Zs(r){return function(){}}function Tc(r,t,e,i){var n;if(e&&e!=="none")if(n=J("rect","bg",{width:r,height:t,x:"0",y:"0"}),No(e))Gf({fill:e},n.attrs,"fill",i);else if(la(e))Nf({style:{fill:e},dirty:br,getBoundingRect:function(){return{width:r,height:t}}},n.attrs,"fill",i);else{var a=Te(e),s=a.color,o=a.opacity;n.attrs.fill=s,o<1&&(n.attrs["fill-opacity"]=o)}return n}const Ed=wc;function Ks(r,t,e){var i=xe.createCanvas(),n=t.getWidth(),a=t.getHeight(),s=i.style;return s&&(s.position="absolute",s.left="0",s.top="0",s.width=n+"px",s.height=a+"px",i.setAttribute("data-zr-dom-id",r)),i.width=n*e,i.height=a*e,i}var bc=function(r){$(t,r);function t(e,i,n){var a=r.call(this)||this;a.motionBlur=!1,a.lastFrameAlpha=.7,a.dpr=1,a.virtual=!1,a.config={},a.incremental=!1,a.zlevel=0,a.maxRepaintRectCount=5,a.__dirty=!0,a.__firstTimePaint=!0,a.__used=!1,a.__drawIndex=0,a.__startIndex=0,a.__endIndex=0,a.__prevStartIndex=null,a.__prevEndIndex=null;var s;n=n||gi,typeof e=="string"?s=Ks(e,i,n):Yt(e)&&(s=e,e=s.id),a.id=e,a.dom=s;var o=s.style;return o&&(To(s),s.onselectstart=function(){return!1},o.padding="0",o.margin="0",o.borderWidth="0"),a.painter=i,a.dpr=n,a}return t.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},t.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},t.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},t.prototype.setUnpainted=function(){this.__firstTimePaint=!0},t.prototype.createBackBuffer=function(){var e=this.dpr;this.domBack=Ks("back-"+this.id,this.painter,e),this.ctxBack=this.domBack.getContext("2d"),e!==1&&this.ctxBack.scale(e,e)},t.prototype.createRepaintRects=function(e,i,n,a){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var s=[],o=this.maxRepaintRectCount,f=!1,h=new X(0,0,0,0);function u(y){if(!(!y.isFinite()||y.isZero()))if(s.length===0){var m=new X(0,0,0,0);m.copy(y),s.push(m)}else{for(var w=!1,S=1/0,b=0,T=0;T<s.length;++T){var C=s[T];if(C.intersect(y)){var M=new X(0,0,0,0);M.copy(C),M.union(y),s[T]=M,w=!0;break}else if(f){h.copy(y),h.union(C);var P=y.width*y.height,L=C.width*C.height,R=h.width*h.height,D=R-P-L;D<S&&(S=D,b=T)}}if(f&&(s[b].union(y),w=!0),!w){var m=new X(0,0,0,0);m.copy(y),s.push(m)}f||(f=s.length>=o)}}for(var v=this.__startIndex;v<this.__endIndex;++v){var l=e[v];if(l){var c=l.shouldBePainted(n,a,!0,!0),p=l.__isRendered&&(l.__dirty&ct||!c)?l.getPrevPaintRect():null;p&&u(p);var _=c&&(l.__dirty&ct||!l.__isRendered)?l.getPaintRect():null;_&&u(_)}}for(var v=this.__prevStartIndex;v<this.__prevEndIndex;++v){var l=i[v],c=l&&l.shouldBePainted(n,a,!0,!0);if(l&&(!c||!l.__zr)&&l.__isRendered){var p=l.getPrevPaintRect();p&&u(p)}}var d;do{d=!1;for(var v=0;v<s.length;){if(s[v].isZero()){s.splice(v,1);continue}for(var g=v+1;g<s.length;)s[v].intersect(s[g])?(d=!0,s[v].union(s[g]),s.splice(g,1)):g++;v++}}while(d);return this._paintRects=s,s},t.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},t.prototype.resize=function(e,i){var n=this.dpr,a=this.dom,s=a.style,o=this.domBack;s&&(s.width=e+"px",s.height=i+"px"),a.width=e*n,a.height=i*n,o&&(o.width=e*n,o.height=i*n,n!==1&&this.ctxBack.scale(n,n))},t.prototype.clear=function(e,i,n){var a=this.dom,s=this.ctx,o=a.width,f=a.height;i=i||this.clearColor;var h=this.motionBlur&&!e,u=this.lastFrameAlpha,v=this.dpr,l=this;h&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(a,0,0,o/v,f/v));var c=this.domBack;function p(_,d,g,y){if(s.clearRect(_,d,g,y),i&&i!=="transparent"){var m=void 0;if(Ri(i)){var w=i.global||i.__width===g&&i.__height===y;m=w&&i.__canvasGradient||Qn(s,i,{x:0,y:0,width:g,height:y}),i.__canvasGradient=m,i.__width=g,i.__height=y}else lh(i)&&(i.scaleX=i.scaleX||v,i.scaleY=i.scaleY||v,m=Jn(s,i,{dirty:function(){l.setUnpainted(),l.painter.refresh()}}));s.save(),s.fillStyle=m||i,s.fillRect(_,d,g,y),s.restore()}h&&(s.save(),s.globalAlpha=u,s.drawImage(c,_,d,g,y),s.restore())}!n||h?p(0,0,o,f):n.length&&at(n,function(_){p(_.x*v,_.y*v,_.width*v,_.height*v)})},t}(jr);const Cn=bc;var Qs=1e5,_r=314159,ei=.01,Sc=.001;function Cc(r){return r?r.__builtin__?!0:!(typeof r.resize!="function"||typeof r.refresh!="function"):!1}function Mc(r,t){var e=document.createElement("div");return e.style.cssText=["position:relative","width:"+r+"px","height:"+t+"px","padding:0","margin:0","border-width:0"].join(";")+";",e}var Pc=function(){function r(t,e,i,n){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var a=!t.nodeName||t.nodeName.toUpperCase()==="CANVAS";this._opts=i=z({},i||{}),this.dpr=i.devicePixelRatio||gi,this._singleCanvas=a,this.root=t;var s=t.style;s&&(To(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var f=this._layers;if(a){var u=t,v=u.width,l=u.height;i.width!=null&&(v=i.width),i.height!=null&&(l=i.height),this.dpr=i.devicePixelRatio||1,u.width=v*this.dpr,u.height=l*this.dpr,this._width=v,this._height=l;var c=new Cn(u,this,this.dpr);c.__builtin__=!0,c.initContext(),f[_r]=c,c.zlevel=_r,o.push(_r),this._domRoot=t}else{this._width=qr(t,0,i),this._height=qr(t,1,i);var h=this._domRoot=Mc(this._width,this._height);t.appendChild(h)}}return r.prototype.getType=function(){return"canvas"},r.prototype.isSingleCanvas=function(){return this._singleCanvas},r.prototype.getViewportRoot=function(){return this._domRoot},r.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},r.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),i=this._prevDisplayList,n=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,i,t,this._redrawId);for(var a=0;a<n.length;a++){var s=n[a],o=this._layers[s];if(!o.__builtin__&&o.refresh){var f=a===0?this._backgroundColor:null;o.refresh(f)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},r.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},r.prototype._paintHoverList=function(t){var e=t.length,i=this._hoverlayer;if(i&&i.clear(),!!e){for(var n={inHover:!0,viewWidth:this._width,viewHeight:this._height},a,s=0;s<e;s++){var o=t[s];o.__inHover&&(i||(i=this._hoverlayer=this.getLayer(Qs)),a||(a=i.ctx,a.save()),Tr(a,o,n,s===e-1))}a&&a.restore()}},r.prototype.getHoverLayer=function(){return this.getLayer(Qs)},r.prototype.paintOne=function(t,e){zl(t,e)},r.prototype._paintList=function(t,e,i,n){if(this._redrawId===n){i=i||!1,this._updateLayerStatus(t);var a=this._doPaintList(t,e,i),s=a.finished,o=a.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),o&&this._paintHoverList(t),s)this.eachLayer(function(h){h.afterBrush&&h.afterBrush()});else{var f=this;Fn(function(){f._paintList(t,e,i,n)})}}},r.prototype._compositeManually=function(){var t=this.getLayer(_r).ctx,e=this._domRoot.width,i=this._domRoot.height;t.clearRect(0,0,e,i),this.eachBuiltinLayer(function(n){n.virtual&&t.drawImage(n.dom,0,0,e,i)})},r.prototype._doPaintList=function(t,e,i){for(var n=this,a=[],s=this._opts.useDirtyRect,o=0;o<this._zlevelList.length;o++){var f=this._zlevelList[o],h=this._layers[f];h.__builtin__&&h!==this._hoverlayer&&(h.__dirty||i)&&a.push(h)}for(var u=!0,v=!1,l=function(_){var d=a[_],g=d.ctx,y=s&&d.createRepaintRects(t,e,c._width,c._height),m=i?d.__startIndex:d.__drawIndex,w=!i&&d.incremental&&Date.now,S=w&&Date.now(),b=d.zlevel===c._zlevelList[0]?c._backgroundColor:null;if(d.__startIndex===d.__endIndex)d.clear(!1,b,y);else if(m===d.__startIndex){var T=t[m];(!T.incremental||!T.notClear||i)&&d.clear(!1,b,y)}m===-1&&(console.error("For some unknown reason. drawIndex is -1"),m=d.__startIndex);var C,M=function(D){var x={inHover:!1,allClipped:!1,prevEl:null,viewWidth:n._width,viewHeight:n._height};for(C=m;C<d.__endIndex;C++){var E=t[C];if(E.__inHover&&(v=!0),n._doPaintEl(E,d,s,D,x,C===d.__endIndex-1),w){var A=Date.now()-S;if(A>15)break}}x.prevElClipPaths&&g.restore()};if(y)if(y.length===0)C=d.__endIndex;else for(var P=c.dpr,L=0;L<y.length;++L){var R=y[L];g.save(),g.beginPath(),g.rect(R.x*P,R.y*P,R.width*P,R.height*P),g.clip(),M(R),g.restore()}else g.save(),M(),g.restore();d.__drawIndex=C,d.__drawIndex<d.__endIndex&&(u=!1)},c=this,p=0;p<a.length;p++)l(p);return nt.wxa&&at(this._layers,function(_){_&&_.ctx&&_.ctx.draw&&_.ctx.draw()}),{finished:u,needsRefreshHover:v}},r.prototype._doPaintEl=function(t,e,i,n,a,s){var o=e.ctx;if(i){var f=t.getPaintRect();(!n||f&&f.intersect(n))&&(Tr(o,t,a,s),t.setPrevPaintRect(f))}else Tr(o,t,a,s)},r.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=_r);var i=this._layers[t];return i||(i=new Cn("zr_"+t,this,this.dpr),i.zlevel=t,i.__builtin__=!0,this._layerConfig[t]?zr(i,this._layerConfig[t],!0):this._layerConfig[t-ei]&&zr(i,this._layerConfig[t-ei],!0),e&&(i.virtual=e),this.insertLayer(t,i),i.initContext()),i},r.prototype.insertLayer=function(t,e){var i=this._layers,n=this._zlevelList,a=n.length,s=this._domRoot,o=null,f=-1;if(!i[t]&&!!Cc(e)){if(a>0&&t>n[0]){for(f=0;f<a-1&&!(n[f]<t&&n[f+1]>t);f++);o=i[n[f]]}if(n.splice(f+1,0,t),i[t]=e,!e.virtual)if(o){var h=o.dom;h.nextSibling?s.insertBefore(e.dom,h.nextSibling):s.appendChild(e.dom)}else s.firstChild?s.insertBefore(e.dom,s.firstChild):s.appendChild(e.dom);e.painter||(e.painter=this)}},r.prototype.eachLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n];t.call(e,this._layers[a],a)}},r.prototype.eachBuiltinLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],s=this._layers[a];s.__builtin__&&t.call(e,s,a)}},r.prototype.eachOtherLayer=function(t,e){for(var i=this._zlevelList,n=0;n<i.length;n++){var a=i[n],s=this._layers[a];s.__builtin__||t.call(e,s,a)}},r.prototype.getLayers=function(){return this._layers},r.prototype._updateLayerStatus=function(t){this.eachBuiltinLayer(function(v,l){v.__dirty=v.__used=!1});function e(v){a&&(a.__endIndex!==v&&(a.__dirty=!0),a.__endIndex=v)}if(this._singleCanvas)for(var i=1;i<t.length;i++){var n=t[i];if(n.zlevel!==t[i-1].zlevel||n.incremental){this._needsManuallyCompositing=!0;break}}var a=null,s=0,o,f;for(f=0;f<t.length;f++){var n=t[f],h=n.zlevel,u=void 0;o!==h&&(o=h,s=0),n.incremental?(u=this.getLayer(h+Sc,this._needsManuallyCompositing),u.incremental=!0,s=1):u=this.getLayer(h+(s>0?ei:0),this._needsManuallyCompositing),u.__builtin__||ha("ZLevel "+h+" has been used by unkown layer "+u.id),u!==a&&(u.__used=!0,u.__startIndex!==f&&(u.__dirty=!0),u.__startIndex=f,u.incremental?u.__drawIndex=-1:u.__drawIndex=f,e(f),a=u),n.__dirty&ct&&!n.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=f))}e(f),this.eachBuiltinLayer(function(v,l){!v.__used&&v.getElementCount()>0&&(v.__dirty=!0,v.__startIndex=v.__endIndex=v.__drawIndex=0),v.__dirty&&v.__drawIndex<0&&(v.__drawIndex=v.__startIndex)})},r.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},r.prototype._clearLayer=function(t){t.clear()},r.prototype.setBackgroundColor=function(t){this._backgroundColor=t,at(this._layers,function(e){e.setUnpainted()})},r.prototype.configLayer=function(t,e){if(e){var i=this._layerConfig;i[t]?zr(i[t],e,!0):i[t]=e;for(var n=0;n<this._zlevelList.length;n++){var a=this._zlevelList[n];if(a===t||a===t+ei){var s=this._layers[a];zr(s,i[t],!0)}}}},r.prototype.delLayer=function(t){var e=this._layers,i=this._zlevelList,n=e[t];!n||(n.dom.parentNode.removeChild(n.dom),delete e[t],i.splice(Et(i,t),1))},r.prototype.resize=function(t,e){if(this._domRoot.style){var i=this._domRoot;i.style.display="none";var n=this._opts,a=this.root;if(t!=null&&(n.width=t),e!=null&&(n.height=e),t=qr(a,0,n),e=qr(a,1,n),i.style.display="",this._width!==t||e!==this._height){i.style.width=t+"px",i.style.height=e+"px";for(var s in this._layers)this._layers.hasOwnProperty(s)&&this._layers[s].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(t==null||e==null)return;this._width=t,this._height=e,this.getLayer(_r).resize(t,e)}return this},r.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},r.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},r.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[_r].dom;var e=new Cn("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var i=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var n=e.dom.width,a=e.dom.height;this.eachLayer(function(v){v.__builtin__?i.drawImage(v.dom,0,0,n,a):v.renderToCanvas&&(i.save(),v.renderToCanvas(i),i.restore())})}else for(var s={inHover:!1,viewWidth:this._width,viewHeight:this._height},o=this.storage.getDisplayList(!0),f=0,h=o.length;f<h;f++){var u=o[f];Tr(i,u,s,f===h-1)}return e.dom},r.prototype.getWidth=function(){return this._width},r.prototype.getHeight=function(){return this._height},r}();const Fd=Pc;function Lc(r){if(kt(r)){var t=new DOMParser;r=t.parseFromString(r,"text/xml")}var e=r;for(e.nodeType===9&&(e=e.firstChild);e.nodeName.toLowerCase()!=="svg"||e.nodeType!==1;)e=e.nextSibling;return e}var Mn,bi={fill:"fill",stroke:"stroke","stroke-width":"lineWidth",opacity:"opacity","fill-opacity":"fillOpacity","stroke-opacity":"strokeOpacity","stroke-dasharray":"lineDash","stroke-dashoffset":"lineDashOffset","stroke-linecap":"lineCap","stroke-linejoin":"lineJoin","stroke-miterlimit":"miterLimit","font-family":"fontFamily","font-size":"fontSize","font-style":"fontStyle","font-weight":"fontWeight","text-anchor":"textAlign",visibility:"visibility",display:"display"},Js=Y(bi),Si={"alignment-baseline":"textBaseline","stop-color":"stopColor"},js=Y(Si),xc=function(){function r(){this._defs={},this._root=null}return r.prototype.parse=function(t,e){e=e||{};var i=Lc(t);this._defsUsePending=[];var n=new Br;this._root=n;var a=[],s=i.getAttribute("viewBox")||"",o=parseFloat(i.getAttribute("width")||e.width),f=parseFloat(i.getAttribute("height")||e.height);isNaN(o)&&(o=null),isNaN(f)&&(f=null),lt(i,n,null,!0,!1);for(var h=i.firstChild;h;)this._parseNode(h,n,a,null,!1,!1),h=h.nextSibling;Ac(this._defs,this._defsUsePending),this._defsUsePending=[];var u,v;if(s){var l=Bi(s);l.length>=4&&(u={x:parseFloat(l[0]||0),y:parseFloat(l[1]||0),width:parseFloat(l[2]),height:parseFloat(l[3])})}if(u&&o!=null&&f!=null&&(v=kc(u,{x:0,y:0,width:o,height:f}),!e.ignoreViewBox)){var c=n;n=new Br,n.add(c),c.scaleX=c.scaleY=v.scale,c.x=v.x,c.y=v.y}return!e.ignoreRootClip&&o!=null&&f!=null&&n.setClipPath(new yi({shape:{x:0,y:0,width:o,height:f}})),{root:n,width:o,height:f,viewBoxRect:u,viewBoxTransform:v,named:a}},r.prototype._parseNode=function(t,e,i,n,a,s){var o=t.nodeName.toLowerCase(),f,h=n;if(o==="defs"&&(a=!0),o==="text"&&(s=!0),o==="defs"||o==="switch")f=e;else{if(!a){var u=Mn[o];if(u&&vi(Mn,o)){f=u.call(this,t,e);var v=t.getAttribute("name");if(v){var l={name:v,namedFrom:null,svgNodeTagLower:o,el:f};i.push(l),o==="g"&&(h=l)}else n&&i.push({name:n.name,namedFrom:n,svgNodeTagLower:o,el:f});e.add(f)}}var c=to[o];if(c&&vi(to,o)){var p=c.call(this,t),_=t.getAttribute("id");_&&(this._defs[_]=p)}}if(f&&f.isGroup)for(var d=t.firstChild;d;)d.nodeType===1?this._parseNode(d,f,i,h,a,s):d.nodeType===3&&s&&this._parseText(d,f),d=d.nextSibling},r.prototype._parseText=function(t,e){var i=new Ce({style:{text:t.textContent},silent:!0,x:this._textX||0,y:this._textY||0});_t(e,i),lt(t,i,this._defsUsePending,!1,!1),Rc(i,e);var n=i.style,a=n.fontSize;a&&a<9&&(n.fontSize=9,i.scaleX*=a/9,i.scaleY*=a/9);var s=(n.fontSize||n.fontFamily)&&[n.fontStyle,n.fontWeight,(n.fontSize||12)+"px",n.fontFamily||"sans-serif"].join(" ");n.font=s;var o=i.getBoundingRect();return this._textX+=o.width,e.add(i),i},r.internalField=function(){Mn={g:function(t,e){var i=new Br;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i},rect:function(t,e){var i=new yi;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i.setShape({x:parseFloat(t.getAttribute("x")||"0"),y:parseFloat(t.getAttribute("y")||"0"),width:parseFloat(t.getAttribute("width")||"0"),height:parseFloat(t.getAttribute("height")||"0")}),i.silent=!0,i},circle:function(t,e){var i=new Kv;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),r:parseFloat(t.getAttribute("r")||"0")}),i.silent=!0,i},line:function(t,e){var i=new vl;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i.setShape({x1:parseFloat(t.getAttribute("x1")||"0"),y1:parseFloat(t.getAttribute("y1")||"0"),x2:parseFloat(t.getAttribute("x2")||"0"),y2:parseFloat(t.getAttribute("y2")||"0")}),i.silent=!0,i},ellipse:function(t,e){var i=new Jv;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i.setShape({cx:parseFloat(t.getAttribute("cx")||"0"),cy:parseFloat(t.getAttribute("cy")||"0"),rx:parseFloat(t.getAttribute("rx")||"0"),ry:parseFloat(t.getAttribute("ry")||"0")}),i.silent=!0,i},polygon:function(t,e){var i=t.getAttribute("points"),n;i&&(n=io(i));var a=new Sf({shape:{points:n||[]},silent:!0});return _t(e,a),lt(t,a,this._defsUsePending,!1,!1),a},polyline:function(t,e){var i=t.getAttribute("points"),n;i&&(n=io(i));var a=new fl({shape:{points:n||[]},silent:!0});return _t(e,a),lt(t,a,this._defsUsePending,!1,!1),a},image:function(t,e){var i=new Ie;return _t(e,i),lt(t,i,this._defsUsePending,!1,!1),i.setStyle({image:t.getAttribute("xlink:href")||t.getAttribute("href"),x:+t.getAttribute("x"),y:+t.getAttribute("y"),width:+t.getAttribute("width"),height:+t.getAttribute("height")}),i.silent=!0,i},text:function(t,e){var i=t.getAttribute("x")||"0",n=t.getAttribute("y")||"0",a=t.getAttribute("dx")||"0",s=t.getAttribute("dy")||"0";this._textX=parseFloat(i)+parseFloat(a),this._textY=parseFloat(n)+parseFloat(s);var o=new Br;return _t(e,o),lt(t,o,this._defsUsePending,!1,!0),o},tspan:function(t,e){var i=t.getAttribute("x"),n=t.getAttribute("y");i!=null&&(this._textX=parseFloat(i)),n!=null&&(this._textY=parseFloat(n));var a=t.getAttribute("dx")||"0",s=t.getAttribute("dy")||"0",o=new Br;return _t(e,o),lt(t,o,this._defsUsePending,!1,!0),this._textX+=parseFloat(a),this._textY+=parseFloat(s),o},path:function(t,e){var i=t.getAttribute("d")||"",n=Vv(i);return _t(e,n),lt(t,n,this._defsUsePending,!1,!1),n.silent=!0,n}}}(),r}(),to={lineargradient:function(r){var t=parseInt(r.getAttribute("x1")||"0",10),e=parseInt(r.getAttribute("y1")||"0",10),i=parseInt(r.getAttribute("x2")||"10",10),n=parseInt(r.getAttribute("y2")||"0",10),a=new yl(t,e,i,n);return ro(r,a),eo(r,a),a},radialgradient:function(r){var t=parseInt(r.getAttribute("cx")||"0",10),e=parseInt(r.getAttribute("cy")||"0",10),i=parseInt(r.getAttribute("r")||"0",10),n=new wl(t,e,i);return ro(r,n),eo(r,n),n}};function ro(r,t){var e=r.getAttribute("gradientUnits");e==="userSpaceOnUse"&&(t.global=!0)}function eo(r,t){for(var e=r.firstChild;e;){if(e.nodeType===1&&e.nodeName.toLocaleLowerCase()==="stop"){var i=e.getAttribute("offset"),n=void 0;i&&i.indexOf("%")>0?n=parseInt(i,10)/100:i?n=parseFloat(i):n=0;var a={};qf(e,a,a);var s=a.stopColor||e.getAttribute("stop-color")||"#000000";t.colorStops.push({offset:n,color:s})}e=e.nextSibling}}function _t(r,t){r&&r.__inheritedStyle&&(t.__inheritedStyle||(t.__inheritedStyle={}),Tt(t.__inheritedStyle,r.__inheritedStyle))}function io(r){for(var t=Bi(r),e=[],i=0;i<t.length;i+=2){var n=parseFloat(t[i]),a=parseFloat(t[i+1]);e.push([n,a])}return e}function lt(r,t,e,i,n){var a=t,s=a.__inheritedStyle=a.__inheritedStyle||{},o={};r.nodeType===1&&(Fc(r,t),qf(r,s,o),i||Oc(r,s,o)),a.style=a.style||{},s.fill!=null&&(a.style.fill=no(a,"fill",s.fill,e)),s.stroke!=null&&(a.style.stroke=no(a,"stroke",s.stroke,e)),at(["lineWidth","opacity","fillOpacity","strokeOpacity","miterLimit","fontSize"],function(f){s[f]!=null&&(a.style[f]=parseFloat(s[f]))}),at(["lineDashOffset","lineCap","lineJoin","fontWeight","fontFamily","fontStyle","textAlign"],function(f){s[f]!=null&&(a.style[f]=s[f])}),n&&(a.__selfStyle=o),s.lineDash&&(a.style.lineDash=Z(Bi(s.lineDash),function(f){return parseFloat(f)})),(s.visibility==="hidden"||s.visibility==="collapse")&&(a.invisible=!0),s.display==="none"&&(a.ignore=!0)}function Rc(r,t){var e=t.__selfStyle;if(e){var i=e.textBaseline,n=i;!i||i==="auto"||i==="baseline"?n="alphabetic":i==="before-edge"||i==="text-before-edge"?n="top":i==="after-edge"||i==="text-after-edge"?n="bottom":(i==="central"||i==="mathematical")&&(n="middle"),r.style.textBaseline=n}var a=t.__inheritedStyle;if(a){var s=a.textAlign,o=s;s&&(s==="middle"&&(o="center"),r.style.textAlign=o)}}var Dc=/^url\(\s*#(.*?)\)/;function no(r,t,e,i){var n=e&&e.match(Dc);if(n){var a=$r(n[1]);i.push([r,t,a]);return}return e==="none"&&(e=null),e}function Ac(r,t){for(var e=0;e<t.length;e++){var i=t[e];i[0].style[i[1]]=r[i[2]]}}var Ic=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;function Bi(r){return r.match(Ic)||[]}var Ec=/(translate|scale|rotate|skewX|skewY|matrix)\(([\-\s0-9\.eE,]*)\)/g,Pn=Math.PI/180;function Fc(r,t){var e=r.getAttribute("transform");if(e){e=e.replace(/,/g," ");var i=[],n=null;e.replace(Ec,function(v,l,c){return i.push(l,c),""});for(var a=i.length-1;a>0;a-=2){var s=i[a],o=i[a-1],f=Bi(s);switch(n=n||Sr(),o){case"translate":En(n,n,[parseFloat(f[0]),parseFloat(f[1]||"0")]);break;case"scale":Mo(n,n,[parseFloat(f[0]),parseFloat(f[1]||f[0])]);break;case"rotate":Co(n,n,-parseFloat(f[0])*Pn,[parseFloat(f[1]||"0"),parseFloat(f[2]||"0")]);break;case"skewX":var h=Math.tan(parseFloat(f[0])*Pn);ge(n,[1,0,h,1,0,0],n);break;case"skewY":var u=Math.tan(parseFloat(f[0])*Pn);ge(n,[1,u,0,1,0,0],n);break;case"matrix":n[0]=parseFloat(f[0]),n[1]=parseFloat(f[1]),n[2]=parseFloat(f[2]),n[3]=parseFloat(f[3]),n[4]=parseFloat(f[4]),n[5]=parseFloat(f[5]);break}}t.setLocalTransform(n)}}var ao=/([^\s:;]+)\s*:\s*([^:;]+)/g;function qf(r,t,e){var i=r.getAttribute("style");if(!!i){ao.lastIndex=0;for(var n;(n=ao.exec(i))!=null;){var a=n[1],s=vi(bi,a)?bi[a]:null;s&&(t[s]=n[2]);var o=vi(Si,a)?Si[a]:null;o&&(e[o]=n[2])}}}function Oc(r,t,e){for(var i=0;i<Js.length;i++){var n=Js[i],a=r.getAttribute(n);a!=null&&(t[bi[n]]=a)}for(var i=0;i<js.length;i++){var n=js[i],a=r.getAttribute(n);a!=null&&(e[Si[n]]=a)}}function kc(r,t){var e=t.width/r.width,i=t.height/r.height,n=Math.min(e,i);return{scale:n,x:-(r.x+r.width/2)*n+(t.x+t.width/2),y:-(r.y+r.height/2)*n+(t.y+t.height/2)}}function Od(r,t){var e=new xc;return e.parse(r,t)}var At=tr.CMD;function Ur(r,t){return Math.abs(r-t)<1e-5}function ea(r){var t=r.data,e=r.len(),i=[],n,a=0,s=0,o=0,f=0;function h(D,x){n&&n.length>2&&i.push(n),n=[D,x]}function u(D,x,E,A){Ur(D,E)&&Ur(x,A)||n.push(D,x,E,A,E,A)}function v(D,x,E,A,F,H){var N=Math.abs(x-D),V=Math.tan(N/4)*4/3,W=x<D?-1:1,G=Math.cos(D),rt=Math.sin(D),j=Math.cos(x),pt=Math.sin(x),Bt=G*F+E,Ht=rt*H+A,gt=j*F+E,Q=pt*H+A,q=F*V*W,I=H*V*W;n.push(Bt-q*rt,Ht+I*G,gt+q*pt,Q-I*j,gt,Q)}for(var l,c,p,_,d=0;d<e;){var g=t[d++],y=d===1;switch(y&&(a=t[d],s=t[d+1],o=a,f=s,(g===At.L||g===At.C||g===At.Q)&&(n=[o,f])),g){case At.M:a=o=t[d++],s=f=t[d++],h(o,f);break;case At.L:l=t[d++],c=t[d++],u(a,s,l,c),a=l,s=c;break;case At.C:n.push(t[d++],t[d++],t[d++],t[d++],a=t[d++],s=t[d++]);break;case At.Q:l=t[d++],c=t[d++],p=t[d++],_=t[d++],n.push(a+2/3*(l-a),s+2/3*(c-s),p+2/3*(l-p),_+2/3*(c-_),p,_),a=p,s=_;break;case At.A:var m=t[d++],w=t[d++],S=t[d++],b=t[d++],T=t[d++],C=t[d++]+T;d+=1;var M=!t[d++];l=Math.cos(T)*S+m,c=Math.sin(T)*b+w,y?(o=l,f=c,h(o,f)):u(a,s,l,c),a=Math.cos(C)*S+m,s=Math.sin(C)*b+w;for(var P=(M?-1:1)*Math.PI/2,L=T;M?L>C:L<C;L+=P){var R=M?Math.max(L+P,C):Math.min(L+P,C);v(L,R,m,w,S,b)}break;case At.R:o=a=t[d++],f=s=t[d++],l=o+t[d++],c=f+t[d++],h(l,f),u(l,f,l,c),u(l,c,o,c),u(o,c,o,f),u(o,f,l,f);break;case At.Z:n&&u(a,s,o,f),a=o,s=f;break}}return n&&n.length>2&&i.push(n),i}function ia(r,t,e,i,n,a,s,o,f,h){if(Ur(r,e)&&Ur(t,i)&&Ur(n,s)&&Ur(a,o)){f.push(s,o);return}var u=2/h,v=u*u,l=s-r,c=o-t,p=Math.sqrt(l*l+c*c);l/=p,c/=p;var _=e-r,d=i-t,g=n-s,y=a-o,m=_*_+d*d,w=g*g+y*y;if(m<v&&w<v){f.push(s,o);return}var S=l*_+c*d,b=-l*g-c*y,T=m-S*S,C=w-b*b;if(T<v&&S>=0&&C<v&&b>=0){f.push(s,o);return}var M=[],P=[];jt(r,e,n,s,.5,M),jt(t,i,a,o,.5,P),ia(M[0],P[0],M[1],P[1],M[2],P[2],M[3],P[3],f,h),ia(M[4],P[4],M[5],P[5],M[6],P[6],M[7],P[7],f,h)}function Bc(r,t){var e=ea(r),i=[];t=t||1;for(var n=0;n<e.length;n++){var a=e[n],s=[],o=a[0],f=a[1];s.push(o,f);for(var h=2;h<a.length;){var u=a[h++],v=a[h++],l=a[h++],c=a[h++],p=a[h++],_=a[h++];ia(o,f,u,v,l,c,p,_,s,t),o=p,f=_}i.push(s)}return i}function Uf(r,t,e){var i=r[t],n=r[1-t],a=Math.abs(i/n),s=Math.ceil(Math.sqrt(a*e)),o=Math.floor(e/s);o===0&&(o=1,s=e);for(var f=[],h=0;h<s;h++)f.push(o);var u=s*o,v=e-u;if(v>0)for(var h=0;h<v;h++)f[h%s]+=1;return f}function so(r,t,e){for(var i=r.r0,n=r.r,a=r.startAngle,s=r.endAngle,o=Math.abs(s-a),f=o*n,h=n-i,u=f>Math.abs(h),v=Uf([f,h],u?0:1,t),l=(u?o:h)/v.length,c=0;c<v.length;c++)for(var p=(u?h:o)/v[c],_=0;_<v[c];_++){var d={};u?(d.startAngle=a+l*c,d.endAngle=a+l*(c+1),d.r0=i+p*_,d.r=i+p*(_+1)):(d.startAngle=a+p*_,d.endAngle=a+p*(_+1),d.r0=i+l*c,d.r=i+l*(c+1)),d.clockwise=r.clockwise,d.cx=r.cx,d.cy=r.cy,e.push(d)}}function Hc(r,t,e){for(var i=r.width,n=r.height,a=i>n,s=Uf([i,n],a?0:1,t),o=a?"width":"height",f=a?"height":"width",h=a?"x":"y",u=a?"y":"x",v=r[o]/s.length,l=0;l<s.length;l++)for(var c=r[f]/s[l],p=0;p<s[l];p++){var _={};_[h]=l*v,_[u]=p*c,_[o]=v,_[f]=c,_.x+=r.x,_.y+=r.y,e.push(_)}}function oo(r,t,e,i){return r*i-e*t}function zc(r,t,e,i,n,a,s,o){var f=e-r,h=i-t,u=s-n,v=o-a,l=oo(u,v,f,h);if(Math.abs(l)<1e-6)return null;var c=r-n,p=t-a,_=oo(c,p,u,v)/l;return _<0||_>1?null:new k(_*f+r,_*h+t)}function $c(r,t,e){var i=new k;k.sub(i,e,t),i.normalize();var n=new k;k.sub(n,r,t);var a=n.dot(i);return a}function Fr(r,t){var e=r[r.length-1];e&&e[0]===t[0]&&e[1]===t[1]||r.push(t)}function Gc(r,t,e){for(var i=r.length,n=[],a=0;a<i;a++){var s=r[a],o=r[(a+1)%i],f=zc(s[0],s[1],o[0],o[1],t.x,t.y,e.x,e.y);f&&n.push({projPt:$c(f,t,e),pt:f,idx:a})}if(n.length<2)return[{points:r},{points:r}];n.sort(function(d,g){return d.projPt-g.projPt});var h=n[0],u=n[n.length-1];if(u.idx<h.idx){var v=h;h=u,u=v}for(var l=[h.pt.x,h.pt.y],c=[u.pt.x,u.pt.y],p=[l],_=[c],a=h.idx+1;a<=u.idx;a++)Fr(p,r[a].slice());Fr(p,c),Fr(p,l);for(var a=u.idx+1;a<=h.idx+i;a++)Fr(_,r[a%i].slice());return Fr(_,l),Fr(_,c),[{points:p},{points:_}]}function fo(r){var t=r.points,e=[],i=[];ef(t,e,i);var n=new X(e[0],e[1],i[0]-e[0],i[1]-e[1]),a=n.width,s=n.height,o=n.x,f=n.y,h=new k,u=new k;return a>s?(h.x=u.x=o+a/2,h.y=f,u.y=f+s):(h.y=u.y=f+s/2,h.x=o,u.x=o+a),Gc(t,h,u)}function Ci(r,t,e,i){if(e===1)i.push(t);else{var n=Math.floor(e/2),a=r(t);Ci(r,a[0],n,i),Ci(r,a[1],e-n,i)}return i}function Nc(r,t){for(var e=[],i=0;i<t;i++)e.push(pf(r));return e}function Yc(r,t){t.setStyle(r.style),t.z=r.z,t.z2=r.z2,t.zlevel=r.zlevel}function Xc(r){for(var t=[],e=0;e<r.length;)t.push([r[e++],r[e++]]);return t}function Wc(r,t){var e=[],i=r.shape,n;switch(r.type){case"rect":Hc(i,t,e),n=yi;break;case"sector":so(i,t,e),n=Ss;break;case"circle":so({r0:0,r:i.r,startAngle:0,endAngle:Math.PI*2,cx:i.cx,cy:i.cy},t,e),n=Ss;break;default:var a=r.getComputedTransform(),s=a?Math.sqrt(Math.max(a[0]*a[0]+a[1]*a[1],a[2]*a[2]+a[3]*a[3])):1,o=Z(Bc(r.getUpdatedPathProxy(),s),function(g){return Xc(g)}),f=o.length;if(f===0)Ci(fo,{points:o[0]},t,e);else if(f===t)for(var h=0;h<f;h++)e.push({points:o[h]});else{var u=0,v=Z(o,function(g){var y=[],m=[];ef(g,y,m);var w=(m[1]-y[1])*(m[0]-y[0]);return u+=w,{poly:g,area:w}});v.sort(function(g,y){return y.area-g.area});for(var l=t,h=0;h<f;h++){var c=v[h];if(l<=0)break;var p=h===f-1?l:Math.ceil(c.area/u*t);p<0||(Ci(fo,{points:c.poly},p,e),l-=p)}}n=Sf;break}if(!n)return Nc(r,t);for(var _=[],h=0;h<e.length;h++){var d=new n;d.setShape(e[h]),Yc(r,d),_.push(d)}return _}function qc(r,t){var e=r.length,i=t.length;if(e===i)return[r,t];for(var n=[],a=[],s=e<i?r:t,o=Math.min(e,i),f=Math.abs(i-e)/6,h=(o-2)/6,u=Math.ceil(f/h)+1,v=[s[0],s[1]],l=f,c=2;c<o;){var p=s[c-2],_=s[c-1],d=s[c++],g=s[c++],y=s[c++],m=s[c++],w=s[c++],S=s[c++];if(l<=0){v.push(d,g,y,m,w,S);continue}for(var b=Math.min(l,u-1)+1,T=1;T<=b;T++){var C=T/b;jt(p,d,y,w,C,n),jt(_,g,m,S,C,a),p=n[3],_=a[3],v.push(n[1],a[1],n[2],a[2],p,_),d=n[5],g=a[5],y=n[6],m=a[6]}l-=b-1}return s===r?[v,t]:[r,v]}function ho(r,t){for(var e=r.length,i=r[e-2],n=r[e-1],a=[],s=0;s<t.length;)a[s++]=i,a[s++]=n;return a}function Uc(r,t){for(var e,i,n,a=[],s=[],o=0;o<Math.max(r.length,t.length);o++){var f=r[o],h=t[o],u=void 0,v=void 0;f?h?(e=qc(f,h),u=e[0],v=e[1],i=u,n=v):(v=ho(n||f,f),u=f):(u=ho(i||h,h),v=h),a.push(u),s.push(v)}return[a,s]}function uo(r){for(var t=0,e=0,i=0,n=r.length,a=0,s=n-2;a<n;s=a,a+=2){var o=r[s],f=r[s+1],h=r[a],u=r[a+1],v=o*u-h*f;t+=v,e+=(o+h)*v,i+=(f+u)*v}return t===0?[r[0]||0,r[1]||0]:[e/t/3,i/t/3,t]}function Vc(r,t,e,i){for(var n=(r.length-2)/6,a=1/0,s=0,o=r.length,f=o-2,h=0;h<n;h++){for(var u=h*6,v=0,l=0;l<o;l+=2){var c=l===0?u:(u+l-2)%f+2,p=r[c]-e[0],_=r[c+1]-e[1],d=t[l]-i[0],g=t[l+1]-i[1],y=d-p,m=g-_;v+=y*y+m*m}v<a&&(a=v,s=h)}return s}function Zc(r){for(var t=[],e=r.length,i=0;i<e;i+=2)t[i]=r[e-i-2],t[i+1]=r[e-i-1];return t}function Kc(r,t,e,i){for(var n=[],a,s=0;s<r.length;s++){var o=r[s],f=t[s],h=uo(o),u=uo(f);a==null&&(a=h[2]<0!=u[2]<0);var v=[],l=[],c=0,p=1/0,_=[],d=o.length;a&&(o=Zc(o));for(var g=Vc(o,f,h,u)*6,y=d-2,m=0;m<y;m+=2){var w=(g+m)%y+2;v[m+2]=o[w]-h[0],v[m+3]=o[w+1]-h[1]}if(v[0]=o[g]-h[0],v[1]=o[g+1]-h[1],e>0)for(var S=i/e,b=-i/2;b<=i/2;b+=S){for(var T=Math.sin(b),C=Math.cos(b),M=0,m=0;m<o.length;m+=2){var P=v[m],L=v[m+1],R=f[m]-u[0],D=f[m+1]-u[1],x=R*C-D*T,E=R*T+D*C;_[m]=x,_[m+1]=E;var A=x-P,F=E-L;M+=A*A+F*F}if(M<p){p=M,c=b;for(var H=0;H<_.length;H++)l[H]=_[H]}}else for(var N=0;N<d;N+=2)l[N]=f[N]-u[0],l[N+1]=f[N+1]-u[1];n.push({from:v,to:l,fromCp:h,toCp:u,rotation:-c})}return n}function na(r){return r.__isCombineMorphing}var Vf="__mOriginal_";function Mi(r,t,e){var i=Vf+t,n=r[i]||r[t];r[i]||(r[i]=r[t]);var a=e.replace,s=e.after,o=e.before;r[t]=function(){var f=arguments,h;return o&&o.apply(this,f),a?h=a.apply(this,f):h=n.apply(this,f),s&&s.apply(this,f),h}}function me(r,t){var e=Vf+t;r[e]&&(r[t]=r[e],r[e]=null)}function vo(r,t){for(var e=0;e<r.length;e++)for(var i=r[e],n=0;n<i.length;){var a=i[n],s=i[n+1];i[n++]=t[0]*a+t[2]*s+t[4],i[n++]=t[1]*a+t[3]*s+t[5]}}function Zf(r,t){var e=r.getUpdatedPathProxy(),i=t.getUpdatedPathProxy(),n=Uc(ea(e),ea(i)),a=n[0],s=n[1],o=r.getComputedTransform(),f=t.getComputedTransform();function h(){this.transform=null}o&&vo(a,o),f&&vo(s,f),Mi(t,"updateTransform",{replace:h}),t.transform=null;var u=Kc(a,s,10,Math.PI),v=[];Mi(t,"buildPath",{replace:function(l){for(var c=t.__morphT,p=1-c,_=[],d=0;d<u.length;d++){var g=u[d],y=g.from,m=g.to,w=g.rotation*c,S=g.fromCp,b=g.toCp,T=Math.sin(w),C=Math.cos(w);Mh(_,S,b,c);for(var M=0;M<y.length;M+=2){var P=y[M],L=y[M+1],R=m[M],D=m[M+1],x=P*p+R*c,E=L*p+D*c;v[M]=x*C-E*T+_[0],v[M+1]=x*T+E*C+_[1]}var A=v[0],F=v[1];l.moveTo(A,F);for(var M=2;M<y.length;){var R=v[M++],D=v[M++],H=v[M++],N=v[M++],V=v[M++],W=v[M++];A===R&&F===D&&H===V&&N===W?l.lineTo(V,W):l.bezierCurveTo(R,D,H,N,V,W),A=V,F=W}}}})}function Kf(r,t,e){if(!r||!t)return t;var i=e.done,n=e.during;Zf(r,t),t.__morphT=0;function a(){me(t,"buildPath"),me(t,"updateTransform"),t.__morphT=-1,t.createPathProxy(),t.dirtyShape()}return t.animateTo({__morphT:1},Tt({during:function(s){t.dirtyShape(),n&&n(s)},done:function(){a(),i&&i()}},e)),t}function Qc(r,t,e,i,n,a){var s=16;r=n===e?0:Math.round(32767*(r-e)/(n-e)),t=a===i?0:Math.round(32767*(t-i)/(a-i));for(var o=0,f,h=(1<<s)/2;h>0;h/=2){var u=0,v=0;(r&h)>0&&(u=1),(t&h)>0&&(v=1),o+=h*h*(3*u^v),v===0&&(u===1&&(r=h-1-r,t=h-1-t),f=r,r=t,t=f)}return o}function Pi(r){var t=1/0,e=1/0,i=-1/0,n=-1/0,a=Z(r,function(o){var f=o.getBoundingRect(),h=o.getComputedTransform(),u=f.x+f.width/2+(h?h[4]:0),v=f.y+f.height/2+(h?h[5]:0);return t=Math.min(u,t),e=Math.min(v,e),i=Math.max(u,i),n=Math.max(v,n),[u,v]}),s=Z(a,function(o,f){return{cp:o,z:Qc(o[0],o[1],t,e,i,n),path:r[f]}});return s.sort(function(o,f){return o.z-f.z}).map(function(o){return o.path})}function Qf(r){return Wc(r.path,r.count)}function aa(){return{fromIndividuals:[],toIndividuals:[],count:0}}function kd(r,t,e){var i=[];function n(S){for(var b=0;b<S.length;b++){var T=S[b];na(T)?n(T.childrenRef()):T instanceof K&&i.push(T)}}n(r);var a=i.length;if(!a)return aa();var s=e.dividePath||Qf,o=s({path:t,count:a});if(o.length!==a)return console.error("Invalid morphing: unmatched splitted path"),aa();i=Pi(i),o=Pi(o);for(var f=e.done,h=e.during,u=e.individualDelay,v=new da,l=0;l<a;l++){var c=i[l],p=o[l];p.parent=t,p.copyTransform(v),u||Zf(c,p)}t.__isCombineMorphing=!0,t.childrenRef=function(){return o};function _(S){for(var b=0;b<o.length;b++)o[b].addSelfToZr(S)}Mi(t,"addSelfToZr",{after:function(S){_(S)}}),Mi(t,"removeSelfFromZr",{after:function(S){for(var b=0;b<o.length;b++)o[b].removeSelfFromZr(S)}});function d(){t.__isCombineMorphing=!1,t.__morphT=-1,t.childrenRef=null,me(t,"addSelfToZr"),me(t,"removeSelfFromZr")}var g=o.length;if(u)for(var y=g,m=function(){y--,y===0&&(d(),f&&f())},l=0;l<g;l++){var w=u?Tt({delay:(e.delay||0)+u(l,g,i[l],o[l]),done:m},e):e;Kf(i[l],o[l],w)}else t.__morphT=0,t.animateTo({__morphT:1},Tt({during:function(S){for(var b=0;b<g;b++){var T=o[b];T.__morphT=t.__morphT,T.dirtyShape()}h&&h(S)},done:function(){d();for(var S=0;S<r.length;S++)me(r[S],"updateTransform");f&&f()}},e));return t.__zr&&_(t.__zr),{fromIndividuals:i,toIndividuals:o,count:g}}function Bd(r,t,e){var i=t.length,n=[],a=e.dividePath||Qf;function s(c){for(var p=0;p<c.length;p++){var _=c[p];na(_)?s(_.childrenRef()):_ instanceof K&&n.push(_)}}if(na(r)){s(r.childrenRef());var o=n.length;if(o<i)for(var f=0,h=o;h<i;h++)n.push(pf(n[f++%o]));n.length=i}else{n=a({path:r,count:i});for(var u=r.getComputedTransform(),h=0;h<n.length;h++)n[h].setLocalTransform(u);if(n.length!==i)return console.error("Invalid morphing: unmatched splitted path"),aa()}n=Pi(n),t=Pi(t);for(var v=e.individualDelay,h=0;h<i;h++){var l=v?Tt({delay:(e.delay||0)+v(h,i,n[h],t[h])},e):e;Kf(n[h],t[h],l)}return{fromIndividuals:n,toIndividuals:t,count:t.length}}export{$r as $,Xh as A,pe as B,Tt as C,vi as D,Cd as E,Kv as F,Jv as G,Sf as H,fl as I,yi as J,Ld as K,vl as L,xd as M,Br as N,Sd as O,K as P,Dd as Q,Pd as R,Ss as S,da as T,pl as U,yl as V,wl as W,X,Rd as Y,Ie as Z,k as _,Kr as a,yh as a$,zr as a0,Pr as a1,_o as a2,dh as a3,Fh as a4,id as a5,vh as a6,Ln as a7,ed as a8,od as a9,Fd as aA,Fo as aB,tt as aC,_d as aD,gv as aE,ii as aF,sd as aG,Co as aH,rd as aI,dd as aJ,$h as aK,Lc as aL,Od as aM,kc as aN,Ae as aO,So as aP,Sr as aQ,ud as aR,jc as aS,ef as aT,En as aU,Mo as aV,gd as aW,De as aX,Pt as aY,yd as aZ,md as a_,hd as aa,br as ab,Yu as ac,Ad as ad,Ai as ae,xe as af,zl as ag,jr as ah,wd as ai,eu as aj,bd as ak,Td as al,yo as am,Se as an,ch as ao,Id as ap,Gr as aq,Nr as ar,Nu as as,Sh as at,Mh as au,fu as av,au as aw,tr as ax,Ze as ay,Ed as az,Yt as b,bh as b0,zi as b1,Jr as b2,mh as b3,wh as b4,vd as b5,ld as b6,ci as b7,it as b8,Vr as b9,pd as ba,Ga as bb,Ar as bc,yv as bd,Ya as be,be as bf,si as bg,xn as bh,Hh as bi,St as bj,cd as bk,ad as bl,na as bm,Kf as bn,kd as bo,Bd as bp,pf as bq,fd as c,xa as d,at as e,ce as f,nd as g,Et as h,kt as i,nt as j,td as k,z as l,Z as m,Re as n,_u as o,Ot as p,Y as q,xi as r,U as s,Vv as t,Md as u,Ov as v,kv as w,Wr as x,Yh as y,ge as z};
