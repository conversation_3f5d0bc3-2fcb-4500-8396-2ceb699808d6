<template>
  <div class="container">
    <car-free-search @form-search="searchParkInfoList" @reset="resetParamsAndData" />
    <car-free-table ref="table" />
  </div>
</template>

<script setup name="CarFree">
import CarFreeSearch from './carFree/CarFreeSearch.vue';
import CarFreeTable from './carFree/CarFreeTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
