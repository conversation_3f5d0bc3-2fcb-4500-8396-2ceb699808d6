/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找总部长租规则
export const pagingGroupRentRule = (data) => {
  return $({
    url: '/console/park/group/pagingGroupRentRule',
    method: 'post',
    data
  });
};

// 新建总部长租规则
export const createGroupRentRule = (data) => {
  return $({
    url: '/console/park/group/createGroupRentRule',
    method: 'post',
    data
  });
};

// 启用长租规则
export const enableRentRule = (id) => {
  return $({
    url: '/console/park/group/enableRentRule/' + id,
    method: 'post'
  });
};

// 禁用长租规则
export const disableRentRule = (id) => {
  return $({
    url: '/console/park/group/disableRentRule/' + id,
    method: 'post'
  });
};
/**
 * @description 修改总部长租规则
 * @param {*} data 表单数据
 */
export const updateRentRule = (data) => {
  return $({
    url: '/console/park/group/updateRentRule/' + data.id,
    method: 'post',
    data
  });
};
/**
 * @description 删除总部长租规则
 * @param {*} data 规则ID
 */
export const deleteRentRule = (id) => {
  return $({
    url: '/console/park/group/deleteRentRule/' + id,
    method: 'post'
  });
};
