<template>
  <el-card class="summarize-table" shadow="never">
    <span class="title">停车场交易汇总（易宝数据）</span>
    <el-button class="print" type="primary" @click="handlePrint">打 印</el-button>
    <div>
      <el-table ref="table" id="table" :data="tableData" v-loading="tableLoading" border @row-click="handleRowClick">
        <el-table-column prop="date" label="账单日期" align="center" />
        <el-table-column prop="merchant_no" label="商户编号" align="center" />
        <el-table-column prop="total_amount" label="交易总金额" align="center" />
        <el-table-column prop="number" label="交易总笔数" align="center" />
        <el-table-column prop="paid_in_handling_fees" label="实收手续费" align="center" />
        <el-table-column prop="total_settlement_amount" label="结算总金额" align="center" />
        <el-table-column prop="settlement_handling_fee" label="结算手续费" align="center" />
        <el-table-column prop="total_amount_received" label="到账金额" align="center" />
        <el-table-column label="对账操作" fixed="right" align="center">
          <template #default="scope">
            <el-button v-if="scope.row.date !== '月合计'" size="small" type="primary" link @click="handleExport(scope.row)">下载对账单</el-button>
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="YeepayReconcileSummarizeTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import printJS from 'print-js';
import etcReconcileApi from '@/service/finance/YeepayReconcileService';
import { saveToFile } from '@/utils/utils.js';

const emits = defineEmits(['update-records', 'update-records-loading']);
const table = ref(null);
const tableData = ref([]);
const totalInfo = reactive({
  pay_park_summary_vo: {},
  pay_rent_summary_vo: {},
  refund_summary_vo: {}
});
const tableLoading = ref(false);
const data = reactive({
  queryParams: {}
});

// 设置表格加载状态
const setTableLoading = (loading) => {
  tableLoading.value = loading;
};

// 设置合计数据
const setTotalInfo = (result) => {
  const { pay_park_summary_vo, pay_rent_summary_vo, refund_summary_vo } = result;
  totalInfo.pay_park_summary_vo = pay_park_summary_vo;
  totalInfo.pay_rent_summary_vo = pay_rent_summary_vo;
  totalInfo.refund_summary_vo = refund_summary_vo;
};

// 设置汇总数据
const setTableData = (list) => {
  if (list?.length) {
    tableData.value = [
      {
        date: '月合计',
        merchant_no: '-',
        total_amount: list[0].total_amount,
        number: list[0].number,
        paid_in_handling_fees: list[0].paid_in_handling_fees,
        total_settlement_amount: list[0].total_settlement_amount,
        settlement_handling_fee: list[0].settlement_handling_fee,
        total_amount_received: list[0].total_amount_received
      },
      ...list.filter((item, index) => index !== 0)
    ];
  } else {
    tableData.value = [];
  }
  if (tableData.value?.length) {
    setRecordsTable();
  }
};

// 设置对账数据
const setRecordsTable = (originData) => {
  const payParkTableData = [
    {
      date: '月合计',
      park_name: '-',
      count: totalInfo.pay_park_summary_vo?.count || 0,
      sum_total_money: totalInfo.pay_park_summary_vo?.sum_total_money || 0,
      sum_should_pay_money: totalInfo.pay_park_summary_vo?.sum_should_pay_money || 0
    }
  ];
  const payRentTableData = [
    {
      date: '月合计',
      park_name: '-',
      count: totalInfo.pay_rent_summary_vo?.count || 0,
      sum_total_money: totalInfo.pay_rent_summary_vo?.sum_total_money || 0,
      sum_should_pay_money: totalInfo.pay_rent_summary_vo?.sum_should_pay_money || 0
    }
  ];
  const refundTableData = [
    {
      date: '月合计',
      park_name: '-',
      count: totalInfo.refund_summary_vo?.count || 0,
      sum_refund_amount: totalInfo.refund_summary_vo?.sum_refund_amount || 0,
      sum_real_refund_amount: totalInfo.refund_summary_vo?.sum_real_refund_amount || 0,
      sum_refund_merchant_fee: totalInfo.refund_summary_vo?.sum_refund_merchant_fee || 0
    }
  ];
  if (originData?.pay_park_summary_vo) {
    payParkTableData.push(originData?.pay_park_summary_vo);
  }
  if (originData?.pay_rent_summary_vo) {
    payRentTableData.push(originData?.pay_rent_summary_vo);
  }
  if (originData?.refund_summary_vo) {
    refundTableData.push(originData?.refund_summary_vo);
  }
  emits('update-records', {
    payParkTableData,
    payRentTableData,
    refundTableData
  });
};

// 设置对账列表loading
const setRecordsLoading = (loading) => {
  emits('update-records-loading', loading);
};

// 获取汇总数据
const getList = (params) => {
  setTableLoading(true);
  data.queryParams = params;
  etcReconcileApi.monthYeeSettle({ parkId: params.park_id, month: params.stl_month }).then((response) => {
    if (response.success === true) {
      setTotalInfo(response.data);
      setTableData(response.data.yee_reconciliation_vos ?? []);

      setTableLoading(false);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      setTableLoading(false);
    }
  });
};

// 打印
const handlePrint = async () => {
  printJS({
    printable: JSON.parse(JSON.stringify(tableData.value)),

    properties: [
      { field: 'date', displayName: '账单日期' },
      { field: 'merchant_no', displayName: '商户编号' },
      { field: 'total_amount', displayName: '交易总金额' },
      { field: 'number', displayName: '交易总笔数' },
      { field: 'paid_in_handling_fees', displayName: '实收手续费' },
      { field: 'total_settlement_amount', displayName: '结算总金额' },
      { field: 'settlement_handling_fee', displayName: '结算手续费' },
      { field: 'total_amount_received', displayName: '到账金额' }
    ],
    type: 'json',
    header: '停车场交易汇总（易宝数据）',
    gridHeaderStyle: ';padding: 10px 16px;font-size: 14px;font-weight: 400;border: 0.5px solid #333333;',
    gridStyle: 'padding: 5px 8px;border: 0.5px solid #333333;text-align: center;font-size: 12px;'
  });
};

// 导出明细
const handleExport = (record) => {
  console.log('record:', record);
  etcReconcileApi
    .settleFileDownload({
      parkId: data.queryParams.park_id,
      date: record.date
    })
    .then((response) => {
      if (response.status === 200) {
        let fileName = response.headers['content-disposition'].split(';')[1].split('filename=')[1].replace(/\"/g, '');
        saveToFile(response.data, decodeURIComponent(fileName));
      } else {
        ElMessage({
          message: response.statusText,
          type: 'error'
        });
      }
    });
};

// 查询明细
const handleRowClick = (record) => {
  if (record.date === '月合计') {
    return;
  }
  setRecordsLoading(true);
  etcReconcileApi
    .dayYeeSettle({
      parkId: data.queryParams.park_id,
      date: record.date
    })
    .then((response) => {
      if (response.success === true) {
        setRecordsLoading(false);
        const { pay_park_summary_vo, pay_rent_summary_vo, refund_summary_vo } = response.data ?? {};
        setRecordsTable({
          pay_park_summary_vo,
          pay_rent_summary_vo,
          refund_summary_vo
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        setRecordsLoading(false);
      }
    });
};

defineExpose({
  getList,
  setTableData
});
</script>
<style lang="scss" scoped>
.summarize-table {
  position: relative;
  .print {
    position: absolute;
    right: 10px;
  }
  .title {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 16px;
  }
  .el-table {
    height: calc(100vh - 240px);
  }
  .cell-with-diagonal {
    display: flex;
    flex-direction: column;
    .category,
    .dateColumn {
      position: relative;
      font-weight: 500;
    }
    .category {
      top: 0px;
      right: 0px;
      align-self: flex-end;
    }
    .diagonal-line {
      width: 100%;
      height: 100%;
      border-top: 1px solid rgba(195, 195, 195, 0.5); /* 调整颜色和样式 */
      transform: rotate(24deg);
    }
    .dateColumn {
      display: inline-block;
      width: 32px;
      left: 0px;
      bottom: 0px;
    }
  }
  :deep(th.el-table-fixed-column--right) {
    background-color: #fafafa !important;
  }
}
</style>
