/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找设备状态
export const pagingDeviceStatus = (data) => {
  return $({
    url: '/console/park/device/state/pagingDeviceStates',
    method: 'post',
    data
  });
};

// 查询设备的报警数量
export const getWarningCount = (data) => {
  return $({
    url: '/console/park/device_event/getWarningCount?parkIds=' + data,
    method: 'get',
    data
  });
};

// 开启报警
export const turnOnAlarm = (id) => {
  return $({
    url: '/console/park/device/enableDeviceMonitor?parkDeviceId=' + id,
    method: 'post'
  });
};

// 关闭报警
export const turnOffAlarm = (id) => {
  return $({
    url: '/console/park/device/disableDeviceMonitor?parkDeviceId=' + id,
    method: 'post'
  });
};

// 查询设备事件
export const pageDeviceEvent = (data) => {
  return $({
    url: '/console/park/device_event/pageDeviceEvent',
    method: 'post',
    data
  });
};

// 清除所有告警信息
export const clearAllEvent = (data) => {
  return $({
    url: '/console/park/device_event/clearAllEvent',
    method: 'post',
    data
  });
};

// 清除一批告警信息
export const clearWarnInfo = (id) => {
  return $({
    url: '/console/park/device_event/clearWarnInfo?eventIds=' + id,
    method: 'post'
  });
};

// 移出所有的事件
export const deleteAllEvent = (data) => {
  return $({
    url: '/console/park/device_event/deleteAllEvent',
    method: 'post',
    data
  });
};

// 删除指定的事件
export const deleteEvent = (id) => {
  return $({
    url: '/console/park/device_event/deleteEvent?eventIds=' + id,
    method: 'post'
  });
};
