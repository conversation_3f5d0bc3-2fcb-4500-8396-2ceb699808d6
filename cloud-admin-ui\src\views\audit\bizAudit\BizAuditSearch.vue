<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.title" placeholder="审核标题" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" placeholder="业务类型" multiple clearable>
        <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" placeholder="审核状态" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="申请开始时间"
        end-placeholder="申请结束时间"
        style="width: 100%"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
    <template #button>
      <el-button type="success" @click="handleBatch">批量审批</el-button>
    </template>
  </FormSearch>
</template>

<script name="BizAuditSearch" setup>
import { dayjs } from 'element-plus';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search', 'batch']);
const form = reactive({
  queryParams: {
    title: '',
    types: [],
    audit_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const types = ref([]);
const states = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'types', enum_value: 'EnumAuditType' },
    { enum_key: 'states', enum_value: 'EnumAuditInstanceState' }
  ];
  commonService.findEnums('audit', param).then((response) => {
    states.value = response.data.states;
    types.value = response.data.types;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    title: '',
    types: [],
    audit_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
const handleBatch = () => {
  emits('batch');
};
</script>
<style lang="scss" scoped></style>
