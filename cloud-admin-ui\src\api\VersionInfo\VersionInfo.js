import $ from '@/utils/axios';
//查询所有版本信息
export function getAllVersionInfo() {
    return $.get('/console/version/listParkAllReleaseVersion')
}
//编辑版本信息
export function editVersionInfo(data) {
    return $.put('/console/version/updateParkReleaseVersion', data)
}
//获取最新版本信息
export function getNewVersionInfo() {
    return $.get('/console/version/newParkReleaseVersion')
}
//新增版本信息
export function addVersionInfo(data) {
    return $.post('/console/version/addParkReleaseVersion', data)
}