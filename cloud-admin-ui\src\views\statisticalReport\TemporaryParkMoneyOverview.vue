<template>
  <div class="container">
    <temporary-park-money-overview-search @form-search="searchTemporaryParkList" @reset="resetParamsAndData" />
    <temporary-park-money-overview-table ref="table" />
  </div>
</template>

<script setup name="TemporaryParkMoneyOverview">
import TemporaryParkMoneyOverviewSearch from './temporaryParkMoneyOverview/TemporaryParkMoneyOverviewSearch.vue';
import TemporaryParkMoneyOverviewTable from './temporaryParkMoneyOverview/TemporaryParkMoneyOverviewTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageTemporaryPark = (queryParams) => {
  table.value.getList(queryParams);
};

const searchTemporaryParkList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.department_name = undefined;
};
defineExpose({
  pageTemporaryPark
});
</script>
