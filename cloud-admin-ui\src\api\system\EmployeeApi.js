/* jshint esversion: 6 */
import $ from '@/utils/axios';
import { useUser } from '@/stores/user';

//修改密码;
export const changePasswd = (data) => {
  return $({
    url: '/console/employee/changePasswd',
    method: 'post',
    data
  });
};

// 分页查找员工信息
export const pagingEmployees = (data) => {
  return $({
    url: '/console/employee/pagingEmployees',
    method: 'post',
    data
  });
};

//据部门ID分页查找员工信息;
export const pagingEmployeesByDepartmentId = (data) => {
  return $({
    url: '/console/employee/pagingEmployeesByDepartmentId',
    method: 'post',
    data
  });
};

// 员工信息保存
export const createEmployee = (data) => {
  return $({
    url: '/console/employee/createEmployee',
    method: 'post',
    data
  });
};

// 员工信息修改
export const updateEmployee = (data) => {
  return $({
    url: '/console/employee/updateEmployee',
    method: 'post',
    data
  });
};

// 员工信息删除
export const deleteEmployees = (data) => {
  return $({
    url: '/console/employee/deleteEmployees',
    method: 'post',
    data
  });
};

// 启用员工
export const enable = (id) => {
  return $({
    url: '/console/employee/enableEmployee/' + id,
    method: 'post'
  });
};

// 禁用员工
export const disable = (id) => {
  return $({
    url: '/console/employee/disableEmployee/' + id,
    method: 'post'
  });
};

// 重置密码
export const resetPassword = (id) => {
  return $({
    url: '/console/employee/resetPassword/' + id,
    method: 'post'
  });
};

// 查询单条员工信息
export const getEmployeeById = (id) => {
  return $({
    url: '/console/employee/getEmployeeById/' + id,
    method: 'post'
  });
};

// 获取部门树
export const departmentTree = () => {
  return $({
    url: '/console/department/listDepartmentTree',
    method: 'get'
  });
};

// 查询角色
export const findRoles = () => {
  return $({
    url: '/console/role/rolesList',
    method: 'get'
  });
};

// 分管车场授权
export const parkingAuthority = (data) => {
  return $({
    url: '/console/employee/parkingAuthority',
    method: 'post',
    data
  });
};

// 查询车场信息
export const getParkList = (data) => {
  return $({
    url: '/console/park/park/listPark',
    method: 'post',
    data
  });
};

// 查询员工授权充车场信息
export const employeeParkList = (employeeId) => {
  return $({
    url: '/console/employee/employeeParkList/' + employeeId,
    method: 'post'
  });
};

// 一键授权
export const authAllParks = (employeeId) => {
  return $({
    url: '/console/employee/authAllParks/' + employeeId,
    method: 'post'
  });
};

export const searchUserFromPlatform = (data) => {
  const user = useUser();
  console.log('searchUserFromPlatform', user.iam_token);
  const params = {
    // TODO token临时使用，接入IAM登录后获取登录信息
    token: user.iam_token || '',
    userNo: '',
    ...data
  };
  return $({
    url: '/console/employee/getUserInfo',
    method: 'get',
    params
  });
};
