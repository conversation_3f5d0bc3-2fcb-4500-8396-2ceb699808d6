/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找员工信息
export const pagingParkAccount = (data) => {
  return $({
    url: '/console/park/account/pagingParkAccount',
    method: 'post',
    data
  });
};

// 员工信息保存
export const createParkAccount = (data) => {
  return $({
    url: '/console/park/account/createParkAccount',
    method: 'post',
    data
  });
};

// 员工信息删除
export const deleteParkAccount = (id) => {
  return $({
    url: '/console/park/account/deleteParkAccount/' + id,
    method: 'post'
  });
};

// 启用员工
export const enableParkAccount = (id) => {
  return $({
    url: '/console/park/account/enableParkAccount/' + id,
    method: 'post'
  });
};

// 禁用员工
export const disableParkAccount = (id) => {
  return $({
    url: '/console/park/account/disableParkAccount/' + id,
    method: 'post'
  });
};

// 分页查询员工授权通道
export const pagingAccountAuthorization = (data) => {
  return $({
    url: '/console/park/account/pagingAccountAuthorization',
    method: 'post',
    data
  });
};

// 查询当前车场通道列表
export const listParkGateway = (data) => {
  return $({
    url: '/console/park/account/listParkGateway',
    method: 'post',
    data
  });
};

// 通道授权
export const authorizationGateway = (data) => {
  return $({
    url: '/console/park/account/authorizationGateway',
    method: 'post',
    data
  });
};

// 查询当前员工在当前车场已授权的通道列表
export const listAccountGateway = (data) => {
  return $({
    url: '/console/park/account/listAccountGateway',
    method: 'post',
    data
  });
};
