<template>
  <div ref="chartRef" class="chart-warp"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { isArray } from 'lodash';
import { markRaw, onBeforeUnmount, onMounted, ref } from 'vue';

const props = defineProps({
  // 颜色数组
  color: {
    type: Array,
    default: () => ['#00dbf2', '#ff917c', '#1890ff', '#6dfacd', '#ffa800', '#ff5b00', '#ff3000', '#ea7ccc', '#DDD7C6', '#5470c6']
  },
  // 是否只有一组数据
  single: {
    type: Boolean,
    default: false
  },
  // 是否展示顶端label
  showLabel: {
    type: Boolean,
    default: true
  },
  // 条形图宽度
  barWidth: {
    type: Number,
    default: 18
  },
  // 图表标题
  title: {
    type: String,
    default: ''
  },
  // 图表间隔
  barGap: {
    type: String,
    default: '30%'
  },
  gridbottom: {
    type: Number,
    default: -1
  },
  maxLeftY: {
    type: Number,
    default: -1
  }
});
const leftYMax = ref(null);
// 图表容器ref
const chartRef = ref(null);
// 图表实例
let chartInstance = null;
onMounted(() => {
  chartInstance = markRaw(echarts.init(chartRef.value));
  window.addEventListener('resize', resizeHandler);
});
/**
 * @description: 初始化图表
 */
const setData = (data, xData, yAxis = ['']) => {
  const colorArr = props.color.map(
    (item) =>
      new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        {
          offset: 0,
          color: item[0]
        },
        {
          offset: 1,
          color: item[1]
        }
      ])
  );
  chartInstance.clear();
  let options = {};
  const dataZoomEnd = xData ? (xData.length > 13 ? (13 / xData.length) * 100 : 10) : 0;
  let dataZoom = [];
  if (xData && xData.length > 13) {
    dataZoom = [
      {
        show: dataZoomEnd == 100 ? false : true,
        type: 'slider',
        realtime: true,
        start: 0,
        end: dataZoomEnd,
        height: 10,
        bottom: 0,
        handleSize: 0, // 左右2个滑动条的大小
        borderColor: '#035ba5', // 滑动通道的边框颜色
        showDetail: false,
        fillerColor: '#00fff4',
        backgroundColor: '#035ba5', // 未选中的滑动条的颜色
        showDataShadow: false // 是否显示数据阴影 默认auto
      }

      // {
      //   // 没有下面这块的话，只能拖动滚动条，鼠标滚轮在区域内不能控制外部滚动条(当前滑块可控制)
      //   type: 'inside',
      //   realtime: true,
      //   start: 0,
      //   end: dataZoomEnd
      // }
    ];
  }
  options = {
    dataZoom: dataZoom,
    tooltip: {
      // 是否只显示一个
      trigger: 'item'
    },
    // 标题
    title: {
      // 是否显示
      show: !!props.title,
      // 标题文本
      text: props.title,
      // 标题文本样式
      textStyle: {
        color: '#59baf2',
        fontWeight: 'normal',
        fontSize: 16
      },
      // 标题位置
      left: 'center'
    },
    // 图例
    legend: {
      // 图例方向
      orient: 'horizontal',
      // 图例图标
      icon: 'roundRect',
      // 图例位置
      bottom: '4%',
      // 图例位置
      x: 'center',
      // 图例文本样式
      textStyle: {
        color: '#fff'
      },
      // 图例宽度
      itemWidth: 14,
      // 图例高度
      itemHeight: 8,
      selectedMode: false
    },
    // x轴
    xAxis: {
      // 是否显示
      show: !props.single,
      // x轴类型
      type: 'category',
      // x轴轴线样式
      lineStyle: {
        opaticy: 0
      },
      // x轴轴标签文本样式
      axisLabel: {
        color: '#59baf2'
      },
      // x轴数据
      data: xData || []
    },
    // y轴
    yAxis: dealY(yAxis),
    // 网格
    grid: {
      // 网格距离顶部距离
      top: 39,
      // 网格距离底部距离
      bottom: props.gridbottom == -1 ? (!props.single ? 50 : 30) : props.gridbottom,
      // 网格距离左边距离
      left: 20,
      containLabel: true
    },
    // 颜色
    color: colorArr,
    // 数据
    series: dealData(data, yAxis)
  };
  chartInstance.setOption(options, true);
};

/**
 * @description 处理series数据
 * @param {*} data series数据
 *  @param {*} yAxis y轴数据 用于多y轴判断
 */
const dealData = (data, yAxis) => {
  const seriesData = [];
  // 遍历data，将每一项的值添加到seriesData中
  data.forEach((element, index) => {
    seriesData.push(
      // 柱底圆片
      {
        name: element.name,
        type: 'pictorialBar',
        symbolSize: [props.barWidth, 10], // 调整截面形状
        symbolOffset: [index % 2 !== 0 ? '65%' : '-65%', 5],
        itemStyle: {
          normal: {
            color: index === 0 ? '#5dd7ff' : '#fff073'
          }
        },
        z: 12,
        data: element.value
      },
      {
        // 类型
        type: 'bar',
        // 位置
        top: 'top',
        // 最大宽度
        barMaxWidth: props.barWidth,
        // 间距
        barGap: props.barGap,
        // 名称
        name: element.name,
        // 数据
        data: isArray(element.value) ? element.value : [element.value],
        // y轴索引
        yAxisIndex: yAxis && yAxis.length > 1 ? index : 0,
        // 标签
        label: {
          // 是否显示
          show: props.showLabel,
          // 位置
          position: 'top',
          // 颜色
          color: '#59baf2'
        }
      },
      // 柱顶圆片
      {
        name: element.name,
        type: 'pictorialBar',
        symbolSize: [props.barWidth, 10], // 调整截面形状
        symbolOffset: [index % 2 !== 0 ? '65%' : '-65%', -5],
        symbolPosition: 'end',
        itemStyle: {
          normal: {
            color: index === 0 ? '#5dd7ff' : '#fff073'
          }
        },
        z: 12,
        data: element.value
      }
    );
  });
  // 返回seriesData
  return seriesData;
};
/**
 * @description 处理y轴数据
 * @param {*} yAxis y轴数据
 */
const dealY = (data) => {
  const yAxisData = [];
  // 遍历data，将每一项添加到yAxisData中
  data.forEach((element, index) => {
    yAxisData.push({
      // 类型
      type: 'value',
      // 名称
      name: element,
      max: index % 2 === 0 ? leftYMax.value || props.maxLeftY || null : null,
      // 位置
      position: index % 2 === 0 ? 'left' : 'right',
      // 线条样式
      lineStyle: {
        opaticy: 0
      },
      // 标签样式
      axisLabel: {
        color: '#59baf2'
      },
      // 分割线样式
      splitLine: {
        lineStyle: {
          color: '#0b73ca'
        }
      },
      // 名称文本样式
      nameTextStyle: {
        color: '#59baf2'
      }
    });
  });
  // 返回yAxisData
  return yAxisData;
};

const resizeHandler = () => {
  if (!chartInstance) return;
  chartInstance.resize();
};

const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
  window.removeEventListener('resize', resizeHandler);
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData,
  leftYMax
});
</script>

<style lang="scss" scoped>
.chart-warp {
  width: 100%;
  height: 100%;
}
</style>
