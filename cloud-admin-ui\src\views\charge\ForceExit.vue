<template>
  <div class="container">
    <force-exit-search @form-search="searchForceExitList" @reset="resetParamsAndData" />
    <force-exit-table ref="table" />
  </div>
</template>

<script setup name="ForceExit">
import ForceExitSearch from './forceExit/ForceExitSearch.vue';
import ForceExitTable from './forceExit/ForceExitTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageForceExit = (queryParams) => {
  table.value.getList(queryParams);
};

const searchForceExitList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageForceExit
});
</script>
