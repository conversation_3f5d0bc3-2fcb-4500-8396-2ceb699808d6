<template>
  <div class="VersionInfocardbox">
    <el-collapse v-model="activeNames" @change="handleChange">
      <el-collapse-item name="1">
        <!-- 卡片标题 -->
        <template #title>
          <div class="car_title">
            <div class="car_versition">
              <div class="car_title_left">{{ data.version_name }}</div>
              <div class="car_title_right"></div>
            </div>
            <el-button v-if="useUserStore.role_id == 1" icon="edit" size="small" type="primary" @click.stop="editVersition">编辑</el-button>
          </div>
        </template>
        <!-- 卡片内容 -->
        <div class="fixinfo">
          {{ data.version_update_content }}
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
</template>

<script setup>
import { ref } from 'vue'; //引入vue
import { useUser } from '@/stores/user';
const useUserStore = useUser(); //userStore
const prop = defineProps(['data']);
const emit = defineEmits(['editVersition']);
const activeNames = ref(['1']); //始终展开卡片内容
//点击“编辑”按钮
const editVersition = () => {
  emit('editVersition', prop.data);
  console.log('编辑');
};
</script>

<style scoped lang="scss">
.VersionInfocardbox {
  margin-bottom: 20px;
  border-radius: 10px;
  overflow: hidden;
  // border: 1px solid #dcdcdc;
  :deep(.el-collapse-item__header,.el-collapse-item__wrap){
    background-color: #f7f7f7 !important;
  }
  :deep(.el-collapse-item__wrap){
    background-color: #f7f7f7 !important;
  }
  .car_title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    border-bottom: 1px solid #dcdcdc;
    .car_versition {
      //   height: 100%;
      display: flex;
      gap: 10px;
      align-items: flex-end;
      .car_title_left {
        height: fit-content;
        font-size: 16px;
      }
      .car_title_right {
        height: fit-content;
        font-size: 11px;
        line-height: 46px;
        color: #7b7b7b;
      }
    }
  }
  .fixinfo {
    flex: 1;
    overflow: auto;
    white-space: pre-line;
    padding-top: 5px;
    line-height: 30px;
    font-size: 15px;
    color: #000;
    padding-left: 20px;
  }
}
</style>
