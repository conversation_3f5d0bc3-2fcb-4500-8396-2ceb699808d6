<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleRelated()">关联商家</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" @click="handleEdit(scope.row, true)">
              编辑
            </el-button>
            <template v-if="scope.row.audit_state == 1 || scope.row.audit_state == 2">
              <el-button link type="primary" @click="handleEdit(scope.row, false)"> 查看 </el-button>
              <el-button link type="danger" v-if="scope.row.enabled_status == 1" @click="confirmWUsed(scope.row)"> 停用 </el-button>
              <el-button
                link
                type="primary"
                v-if="scope.row.enabled_status == 0"
                @click="confirmWUsed(scope.row)"
                :disabled="dayjs(scope.row.valid_end_time).isBefore(dayjs())"
              >
                启用
              </el-button>
            </template>
            <el-button link type="danger" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
            <el-button link v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" type="success" @click="handleAudit(scope.row.id)">
              提交审核
            </el-button>
            <el-button link type="primary" v-if="scope.row.audit_state == 1" @click="handleRevoke(scope.row.id)"> 撤回 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商家名称" align="center" min-width="140" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="160" />
        <el-table-column prop="coupon_meta_name" label="优免券名称" align="center" min-width="100" />
        <el-table-column prop="type_desc" label="优免券类型" align="center" min-width="100" />
        <el-table-column label="优惠内容" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.type == 1">{{ scope.row.coupon_meta_param.derate_hour + '小时' }}</span>
            <span v-if="scope.row.type == 2">{{ scope.row.coupon_meta_param.derate_money + '元' }}</span>
            <span v-if="scope.row.type == 3">{{ scope.row.coupon_meta_param.discount_ratio + '折' }}</span>
            <span v-if="scope.row.type == 5">{{ scope.row.coupon_meta_param.start_time + '~' + scope.row.coupon_meta_param.end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="unit_price" label="单价" align="center" min-width="100" />
        <el-table-column label="有效期" align="center" min-width="200">
          <template v-slot="scope">
            <span>{{ scope.row.valid_start_time + '—' + scope.row.valid_end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_count" label="优免券数量" align="center" min-width="100" />
        <el-table-column prop="audit_state_desc" label="审核状态" align="center" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="150" />
        <el-table-column prop="audit_comment" label="驳回意见" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="关联商家" v-model="relatedDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="700px">
        <el-form ref="addForm" label-width="140px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择商家" prop="merchant_id">
            <el-input v-model="data.form.merchant_name" placeholder="请选择商家" readonly @click="authMerchantCharge(true, 'add')" />
          </el-form-item>
          <el-form-item label="停车场名称">
            {{ data.form.park_name }}
          </el-form-item>
          <el-form-item prop="coupon_meta_id" label="优免模板">
            <el-input v-model="data.form.coupon_meta_name" placeholder="请选择优免券" readonly @click="authCouponMetaCharge(true, 'add')" />
          </el-form-item>
          <el-form-item prop="total_count" label="优免券数量">
            <el-input-number v-model="data.form.total_count" :min="1" style="width: 100%" :step="data.form.total_count < 10 ? 1 : 10" />
          </el-form-item>
          <el-form-item label-width="160px">
            <template #label>
              <span class="label-flex">
                商家限制
                <el-tooltip placement="top">
                  <template #content> 限制商家在同一自然日内对同一车牌最多发放优免劵张数。 </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
                <el-switch
                  v-model="data.form.max_count_switch"
                  @change="formMaxCountSwitch"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                />
              </span>
            </template>
            <el-input-number
              v-model="data.form.per_plate_max_grant_count"
              :disabled="data.form.max_count_switch === 0"
              :min="1"
              style="width: 100%"
              :step="data.form.per_plate_max_grant_count < 10 ? 1 : 10"
            />
          </el-form-item>
          <el-form-item prop="stack_type" label="优免劵叠加方式" v-if="globalCouponConfig.stack === 1">
            <el-select v-model="data.form.stack_type" placeholder="请选择叠加方式">
              <el-option label="不允许叠加" :value="0" />
              <el-option label="小时券叠加" :value="1" v-if="data.form.type == 1" />
              <el-option label="金额券叠加" :value="2" v-if="data.form.type == 2" />
            </el-select>
          </el-form-item>
          <el-form-item prop="max_stack_count" label="最大叠加数量限制" v-if="globalCouponConfig.stack === 1 && data.form.stack_type !== 0">
            <el-input-number v-model="data.form.max_stack_count" :min="0" style="width: 100%" :step="data.form.max_stack_count < 10 ? 1 : 10" />
          </el-form-item>
          <el-form-item>
            <template #label>
              <span class="label-flex">
                劵存量下限提醒
                <el-tooltip placement="top">
                  <template #content>
                    1、数量不等于0时候，手机号必填，等于0的时候，手机号非必填。<br />2、告警短信联系人请输入商户联系人联系方式或者车场联系人联系方式。
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <div style="display: flex; align-items: center; width: 100%">
              数量低于
              <el-input-number v-model="data.form.warn_threshold" :min="0" style="width: 120px" :step="data.form.warn_threshold < 10 ? 1 : 10" />
              时发送警告短信至
              <el-input v-model="data.form.warn_phone" placeholder="请输入手机号" style="width: 160px" />
            </div>
          </el-form-item>
          <el-form-item prop="total_price" label="总价（元）">
            <el-input-number
              v-model="data.form.total_price"
              :min="0"
              :precision="2"
              style="width: 100%"
              :step="data.form.total_price < 10 ? 1 : 10"
            />
          </el-form-item>
          <el-form-item label="单价（元）" class="required">
            <el-input-number v-model="form_per_price" :precision="2" style="width: 100%" disabled />
          </el-form-item>
          <el-form-item v-if="globalCouponConfig.share === 1" prop="free_hours">
            <template #label>
              <span class="label-flex">
                免费时长
                <el-tooltip placement="top">
                  <template #content>
                    ①允许共享，允许当前优免劵与免费时长共享，在使用优免后，免费时长不会计算费用。<br />②不允许共享，不允许当前优免劵与免费时长共享，在使用优免劵后，免费时长会被计算费用。
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-select v-model="data.form.free_hours" placeholder="请选择">
              <el-option label="允许共享" :value="1" />
              <el-option label="不允许共享" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="有效期" class="required">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="创建开始时间"
              end-placeholder="创建结束时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createCoupon(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog
        :title="editState ? '编辑' : '详情'"
        v-model="updateDialogVisible"
        :close-on-click-modal="false"
        @close="closeEditDialog(editForm)"
        width="700px"
      >
        <el-form ref="editForm" label-width="140px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="选择商家" prop="merchant_id">
            <el-input
              v-model="data.updateForm.merchant_name"
              placeholder="请选择商家"
              :disabled="!editState"
              readonly
              @click="authMerchantCharge(true, 'edit')"
            />
          </el-form-item>
          <el-form-item label="停车场名称">
            {{ data.updateForm.park_name }}
          </el-form-item>
          <el-form-item prop="coupon_meta_id" label="优免模板">
            <el-input
              v-model="data.updateForm.coupon_meta_name"
              placeholder="请选择优免券"
              :disabled="!editState"
              readonly
              @click="authCouponMetaCharge(true, 'edit')"
            />
          </el-form-item>
          <el-form-item prop="total_count" label="优免券数量">
            <el-input-number
              v-model="data.updateForm.total_count"
              :min="1"
              style="width: 100%"
              :step="data.updateForm.total_count < 10 ? 1 : 10"
              :disabled="!editState"
            />
          </el-form-item>
          <el-form-item label-width="160px">
            <template #label>
              <span class="label-flex">
                商家限制
                <el-tooltip placement="top">
                  <template #content> 限制商家在同一自然日内对同一车牌最多发放优免劵张数。 </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
                <el-switch
                  v-model="data.updateForm.max_count_switch"
                  @change="updateFormMaxCountSwitch"
                  :active-value="1"
                  :inactive-value="0"
                  active-color="#13ce66"
                  inactive-color="#ff4949"
                  :disabled="!editState"
                />
              </span>
            </template>
            <el-input-number
              v-model="data.updateForm.per_plate_max_grant_count"
              :min="1"
              style="width: 100%"
              :step="data.updateForm.per_plate_max_grant_count < 10 ? 1 : 10"
              :disabled="!editState || data.updateForm.max_count_switch === 0"
            />
          </el-form-item>
          <el-form-item prop="stack_type" label="优免劵叠加方式" v-if="globalCouponConfig.stack === 1">
            <el-select v-model="data.updateForm.stack_type" placeholder="请选择叠加方式" :disabled="!editState">
              <el-option label="不允许叠加" :value="0" />
              <el-option label="小时券叠加" :value="1" v-if="data.updateForm.type == 1" />
              <el-option label="金额券叠加" :value="2" v-if="data.updateForm.type == 2" />
            </el-select>
          </el-form-item>
          <el-form-item prop="max_stack_count" label="最大叠加数量限制" v-if="globalCouponConfig.stack === 1 && data.updateForm.stack_type !== 0">
            <el-input-number
              v-model="data.updateForm.max_stack_count"
              :min="0"
              style="width: 100%"
              :step="data.updateForm.max_stack_count < 10 ? 1 : 10"
              :disabled="!editState"
            />
          </el-form-item>
          <el-form-item>
            <template #label>
              <span class="label-flex">
                劵存量下限提醒
                <el-tooltip placement="top">
                  <template #content>
                    1、数量不等于0时候，手机号必填，等于0的时候，手机号非必填。<br />2、告警短信联系人请输入商户联系人联系方式或者车场联系人联系方式。
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <div style="display: flex; align-items: center; width: 100%">
              数量低于
              <el-input-number
                v-model="data.updateForm.warn_threshold"
                :min="0"
                style="width: 120px"
                :disabled="!editState"
                :step="data.updateForm.warn_threshold < 10 ? 1 : 10"
              />
              时发送警告短信至
              <el-input v-model="data.updateForm.warn_phone" placeholder="请输入手机号" :disabled="!editState" style="width: 160px" />
            </div>
          </el-form-item>
          <el-form-item prop="total_price" label="总价（元）">
            <el-input-number
              v-model="data.updateForm.total_price"
              :min="0"
              :precision="2"
              style="width: 100%"
              :disabled="!editState"
              :step="data.updateForm.total_price < 10 ? 1 : 10"
            />
          </el-form-item>
          <el-form-item label="单价（元）" class="required">
            <el-input-number v-model="form_per_price" :precision="2" style="width: 100%" disabled />
          </el-form-item>
          <el-form-item v-if="globalCouponConfig.share === 1" prop="free_hours" :disabled="!editState">
            <template #label>
              <span class="label-flex">
                免费时长
                <el-tooltip placement="top">
                  <template #content>
                    ①允许共享，允许当前优免劵与免费时长共享，在使用优免后，免费时长不会计算费用。<br />②不允许共享，不允许当前优免劵与免费时长共享，在使用优免劵后，免费时长会被计算费用。
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-select v-model="data.updateForm.free_hours" placeholder="请选择" :disabled="!editState">
              <el-option label="允许共享" :value="1" />
              <el-option label="不允许共享" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item label="有效期" class="required">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
              :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
              :disabled="!editState"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-form>
        <template #footer v-if="editState">
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateCoupon(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 商户查找带回 -->
      <el-dialog v-if="merchantDialogVisible" width="80%" title="选择商户" v-model="merchantDialogVisible" :before-close="handleMerchantClose">
        <merchant-find-back
          :merchant_id="merchant_id"
          :merchant_name="merchant_name"
          :mode="flag"
          @authCharge="authMerchantCharge(false, '')"
          @renderTableInput="renderMerchantTableInput"
        />
      </el-dialog>

      <!-- 优免模板查找带回 -->
      <el-dialog
        v-if="couponMetaDialogVisible"
        width="80%"
        title="选择优免模板"
        v-model="couponMetaDialogVisible"
        :before-close="handleCouponMetaClose"
      >
        <coupon-meta-find-back
          :park_id="park_id"
          :meta_id="meta_id"
          :meta_name="meta_name"
          :mode="flag"
          :type="type"
          @authCharge="authCouponMetaCharge(false, '')"
          @renderTableInput="renderCouponMetaTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CouponTable" setup>
import { reactive, ref, onActivated, computed, watch } from 'vue';
import { ElMessage, ElMessageBox, dayjs } from 'element-plus';
import couponService from '@/service/merchant/CouponService';
import MerchantFindBack from '../MerchantFindBack.vue';
import CouponMetaFindBack from '../CouponMetaFindBack.vue';
import merchantCouponService from '@/service/park/MerchantCouponService';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const relatedDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const editState = ref(false);
const merchantDialogVisible = ref(false);
const couponMetaDialogVisible = ref(false);

const dateRange = ref([]);
//查找带回需要
const park_id = ref('');
const merchant_id = ref('');
const merchant_name = ref('');
const meta_id = ref('');
const meta_name = ref('');
const type = ref('');
const flag = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    merchant_id: '',
    merchant_name: '',
    park_id: '',
    park_name: '',
    coupon_meta_id: '',
    coupon_meta_name: '',
    total_count: 0,
    coupon_money: '',
    valid_start_time: '',
    valid_end_time: '',
    free_hours: undefined,
    per_plate_max_grant_count: 1,
    warn_threshold: undefined,
    warn_phone: '',
    total_price: undefined,
    max_count_switch: undefined,
    stack_type: undefined,
    max_stack_count: undefined,
    form_per_price: undefined
  },
  updateForm: {
    free_hours: undefined,
    per_plate_max_grant_count: 1,
    warn_threshold: undefined,
    warn_phone: '',
    total_price: undefined,
    max_count_switch: undefined,
    stack_type: undefined,
    max_stack_count: undefined,
    form_per_price: undefined
  },
  rules: {
    merchant_id: [
      {
        required: true,
        message: '请选择商家',
        trigger: 'change'
      }
    ],
    coupon_meta_id: [
      {
        required: true,
        message: '请选择优免模板',
        trigger: 'blur'
      }
    ],
    total_count: [
      {
        required: true,
        message: '请输入优免券数量',
        trigger: 'blur'
      }
    ],
    free_hours: [
      {
        required: true,
        message: '请选择',
        trigger: 'blur'
      }
    ],
    stack_type: [
      {
        required: true,
        message: '请选择优免券叠加方式',
        trigger: 'blur'
      }
    ],
    max_stack_count: [
      {
        required: true,
        message: '请输入最大叠加数量限制',
        trigger: 'change'
      }
    ],
    total_price: [
      {
        required: true,
        message: '请输入总价',
        trigger: 'change'
      }
    ]
  }
});

const form_per_price = computed(() => {
  if (relatedDialogVisible.value) {
    if (data.form.total_price >= 0 && data.form.total_count > 0) {
      return (data.form.total_price / data.form.total_count).toFixed(2);
    } else {
      return 0;
    }
  } else if (updateDialogVisible.value) {
    if (data.updateForm.total_price >= 0 && data.updateForm.total_count > 0) {
      return (data.updateForm.total_price / data.updateForm.total_count).toFixed(2);
    } else {
      return 0;
    }
  }
  return 0;
});
watch(form_per_price, (val) => {
  if (relatedDialogVisible.value) {
    data.form.unit_price = val;
  }
  if (updateDialogVisible.value) {
    data.updateForm.unit_price = val;
  }
});

onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  couponService.pagingCouponMerchants(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleRelated = () => {
  data.form = {
    merchant_id: '',
    merchant_name: '',
    park_id: '',
    park_name: '',
    coupon_meta_id: '',
    coupon_meta_name: '',
    total_count: '',
    coupon_money: '',
    valid_start_time: '',
    valid_end_time: '',
    free_hours: undefined
  };
  globalCouponConfig.value = { available_populations: undefined, stack: undefined, share: undefined };
  dateRange.value = [];
  relatedDialogVisible.value = true;
};

const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7; //当天之后的时间可选
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createCoupon = (addForm) => {
  addForm.validate().then(() => {
    if (dateRange.value.length == 0) {
      ElMessage({
        message: '有效期不能为空',
        type: 'warning'
      });
      return false;
    }
    if (data.form.warn_threshold > 0) {
      if (!data.form.warn_phone) {
        ElMessage({
          message: '告警短信联系人手机号不能为空',
          type: 'warning'
        });
        return false;
      } else if (!/^1[0123456789]\d{9}$/.test(data.form.warn_phone)) {
        ElMessage({
          message: '告警短信联系人手机号格式不正确',
          type: 'warning'
        });
        return false;
      }
    }

    data.form.valid_start_time = dateRange.value[0];
    data.form.valid_end_time = dateRange.value[1];
    // delete data.form.merchant_name;
    // delete data.form.coupon_meta_name;
    couponService
      .createCouponMerchant(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          relatedDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch((err) => {
        ElMessage({
          message: err.response.data + ':' + err.response.detail_message != '' ? err.response.detail_message : err.response.data,
          type: 'error'
        });
      });
  });
};
const formMaxCountSwitch = (val) => {
  if (val === 0) {
    data.form.per_plate_max_grant_count = 1;
  }
};
const updateFormMaxCountSwitch = (val) => {
  if (val === 0) {
    data.updateForm.per_plate_max_grant_count = 1;
  }
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService
      .deleteCouponMerchant(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const confirmWUsed = (row) => {
  ElMessageBox.confirm(row.enabled_status == 0 ? '启用后，优免劵可正常领取！' : '停用后，优免劵将不可再领取！', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res;
    if (row.enabled_status == 0) {
      res = await couponService.enableMerchantCouponMeta(row.id);
    } else {
      res = await couponService.disableMerchantCouponMeta(row.id);
    }
    console.log(res);
    if (res.success) {
      ElMessage({
        message: (row.enabled_status == 0 ? '启用' : '停用') + '成功',
        type: 'success'
      });
      getList(data.queryParams);
    } else {
      ElMessage({
        message: res.detail_message != '' ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};

const handleEdit = (val, state) => {
  console.log(val, state);
  data.updateForm = {
    id: val.id,
    merchant_id: val.merchant_id,
    merchant_name: val.merchant_name,
    park_id: val.park_id,
    park_name: val.park_name,
    type: val.type,
    coupon_meta_id: val.coupon_meta_id,
    coupon_meta_name: val.coupon_meta_name,
    total_count: val.total_count,
    unit_price: val.unit_price,
    coupon_money: val.coupon_money,
    valid_start_time: val.valid_start_time,
    valid_end_time: val.valid_end_time,
    free_hours: val.free_hours,
    per_plate_max_grant_count: val.per_plate_max_grant_count,
    warn_threshold: val.warn_threshold,
    warn_phone: val.warn_phone,
    total_price: val.total_price,
    max_count_switch: val.max_count_switch,
    stack_type: val.stack_type,
    max_stack_count: val.max_stack_count,
    form_per_price: val.form_per_price
  };
  getMerchantCouponConfig(val.park_id);
  dateRange.value = [val.valid_start_time + ' 00:00:00', val.valid_end_time + ' 23:59:59'];
  updateDialogVisible.value = true;
  editState.value = state;
};
const updateCoupon = (editForm) => {
  editForm.validate().then(() => {
    if (dateRange.value.length == 0) {
      ElMessage({
        message: '有效期不能为空',
        type: 'warning'
      });
      return false;
    }
    if (data.updateForm.warn_threshold > 0) {
      if (!data.updateForm.warn_phone) {
        ElMessage({
          message: '告警短信联系人手机号不能为空',
          type: 'warning'
        });
        return false;
      } else if (!/^1[0123456789]\d{9}$/.test(data.updateForm.warn_phone)) {
        ElMessage({
          message: '告警短信联系人手机号格式不正确',
          type: 'warning'
        });
        return false;
      }
    }
    data.updateForm.valid_start_time = dateRange.value[0];
    data.updateForm.valid_end_time = dateRange.value[1];
    // delete data.updateForm.merchant_name;
    // delete data.updateForm.coupon_meta_name;
    couponService
      .updateCouponMerchant(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch((err) => {
        ElMessage({
          message: err.response.data + ':' + err.response.detail_message != '' ? err.response.detail_message : err.response.data,
          type: 'error'
        });
      });
  });
};

//审核
const handleAudit = (id) => {
  ElMessageBox.confirm('是否提交审核？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService.submitAuditCouponMerchant(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

//商户查找带回
const handleMerchantClose = () => {
  merchantDialogVisible.value = false;
};
const authMerchantCharge = (visible, mode) => {
  if (visible === false) {
    merchantDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      merchant_id.value = data.form.merchant_id;
      merchant_name.value = data.form.merchant_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      merchant_id.value = data.updateForm.merchant_id;
      merchant_name.value = data.updateForm.merchant_name;
      flag.value = mode;
    }
    merchantDialogVisible.value = true;
  }
};
const renderMerchantTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
    data.form.merchant_id = val[0].merchant_id;
    data.form.merchant_name = val[0].merchant_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
    data.updateForm.merchant_id = val[0].merchant_id;
    data.updateForm.merchant_name = val[0].merchant_name;
  }
  getMerchantCouponConfig(val[0].park_id);
};

//优免模板查找带回
const handleCouponMetaClose = () => {
  couponMetaDialogVisible.value = false;
};

const authCouponMetaCharge = (visible, mode) => {
  if (visible === false) {
    couponMetaDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      meta_id.value = data.form.coupon_meta_id;
      meta_name.value = data.form.coupon_meta_name;
      type.value = data.form.type;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      meta_id.value = data.updateForm.coupon_meta_id;
      meta_name.value = data.updateForm.coupon_meta_name;
      type.value = data.updateForm.type;
      flag.value = mode;
    }
    couponMetaDialogVisible.value = true;
  }
};

const renderCouponMetaTableInput = (val) => {
  console.log('val===', val);
  if (val[0].mode == 'add') {
    data.form.coupon_meta_id = val[0].meta_id;
    data.form.coupon_meta_name = val[0].meta_name;
    data.form.type = val[0].type;
  } else {
    data.updateForm.coupon_meta_id = val[0].meta_id;
    data.updateForm.coupon_meta_name = val[0].meta_name;
    data.updateForm.type = val[0].type;
  }
};

const handleRevoke = (id) => {
  ElMessageBox.confirm('请确认是否撤回？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService
      .revokeAuditCouponMerchant(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  relatedDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const globalCouponConfig = ref({ available_populations: undefined, stack: undefined, share: undefined });
const getMerchantCouponConfig = (id) => {
  merchantCouponService.getMerchant({ park_id: id }).then((res) => {
    if (res.success == true) {
      globalCouponConfig.value = res.data;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.label-flex {
  display: flex;
  align-items: center;
}

:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
