<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleRelated()">关联商家</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button link type="danger" v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
            <el-button link v-if="scope.row.audit_state == 0 || scope.row.audit_state == 3" type="success" @click="handleAudit(scope.row.id)">
              提交审核
            </el-button>
            <el-button link type="primary" v-if="scope.row.audit_state == 1" @click="handleRevoke(scope.row.id)"> 撤回 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商家名称" align="center" min-width="140" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="160" />
        <el-table-column prop="coupon_meta_name" label="优免券名称" align="center" min-width="100" />
        <el-table-column prop="type_desc" label="优免券类型" align="center" min-width="100" />
        <el-table-column label="优惠数" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.type == 1">{{ scope.row.coupon_meta_param.derate_hour }}</span>
            <span v-if="scope.row.type == 2">{{ scope.row.coupon_meta_param.derate_money }}</span>
            <span v-if="scope.row.type == 3">{{ scope.row.coupon_meta_param.discount_ratio }}</span>
          </template>
        </el-table-column>
        <el-table-column label="有效期" align="center" min-width="200">
          <template v-slot="scope">
            <span>{{ scope.row.valid_start_time + '—' + scope.row.valid_end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_count" label="优免券数量" align="center" min-width="100" />
        <el-table-column prop="audit_state_desc" label="审核状态" align="center" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="150" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="关联商家" v-model="relatedDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择商家" prop="merchant_id">
            <el-input v-model="data.form.merchant_name" placeholder="请选择商家" readonly @click="authMerchantCharge(true, 'add')" />
          </el-form-item>
          <el-form-item label="停车场名称">
            {{ data.form.park_name }}
          </el-form-item>
          <el-form-item prop="coupon_meta_id" label="优免模板">
            <el-input v-model="data.form.coupon_meta_name" placeholder="请选择优免券" readonly @click="authCouponMetaCharge(true, 'add')" />
          </el-form-item>
          <el-form-item prop="total_count" label="优免券数量">
            <el-input-number v-model="data.form.total_count" min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="有效期" class="required">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="创建开始时间"
              end-placeholder="创建结束时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createCoupon(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog title="编辑" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="选择商家" prop="merchant_id">
            <el-input v-model="data.updateForm.merchant_name" placeholder="请选择商家" readonly @click="authMerchantCharge(true, 'edit')" />
          </el-form-item>
          <el-form-item label="停车场名称">
            {{ data.updateForm.park_name }}
          </el-form-item>
          <el-form-item prop="coupon_meta_id" label="优免模板">
            <el-input v-model="data.updateForm.coupon_meta_name" placeholder="请选择优免券" readonly @click="authCouponMetaCharge(true, 'edit')" />
          </el-form-item>
          <el-form-item prop="total_count" label="优免券数量">
            <el-input-number v-model="data.updateForm.total_count" min="0" style="width: 100%" @change="changeEditTotalCount" />
          </el-form-item>
          <el-form-item label="有效期" class="required">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateCoupon(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 商户查找带回 -->
      <el-dialog v-if="merchantDialogVisible" width="80%" title="选择商户" v-model="merchantDialogVisible" :before-close="handleMerchantClose">
        <merchant-find-back
          :merchant_id="merchant_id"
          :merchant_name="merchant_name"
          :mode="flag"
          @authCharge="authMerchantCharge(false, '')"
          @renderTableInput="renderMerchantTableInput"
        />
      </el-dialog>

      <!-- 优免模板查找带回 -->
      <el-dialog
        v-if="couponMetaDialogVisible"
        width="80%"
        title="选择优免模板"
        v-model="couponMetaDialogVisible"
        :before-close="handleCouponMetaClose"
      >
        <coupon-meta-find-back
          :park_id="park_id"
          :meta_id="meta_id"
          :meta_name="meta_name"
          :mode="flag"
          @authCharge="authCouponMetaCharge(false, '')"
          @renderTableInput="renderCouponMetaTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CouponTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import couponService from '@/service/merchant/CouponService';
import MerchantFindBack from '../MerchantFindBack.vue';
import CouponMetaFindBack from '../CouponMetaFindBack.vue';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const relatedDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const merchantDialogVisible = ref(false);
const couponMetaDialogVisible = ref(false);

const dateRange = ref([]);
//查找带回需要
const park_id = ref('');
const merchant_id = ref('');
const merchant_name = ref('');
const meta_id = ref('');
const meta_name = ref('');
const flag = ref('');

const unit_price = ref('');
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    merchant_id: '',
    merchant_name: '',
    park_id: '',
    park_name: '',
    coupon_meta_id: '',
    coupon_meta_name: '',
    total_count: '',
    coupon_money: '',
    valid_start_time: '',
    valid_end_time: ''
  },
  updateForm: {},
  rules: {
    merchant_id: [
      {
        required: true,
        message: '请选择商家',
        trigger: 'blur'
      }
    ],
    coupon_meta_id: [
      {
        required: true,
        message: '请选择优免模板',
        trigger: 'blur'
      }
    ],
    total_count: [
      {
        required: true,
        message: '请输入优免券数量',
        trigger: 'blur'
      }
    ]
  }
});
onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  couponService.pagingCouponMerchants(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleRelated = () => {
  data.form = {
    merchant_id: '',
    merchant_name: '',
    park_id: '',
    park_name: '',
    coupon_meta_id: '',
    coupon_meta_name: '',
    total_count: '',
    coupon_money: '',
    valid_start_time: '',
    valid_end_time: ''
  };
  dateRange.value = [];
  relatedDialogVisible.value = true;
};

const disabledDate = (time) => {
  return time.getTime() < Date.now() - 8.64e7; //当天之后的时间可选
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createCoupon = (addForm) => {
  addForm.validate().then(() => {
    if (dateRange.value.length == 0) {
      ElMessage({
        message: '有效期不能为空',
        type: 'warning'
      });
      return false;
    }
    data.form.valid_start_time = dateRange.value[0];
    data.form.valid_end_time = dateRange.value[1];
    delete data.form.merchant_name;
    delete data.form.coupon_meta_name;
    couponService
      .createCouponMerchant(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          relatedDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService
      .deleteCouponMerchant(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (val) => {
  data.updateForm = {
    id: val.id,
    merchant_id: val.merchant_id,
    merchant_name: val.merchant_name,
    park_id: val.park_id,
    park_name: val.park_name,
    coupon_meta_id: val.coupon_meta_id,
    coupon_meta_name: val.coupon_meta_name,
    total_count: val.total_count,
    coupon_money: val.coupon_money,
    valid_start_time: val.valid_start_time,
    valid_end_time: val.valid_end_time
  };
  unit_price.value = val.unit_price;
  dateRange.value = [val.valid_start_time, val.valid_end_time];
  updateDialogVisible.value = true;
};
const updateCoupon = (editForm) => {
  editForm.validate().then(() => {
    if (dateRange.value.length == 0) {
      ElMessage({
        message: '有效期不能为空',
        type: 'warning'
      });
      return false;
    }
    data.updateForm.valid_start_time = dateRange.value[0];
    data.updateForm.valid_end_time = dateRange.value[1];
    delete data.updateForm.merchant_name;
    delete data.updateForm.coupon_meta_name;
    couponService
      .updateCouponMerchant(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.message + ':' + response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//审核
const handleAudit = (id) => {
  ElMessageBox.confirm('是否提交审核？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService.submitAuditCouponMerchant(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

//商户查找带回
const handleMerchantClose = () => {
  merchantDialogVisible.value = false;
};
const authMerchantCharge = (visible, mode) => {
  if (visible === false) {
    merchantDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      merchant_id.value = data.form.merchant_id;
      merchant_name.value = data.form.merchant_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      merchant_id.value = data.updateForm.merchant_id;
      merchant_name.value = data.updateForm.merchant_name;
      flag.value = mode;
    }
    merchantDialogVisible.value = true;
  }
};
const renderMerchantTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
    data.form.merchant_id = val[0].merchant_id;
    data.form.merchant_name = val[0].merchant_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
    data.updateForm.merchant_id = val[0].merchant_id;
    data.updateForm.merchant_name = val[0].merchant_name;
  }
};

//优免模板查找带回
const handleCouponMetaClose = () => {
  couponMetaDialogVisible.value = false;
};

const authCouponMetaCharge = (visible, mode) => {
  if (visible === false) {
    couponMetaDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      meta_id.value = data.form.coupon_meta_id;
      meta_name.value = data.form.coupon_meta_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      meta_id.value = data.updateForm.coupon_meta_id;
      meta_name.value = data.updateForm.coupon_meta_name;
      flag.value = mode;
    }
    couponMetaDialogVisible.value = true;
  }
};

const renderCouponMetaTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.coupon_meta_id = val[0].meta_id;
    data.form.coupon_meta_name = val[0].meta_name;
    unit_price.value = val[0].unit_price;
  } else {
    data.updateForm.coupon_meta_id = val[0].meta_id;
    data.updateForm.coupon_meta_name = val[0].park_name;
    unit_price.value = val[0].unit_price;
  }
};

const handleRevoke = (id) => {
  ElMessageBox.confirm('请确认是否撤回？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    couponService
      .revokeAuditCouponMerchant(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  relatedDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
