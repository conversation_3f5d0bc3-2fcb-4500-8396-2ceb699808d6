<template>
  <div>
    <park-info-search @form-search="searchParkInfoList" @reset="resetParamsAndData" />
    <park-info-table ref="table" />
  </div>
</template>

<script setup name="ParkInfo">
import ParkInfoSearch from './parkInfo/ParkInfoSearch.vue';
import ParkInfoTable from './parkInfo/ParkInfoTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
