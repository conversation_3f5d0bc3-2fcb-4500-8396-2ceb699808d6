<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space> </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="order_no" label="订单号" align="center" width="280" />
        <el-table-column prop="park_name" label="停车场名称" align="center" width="180" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="120" />
        <el-table-column prop="in_time" label="入场时间" align="center" width="100" />
        <el-table-column prop="in_gateway_name" label="入场通道" align="center" width="90" />
        <el-table-column prop="to_time" label="出场时间" align="center" width="100" />
        <el-table-column prop="out_gateway_name" label="出场通道" align="center" width="90" />
        <el-table-column prop="park_time" label="停车时长（分钟）" align="center" width="90" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" width="90" />
        <el-table-column prop="park_type_desc" label="停车类型" align="center" width="90" />
        <el-table-column prop="plate_no" label="车牌号" align="center" width="90" />
        <el-table-column prop="order_state_desc" label="订单状态" align="center" width="90" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" width="90" />
        <el-table-column prop="should_pay_money" label="应交金额" align="center" width="90" />
        <el-table-column prop="current_coupon_money" label="优惠金额" align="center" width="90" />
        <el-table-column prop="payed_money" label="实缴金额" align="center" width="90" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="90" />
        <el-table-column prop="charge_name" label="收费员" align="center" width="90" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="StopFee" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import invoiceService from '@/service/invoice/InvoiceService';

const park_id = ref('');
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    id: undefined,
    mobile: undefined,
    page: 1,
    limit: 30
  }
});

// 关联临停费用订单的分页数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  invoiceService.relativeParkOrderPaging(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
