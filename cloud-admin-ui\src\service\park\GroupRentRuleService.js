import * as groupRentRuleApi from '@/api/park/GroupRentRuleApi';

/**
 * 总部长租规则
 */
export default {
  /**
   * 分页查找总部长租规则
   */
  pagingGroupRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi.pagingGroupRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建总部长租规则
   */
  createGroupRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi.createGroupRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用长租规则
   */
  enableRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi.enableRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 禁用长租规则
   */
  disableRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi.disableRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  updateRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi
          .updateRentRule(data)
          .then((res) => {
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      } catch (error) {
        reject(error);
      }
    });
  },

  deleteRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        groupRentRuleApi.deleteRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
