<template>
  <div class="seatBox">
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
      <form-search-item>
        <el-date-picker
          v-model="form.outDateRange"
          type="datetimerange"
          style="width: 100%"
          range-separator="至"
          start-placeholder="入场开始时间"
          end-placeholder="入场结束时间"
          :shortcuts="shortcuts"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
          :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
        />
      </form-search-item>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
      </div>
      <div ref="table">
        <el-table :data="tableData" v-loading="loading" border style="max-height: calc(100vh - 484px)">
          <el-table-column align="center" label="车牌号" prop="login_user"></el-table-column>
          <el-table-column align="center" label="入场时间" prop="login_user"></el-table-column>
          <el-table-column align="center" label="入口通道" prop="login_password"></el-table-column>
          <el-table-column align="center" label="入场图片" prop="login_password"></el-table-column>
          <el-table-column align="center" label="操作">
            <template v-slot="{ row }">
              <div class="buttonbox">
                <el-button type="primary" link @click="checkSeatInfo(row)">匹配</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import seatManagementService from '@/service/system/SeatManagementService';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, ref } from 'vue'; //引入“vue”
// import ParkFindBack from './ParkFindBack.vue'; //引入"关联车场"组件

const lookInfoDialog = ref(false); //控制“查看坐席信息”弹出框
const lookInfoForm = ref({
  login_user: '',
  login_password: '',
  user_name: '',
  phone: ''
});

const queryParams = ref({
  page: 1,
  limit: 30
});
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const form = ref({
  plate_no: '',
  outDateRange: []
});
const employeeLists = ref([]); // 员工列表
onMounted(() => {
  handleDataSearch(); // 页面加载时获取数据
  seatManagementService.employeeList().then((response) => {
    if (response.success === true) {
      employeeLists.value = response.data.map((item) => {
        return {
          id: parseInt(item.id),
          name: item.name
        };
      }); // 获取员工列表
    } else {
      ElMessage({
        message: response.detail_message || response.message,
        type: 'error'
      });
    }
  });
});
const shortcuts = [
  {
    text: '最近三天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  }
  // {
  //   text: '最近三个月',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
  //     return [start, end];
  //   }
  // },
  // {
  //   text: '最近一年',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
  //     return [start, end];
  //   }
  // }
];

//点击了“查看”按钮
const checkSeatInfo = (row) => {
  seatManagementService.watchSeatsDetail(row.id).then((response) => {
    if (response.success === true) {
      lookInfoForm.value = response.data;
      lookInfoDialog.value = true;
    } else {
      ElMessage({
        message: response.detail_message || response.message,
        type: 'error'
      });
    }
  });
};

const handleDataSearch = (params) => {
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  return;
  seatManagementService.watchSeatsList(queryParams.value).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};
</script>

<style lang="scss" scoped>
.formClass {
  // height: 150px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-input {
    width: 100%;
  }
}
</style>
