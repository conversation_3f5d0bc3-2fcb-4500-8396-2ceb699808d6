<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate">添加长租规则</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="success" v-if="scope.row.state === 0" @click="enabled(scope.row)"> 启用 </el-button>
            <el-button link type="danger" v-if="scope.row.state === 1" @click="disabled(scope.row)"> 禁用 </el-button>
            <el-button link type="primary" v-if="scope.row.state === 0" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" v-if="scope.row.state === 0" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="规则名称" align="center" />
        <el-table-column prop="details" label="长租规则" align="center">
          <template v-slot="scope">
            <span v-for="item in scope.row.details" :key="item.value" :label="item.key" :value="item.value"
              >{{ scope.row.prk_rent_rule_type_desc }}-{{ item.state_desc }}-{{ item.money }}<br
            /></span>
          </template>
        </el-table-column>
        <el-table-column prop="state_desc" label="启用状态" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <long-rent-dialog ref="rentDialog" @submit="getList(data.queryParams)" />
</template>

<script name="GroupLongRentRuleTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import groupRentRuleService from '@/service/park/GroupRentRuleService';
import longRentDialog from '../components/longRentDialog.vue';
import { cloneDeep } from 'lodash';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const park_id = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  }
});

onMounted(() => {
  getList(data.queryParams);
});

// 分页查询长租规则列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  groupRentRuleService.pagingGroupRentRule(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 启用
const enabled = (row) => {
  ElMessageBox.confirm('是否要启用该长租规则？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    groupRentRuleService.enableRentRule(row.id).then(() => {
      ElMessage({
        message: '长租规则启用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 禁用
const disabled = (row) => {
  ElMessageBox.confirm('是否要禁用该长租规则？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    groupRentRuleService.disableRentRule(row.id).then(() => {
      ElMessage({
        message: '长租规则禁用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 添加长租规则
const handleCreate = () => {
  rentDialog.value.showDialog();
};
/**
 * @description 修改总部长租规则
 */
const handleEdit = (row) => {
  const params = cloneDeep(row);
  rentDialog.value.showDialog({
    id: params.id,
    park_id: params.park_id,
    name: params.name,
    type: params.prk_rent_rule_type,
    public_open: params.public_open,
    money: params.details[0].money,
    product_type: params.details[0].type,
    product_list: params.details,
    product_type_list: params.product_type_list
  });
};

const handleDelete = (id) => {
  ElMessageBox.confirm('删除后无法恢复，是否确定删除当前长租规则？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    groupRentRuleService.deleteRentRule(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
    });
  });
};

// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const rentDialog = ref();

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
