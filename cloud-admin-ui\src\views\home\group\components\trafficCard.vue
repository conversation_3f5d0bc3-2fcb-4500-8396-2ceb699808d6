<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-12 14:20:38
 * @LastEditTime: 2024-06-24 17:37:25
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\home\group\components\trafficCard.vue
-->
<template>
  <div class="card-container">
    <div class="title">{{ props.label }}</div>
    <div class="value">{{ truncateDecimal(props.value, 3) + props.unit }}</div>
  </div>
</template>

<script setup>
import { truncateDecimal } from '@/utils/utils';
const props = defineProps({
  label: {
    type: String,
    default: '- -'
  },
  value: {
    type: String,
    default: '- -'
  },
  unit: {
    type: String,
    default: '- -'
  }
});
</script>

<style lang="scss" scoped>
.card-container {
  width: 100%;
  height: 40px;
  background: rgba(#3da2ec, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-radius: 4px;
  border: 1px solid #0b73ca;
  color: #fff;
  font-size: 14px;
  padding: 10px;
  .value {
    color: #00d0fd;
    font-weight: 550;
    font-size: 20px;
  }
}
</style>
