<template>
  <div>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
      <form-search-item>
        <ClearableChargeInput v-model="queryParams.org_department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
      </form-search-item>
      <form-search-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          style="width: 100%"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </form-search-item>
      <form-search-item>
        <el-input v-model="queryParams.plate_no" placeholder="车牌号" clearable />
      </form-search-item>
      <form-search-item>
        <el-select v-model="queryParams.device_id" placeholder="缴费机名称" clearable>
          <el-option v-for="item in payMachines" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </form-search-item>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="index" :index="(index) => index + 1" label="序号" align="center" width="80" />
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="group_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="car_no" label="车牌号" align="center" />
        <el-table-column prop="event_time" label="缴费时间" align="center" />
        <el-table-column prop="device_name" label="缴费机名称" align="center">
          <template #="{ row }"> {{ row.gateway_name }}-{{ row.device_name }} </template>
        </el-table-column>
        <el-table-column prop="order_total_money" label="现金支付金额（元）" align="center" />
        <el-table-column prop="cash_total" label="纸币收入（元）" align="center" />
        <el-table-column prop="electron_pay_amount" label="电子支付金额（元）" align="center" />
        <el-table-column label="微信找零（元）" align="center">
          <el-table-column prop="change_amount" label="找零金额" align="center">
            <template #default="scope">
              <span>{{ (scope.row.change_amount || 0).toFixed(2) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="last_order_update_time" label="领取时间" align="center"></el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination
        class="table-pagination"
        background
        :current-page="queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!-- 关联组织架构 -->
    <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
      <org-find-back
        :organization_id="department_id"
        :department_name="department_name"
        @orgCharge="orgCharge(false)"
        :mode="flag"
        @renderOrgTableInput="renderOrgTableInput"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onActivated, watch } from 'vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
// import OrgFindBack from '@/components/OrgFindBack.vue';
import OrgFindBack from './components/OrgFindBack.vue';
import { ElMessage, dayjs } from 'element-plus';
import selfServicePaymentService from '@/service/charge/SelfServicePaymentService';

const props = defineProps({
  searchParam: {
    type: Object,
    default: () => ({})
  }
});

const queryParams = ref({
  organization_ids: undefined,
  org_department_name: undefined,
  device_id: undefined,
  withdraw_record_id: undefined,
  plate_no: undefined,
  start_time: undefined,
  end_time: undefined,
  page: 1,
  limit: 30
});
const dateRange = ref([dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
const tableData = ref([]);
const payMachines = ref([]);
const loading = ref(false);
const total = ref(0);
const department_id = ref('');
const department_name = ref('');
const relatedOrgDialogVisible = ref(false);

const shortcuts = [
  {
    text: '最近7天',
    value: () => {
      return [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近14天',
    value: () => {
      return [dayjs().subtract(13, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近30天',
    value: () => {
      return [dayjs().subtract(29, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近90天',
    value: () => {
      return [dayjs().subtract(89, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  }
];

onActivated(() => {
  // 数据初始化
  selfServicePaymentService.listPayMachines().then((response) => {
    if (response.success === true) {
      payMachines.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
});

const propsSearchParam = ref();
watch(
  () => props.searchParam,
  (newVal) => {
    propsSearchParam.value = newVal;
    if (newVal && Object.keys(newVal).length > 0) {
      dateRange.value = undefined;
    } else {
      dateRange.value = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  { deep: true, immediate: true }
);

const handleDataSearch = (params) => {
  if (undefined !== dateRange.value && null !== dateRange.value && dateRange.value.length > 0) {
    queryParams.value.start_time = dateRange.value[0] + ' 00:00:00';
    queryParams.value.end_time = dateRange.value[1] + ' 23:59:59';
  } else {
    queryParams.value.start_time = undefined;
    queryParams.value.end_time = undefined;
  }
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  const pagingParams = {
    ...queryParams.value,
    ...propsSearchParam.value
  };
  selfServicePaymentService.pagingCashChangeRecords(pagingParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
  propsSearchParam.value = {};
  dateRange.value = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  queryParams.value = {
    organization_ids: undefined,
    org_department_name: undefined,
    device_id: undefined,
    withdraw_record_id: undefined,
    plate_no: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  handleDataSearch();
};
const clearDepartment = () => {
  queryParams.value.organization_ids = undefined;
  queryParams.value.org_department_name = undefined;
};
// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    department_id.value = queryParams.value.organization_ids;
    department_name.value = queryParams.value.org_department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  queryParams.value.organization_ids = arrId.toString();
  queryParams.value.org_department_name = arrName.toString();
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};

defineExpose({
  handleDataSearch
});
</script>

<style lang="scss" scoped></style>
