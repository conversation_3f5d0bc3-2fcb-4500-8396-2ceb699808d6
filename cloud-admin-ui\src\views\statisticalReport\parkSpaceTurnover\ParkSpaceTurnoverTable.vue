<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="parkTurnoverService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="组织架构" align="center" min-width="180" />
        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="parking_space_number" label="临停车位数" align="center" min-width="180" />
        <el-table-column prop="parking_out_number" label="临停出场车次数" align="center" min-width="180" />
        <el-table-column prop="parking_payed_out_number" label="付费临停出场车次数" align="center" min-width="180" />
        <el-table-column prop="average_turnround_rate" label="总平均日周转率" align="center" min-width="180" />
        <el-table-column prop="payed_average_turnround_rate" label="付费平均日周转率" align="center" min-width="180" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkSpaceTurnoverTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkTurnoverService from '@/service/statisticalReport/ParkTurnoverService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  debugger;
  loading.value = true;
  data.queryParams = params;
  parkTurnoverService.pagingParkTurnover(params).then((response) => {
    console.log(response.data);
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
