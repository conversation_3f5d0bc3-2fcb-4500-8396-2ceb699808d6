/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:21
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\service\statisticalReport\ParkingCarFlowService.js
 * @Description: {}
 */
import * as parkingCarFlowApi from '@/api/statisticalReport/ParkingCarFlowApi';

/**
 * 车场车流量
 */
export default {
  /**
   * 车场车流量-车流明细-统计金额
   */
  parkTrafficFlowsCount(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.parkTrafficFlowsCount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询车场车流量
   */
  pagingParkingCarFlow(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.pagingParkingCarFlow(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按地区层级查询车场车流量
   */
  chartOrgParkTrafficFlows(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.chartOrgParkTrafficFlows(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按地区层级查询车场车流量
   */
  chartTrendParkTrafficFlows(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.chartTrendParkTrafficFlows(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按地区层级查询车场车流量
   */
  chartPeriodParkTrafficFlows(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.chartPeriodParkTrafficFlows(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按日导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按月导出
   */
  exportDataByMonth(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingCarFlowApi.exportDataByMonth(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
