<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)" @selection-change="handleSelectionChange">
        <el-table-column type="selection" style="text-align: center" width="40" :selectable="(row, index) => row.audit_state === 0" />
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.audit_state == 0" @click="handleAudit(scope.row.id)"> 审核 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="审批单号" align="center" width="100"></el-table-column>
        <el-table-column label="审核标题" min-width="240">
          <template v-slot="scope">
            <el-tooltip :content="scope.row.title" placement="top" effect="dark">
              <el-button link type="primary" @click="handleDetail(scope.row.id, scope.row.audit_state)">
                {{ scope.row.title.length > 50 ? scope.row.title.slice(0, 50) + '...' : scope.row.title }}
              </el-button>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column prop="type_desc" label="业务类型" align="center" min-width="100" />
        <el-table-column label="审核状态" align="center" min-width="100">
          <template v-slot="scope">
            <span v-if="scope.row.audit_state == 0" style="color: red">{{ scope.row.audit_state_desc }}</span>
            <span v-if="scope.row.audit_state == 2" style="color: blue">{{ scope.row.audit_state_desc }}</span>
            <span v-if="scope.row.audit_state == 3" style="color: #daa520">{{ scope.row.audit_state_desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audit_comment" label="审核意见" align="center" min-width="100" />
        <el-table-column prop="updated_at" label="申请时间" align="center" min-width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="长租车申请详情" v-model="spaceRentApplyDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.rentApplyFrom">
          <el-form-item label="停车场名称">{{ data.rentApplyFrom.park_name }} </el-form-item>
          <el-form-item label="车位编号"> {{ data.rentApplyFrom.space_code }}</el-form-item>
          <el-form-item label="续费类型"> {{ data.rentApplyFrom.open_sign_desc }}</el-form-item>
          <el-form-item label="申请来源"> {{ data.rentApplyFrom.channel_desc }}</el-form-item>
          <el-form-item label="规则名称"> {{ data.rentApplyFrom.prk_rent_rule_name }}</el-form-item>
          <el-form-item label="长租类型"> {{ data.rentApplyFrom.prk_rent_rule_type_desc }}</el-form-item>
          <el-form-item label="产品类型"> {{ data.rentApplyFrom.prk_rent_product_name }}</el-form-item>
          <el-form-item label="产品金额"> {{ data.rentApplyFrom.order_money }}</el-form-item>
          <el-form-item label="产品周期">
            {{
              formatRentProductRangeText(rentProductRanges, data.rentApplyFrom.product_range, data.rentApplyFrom.prk_rent_product_id)
            }}</el-form-item
          >
          <el-form-item label="长租时段"> {{ data.rentApplyFrom.rent_time || '全时段' }}</el-form-item>
          <el-form-item label="有效期">
            <span v-if="data.rentApplyFrom.valid_start_time != null && data.rentApplyFrom.valid_end_time != null">{{
              data.rentApplyFrom.valid_start_time + '~' + data.rentApplyFrom.valid_end_time
            }}</span>
            <span v-else>--</span>
          </el-form-item>
          <el-form-item label="车牌号">
            <el-tooltip :content="data.rentApplyFrom.plate_nos || '--'" placement="top">
              <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"> {{ data.rentApplyFrom.plate_nos || '--' }}</span>
            </el-tooltip>
          </el-form-item>
          <el-form-item label="车主姓名"> {{ data.rentApplyFrom.mbr_member_name }}</el-form-item>
          <el-form-item label="车主手机号"> {{ data.rentApplyFrom.mbr_member_mobile }}</el-form-item>
          <el-form-item label="用户身份"> {{ data.rentApplyFrom.user_identity_desc }}</el-form-item>
          <el-form-item label="付款方式" v-if="data.rentApplyFrom.pay_method_desc != undefined">
            {{ data.rentApplyFrom.pay_method_desc }}</el-form-item
          >
          <el-form-item label="实收金额" v-if="data.rentApplyFrom.pay_method !== 4 && data.rentApplyFrom.payed_money != undefined">
            {{ data.rentApplyFrom.payed_money }}</el-form-item
          >
          <el-form-item label="付款备注" v-if="data.rentApplyFrom.pay_method !== 4 && data.rentApplyFrom.payed_memo != undefined">
            <span style="width: 100%">
              {{ data.rentApplyFrom.payed_memo }}
            </span>
          </el-form-item>
          <el-form-item label="审核资料">
            <div style="display: flex; flex-direction: column; align-items: flex-start">
              <el-link
                v-for="url in data.rentApplyFrom.audit_urls"
                :key="url.audit_data_url"
                @click="exportAuditData(url.audit_data_url)"
                type="primary"
                >{{ url.audit_data_name }}</el-link
              >
            </div>
          </el-form-item>
          <el-form-item label="缴费凭证" v-if="!!data.rentApplyFrom.payed_voucher_url">
            <div style="display: flex; flex-direction: column; align-items: flex-start">
              <el-link
                v-for="url in JSON.parse(data.rentApplyFrom.payed_voucher_url)"
                :key="url.voucher_url"
                @click="exportAuditData(url.audit_data_url)"
                type="primary"
                >{{ url.audit_data_name }}</el-link
              >
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="spaceRentApplyDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 访客车预约申请详情 -->
      <el-dialog title="访客车预约申请" v-model="carVisitorDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.visitorForm">
          <el-form-item label="访客姓名">{{ data.visitorForm.visitor_name }} </el-form-item>
          <el-form-item label="访客类型">{{ data.visitorForm.visitor_type_desc }} </el-form-item>
          <el-form-item label="手机号码">{{ data.visitorForm.visitor_phone }} </el-form-item>
          <el-form-item label="车牌号码">{{ data.visitorForm.visitor_plate_no }} </el-form-item>
          <el-form-item label="预约车场">{{ data.visitorForm.park_name }} </el-form-item>
          <el-form-item label="预约时间">{{ data.visitorForm.start_time + '~' + data.visitorForm.end_time }} </el-form-item>
          <el-form-item label="来访事由">{{ data.visitorForm.visit_reason }} </el-form-item>
          <el-form-item label="申请人">{{ data.visitorForm.updator_name }} </el-form-item>
          <el-form-item label="申请时间">{{ data.visitorForm.created_at }} </el-form-item>
          <el-form-item label="来访证明">
            <div style="display: flex; flex-direction: column; align-items: flex-start">
              <el-link
                v-for="url in data.visitorForm.audit_urls"
                :key="url.audit_data_url"
                @click="exportAuditData(url.audit_data_url)"
                type="primary"
                >{{ url.audit_data_name }}</el-link
              >
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass" :loading="passLoading">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="carVisitorDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 长租规则申请详情 -->
      <el-dialog title="长租规则申请详情" v-model="rentRuleDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.rentRuleFrom">
          <el-form-item label="停车场名称">{{ data.rentRuleFrom.park_name }} </el-form-item>
          <el-form-item label="规则名称"> {{ data.rentRuleFrom.name }}</el-form-item>
          <el-form-item label="长租类型"> {{ data.rentRuleFrom.type_desc }}</el-form-item>
          <el-form-item label="产品类型"> {{ data.rentRuleFrom.product_type_desc }}</el-form-item>
          <el-form-item label="产品金额"> {{ data.rentRuleFrom.money }}</el-form-item>
          <el-form-item label="产品周期">
            {{ formatRentProductRangeText(rentProductRanges, data.rentRuleFrom.product_range, data.rentRuleFrom.product_type) }}</el-form-item
          >
          <el-form-item label="长租时段"> {{ data.rentRuleFrom.rent_time || '全时段' }}</el-form-item>
          <el-form-item label="是否对小程序开放"> {{ data.rentRuleFrom.public_open_desc }}</el-form-item>
          <el-form-item label="创建人"> {{ data.rentRuleFrom.creator || '--' }}</el-form-item>
          <el-form-item label="创建时间"> {{ data.rentRuleFrom.rent_rule_create_time || '--' }}</el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="rentRuleDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 免费车申请详情 -->
      <el-dialog title="免费车申请详情" v-model="whiteListDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.whiteListFrom">
          <el-form-item label="停车场名称">{{ data.whiteListFrom.park_name }} </el-form-item>
          <el-form-item label="车位编号"> {{ data.whiteListFrom.space_code }}</el-form-item>
          <el-form-item label="车牌号"> {{ data.whiteListFrom.plate_no }}</el-form-item>
          <el-form-item label="车主姓名"> {{ data.whiteListFrom.name }}</el-form-item>
          <el-form-item label="业态属性"> {{ data.whiteListFrom.business_format_desc || '--' }}</el-form-item>
          <el-form-item label="手机号"> {{ data.whiteListFrom.mobile }}</el-form-item>
          <el-form-item label="有效期"> {{ data.whiteListFrom.effective_start_time + '—' + data.whiteListFrom.effective_end_time }}</el-form-item>
          <el-form-item label="审核资料">
            <el-link @click="exportAuditData(data.whiteListFrom.audit_data_url)" type="primary">{{ data.whiteListFrom.audit_data_name }}</el-link>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="whiteListDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!--优免商家详情 -->
      <el-dialog title="优免商家详情" v-model="couponMerchantDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.couponMerchantAuditFrom">
          <el-form-item label="停车场名称">{{ data.couponMerchantAuditFrom.park_name }} </el-form-item>
          <el-form-item label="商家名称"> {{ data.couponMerchantAuditFrom.merchant_name }}</el-form-item>
          <el-form-item label="优免券名称"> {{ data.couponMerchantAuditFrom.coupon_meta_name }}</el-form-item>
          <el-form-item label="优免券类型"> {{ data.couponMerchantAuditFrom.type_desc }}</el-form-item>
          <el-form-item label="有效期">
            {{ data.couponMerchantAuditFrom.valid_start_time + '—' + data.couponMerchantAuditFrom.valid_end_time }}</el-form-item
          >
          <el-form-item label="优免券数量"> {{ data.couponMerchantAuditFrom.total_count }}</el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="couponMerchantDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <!--纸质发票申请详情 -->
      <el-dialog title="定额发票申请详情" v-model="paperInvoiceDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.invoiceForm">
          <el-form-item label="停车场名称">{{ data.invoiceForm.park_name }} </el-form-item>
          <el-form-item label="开票公司"> {{ data.invoiceForm.invoice_title }}</el-form-item>
          <el-form-item label="单张金额"> {{ data.invoiceForm.unit_money }}</el-form-item>
          <el-form-item label="申请数量"> {{ data.invoiceForm.apply_count }}</el-form-item>
          <el-form-item label="预计领取时间"> {{ data.invoiceForm.plan_draw_time }}</el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="paperInvoiceDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="临停车规则详情" v-model="parkFeeDialogVisible" :close-on-click-modal="false" width="1000px" class="overflow-dialog">
        <el-card shadow="hover">
          <template #header>
            <div style="display: inline-block; line-height: 20px">基础设置</div>
          </template>
          <div>
            <el-form :model="data.feeForm" label-width="170px">
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="规则名称">
                    {{ data.feeForm.name }}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="车辆类型">
                    {{ data.feeForm.car_type_desc }}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="关联子场">
                    {{ data.feeForm.park_region_name }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="同车重新起收限制"> {{ data.feeForm.same_car_time }}&nbsp;时 </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="计费精度">{{ data.feeForm.fee_precision_desc }} </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="费用精度"> {{ data.feeForm.cost_precision_desc }} </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="进位方式">
                    {{ data.feeForm.trunc_mode_desc }}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="跨分段计费方式">
                    {{ data.feeForm.cross_time_fee_type_desc }}
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="跨分段起收计费方式">
                    {{ data.feeForm.cross_time_start_fee_type_desc }}
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="单笔封顶费用">
                    <span v-if="data.feeForm.bill_top_mode == 0">无</span>
                    <span v-else>{{ data.feeForm.bill_top_money }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="首日限额">
                    <span v-if="data.feeForm.first_day_limit_mode == 0">无</span>
                    <span v-else>{{ data.feeForm.first_day_limit_money }}</span>
                  </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="每日限额">
                    <span v-if="data.feeForm.each_day_limit_mode == 0">无</span>
                    <span v-else>{{ data.feeForm.each_day_limit_money }}</span>
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="10">
                <el-col :span="8">
                  <el-form-item label="提前缴费出场时限"> {{ data.feeForm.pre_payment_time_limit }}&nbsp;分 </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="二次免费时长"> {{ data.feeForm.pre_payment_time_limit_two }}&nbsp;分 </el-form-item>
                </el-col>
                <el-col :span="8">
                  <el-form-item label="生效日期">
                    {{ data.feeForm.valid_start_time }}
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </el-card>
        <el-card shadow="hover" style="margin-top: 10px">
          <template #header>
            <div style="display: inline-block; line-height: 20px">计费规则</div>
          </template>
          <div class="feeSetting">
            <div v-if="data.feeForm.fee_rules === undefined || data.feeForm.fee_rules.length > 0">
              <el-card class="box-card" v-for="item in data.feeForm.fee_rules" :key="item.id" style="width: 33%">
                <template #header>
                  <span>{{ item.name }}</span>
                </template>
                <div class="textItem">
                  <div class="tag">时间段</div>
                  <div class="right" style="color: rgb(214, 110, 110)">
                    {{ item.start_time + '-' + item.end_time }}
                  </div>
                </div>
                <div class="textItem">
                  <div class="tag">计费方式</div>
                  <div class="right">{{ item.type_desc }}</div>
                </div>
                <!-- 类型为1 -->
                <div v-if="item.type == 1">
                  <div class="textItem">
                    <div class="tag">收费金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.times_fee.money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">免费时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.times_fee.free_time }}</span> 分钟
                    </div>
                  </div>
                </div>
                <!-- 类型为2 -->
                <div v-if="item.type == 3">
                  <div class="textItem">
                    <div class="tag">起收时长（分）</div>
                    <div class="right" style="color: rgb(147, 162, 251)">{{ item.period_fee.start_fee_time }}</div>
                  </div>
                  <div class="textItem">
                    <div class="tag">起收单位时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">起收单位金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">免费时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.free_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">续费单位时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">续费单位金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">不足一个时段是否计费</div>
                    <div class="right">
                      <span v-if="item.period_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                      <span v-if="item.period_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">时限额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.period_fee.time_limit_money }}</span
                      >元
                    </div>
                  </div>
                </div>
                <!-- 类型为3 -->
                <div v-if="item.type == 2">
                  <div class="textItem">
                    <div class="tag">起收时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(147, 162, 251)">{{ item.duration_fee.start_fee_time }}</span
                      >分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">起收金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.start_fee_money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">免费时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.free_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">续收时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">续收金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">不足一个时段是否计费</div>
                    <div class="right">
                      <span v-if="item.duration_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                      <span v-if="item.duration_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">时限额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.time_limit_money }}</span
                      >元
                    </div>
                  </div>
                </div>
                <!-- 类型4 -->
                <div v-if="item.type == 4">
                  <div class="textItem">
                    <div class="tag">阶段时长（分）</div>
                    <div class="right">{{ item.stair_fee.stair_time }}分钟</div>
                  </div>
                  <div class="textItem" v-for="(money, index) in item.stair_fee.stair_moneys" :key="index">
                    <div class="tag">第{{ index + 1 }}阶段金额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ money }}</span> 元
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">免费时长（分）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.free_time }}</span> 分钟
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">满24小时是否从新计算</div>
                    <div class="right">
                      <span v-if="item.stair_fee.beyond_day_calcute_again_type == 1" style="color: rgb(214, 110, 110)">是</span>
                      <span v-if="item.stair_fee.beyond_day_calcute_again_type == 0" style="color: rgb(214, 110, 110)">否</span>
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">不足一个时段是否计费</div>
                    <div class="right">
                      <span v-if="item.stair_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                      <span v-if="item.stair_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                    </div>
                  </div>
                  <div class="textItem">
                    <div class="tag">时限额（元）</div>
                    <div class="right">
                      <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.time_limit_money }}</span
                      >元
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </div>
        </el-card>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="parkFeeDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 审核驳回-->
      <el-dialog title="审核" v-model="rejectDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form ref="reject" label-width="100px" :rules="rules" :model="data.rejectForm">
          <el-form-item label="审核意见" prop="audit_comment">
            <el-input v-model="data.rejectForm.audit_comment" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="rejectDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveReject(reject)" :loading="rejectLoading">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 批量审核 -->
      <!-- <el-dialog title="批量审核" v-model="batchDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form ref="reject" label-width="100px" :rules="rules" :model="data.rejectForm">
          <el-form-item label="" prop="audit_comment">
            <el-radio-group v-model="data.batchForm.audit_type" class="ml-4">
              <el-radio value="1">通过</el-radio>
              <el-radio value="0">驳回</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="审核意见" prop="audit_comment" v-if="data.batchForm.audit_type === 0">
            <el-input v-model="data.batchForm.audit_comment" placeholder="请输入审核意见" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="batchDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="saveBatch">确 定</el-button>
          </span>
        </template>
      </el-dialog> -->

      <!-- 长租车续费延期申请 -->
      <el-dialog title="长租车续费延期申请" v-model="reRentDialogVisible" :close-on-click-modal="false" width="500px">
        <el-form label-width="155px" :model="data.reRentFrom">
          <el-form-item label="车主姓名"> {{ data.reRentFrom.mbr_member_name }}</el-form-item>
          <el-form-item label="手机号"> {{ data.reRentFrom.mbr_member_mobile }}</el-form-item>
          <el-form-item label="用户身份"> {{ data.reRentFrom.user_identity_desc }}</el-form-item>
          <el-form-item label="车牌号"> {{ data.reRentFrom.plate_nos }}</el-form-item>
          <el-form-item label="停车场名称">{{ data.reRentFrom.park_name }} </el-form-item>
          <el-form-item label="车位编号"> {{ data.reRentFrom.space_code }}</el-form-item>
          <!-- <el-form-item label="有效期"> {{ data.reRentFrom.effective_start_time + '—' + data.reRentFrom.effective_end_time }}</el-form-item> -->
          <div style="background-color: #ecf5ff; padding: 4px 10px">拟续费套餐</div>
          <el-form-item label="规则名称"> {{ data.reRentFrom.prk_rent_rule_name }}</el-form-item>
          <el-form-item label="产品类型"> {{ data.reRentFrom.prk_rent_product_name }}</el-form-item>
          <el-form-item label="长租类型"> {{ data.reRentFrom.prk_rent_rule_type_desc }}</el-form-item>
          <el-form-item label="产品金额"> {{ data.reRentFrom.order_money }}</el-form-item>
          <el-form-item label="产品周期">
            {{ formatRentProductRangeText(rentProductRanges, data.reRentFrom.product_range, data.reRentFrom.prk_rent_product_id) }}</el-form-item
          >
          <el-form-item label="长租时段"> {{ data.reRentFrom.rent_time || '全时段' }}</el-form-item>
          <el-form-item label="有效期">
            <span v-if="data.reRentFrom.valid_start_time != null && data.reRentFrom.valid_end_time != null">{{
              data.reRentFrom.valid_start_time + '~' + data.reRentFrom.valid_end_time
            }}</span>
            <span v-else>--</span>
          </el-form-item>
          <el-form-item label="申请来源"> {{ data.reRentFrom.channel_desc }}</el-form-item>
          <el-form-item label="付款方式"> {{ data.reRentFrom.pay_method_desc }}</el-form-item>
          <el-form-item label="实收金额" v-if="data.reRentFrom.pay_method !== 4"> {{ data.reRentFrom.payed_money }}</el-form-item>
          <el-form-item label="付款备注" v-if="data.reRentFrom.pay_method !== 4">
            <span style="width: 100%">
              {{ data.reRentFrom.payed_memo }}
            </span>
          </el-form-item>
          <el-form-item label="缴费凭证" v-if="data.reRentFrom.pay_method !== 4">
            <div v-if="!!data.reRentFrom.payed_voucher_url" style="display: flex; flex-direction: column; align-items: flex-start">
              <el-link
                v-for="url in JSON.parse(data.reRentFrom.payed_voucher_url)"
                :key="url.voucher_url"
                @click="exportAuditData(url.audit_data_url)"
                type="primary"
                >{{ url.audit_data_name }}</el-link
              >
            </div>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button v-if="flag" type="primary" @click="handlePass">通 过</el-button>
            <el-button v-if="flag" type="danger" @click="handleReject">驳 回</el-button>
            <el-button @click="reRentDialogVisible = false">取 消</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="BizAuditTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import bizAuditService from '@/service/bizAudit/BizAuditService';
import { useRoute } from 'vue-router';
import { rentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';

const route = useRoute();
const reject = ref();
const flag = ref(true);
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const passLoading = ref(false);
const rejectLoading = ref(false);
const spaceRentApplyDialogVisible = ref(false);
const carVisitorDialogVisible = ref(false);
const rentRuleDialogVisible = ref(false);
const whiteListDialogVisible = ref(false);
const parkFeeDialogVisible = ref(false);
const reRentDialogVisible = ref(false);
const rejectDialogVisible = ref(false);

const couponMerchantDialogVisible = ref(false);
const paperInvoiceDialogVisible = ref(false);
const audit_id = ref('');

const rules = reactive({
  audit_type: {
    required: true,
    message: '请输入审核结果',
    trigger: 'blur'
  },
  audit_comment: [
    {
      required: true,
      message: '请输入审核意见',
      trigger: 'blur'
    }
  ]
});
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    audit_states: []
  },
  rentApplyFrom: {},
  reRentFrom: {},
  whiteListFrom: [],
  rentRuleFrom: {},
  couponMerchantAuditFrom: {},
  invoiceForm: {},
  feeForm: {},
  rejectForm: {
    id: '',
    audit_comment: ''
  },
  passForm: {
    id: '',
    audit_comment: ''
  },
  batchForm: {
    audit_type: 0,
    audit_comment: ''
  }
});

onActivated(() => {
  if ({} !== route.query && undefined !== route.query.audit_states) {
    if (Array.isArray(route.query.audit_states)) {
      data.queryParams.audit_states = route.query.audit_states;
    } else {
      data.queryParams.audit_states.push(route.query.audit_states);
    }
  }
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  selectedRows.value = [];
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  bizAuditService.pagingBizAudits(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleReject = () => {
  data.rejectForm = {
    id: audit_id.value,
    audit_comment: ''
  };
  rejectDialogVisible.value = true;
};
const handlePass = () => {
  passLoading.value = true;
  data.passForm = {
    id: audit_id.value
  };
  bizAuditService
    .passAudit(data.passForm)
    .then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
        parkFeeDialogVisible.value = false;
        rentRuleDialogVisible.value = false;
        whiteListDialogVisible.value = false;
        spaceRentApplyDialogVisible.value = false;
        carVisitorDialogVisible.value = false;
        couponMerchantDialogVisible.value = false;
        paperInvoiceDialogVisible.value = false;
        reRentDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
      passLoading.value = false;
    })
    .catch(() => {
      passLoading.value = false;
      getList(data.queryParams);
    });
};

const handleAudit = (id) => {
  audit_id.value = id;
  flag.value = true;
  bizAuditService.getBizAuditFormById(id).then((response) => {
    if (response.success === true) {
      if (response.data.type === 1) {
        //临停规则
        data.feeForm = response.data.park_fee_audit;
        parkFeeDialogVisible.value = true;
      }
      if (response.data.type === 2) {
        //长租规则申请
        data.rentRuleFrom = response.data.rent_rule_apply_audit;
        rentRuleDialogVisible.value = true;
      }
      if (response.data.type === 3) {
        //长租申请
        data.rentApplyFrom = response.data.rent_space_apply_audit;
        spaceRentApplyDialogVisible.value = true;
      }
      if (response.data.type === 4) {
        data.couponMerchantAuditFrom = response.data.coupon_merchant_audit_vo;
        couponMerchantDialogVisible.value = true;
      }
      if (response.data.type === 5) {
        //免费车申请
        data.whiteListFrom = response.data.white_list_audit;
        whiteListDialogVisible.value = true;
      }
      if (response.data.type === 6) {
        //纸质发票申请
        data.invoiceForm = response.data.quota_invoice_apply_audit_vo;
        paperInvoiceDialogVisible.value = true;
      }
      if (response.data.type === 7) {
        //续费申请
        data.reRentFrom = response.data.rent_renew_space_apply_audit_vo;
        reRentDialogVisible.value = true;
      }
      if (response.data.type === 8) {
        //访客车申请
        data.visitorForm = response.data.visitor_apply_audit_vo;
        carVisitorDialogVisible.value = true;
      }
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const handleDetail = (id, state) => {
  audit_id.value = id;
  // if (state == 2 || state == 3) {
  flag.value = false;
  // } else {
  //   flag.value = true;
  // }
  bizAuditService.getBizAuditFormById(id).then((response) => {
    if (response.success === true) {
      if (response.data.type === 1) {
        //临停规则
        data.feeForm = response.data.park_fee_audit;
        parkFeeDialogVisible.value = true;
      }
      if (response.data.type === 2) {
        //长租规则申请
        data.rentRuleFrom = response.data.rent_rule_apply_audit;
        rentRuleDialogVisible.value = true;
      }
      if (response.data.type === 3) {
        //长租申请
        data.rentApplyFrom = response.data.rent_space_apply_audit;
        spaceRentApplyDialogVisible.value = true;
      }
      if (response.data.type === 4) {
        data.couponMerchantAuditFrom = response.data.coupon_merchant_audit_vo;
        couponMerchantDialogVisible.value = true;
      }
      if (response.data.type === 5) {
        //免费车申请
        data.whiteListFrom = response.data.white_list_audit;
        whiteListDialogVisible.value = true;
      }
      if (response.data.type === 6) {
        //定额发票申请
        data.invoiceForm = response.data.quota_invoice_apply_audit_vo;
        paperInvoiceDialogVisible.value = true;
      }
      if (response.data.type === 7) {
        //续费申请
        data.reRentFrom = response.data.rent_renew_space_apply_audit_vo;
        reRentDialogVisible.value = true;
      }
      if (response.data.type === 8) {
        //访客车申请
        data.visitorForm = response.data.visitor_apply_audit_vo;
        carVisitorDialogVisible.value = true;
      }
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
// 产品周期内容格式化
const formatRentProductRangeText = (rentProductRanges, productRange, productType) => {
  console.log('productType', productType);
  if (productType == 8) {
    return productRange + '天';
  } else if (productType == 9) {
    return productRange + '周';
  } else {
    return getRentProductRangeText(rentProductRanges, productRange) || '--';
  }
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
//审核驳回
const saveReject = (reject) => {
  reject.validate().then(() => {
    rejectLoading.value = true;
    bizAuditService
      .rejectAudit(data.rejectForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          rejectDialogVisible.value = false;
          parkFeeDialogVisible.value = false;
          spaceRentApplyDialogVisible.value = false;
          carVisitorDialogVisible.value = false;
          rentRuleDialogVisible.value = false;
          whiteListDialogVisible.value = false;
          couponMerchantDialogVisible.value = false;
          paperInvoiceDialogVisible.value = false;
          reRentDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
        setTimeout(() => {
          rejectLoading.value = false;
        }, 600);
      })
      .catch(() => {
        rejectLoading.value = false;
        getList(data.queryParams);
      });
  });
};

const exportAuditData = (val) => {
  window.open(val, '_blank');
};

// const batchDialogVisible = ref(false);
const selectedRows = ref([]);
const handleSelectionChange = (val) => {
  selectedRows.value = val;
};
/**
 * @description 批量审批
 */
const handleBatch = () => {
  let ids = [];
  selectedRows.value.forEach((item) => {
    if (item.audit_state === 0) {
      ids.push(item.id);
    }
  });
  if (!ids.length) {
    ElMessage.warning('请选择待审批的数据!');
    return;
  }
  ElMessageBox.confirm('确定批量审批通过当前选中的数据?', '提示', {
    callback: (action) => {
      if (action === 'confirm') {
        saveBatch(ids);
      }
    }
  });
};

const saveBatch = async (ids) => {
  console.log('save');
  const res = await bizAuditService.passAudit(ids, 'batch');
  console.log(res);
  if (res.success) {
    ElMessage.success('批量审批通过成功!');
    getList(data.queryParams);
  } else {
    ElMessage.error(res.message);
  }
};

defineExpose({
  getList,
  handleBatch
});
</script>
<style lang="scss">
.overflow-dialog {
  .el-dialog__body {
    height: 600px;
    overflow-y: scroll;
  }
}
</style>
<style lang="scss" scoped>
.feeSetting {
  width: 100%;
  height: 100%;
}

.box-card {
  position: relative;
  color: rgb(90, 90, 90);
  margin-right: 10px;
  margin-bottom: 10px;
  float: left;
}

.textItem {
  margin-top: 10px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .tag {
    color: var(--el-text-color-secondary);
  }
}
:deep(.el-form-item__label) {
  color: var(--el-text-color-secondary);
}
</style>
