<template>
  <div ref="chartRef" class="chart-warp"></div>
</template>

<script setup>
import { onMounted, onBeforeUnmount, ref, markRaw } from 'vue';
import * as echarts from 'echarts';

// 图表容器ref
const chartRef = ref(null);
// 图表实例
let chartInstance = null;
onMounted(() => {
  chartInstance = markRaw(echarts.init(chartRef.value));
  window.addEventListener('resize', resizeHandler);
});
/**
 * @description: 初始化图表
 */
const setData = (data) => {
  chartInstance.clear();
  const options = {
    title: [
      {
        text: '平均周转率',
        bottom: '10%',
        left: 'center',
        textStyle: {
          fontWeight: 'normal',
          fontSize: 16,
          color: '#fff'
        }
      }
    ],
    grid: {
      show: false,
      left: '0',
      right: '0',
      bottom: '0',
      top: '0'
    },
    series: [
      {
        type: 'gauge',
        startAngle: 199,
        endAngle: -20,
        min: 0,
        max: 100,
        center: ['50%', '50%'],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
            {
              offset: 0,
              color: '#8CDFFE'
            },
            {
              offset: 1,
              color: '#0A80FD'
            }
          ]),
          shadowColor: '#fff',
          shadowBlur: 2,
          shadowOffsetX: 0,
          shadowOffsetY: 0
        },
        progress: {
          show: true,
          roundCap: true,
          width: 15
        },
        pointer: {
          show: false
        },
        axisLine: {
          roundCap: true,
          lineStyle: {
            width: 14,
            color: [[1, '#e7eeff']]
          }
        },
        axisTick: {
          show: false
        },
        splitLine: {
          show: false
        },
        axisLabel: {
          show: false
        },
        detail: {
          offsetCenter: [0, '-10%'],
          valueAnimation: true,
          formatter: function (value) {
            return '{value|' + value + '%}';
          },
          rich: {
            value: {
              fontSize: 16,
              fontWeight: 'bolder',
              color: '#fff'
            }
          }
        },
        data: [
          {
            value: data
          }
        ]
      }
    ]
  };
  chartInstance.setOption(options, true);
};

const resizeHandler = () => {
  if (!chartInstance) return;
  chartInstance.resize();
};

const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
  window.removeEventListener('resize', resizeHandler);
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData
});
</script>

<style lang="scss" scoped>
.chart-warp {
  width: 100%;
  height: 100%;
}
</style>
