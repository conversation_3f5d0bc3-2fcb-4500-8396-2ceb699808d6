import * as selfServicePaymentApi from '@/api/charge/SelfServicePaymentApi';

export default {
  /**
   * 分页查询入场记录
   */
  pagingInvoiceRecords(data) {
    return selfServicePaymentApi.pagingInvoiceRecords(data);
  },
  /**
   * 分页查询入场记录
   */
  saveWithdrawalRecords(data) {
    return selfServicePaymentApi.saveWithdrawalRecords(data);
  },
  /**
   * 分页查询入场记录
   */
  pagingWithdrawalRecords(data) {
    return selfServicePaymentApi.pagingWithdrawalRecords(data);
  },
  /**
   * 分页查询入场记录
   */
  pagingCashChangeRecords(data) {
    return selfServicePaymentApi.pagingCashChangeRecords(data);
  },
  /**
   * 分页查询入场记录
   */
  listPayMachines() {
    return selfServicePaymentApi.listPayMachines();
  }
};
