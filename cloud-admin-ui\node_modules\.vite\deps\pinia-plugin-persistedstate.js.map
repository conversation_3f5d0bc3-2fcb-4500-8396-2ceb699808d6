{"version": 3, "sources": ["../../pinia-plugin-persistedstate/dist/chunk-256H5QT7.mjs", "../../pinia-plugin-persistedstate/dist/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\n\n// src/core/normalize.ts\nfunction isObject(v) {\n  return typeof v === \"object\" && v !== null;\n}\nfunction identity(v) {\n  return v;\n}\nfunction normalizeOptions(options, factoryOptions) {\n  options = isObject(options) ? options : /* @__PURE__ */ Object.create(null);\n  return new Proxy(options, {\n    get(target, key, receiver) {\n      var _a;\n      if (key === \"key\") {\n        return ((_a = factoryOptions.key) != null ? _a : identity)(\n          Reflect.get(target, key, receiver)\n        );\n      }\n      return Reflect.get(target, key, receiver) || Reflect.get(factoryOptions, key, receiver);\n    }\n  });\n}\n\n// src/core/pick.ts\nfunction isObject2(value) {\n  return value !== null && typeof value === \"object\";\n}\nfunction merge(destination, source) {\n  const mergingArrays = Array.isArray(destination) && Array.isArray(source);\n  const mergingObjects = isObject2(destination) && isObject2(source);\n  if (!mergingArrays && !mergingObjects) {\n    throw new Error(\"Can only merge object with object or array with array\");\n  }\n  const result = mergingArrays ? [] : {};\n  const keys = [...Object.keys(destination), ...Object.keys(source)];\n  keys.forEach((key) => {\n    if (Array.isArray(destination[key]) && Array.isArray(source[key])) {\n      result[key] = [\n        ...Object.values(\n          merge(destination[key], source[key])\n        )\n      ];\n    } else if (source[key] !== null && typeof source[key] === \"object\" && typeof destination[key] === \"object\") {\n      result[key] = merge(\n        destination[key],\n        source[key]\n      );\n    } else if (destination[key] !== void 0 && source[key] === void 0) {\n      result[key] = destination[key];\n    } else if (destination[key] === void 0 && source[key] !== void 0) {\n      result[key] = source[key];\n    }\n  });\n  return result;\n}\nfunction get(state, path) {\n  return path.reduce((obj, p) => {\n    if (p === \"[]\" && Array.isArray(obj))\n      return obj;\n    return obj == null ? void 0 : obj[p];\n  }, state);\n}\nfunction set(state, path, val) {\n  const modifiedState = path.slice(0, -1).reduce((obj, p) => {\n    if (!/^(__proto__)$/.test(p))\n      return obj[p] = obj[p] || {};\n    else\n      return {};\n  }, state);\n  if (Array.isArray(modifiedState[path[path.length - 1]]) && Array.isArray(val)) {\n    const merged = modifiedState[path[path.length - 1]].map(\n      (item, index) => {\n        if (Array.isArray(item) && typeof item !== \"object\") {\n          return [...item, ...val[index]];\n        }\n        if (typeof item === \"object\" && item !== null && Object.keys(item).some((key) => Array.isArray(item[key]))) {\n          return merge(item, val[index]);\n        }\n        return __spreadValues(__spreadValues({}, item), val[index]);\n      }\n    );\n    modifiedState[path[path.length - 1]] = merged;\n  } else if (path[path.length - 1] === void 0 && Array.isArray(modifiedState) && Array.isArray(val)) {\n    modifiedState.push(...val);\n  } else {\n    modifiedState[path[path.length - 1]] = val;\n  }\n  return state;\n}\nfunction pick(baseState, paths) {\n  return paths.reduce(\n    (substate, path) => {\n      const pathArray = path.split(\".\");\n      if (!pathArray.includes(\"[]\")) {\n        return set(substate, pathArray, get(baseState, pathArray));\n      }\n      const arrayIndex = pathArray.indexOf(\"[]\");\n      const pathArrayBeforeArray = pathArray.slice(0, arrayIndex);\n      const pathArrayUntilArray = pathArray.slice(0, arrayIndex + 1);\n      const pathArrayAfterArray = pathArray.slice(arrayIndex + 1);\n      const referencedArray = get(\n        baseState,\n        pathArrayUntilArray\n      );\n      const referencedArraySubstate = [];\n      for (const item of referencedArray) {\n        if (pathArrayAfterArray.length !== 0 && (Array.isArray(item) || typeof item === \"object\")) {\n          referencedArraySubstate.push(\n            pick(item, [pathArrayAfterArray.join(\".\")])\n          );\n        } else {\n          referencedArraySubstate.push(item);\n        }\n      }\n      return set(substate, pathArrayBeforeArray, referencedArraySubstate);\n    },\n    Array.isArray(baseState) ? [] : {}\n  );\n}\n\n// src/core/plugin.ts\nfunction hydrateStore(store, storage, serializer, key, debug) {\n  try {\n    const fromStorage = storage == null ? void 0 : storage.getItem(key);\n    if (fromStorage)\n      store.$patch(serializer == null ? void 0 : serializer.deserialize(fromStorage));\n  } catch (error) {\n    if (debug)\n      console.error(error);\n  }\n}\nfunction createPersistedState(factoryOptions = {}) {\n  return (context) => {\n    const {\n      options: { persist },\n      store\n    } = context;\n    if (!persist)\n      return;\n    const persistences = (Array.isArray(persist) ? persist.map((p) => normalizeOptions(p, factoryOptions)) : [normalizeOptions(persist, factoryOptions)]).map(\n      ({\n        storage = localStorage,\n        beforeRestore = null,\n        afterRestore = null,\n        serializer = {\n          serialize: JSON.stringify,\n          deserialize: JSON.parse\n        },\n        key = store.$id,\n        paths = null,\n        debug = false\n      }) => ({\n        storage,\n        beforeRestore,\n        afterRestore,\n        serializer,\n        key,\n        paths,\n        debug\n      })\n    );\n    persistences.forEach((persistence) => {\n      const {\n        storage,\n        serializer,\n        key,\n        paths,\n        beforeRestore,\n        afterRestore,\n        debug\n      } = persistence;\n      beforeRestore == null ? void 0 : beforeRestore(context);\n      hydrateStore(store, storage, serializer, key, debug);\n      afterRestore == null ? void 0 : afterRestore(context);\n      store.$subscribe(\n        (_mutation, state) => {\n          try {\n            const toStore = Array.isArray(paths) ? pick(state, paths) : state;\n            storage.setItem(key, serializer.serialize(toStore));\n          } catch (error) {\n            if (debug)\n              console.error(error);\n          }\n        },\n        {\n          detached: true\n        }\n      );\n    });\n    store.$hydrate = ({ runHooks = true } = {}) => {\n      persistences.forEach((persistence) => {\n        const { beforeRestore, afterRestore, storage, serializer, key, debug } = persistence;\n        if (runHooks)\n          beforeRestore == null ? void 0 : beforeRestore(context);\n        hydrateStore(store, storage, serializer, key, debug);\n        if (runHooks)\n          afterRestore == null ? void 0 : afterRestore(context);\n      });\n    };\n  };\n}\n\nexport {\n  __spreadValues,\n  createPersistedState\n};\n", "import {\n  createPersistedState\n} from \"./chunk-256H5QT7.mjs\";\n\n// src/index.ts\nvar src_default = createPersistedState();\nexport {\n  createPersistedState,\n  src_default as default\n};\n"], "mappings": ";;;AAAA,IAAI,YAAY,OAAO;AACvB,IAAI,sBAAsB,OAAO;AACjC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,eAAe,OAAO,UAAU;AACpC,IAAI,kBAAkB,CAAC,KAAK,KAAK,UAAU,OAAO,MAAM,UAAU,KAAK,KAAK,EAAE,YAAY,MAAM,cAAc,MAAM,UAAU,MAAM,MAAM,CAAC,IAAI,IAAI,OAAO;AAC1J,IAAI,iBAAiB,CAAC,GAAG,MAAM;AAC7B,WAAS,QAAQ,MAAM,IAAI,CAAC;AAC1B,QAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,sBAAgB,GAAG,MAAM,EAAE,KAAK;AACpC,MAAI;AACF,aAAS,QAAQ,oBAAoB,CAAC,GAAG;AACvC,UAAI,aAAa,KAAK,GAAG,IAAI;AAC3B,wBAAgB,GAAG,MAAM,EAAE,KAAK;AAAA,IACpC;AACF,SAAO;AACT;AAGA,SAAS,SAAS,GAAG;AACnB,SAAO,OAAO,MAAM,YAAY,MAAM;AACxC;AACA,SAAS,SAAS,GAAG;AACnB,SAAO;AACT;AACA,SAAS,iBAAiB,SAAS,gBAAgB;AACjD,YAAU,SAAS,OAAO,IAAI,UAA0B,uBAAO,OAAO,IAAI;AAC1E,SAAO,IAAI,MAAM,SAAS;AAAA,IACxB,IAAI,QAAQ,KAAK,UAAU;AACzB,UAAI;AACJ,UAAI,QAAQ,OAAO;AACjB,iBAAS,KAAK,eAAe,QAAQ,OAAO,KAAK;AAAA,UAC/C,QAAQ,IAAI,QAAQ,KAAK,QAAQ;AAAA,QACnC;AAAA,MACF;AACA,aAAO,QAAQ,IAAI,QAAQ,KAAK,QAAQ,KAAK,QAAQ,IAAI,gBAAgB,KAAK,QAAQ;AAAA,IACxF;AAAA,EACF,CAAC;AACH;AAGA,SAAS,UAAU,OAAO;AACxB,SAAO,UAAU,QAAQ,OAAO,UAAU;AAC5C;AACA,SAAS,MAAM,aAAa,QAAQ;AAClC,QAAM,gBAAgB,MAAM,QAAQ,WAAW,KAAK,MAAM,QAAQ,MAAM;AACxE,QAAM,iBAAiB,UAAU,WAAW,KAAK,UAAU,MAAM;AACjE,MAAI,CAAC,iBAAiB,CAAC,gBAAgB;AACrC,UAAM,IAAI,MAAM,uDAAuD;AAAA,EACzE;AACA,QAAM,SAAS,gBAAgB,CAAC,IAAI,CAAC;AACrC,QAAM,OAAO,CAAC,GAAG,OAAO,KAAK,WAAW,GAAG,GAAG,OAAO,KAAK,MAAM,CAAC;AACjE,OAAK,QAAQ,CAAC,QAAQ;AACpB,QAAI,MAAM,QAAQ,YAAY,IAAI,KAAK,MAAM,QAAQ,OAAO,IAAI,GAAG;AACjE,aAAO,OAAO;AAAA,QACZ,GAAG,OAAO;AAAA,UACR,MAAM,YAAY,MAAM,OAAO,IAAI;AAAA,QACrC;AAAA,MACF;AAAA,IACF,WAAW,OAAO,SAAS,QAAQ,OAAO,OAAO,SAAS,YAAY,OAAO,YAAY,SAAS,UAAU;AAC1G,aAAO,OAAO;AAAA,QACZ,YAAY;AAAA,QACZ,OAAO;AAAA,MACT;AAAA,IACF,WAAW,YAAY,SAAS,UAAU,OAAO,SAAS,QAAQ;AAChE,aAAO,OAAO,YAAY;AAAA,IAC5B,WAAW,YAAY,SAAS,UAAU,OAAO,SAAS,QAAQ;AAChE,aAAO,OAAO,OAAO;AAAA,IACvB;AAAA,EACF,CAAC;AACD,SAAO;AACT;AACA,SAAS,IAAI,OAAO,MAAM;AACxB,SAAO,KAAK,OAAO,CAAC,KAAK,MAAM;AAC7B,QAAI,MAAM,QAAQ,MAAM,QAAQ,GAAG;AACjC,aAAO;AACT,WAAO,OAAO,OAAO,SAAS,IAAI;AAAA,EACpC,GAAG,KAAK;AACV;AACA,SAAS,IAAI,OAAO,MAAM,KAAK;AAC7B,QAAM,gBAAgB,KAAK,MAAM,GAAG,EAAE,EAAE,OAAO,CAAC,KAAK,MAAM;AACzD,QAAI,CAAC,gBAAgB,KAAK,CAAC;AACzB,aAAO,IAAI,KAAK,IAAI,MAAM,CAAC;AAAA;AAE3B,aAAO,CAAC;AAAA,EACZ,GAAG,KAAK;AACR,MAAI,MAAM,QAAQ,cAAc,KAAK,KAAK,SAAS,GAAG,KAAK,MAAM,QAAQ,GAAG,GAAG;AAC7E,UAAM,SAAS,cAAc,KAAK,KAAK,SAAS,IAAI;AAAA,MAClD,CAAC,MAAM,UAAU;AACf,YAAI,MAAM,QAAQ,IAAI,KAAK,OAAO,SAAS,UAAU;AACnD,iBAAO,CAAC,GAAG,MAAM,GAAG,IAAI,MAAM;AAAA,QAChC;AACA,YAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,OAAO,KAAK,IAAI,EAAE,KAAK,CAAC,QAAQ,MAAM,QAAQ,KAAK,IAAI,CAAC,GAAG;AAC1G,iBAAO,MAAM,MAAM,IAAI,MAAM;AAAA,QAC/B;AACA,eAAO,eAAe,eAAe,CAAC,GAAG,IAAI,GAAG,IAAI,MAAM;AAAA,MAC5D;AAAA,IACF;AACA,kBAAc,KAAK,KAAK,SAAS,MAAM;AAAA,EACzC,WAAW,KAAK,KAAK,SAAS,OAAO,UAAU,MAAM,QAAQ,aAAa,KAAK,MAAM,QAAQ,GAAG,GAAG;AACjG,kBAAc,KAAK,GAAG,GAAG;AAAA,EAC3B,OAAO;AACL,kBAAc,KAAK,KAAK,SAAS,MAAM;AAAA,EACzC;AACA,SAAO;AACT;AACA,SAAS,KAAK,WAAW,OAAO;AAC9B,SAAO,MAAM;AAAA,IACX,CAAC,UAAU,SAAS;AAClB,YAAM,YAAY,KAAK,MAAM,GAAG;AAChC,UAAI,CAAC,UAAU,SAAS,IAAI,GAAG;AAC7B,eAAO,IAAI,UAAU,WAAW,IAAI,WAAW,SAAS,CAAC;AAAA,MAC3D;AACA,YAAM,aAAa,UAAU,QAAQ,IAAI;AACzC,YAAM,uBAAuB,UAAU,MAAM,GAAG,UAAU;AAC1D,YAAM,sBAAsB,UAAU,MAAM,GAAG,aAAa,CAAC;AAC7D,YAAM,sBAAsB,UAAU,MAAM,aAAa,CAAC;AAC1D,YAAM,kBAAkB;AAAA,QACtB;AAAA,QACA;AAAA,MACF;AACA,YAAM,0BAA0B,CAAC;AACjC,iBAAW,QAAQ,iBAAiB;AAClC,YAAI,oBAAoB,WAAW,MAAM,MAAM,QAAQ,IAAI,KAAK,OAAO,SAAS,WAAW;AACzF,kCAAwB;AAAA,YACtB,KAAK,MAAM,CAAC,oBAAoB,KAAK,GAAG,CAAC,CAAC;AAAA,UAC5C;AAAA,QACF,OAAO;AACL,kCAAwB,KAAK,IAAI;AAAA,QACnC;AAAA,MACF;AACA,aAAO,IAAI,UAAU,sBAAsB,uBAAuB;AAAA,IACpE;AAAA,IACA,MAAM,QAAQ,SAAS,IAAI,CAAC,IAAI,CAAC;AAAA,EACnC;AACF;AAGA,SAAS,aAAa,OAAO,SAAS,YAAY,KAAK,OAAO;AAC5D,MAAI;AACF,UAAM,cAAc,WAAW,OAAO,SAAS,QAAQ,QAAQ,GAAG;AAClE,QAAI;AACF,YAAM,OAAO,cAAc,OAAO,SAAS,WAAW,YAAY,WAAW,CAAC;AAAA,EAClF,SAAS,OAAP;AACA,QAAI;AACF,cAAQ,MAAM,KAAK;AAAA,EACvB;AACF;AACA,SAAS,qBAAqB,iBAAiB,CAAC,GAAG;AACjD,SAAO,CAAC,YAAY;AAClB,UAAM;AAAA,MACJ,SAAS,EAAE,QAAQ;AAAA,MACnB;AAAA,IACF,IAAI;AACJ,QAAI,CAAC;AACH;AACF,UAAM,gBAAgB,MAAM,QAAQ,OAAO,IAAI,QAAQ,IAAI,CAAC,MAAM,iBAAiB,GAAG,cAAc,CAAC,IAAI,CAAC,iBAAiB,SAAS,cAAc,CAAC,GAAG;AAAA,MACpJ,CAAC;AAAA,QACC,UAAU;AAAA,QACV,gBAAgB;AAAA,QAChB,eAAe;AAAA,QACf,aAAa;AAAA,UACX,WAAW,KAAK;AAAA,UAChB,aAAa,KAAK;AAAA,QACpB;AAAA,QACA,MAAM,MAAM;AAAA,QACZ,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV,OAAO;AAAA,QACL;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,iBAAa,QAAQ,CAAC,gBAAgB;AACpC,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI;AACJ,uBAAiB,OAAO,SAAS,cAAc,OAAO;AACtD,mBAAa,OAAO,SAAS,YAAY,KAAK,KAAK;AACnD,sBAAgB,OAAO,SAAS,aAAa,OAAO;AACpD,YAAM;AAAA,QACJ,CAAC,WAAW,UAAU;AACpB,cAAI;AACF,kBAAM,UAAU,MAAM,QAAQ,KAAK,IAAI,KAAK,OAAO,KAAK,IAAI;AAC5D,oBAAQ,QAAQ,KAAK,WAAW,UAAU,OAAO,CAAC;AAAA,UACpD,SAAS,OAAP;AACA,gBAAI;AACF,sBAAQ,MAAM,KAAK;AAAA,UACvB;AAAA,QACF;AAAA,QACA;AAAA,UACE,UAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF,CAAC;AACD,UAAM,WAAW,CAAC,EAAE,WAAW,KAAK,IAAI,CAAC,MAAM;AAC7C,mBAAa,QAAQ,CAAC,gBAAgB;AACpC,cAAM,EAAE,eAAe,cAAc,SAAS,YAAY,KAAK,MAAM,IAAI;AACzE,YAAI;AACF,2BAAiB,OAAO,SAAS,cAAc,OAAO;AACxD,qBAAa,OAAO,SAAS,YAAY,KAAK,KAAK;AACnD,YAAI;AACF,0BAAgB,OAAO,SAAS,aAAa,OAAO;AAAA,MACxD,CAAC;AAAA,IACH;AAAA,EACF;AACF;;;ACnNA,IAAI,cAAc,qBAAqB;", "names": []}