<template>
  <div class="container my-table-container">
    <park-turnover-search @form-search="searchParkTurnoverList" @reset="resetParamsAndData" :type="type" />
    <park-turnover-table ref="table" :type="type" />
  </div>
</template>

<script setup name="ParkTurnover">
import ParkTurnoverSearch from './parkTurnover/ParkTurnoverSearch.vue';
import ParkTurnoverTable from './parkTurnover/ParkTurnoverTable.vue';
import { ref } from 'vue';

defineProps({
  type: String
});

const table = ref(null);

const pageParkTurnover = (queryParams) => {
  table.value.getList(queryParams);
};

const searchParkTurnoverList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageParkTurnover
});
</script>
s