<template>
  <div class="container">
    <natural-week-turnover-search @form-search="searchNaturalWeekTurnoverList" />
    <natural-week-turnover-table ref="table" />
  </div>
</template>

<script setup name="NaturalWeekTurnover">
import NaturalWeekTurnoverSearch from './naturalWeekTurnover/NaturalWeekTurnoverSearch.vue';
import NaturalWeekTurnoverTable from './naturalWeekTurnover/NaturalWeekTurnoverTable.vue';
import { ref } from 'vue';

const table = ref(null);

const pageNaturalWeek = (queryParams) => {
  table.value.getList(queryParams);
};

const searchNaturalWeekTurnoverList = (queryParams) => {
  table.value.getList(queryParams);
};

defineExpose({
  pageNaturalWeek
});
</script>
