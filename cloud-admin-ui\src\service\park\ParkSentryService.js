import * as parkSentry from '@/api/park/ParkSentryApi';

/**
 * 岗亭
 */
export default {
  /**
   * 分页查询
   */
  pagingParkSentries(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSentry.pagingParkSentries(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增岗亭
   */
  createParkSentry(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSentry.createParkSentry(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改岗亭
   */
  updateParkSentry(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSentry.updateParkSentry(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除岗亭
   */
  deleteParkSentry(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSentry.deleteParkSentry(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通道授权
   */
  accreditGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSentry.accreditGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
