<template>
  <div class="container">
    <merchant-search @form-search="searchMerchants" @reset="resetParamsAndData" />
    <merchant-table ref="table" />  
  </div>
</template>

<script setup name="Merchant">
import MerchantSearch from './merchant/MerchantSearch.vue';
import MerchantTable from './merchant/MerchantTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchMerchants = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
