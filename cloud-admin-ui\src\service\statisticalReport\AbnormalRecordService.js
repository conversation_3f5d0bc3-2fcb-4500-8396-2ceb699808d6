import * as abnormalRecord<PERSON>pi from '@/api/statisticalReport/AbnormalRecordApi';

/**
 * 异常操作记录
 */
export default {
  /**
   * 分页查询异常操作记录
   */
  pagingAbnormalRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        abnormalRecordApi.pagingAbnormalRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        abnormalRecordApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
