import * as parkFee from '@/api/park/ParkFeeApi';

/**
 * 临停规则
 */
export default {
  /**
   * 分页查询
   */
  pagingParkFees(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.pagingParkFees(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询单条临停规则
   */
  getParkFeeById(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.getParkFeeById(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增临停规则
   */
  createParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.createParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改临停规则
   */
  updateParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.updateParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除临停规则
   */
  deleteParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.deleteParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 提交审核临停规则
   */
  submitAuditParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.submitAuditParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 撤销临停规则
   */
  cancelParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.cancelParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 预览临停规则
   */
  previewCalcModel(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFee.previewCalcModel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
