<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div></el-space
      >
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <!-- <el-table-column prop="park_id" label="大区" align="center" />
          <el-table-column prop="park_id" label="城市分公司" align="center" />
          <el-table-column prop="park_id" label="所在省份" align="center" />
          <el-table-column prop="park_id" label="所在城市" align="center" />
          <el-table-column prop="park_id" label="所在区域" align="center" /> -->
          <el-table-column label="组织架构" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="省市区" align="center" min-width="80">
            <template #default="scope">
              <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
                >{{ scope.row.province_name }}/</span
              >
              <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
                >{{ scope.row.city_name }}/</span
              >
              <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
                scope.row.district_name
              }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位总数" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.total_spaces }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="长租收入" align="center">
          <el-table-column label="长租线上续费金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_online_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租线下续费金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_offline_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="临停收入" align="center">
          <el-table-column label="付费临停金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停应收金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_receivable_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="现金支付金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.cash_payed_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="电子支付金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.electronic_payed_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优惠金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.discounts_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="优免金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.coupon_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="特殊放行金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.special_release_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="应收未收金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.receivable_uncollected_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="电子支付收入" align="center">
          <el-table-column label="微信万达小程序主动支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.wx_wd_mini_program_initiative_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信万达小程序被动（无感）支付金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_wd_mini_program_passivity_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序主动支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ali_wd_mini_program_initiative_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序被动（无感）支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ali_wd_mini_program_passivity_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="万达通支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.wd_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信（无感）支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.etcp_wx_passivity_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝（无感）支付金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_ali_passivity_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="ETC支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.etcp_etcpay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.etcp_wx_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.etcp_ali_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="复合支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.etcp_compound_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="其他电子支付金额" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etcp_other_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序提前支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_mini_program_advanced_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序无感支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_mini_program_noninductive_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信扫码支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.hd_wx_scan_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-支付宝扫支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.hd_ali_scan_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-ETC支付金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.hd_etc_scan_pay_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="电子收入占比" align="center">
          <el-table-column label="微信万达小程序主动支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.wx_wd_mini_program_initiative_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="微信万达小程序被动（无感）支付金额占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.wx_wd_mini_program_passivity_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序主动支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.ali_wd_mini_program_initiative_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="支付宝万达小程序被动（无感）支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.ali_wd_mini_program_passivity_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="万达通支付金额占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.wd_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-ETC支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_etcpay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-支付宝无感支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_ali_program_passivity_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-复合支付金额占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.etcp_compound_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-其他支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_other_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序提前支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_mini_program_advanced_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信小程序无感支付金额占比" align="center" min-width="90">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_mini_program_noninductive_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-微信扫码支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_wx_scan_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-支付宝扫支付金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_ali_scan_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
          <el-table-column label="惠达-ETC金额占比" align="center" min-width="110">
            <template #default="scope">
              <span>{{ multiply(scope.row.hd_etc_scan_pay_money_proportion, 100).toFixed(2) }}%</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ElectronicPaymentDataTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import electronicPaymentDataService from '@/service/statisticalReport/ElectronicPaymentDataService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { multiply } from '@/utils/computeData';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(6);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  electronicPaymentDataService.pagingElectronicPaymentData(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      loading.value = false;
      total.value = Number(response.data.total);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
