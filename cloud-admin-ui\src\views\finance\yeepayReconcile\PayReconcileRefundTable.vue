<template>
  <el-card class="refund-table" shadow="never">
    <span class="title"
      >退款汇总（惠达平台数据） <span style="color: #4290f7; font-weight: 700">{{ showDate }}</span></span
    >
    <div ref="table">
      <el-table :data="tableData" v-loading="tableLoading" border>
        <el-table-column prop="date" label="类别" align="center" width="120">
          <template v-slot:header>
            <div class="cell-with-diagonal">
              <span class="category"> 类别 </span>
              <span class="diagonal-line"></span>
              <span class="dateColumn"> 日期 </span>
            </div>
            <!-- 在这里插入自定义内容 -->
          </template>
        </el-table-column>
        <el-table-column prop="sum_refund_amount" label="退款总金额（元）" align="center" />
        <el-table-column prop="count" label="退款总笔数" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="YeepayReconcileSummarizeTable" setup>
import { ref } from 'vue';
const tableData = ref([]);
const tableLoading = ref(false);
const showDate = ref();

const setLoading = (loading) => {
  tableLoading.value = loading;
};

const setTableData = (list, date) => {
  tableData.value = list;
  showDate.value = date;
};

defineExpose({
  setLoading,
  setTableData
});
</script>
<style lang="scss" scoped>
.refund-table {
  .title {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .el-card {
    height: auto;
  }
  .cell-with-diagonal {
    display: flex;
    flex-direction: column;
    .category,
    .dateColumn {
      position: relative;
      font-weight: 500;
    }
    .category {
      top: 0px;
      right: 0px;
      align-self: flex-end;
    }
    .diagonal-line {
      width: 100%;
      height: 100%;
      border-top: 1px solid rgba(195, 195, 195, 0.5); /* 调整颜色和样式 */
      transform: rotate(24deg);
    }
    .dateColumn {
      display: inline-block;
      width: 32px;
      left: 0px;
      bottom: 0px;
    }
  }
}
</style>
