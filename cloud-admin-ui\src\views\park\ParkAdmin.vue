<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="子场管理" name="childPark">
        <park-region ref="parkRegionRef" />
      </el-tab-pane>
      <el-tab-pane label="通道管理" name="passageway">
        <park-gateway ref="parkGatewayRef" />
      </el-tab-pane>
      <el-tab-pane label="岗亭管理" name="sentry">
        <park-sentry ref="parkSentryRef" />
      </el-tab-pane>
      <el-tab-pane label="临停规则" name="parkFee">
        <park-fee ref="parkFeeRef" />
      </el-tab-pane>
      <el-tab-pane label="长租规则" name="longRentRule">
        <long-rent-rule-table ref="longRentRuleTableRef" />
      </el-tab-pane>
      <el-tab-pane label="设备管理" name="device">
        <device ref="deviceRef" />
      </el-tab-pane>
      <el-tab-pane label="员工管理" name="employee">
        <employee-table ref="employeeTableRef" />
      </el-tab-pane>
      <el-tab-pane label="发票设置" name="invoice">
        <invoice-table ref="invoiceTableRef" />
      </el-tab-pane>
      <el-tab-pane label="租用车位协议" name="rentParkingAgreement">
        <park-rent-agreement ref="parkRentAgreementRef" />
      </el-tab-pane>
      <el-tab-pane label="预约车位协议" name="orderParkingAgreement">
        <park-order-agreement ref="parkOrderAgreementRef" />
      </el-tab-pane>
      <el-tab-pane label="小程序信息管理" name="appletInfoManage">
        <applet-info-manage ref="appletInfoManageRef" />
      </el-tab-pane>
      <el-tab-pane label="业务个性配置" name="merchantCoupon">
        <merchant-coupon ref="merchantCouponRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="ParkAdmin" setup>
import LongRentRuleTable from './longRentRule/LongRentRuleTable.vue';
import EmployeeTable from './employee/EmployeeTable.vue';
import InvoiceTable from './invoice/InvoiceTable.vue';
import Device from './Device.vue';
import ParkRegion from './parkRegion/ParkRegion.vue';
import ParkFee from './ParkFee.vue';
import ParkGateway from './parkGateway/ParkGateway.vue';
import ParkSentry from './sentry/ParkSentry.vue';
import ParkRentAgreement from './parkRentAgreement/ParkRentAgreement.vue';
import ParkOrderAgreement from './parkOrderAgreement/ParkOrderAgreement.vue';
import AppletInfoManage from './appletInfoManage/index.vue';
import MerchantCoupon from './merchantCoupon/index.vue';
import { ref, reactive, onActivated } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const activeName = ref('childPark');
const longRentRuleTableRef = ref(null);
const deviceRef = ref(null);
const parkRegionRef = ref(null);
const employeeTableRef = ref(null);
const invoiceTableRef = ref(null);
const parkGatewayRef = ref(null);
const parkSentryRef = ref(null);
const parkFeeRef = ref(null);
const parkRentAgreementRef = ref(null);
const parkOrderAgreementRef = ref(null);
const appletInfoManageRef = ref(null);
const merchantCouponRef = ref(null);

const params = reactive({
  park_id: undefined,
  page: 1,
  limit: 30
});

onActivated(() => {
  if (Object.keys(route.query).length !== 0 && undefined !== route.query.parkId) {
    params.park_id = route.query.parkId;
    if (undefined !== route.query.redirect_tab) {
      activeName.value = route.query.redirect_tab;
      parkFeeRef.value.searchParkFeeList(params);
    } else {
      parkRegionRef.value.getList(params);
    }
  }

  // 重新加载打开的tab
  activeTab(activeName.value);
});

const handleClick = (tab) => {
  activeTab(tab.props.name);
};

const activeTab = (name) => {
  if (name === 'childPark') {
    parkRegionRef.value.getList(params);
  }
  if (name === 'longRentRule') {
    longRentRuleTableRef.value.resetDataAndGetList();
  }
  if (name === 'device') {
    deviceRef.value.resetDataAndGetList();
  }
  if (name === 'employee') {
    employeeTableRef.value.getList(params);
  }
  if (name === 'passageway') {
    parkGatewayRef.value.getList(params);
  }
  if (name === 'sentry') {
    parkSentryRef.value.getList(params);
  }
  if (name === 'parkFee') {
    parkFeeRef.value.resetDataAndGetList();
  }
  if (name === 'invoice') {
    invoiceTableRef.value.getList(params);
  }
  if (name === 'rentParkingAgreement') {
    parkRentAgreementRef.value.getSpaceRentLicense(params.park_id);
    parkRentAgreementRef.value.initSelects();
  }
  if (name === 'orderParkingAgreement') {
    parkOrderAgreementRef.value.getSpaceReserveLicense(params.park_id);
    parkOrderAgreementRef.value.initSelects();
  }
  if (name === 'appletInfoManage') {
    appletInfoManageRef.value.initRemarks(params.park_id);
  }
  if (name === 'merchantCoupon') {
    merchantCouponRef.value.initMerchantCouponConfig(params.park_id);
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
