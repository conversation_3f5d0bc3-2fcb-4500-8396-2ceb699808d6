<template>
  <FormSearch canFold @search="search" @reset="reset">
    <form-search-item>
      <el-input v-model="query.username" placeholder="用户名" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="query.drink" placeholder="冷饮">
        <el-option label="冰淇淋" :value="1" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.username" placeholder="用户名" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.address" placeholder="登录地点" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.username" placeholder="用户名" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.address" placeholder="登录地点" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.username" placeholder="用户名" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.address" placeholder="登录地点" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.username" placeholder="用户名" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="query.address" placeholder="登录地点" />
    </form-search-item>
  </FormSearch>
</template>

<script setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';

const query = reactive({
  username: undefined,
  address: undefined,
  page: 1,
  limit: 30
});

const emits = defineEmits(['form-search']);

const search = () => {
  emits('form-search', query);
};

const reset = () => {
  emits('form-search', resetQuery());
};

const resetQuery = () => {
  query.username = undefined;
  query.address = undefined;
  query.page = 1;
  query.limit = 30;
};
</script>

<style lang="scss" scoped></style>
