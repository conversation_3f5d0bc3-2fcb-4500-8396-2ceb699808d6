<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-26 11:08:29
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\system\permissionGroup\PermissionGroupTable.vue
-->
<template>
  <el-card shadow="never">
    <div class="opers">
      <el-button type="primary" @click="handleCreate()">新 增</el-button>
      <div>
        <el-input v-model="data.queryParams.group_name" class="w-50 m-2" placeholder="权限组名称" @keyup.enter="search" clearable>
          <template #suffix
            ><el-icon @click="search"><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 220px)">
        <el-table-column label="操作" width="200" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="seeDetail(scope.row)"> 查看 </el-button>
            <el-button link type="primary" @click="updateRow(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="deleteRow(scope.row)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="group_name" label="权限组名称" align="center" />
      </el-table>
      <el-pagination
        background
        :total="total"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 30, 50, 100]"
        :current-page="data.queryParams.page"
        :page-size="data.queryParams.limit"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        class="table-pagination"
      />
      <el-dialog title="添加权限组" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="400px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item prop="group_name" label="权限组名称">
            <el-input v-model="data.form.group_name" :autofocus="true" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="creatPermissionGroup(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改权限组" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="400px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="group_name" label="权限组名称">
            <el-input v-model="data.updateForm.group_name" :autofocus="true" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updatePermissionGroup(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-drawer v-model="detailDialogVisible" title="权限组详情" direction="rtl" size="50%">
        <el-table ref="detailForm" :data="tableDetail" border style="width: 100%; margin-top: 10px">
          <el-table-column type="selection" width="40" style="text-align: center" />
          <el-table-column prop="name" label="权限名称" align="center" />
          <el-table-column prop="code" label="权限编码" align="center" />
          <el-table-column prop="type_display" label="权限类型" align="center" />
        </el-table>
        <el-pagination
          style="z-index: 5000"
          background
          :page-sizes="[10, 30, 50, 100]"
          :current-page="data.detailForm.page"
          :page-size="data.detailForm.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalDetail"
          class="table-pagination"
          @size-change="handleDetailSizeChange"
          @current-change="handleDetailCurrentChange"
        />
      </el-drawer>
    </div>
  </el-card>
</template>

<script setup>
import { reactive, ref, onMounted } from 'vue';
import permissionGroupService from '@/service/system/PermissionGroupService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

const tableData = ref([]);
const loading = ref(false);
const tableDetail = ref([]);
const totalDetail = ref(0);
const addForm = ref();
const editForm = ref();
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const detailDialogVisible = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    group_name: undefined
  },
  updateForm: {
    id: undefined,
    group_name: undefined
  },
  detailForm: {
    page: 1,
    limit: 30,
    name: undefined,
    code: undefined,
    type_display: undefined
  },
  rules: {
    group_name: [
      {
        required: true,
        message: '请输入权限组名称',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
});
// 分页查询权限组列表
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  permissionGroupService.pagingPermissionGroup(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const search = () => {
  getList(data.queryParams);
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 获取分页参数
const handleDetailSizeChange = (val) => {
  data.detailForm.limit = val;
  seeDetail(data.detailForm);
};
// 获取分页参数
const handleDetailCurrentChange = (val) => {
  data.detailForm.page = val;
  seeDetail(data.detailForm);
};
const handleCreate = () => {
  data.form = {
    group_name: undefined
  };
  createDialogVisible.value = true;
};
const creatPermissionGroup = (addForm) => {
  addForm.validate().then(() => {
    permissionGroupService
      .createPermissionGroup(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          addForm.resetFields();
          getList(data.queryParams);
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const updateRow = (row) => {
  data.updateForm = {
    id: row.id,
    group_name: row.group_name
  };
  updateDialogVisible.value = true;
};
const seeDetail = (params) => {
  data.detailForm = params;
  params.page === undefined ? (params.page = 1) : (params.page = data.detailForm.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.detailForm.limit);

  permissionGroupService.getPermissionById(params).then((response) => {
    tableDetail.value = response.rows;
    totalDetail.value = parseInt(response.total);
    detailDialogVisible.value = true;
  });
};
const updatePermissionGroup = (editForm) => {
  editForm.validate().then(() => {
    permissionGroupService
      .updatePermissionGroup(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const deleteRow = (row) => {
  ElMessageBox.confirm('删除权限组，确定删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    permissionGroupService.deletePermissionGroup(row.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.message,
          type: 'warning'
        });
      }
    });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
</script>

<style lang="scss" scoped></style>
