<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="停车场名称"
    /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" multiple style="width: 100%" placeholder="设备类型">
        <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
    <park-find-back :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="DeviceStatisticsTable" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onActivated } from 'vue';
const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const park_name = ref('');
const deviceTypeList = ref([]);
const form = reactive({
  queryParams: {
    park_name: '',
    types: [],
    page: 1,
    limit: 30
  }
});
onActivated(() => {
  initSelects();
});
// 数据初始化
const initSelects = () => {
  // 设备类型
  const param = [
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    }
  ];
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: '',
    types: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
