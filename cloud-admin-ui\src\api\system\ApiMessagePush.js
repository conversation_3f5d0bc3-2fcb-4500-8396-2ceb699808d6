/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-06-07 16:57:11
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\api\system\ApiMessagePush.js
 * @Description: {}
 */
/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 消息推送配置设定
export const messageInsertApis = (data) => {
  return $({
    url: '/console/park/push/message/insert',
    method: 'post',
    data
  });
};

// 消息推送查询
export const messageInsertInfoApis = (data) => {
  return $({
    url: '/console/park/push/message/pushMessageDetail?id=' + data.id,
    method: 'post'
    // data
  });
};

// 消息推送查询
export const roleListApis = (data) => {
  return $({
    url: '/console/role/pagingRoles',
    method: 'post',
    data
  });
};
