<template>
  <div class="container">
    <rushed-car-search @form-search="searchRushedCarList" @reset="resetParamsAndData" />
    <rushed-car-table ref="table" />
  </div>
</template>

<script setup name="RushedCar">
import RushedCarSearch from './rushedCar/RushedCarSearch.vue';
import RushedCarTable from './rushedCar/RushedCarTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageRushedCar = (queryParams) => {
  table.value.getList(queryParams);
};

const searchRushedCarList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageRushedCar
});
</script>
