<template>
  <div class="opers">
    <el-space> </el-space>
  </div>
  <div ref="table">
    <el-table :data="tableData" v-loading="loading" border @initData="initDocTypeTree">
      <el-table-column type="selection" style="text-align: center" width="40" />
      <el-table-column prop="action" label="操作" align="center" width="200">
        <template v-slot="scope">
          <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="title" label="文档标题" align="center" min-width="250">
        <template v-slot="scope">
          <el-link type="primary" @click="handleDetail(scope.row)">{{ scope.row.title }}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="上级类目" align="center" />
      <el-table-column prop="update_name" label="更新人/更新时间" align="center">
        <template v-slot="scope">
          <span>{{ scope.row.updator_name }}/{{ scope.row.updated_at }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      :current-page="data.queryParams.page"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="data.queryParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script name="DocumentCenterTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { activeRouteTab } from '@/utils/tabKit';
import { ElMessage, ElMessageBox } from 'element-plus';
import documentService from '@/service/system/DocumentService';

const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const docTypeName = ref('');
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  getList(data.queryParams);
  status.value = true;
});

// 分页查询文档数据
const getList = (params) => {
  docTypeName.value = params.name;
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  documentService.pagingDocument(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 删除文档
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    documentService
      .deleteDocument(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 修改文档
const handleEdit = (row) => {
  activeRouteTab({
    path: '/system/documentEdit',
    query: {
      id: row.id,
      docTypeName: docTypeName.value
    }
  });
};

// 查看文档详情
const handleDetail = (row) => {
  activeRouteTab({
    path: '/system/documentDetail',
    query: {
      id: row.id,
      docTypeName: docTypeName.value
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
