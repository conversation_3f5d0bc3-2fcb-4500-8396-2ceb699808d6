<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-button type="primary" @click="handleCreate()">添加通道</el-button>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="260">
          <template v-slot="scope">
            <el-button link v-if="scope.row.type == 1 || scope.row.type == 3" type="primary" @click="handleQRcode(scope.row)"> 出场二维码 </el-button>
            <el-button link v-if="scope.row.type == 2 || scope.row.type == 3" type="primary" @click="handleQRcode(scope.row)"> 入场二维码 </el-button>
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_region_name" label="子场名称" align="center" />
        <el-table-column prop="name" label="通道名称" align="center" />
        <el-table-column prop="longitude" label="经度" align="center" />
        <el-table-column prop="latitude" label="纬度" align="center" />
        <el-table-column prop="type_desc" label="通道类型" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog title="添加停车场通道" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
      <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
        <el-form-item prop="name" label="通道名称">
          <el-input v-model="data.form.name" />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.form.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="通道坐标">
          <el-row :gutter="8">
            <el-col :span="9">
              <el-input v-model="longitude" readonly placeholder="经度" />
            </el-col>
            <el-col :span="9">
              <el-input v-model="latitude" readonly placeholder="纬度" />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="openMap" style="position: relative; top: -2px"> 打开地图 </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item prop="type" label="通道类型">
          <el-select v-model="data.form.type" style="width: 100%">
            <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createCancel(addForm)">取 消</el-button>
          <el-button type="primary" @click="createParkGateway(addForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="编辑停车场通道" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
      <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
        <el-form-item prop="name" label="通道名称">
          <el-input v-model="data.updateForm.name" />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.updateForm.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="通道坐标">
          <el-row :gutter="8">
            <el-col :span="9">
              <el-input v-model="longitude" readonly placeholder="经度" />
            </el-col>
            <el-col :span="9">
              <el-input v-model="latitude" readonly placeholder="纬度" />
            </el-col>
            <el-col :span="6">
              <el-button type="primary" @click="openMap" style="position: relative; top: -2px"> 打开地图 </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item prop="type" label="通道类型">
          <el-select v-model="data.updateForm.type" style="width: 100%">
            <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateCancel(editForm)">取 消</el-button>
          <el-button type="primary" @click="updateParkGateway(editForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 地图选点弹框 -->
    <el-dialog v-if="mapDialogVisible" title="地图选点" v-model="mapDialogVisible" width="1000px">
      <map-punctuation
        ref="mapPunctuation"
        @checkedPunctuation="checkedPunctuation"
        :longitude="longitude"
        :latitude="latitude"
        @closeMapDialogVisible="mapDialogVisible = false"
      />
    </el-dialog>

    <!-- 查看二维码 -->
    <el-dialog v-model="dialogVisible" width="470px" draggable="true" title="查看二维码">
      <div style="text-align: center; font-weight: 600; margin-top: -25px; margin-bottom: 5px">{{ data.qrCodeDate.title }}</div>
      <qrcode-vue :value="qrCodeUrl" size="430" style="padding: 5px"></qrcode-vue>
      <div class="qrCode_content">
        <span>{{ data.qrCodeDate.park_name }}</span>
        <span>{{ data.qrCodeDate.regin_name }}</span>
        <span>{{ data.qrCodeDate.gateway_name }}</span>
      </div>
    </el-dialog>
  </el-card>
</template>

<script name="ParkGateway" setup>
import { reactive, onMounted, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import commonService from '@/service/common/CommonService';
import parkRegionService from '@/service/park/ParkRegionService';
import parkGatewayService from '@/service/park/ParkGatewayService';
import MapPunctuation from '../parkInfo/MapPunctuation.vue';
import QrcodeVue from 'qrcode.vue';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const park_id = ref('');
const types = ref([]);
const parkRegions = ref([]);
const total = ref(0);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const mapDialogVisible = ref(false);
const longitude = ref('');
const latitude = ref('');
const qrCodeUrl = ref('');
const dialogVisible = ref(false);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    park_id: undefined,
    park_region_id: undefined,
    name: undefined,
    longitude: '', //经度
    latitude: '', //纬度
    type: undefined
  },
  updateForm: {},
  qrCodeDate: {
    title: '',
    park_name: '',
    regin_name: '',
    gateway_name: ''
  },
  rules: {
    name: [
      {
        required: true,
        message: '请输入通道名称',
        trigger: 'blur'
      }
    ],
    park_region_id: [
      {
        required: true,
        message: '请选择所属子场',
        trigger: 'change'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择通道类型',
        trigger: 'change'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'types', enum_value: 'EnumGatewayType' }];
  commonService.findEnums('park', param).then((response) => {
    types.value = response.data.types;
  });
};

const getList = (params) => {
  loading.value = true;
  park_id.value = params.park_id;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkGatewayService.pagingParkGateways(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  parkRegionService.listParkRegion(park_id.value).then((response) => {
    parkRegions.value = response;
  });
};
const handleCreate = () => {
  data.form = {
    park_id: park_id.value,
    park_region_id: undefined,
    name: undefined,
    longitude: '', //经度
    latitude: '', //纬度
    type: undefined
  };
  longitude.value = '';
  latitude.value = '';
  createDialogVisible.value = true;
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createParkGateway = (addForm) => {
  addForm.validate().then(() => {
    data.form.longitude = longitude.value;
    data.form.latitude = latitude.value;

    parkGatewayService
      .createParkGateway(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: row.park_id,
    park_region_id: row.park_region_id,
    name: row.name,
    longitude: row.longitude, //经度
    latitude: row.latitude, //纬度
    type: row.type
  };
  longitude.value = row.longitude;
  latitude.value = row.latitude;
  updateDialogVisible.value = true;
};
const updateParkGateway = (editForm) => {
  editForm.validate().then(() => {
    data.updateForm.longitude = longitude.value;
    data.updateForm.latitude = latitude.value;
    parkGatewayService
      .updateParkGateway(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkGatewayService
      .deleteParkGateway(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 打开地图
const openMap = () => {
  mapDialogVisible.value = true;
};
// 选中的坐标
const checkedPunctuation = (val) => {
  if (val.lng == '') {
    ElMessage({
      message: '获取经纬度失败请重新获取',
      type: 'error'
    });
  } else {
    longitude.value = val.lng;
    latitude.value = val.lat;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const handleQRcode = (val) => {
  if (val.type == 1) {
    data.qrCodeDate.title = '出场结算二维码';
    qrCodeUrl.value =
      (import.meta.env.VITE_BASE_URL || window.location.origin) +
      '/h5/webParkPay.html?park_id=' +
      val.park_id +
      '&park_region_id=' +
      val.park_region_id +
      '&gateway_id=' +
      val.id;
  }
  if (val.type == 2) {
    data.qrCodeDate.title = '无牌车入场二维码';
    qrCodeUrl.value =
      (import.meta.env.VITE_BASE_URL || window.location.origin) +
      '/h5/noPlateCarIn.html?gateway_code=' +
      val.code +
      '&park_id=' +
      val.park_id +
      '&park_region_id=' +
      val.park_region_id +
      '&gateway_id=' +
      val.id;
  }
  data.qrCodeDate.park_name = val.park_name;
  data.qrCodeDate.regin_name = val.park_region_name;
  data.qrCodeDate.gateway_name = val.name;
  dialogVisible.value = true;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.qrCode_content {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
</style>
