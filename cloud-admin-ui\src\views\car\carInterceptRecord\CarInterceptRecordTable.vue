<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加拦截记录</el-button>
      </el-space>
      <el-space>
        <el-upload
          :limit="1"
          :action="uploadExcelUrl"
          accept=".xlsx"
          :headers="headers"
          :show-file-list="false"
          :before-upload="beforeUploadExcel"
          :on-success="onSuccessUploadExcel"
        >
          <el-button plain>批量上传</el-button>
        </el-upload>
        <el-button plain @click="downloadTemplate()">下载模板</el-button>
        <DownloadButton
          btnType="default"
          :exportFunc="carInterceptRecordService.exportCarInterceptRecords"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 385px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button v-if="scope.row.manual_flag == 1" link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="intercept_type_desc" label="拦截类型" align="center" />
        <el-table-column prop="intercepted_name" label="人员姓名" align="center" />
        <el-table-column prop="intercepted_mobile" label="手机号" align="center" />
        <el-table-column prop="health" label="身体状况" align="center" />
        <el-table-column prop="manual_flag_desc" label="是否人工录入" align="center" />
        <el-table-column prop="record_date" label="记录时间" align="center" min-width="120" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加拦截记录" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择车场" prop="park_id">
            <el-input v-model="data.form.park_name" placeholder="请选择车场" readonly @click="authCharge(true, 'add')" />
          </el-form-item>

          <el-form-item prop="plate_no" label="车牌号">
            <el-input v-model="data.form.plate_no" maxlength="8" show-word-limit placeholder="请输入车牌号" />
          </el-form-item>
          <el-form-item label="人员姓名" prop="intercepted_name">
            <el-input v-model="data.form.intercepted_name" maxlength="10" show-word-limit placeholder="请输入人员姓名" />
          </el-form-item>
          <el-form-item prop="intercepted_mobile" label="手机号">
            <el-input v-model="data.form.intercepted_mobile" maxlength="11" show-word-limit placeholder="请输入人员手机号" />
          </el-form-item>
          <el-form-item label="记录时间" class="required">
            <el-date-picker
              v-model="data.form.record_date"
              type="date"
              format="YYYY-MM-DD"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <el-form-item prop="health" label="身体状况">
            <el-input v-model="data.form.health" :rows="4" type="textarea" maxlength="50" show-word-limit placeholder="请输入身体状况" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createCarIntercept(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CarInterceptRecordTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import carInterceptRecordService from '@/service/car/CarInterceptRecordService';
import ParkFindBack from './ParkFindBack.vue';
import { getToken } from '@/utils/common';
import DownloadButton from '@/components/DownloadButton.vue';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const addForm = ref();
const uploadExcelUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/car/importCarInterceptRecord');
const headers = reactive({
  Authorization: getToken()
});
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const createDialogVisible = ref(false);
const parkInfoDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: '',
    park_name: '',
    plate_no: '',
    intercepted_name: '',
    intercepted_mobile: '',
    health: '',
    record_date: undefined
  },
  rules: {
    intercepted_mobile: [
      {
        required: true,
        message: '请输入人员手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ],
    intercepted_name: [
      {
        required: true,
        message: '请输入人员姓名',
        trigger: 'blur'
      }
    ],
    plate_no: [
      {
        required: true,
        message: '请输入车牌号',
        trigger: 'blur'
      }
    ],
    health: [
      {
        required: true,
        message: '请输入健康状况',
        trigger: 'blur'
      }
    ]
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  carInterceptRecordService.pagingCarInterceptRecords(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: '',
    park_name: '',
    plate_no: '',
    intercepted_name: '',
    intercepted_mobile: '',
    health: '',
    record_date: undefined
  };
  createDialogVisible.value = true;
};

const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    carInterceptRecordService
      .deleteCarInterceptRecordById(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createCarIntercept = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.record_date === '' && data.form.record_date === undefined) {
      ElMessage({
        message: '请选择记录日期',
        type: 'warning'
      });
      return false;
    }
    delete data.form.park_name;
    carInterceptRecordService
      .createCarInterceptRecordByManual(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const beforeUploadExcel = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
// 上传
const onSuccessUploadExcel = (response) => {
  if (response.success == true) {
    ElMessage({
      message: response.message,
      type: 'success'
    });
    getList(data.queryParams);
  } else {
    ElMessage({
      message: response.detail_message != '' ? response.detail_message : response.message,
      type: 'error'
    });
  }
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
//下载模板
const downloadTemplate = () => {
  carInterceptRecordService.downloadInterceptRecordTemplate().then((response) => {
    if (response.success == true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      window.open(response.data, '_blank');
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
