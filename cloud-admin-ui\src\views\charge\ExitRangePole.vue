<template>
  <div class="container">
    <exit-range-pole-search @form-search="searchExitRangePoleList" @reset="resetParamsAndData" />
    <exit-range-pole-table ref="table" />
  </div>
</template>

<script setup name="ExitRangePole">
import ExitRangePoleSearch from './exitRangePole/ExitRangePoleSearch.vue';
import ExitRangePoleTable from './exitRangePole/ExitRangePoleTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageExitRangePole = (queryParams) => {
  table.value.getList(queryParams);
};

const searchExitRangePoleList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageExitRangePole
});
</script>
