<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="租用车位协议" name="rentAgreement">
        <rent-agreement ref="agreementByRent" />
      </el-tab-pane>
      <el-tab-pane label="预约车位协议" name="orderAgreement">
        <order-agreement ref="agreementByOrder" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="Agreement" setup>
import RentAgreement from './standardAgreement/RentAgreement.vue';
import OrderAgreement from './standardAgreement/OrderAgreement.vue';
import { ref, reactive, onMounted } from 'vue';

const activeName = ref('rentAgreement');
const agreementByRent = ref(null);
const agreementByOrder = ref(null);
const params = reactive({
  license_type: undefined,
  page: 1,
  limit: 30
});

onMounted(() => {
  params.license_type = '1';
  agreementByRent.value.getStandardLicense(params.license_type);
  setTimeout(function () {
    agreementByRent.value.initSelects();
  }, 100);
});

const handleClick = (tab) => {
  if (tab.props.name === 'rentAgreement') {
    params.license_type = '1';
    agreementByRent.value.getStandardLicense(params.license_type);
    agreementByRent.value.initSelects();
  }
  if (tab.props.name === 'orderAgreement') {
    params.license_type = '2';
    agreementByOrder.value.getStandardLicense(params.license_type);
    agreementByOrder.value.initSelects();
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
