<template>
  <div class="container">
    <group-long-rent-rule-search @form-search="searchGroupLongRentRuleList" @reset="resetParamsAndData" />
    <group-long-rent-rule-table ref="table" />
  </div>
</template>

<script setup name="GroupLongRentRule">
import GroupLongRentRuleSearch from './groupLongRentRule/GroupLongRentRuleSearch.vue';
import GroupLongRentRuleTable from './groupLongRentRule/GroupLongRentRuleTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchGroupLongRentRuleList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
