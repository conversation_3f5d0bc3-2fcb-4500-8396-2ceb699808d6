/* jshint esversion: 6 */
import $ from '@/utils/axios';

/*
 * 无人值守相关接口
 */
export default class UnattendedApi {
  // 分页查询应急通讯录
  static emergencylist = (data) => {
    return $({
      url: '/console/watch/emergency/list',
      method: 'post',
      data
    });
  };
  // 修改应急通讯录
  static emergencyupdate = (data) => {
    return $({
      url: '/console/watch/emergency/update',
      method: 'post',
      data
    });
  };
  // 批量删除应急通讯录
  static emergencydelete = (data) => {
    return $({
      url: '/console/watch/emergency/delete',
      method: 'post',
      data
    });
  };
  //待接受托管列表
  static hostingapplyList = () => {
    return $({
      url: '/console/watch/hosting/applyList',
      method: 'get'
    });
  };
  //接受值守接口
  static hostingbegin = (data) => {
    return $({
      url: '/console/watch/hosting/begin',
      method: 'post',
      data
    });
  };
  //撤回值守接口
  static hostingend = (data) => {
    return $({
      url: '/console/watch/hosting/end',
      method: 'post',
      data
    });
  };
  //首页-值守统计接口
  static statisticsparkId = () => {
    return $({
      url: '/console/watch/hosting/statistics',
      method: 'get'
    });
  };
  //操作台-入场
  static operateentrance = (data) => {
    return $({
      url: '/console/watch/operate/entrance',
      method: 'post',
      data
    });
  };
  //操作台-出场
  static operateexport = (data) => {
    return $({
      url: '/console/watch/operate/export',
      method: 'post',
      data
    });
  };
  //获取通道信息
  static operateselectGatewayInfo = (data) => {
    return $({
      url: '/console/watch/operate/selectGatewayInfo',
      method: 'post',
      data
    });
  };
  //分页查询呼叫受理记录
  static recordlist = (data) => {
    return $({
      url: '/console/call/record/list',
      method: 'post',
      data
    });
  };
  // 添加呼叫受理记录
  static recordadd = (data) => {
    return $({
      url: '/console/call/record/add',
      method: 'post',
      data
    });
  };
  // 添加呼叫原因
  static recordaddCallReason = (data) => {
    return $({
      url: '/console/call/record/addCallReason',
      method: 'post',
      data
    });
  };
  // 最新呼叫记录
  static recordgetResentRecord = () => {
    return $({
      url: '/console/call/record/getResentRecord',
      method: 'get'
    });
  };
  // 根据id获取呼叫受理记录详情
  static recordgetDetailById = (id) => {
    return $({
      url: `/console/call/record/getDetailById/${id}`,
      method: 'get'
    });
  };
  // 查询待处理设备报警
  static pageWatchDeviceEventByUserId = (data) => {
    return $({
      url: '/console/park/device_event/pageWatchDeviceEventByUserId',
      method: 'post',
      data
    });
  };
  // 首页-值守监控台云端
  static monitorconsole = (data) => {
    return $({
      url: '/console/watch/hosting/monitor/console',
      method: 'post',
      data
    });
  };
  // 首页-值守控制台-本地管理车场
  static monitoragent = (data) => {
    return $({
      url: '/console/watch/hosting/monitor/agent',
      method: 'post',
      data
    });
  };
  // 托管中车场列表
  static hostingpagingPark = (data) => {
    return $({
      url: '/console/watch/hosting/pagingPark',
      method: 'post',
      data
    });
  };
  // 手动抓拍
  static triggerCamera = (data) => {
    return $({
      url: '/console/watch/triggerCamera',
      method: 'post',
      data
    });
  };
  // 操作台-手动补录
  static cloudManualRecordCarInRecord = (data) => {
    return $({
      url: '/console/watch/cloudManualRecordCarInRecord' + data,
      method: 'post',
      data: {}
    });
  };
  // 操作台-取消入场
  static cancelPass = (data) => {
    return $({
      url: '/console/watch/cancelPass',
      method: 'post',
      data
    });
  };
  // 操作台-入场抬杆
  static pushOpenStrobe = (data) => {
    return $({
      url: '/console/watch/pushOpenStrobe',
      method: 'post',
      data
    });
  };
  // 首页-获取我的坐席账号
  static getSeatsByUserId = (id) => {
    return $({
      url: '/console/watch/seats/getSeatsByUserId?userId=' + id,
      method: 'get'
    });
  };
  // 异常通道列表
  static abnormalGatewayList = (data = {}) => {
    return $({
      url: '/console/watch/hosting/abnormalGatewayList',
      method: 'post',
      data
    });
  };
  // 首页-待处理异常事件
  static monitorpagingEvents = (data = {}) => {
    return $({
      url: '/console/park/monitor/pagingEvents',
      method: 'post',
      data
    });
  };
  // 操作台-特殊放行
  static cloudSpecialPass = (data = {}) => {
    return $({
      url: '/console/watch/cloudSpecialPass',
      method: 'post',
      data
    });
  };
  // 根据车场id和车牌号查询可用的卡券
  static queryCouponsByPlateNo = (parkId, plateNo) => {
    return $({
      url: '/console/coupon/meta/queryCouponsByPlateNo?parkId=' + parkId + '&plateNo=' + plateNo,
      method: 'post',
      data: {}
    });
  };
  // 车辆绑定的相关信息 （工具箱 一户多车）
  static getCarBindInfo = (data = {}) => {
    return $({
      url: '/console/watch/operate/getCarBindInfo',
      method: 'post',
      data
    });
  };
  //匹配入场 分页查询车辆的入场记录 (工具箱 匹配入场纪录列表)
  static pageParkInRecord = (data = {}) => {
    return $({
      url: '/console/watch/operate/pageParkInRecord',
      method: 'post',
      data
    });
  };
  // 查询车辆最近的入场记录 (工具箱 匹配入场)
  static queryLastParkInRecord = (data = {}) => {
    return $({
      url: '/console/watch/operate/queryLastParkInRecord',
      method: 'post',
      data
    });
  };
  // 查询最近Topnn出场记录 (工具箱 匹配入场)
  static queryParkOutRecord = (data = {}) => {
    return $({
      url: '/console/watch/operate/queryLastParkOutRecord',
      method: 'post',
      data
    });
  };
  // 查询车辆在某个车场的优惠券 (工具箱 匹配入场)
  static queryAvailableCoupons = (data = {}) => {
    return $({
      url: '/console/coupon/meta/queryAvailableCoupons',
      method: 'post',
      data
    });
  };
  // 获取临时车牌号
  static generateCarNoPlateNo = (data = {}) => {
    return $({
      url: '/console/watch/operate/generateCarNoPlateNo',
      method: 'post',
      data
    });
  };
  // 操作台-根据卡券号（UUID），取消优免
  static cancelFreeCoupon = (data = {}) => {
    return $({
      url: '/console/watch/cancelFreeCoupon?' + data,
      method: 'post',
      data: {}
    });
  };
  static cancelFreeCouponByNo = (data = {}) => {
    return $({
      url: '/console/watch/cancelFreeCouponByNo',
      method: 'post',
      data
    });
  };
  // 操作台-修改车牌号
  static updateCarInRecord = (data = {}) => {
    return $({
      url: '/console/watch/modifyPlateNo',
      method: 'post',
      data
    });
  };
  // 根据计费生成临停订单
  static getParkOrder = (data = {}) => {
    return $({
      url: '/console/watch/operate/getParkOrder',
      method: 'post',
      data
    });
  };
  // 查询岗亭值守的通道信息
  static querySentryGateway = (data = {}) => {
    return $({
      url: '/console/watch/operate/querySentryGateway',
      method: 'post',
      data
    });
  };
  // 查询岗亭值守的通道信息
  static refreshOrder = (data = {}) => {
    return $({
      url: '/console/watch/refreshOrder',
      method: 'post',
      data
    });
  };
  // 操作台-收费放行
  static chargeAndPass = (data = {}) => {
    return $({
      url: '/console/watch/chargeAndPass',
      method: 'post',
      data
    });
  };
  // 获取设备监控
  static getAccessUrl = (data = {}) => {
    return $({
      url: '/console/watch/getAccessUrl',
      method: 'get',
      data
    });
  };
  // 待处理异常事件恢复
  static recoveryEvent = (eventId) => {
    return $({
      url: `/console/park/monitor/recoveryEvent/${eventId}`,
      method: 'get'
    });
  };
}
