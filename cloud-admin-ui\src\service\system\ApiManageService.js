import * as api from '@/api/system/ApiManageApi';

/**
 * Api接口
 */
export default {
  /**
   * 分页查询
   */
  pagingApis(data) {
    return new Promise((resolve, reject) => {
      try {
        api.pagingApis(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 创建
   */
  createApi(data) {
    return new Promise((resolve, reject) => {
      try {
        api.createApi(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改
   */
  updateApi(data) {
    return new Promise((resolve, reject) => {
      try {
        api.updateApi(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除Api接口
   */
  deleteApis(data) {
    return new Promise((resolve, reject) => {
      try {
        api.deleteApis(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 编辑时查询数据，根据id查询
   */
  getApiById(data) {
    return new Promise((resolve, reject) => {
      try {
        api.getApiById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * api接口列表
   */
  apiList() {
    return new Promise((resolve, reject) => {
      try {
        api.apiList().then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 权限组列表
   */
  permissionGroupList() {
    return new Promise((resolve, reject) => {
      try {
        api.permissionGroupList().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询权限分组下的api数据  通过分组id
   * @returns
   */
  getApiPermissionByGroupId(data) {
    return new Promise((resolve, reject) => {
      try {
        api.getApiPermissionByGroupId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *查询角色权限分组下的api树
   * @returns
   */
  getRoleApiPermissionTree(data) {
    return new Promise((resolve, reject) => {
      try {
        api.getRoleApiPermissionTree(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
