<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-26 15:31:20
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\system\Role.vue
-->
<template>
  <div class="container">
    <roles-search @form-search="searchRoleList" @reset="resetParamsAndData" />
    <roles-table ref="table" />
  </div>
</template>

<script setup name="Role">
import RolesSearch from './role/RolesSearch.vue';
import RolesTable from './role/RolesTable.vue';
import { ref, reactive } from 'vue';
const table = ref(null);
const params = reactive({});

const searchRoleList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>

<style lang="scss" scoped></style>
