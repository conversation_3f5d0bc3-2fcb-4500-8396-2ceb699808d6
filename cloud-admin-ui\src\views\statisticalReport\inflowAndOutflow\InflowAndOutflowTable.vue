<template>
  <el-card class="table" shadow="never">
    <el-radio-group v-model="tabPosition">
      <el-radio-button value="列表">列表</el-radio-button>
      <el-radio-button value="图表">图表</el-radio-button>
    </el-radio-group>

    <div v-if="tabPosition == '列表'" ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <DownloadButton btnType="default" :exportFunc="inflowAndOutflowTableService.exportData"
            :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
          </DownloadButton>
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="统计范围" align="center">
          <el-table-column label="日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="星期" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.chinese_week }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="车场基础信息" align="center">
          <!-- <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="进场" align="center">
          <el-table-column label="长租进场" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停进场" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合计" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.total_car_in }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="出场" align="center">
          <el-table-column label="长租出场" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_car_out }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停出场" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_car_out }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合计" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.total_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="进出场合计" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.total_car_in_out }}</span>
          </template>
        </el-table-column>
        <el-table-column label="临停停车费" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.temp_car_amount }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar :tableData='tableData'></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%;height: 600px;background: #fff;line-height: 600px;text-align: center;">
        请选择停车场进行统计
      </div>
    </div>
  </el-card>
</template>

<script name="InflowAndOutflowTable" setup>
import DownloadButton from '@/components/DownloadButton.vue';
import inflowAndOutflowTableService from '@/service/statisticalReport/InflowAndOutflowTableService';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import echartbar from './barChart.vue';

const tabPosition = ref('列表')
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});
const parkId = ref(null);

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  inflowAndOutflowTableService.pagingInflowAndOutflow(params).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell>.cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
