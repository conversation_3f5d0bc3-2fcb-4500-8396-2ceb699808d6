<template>
  <el-card class="table" shadow="never">
    <el-radio-group v-model="tabPosition">
      <el-radio-button value="列表">列表</el-radio-button>
      <el-radio-button value="图表">图表</el-radio-button>
    </el-radio-group>

    <div v-if="tabPosition == '列表'" ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <div class="uodataClass">
            <el-tooltip>
              <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
              <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
            </el-tooltip>
            <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
          </div>
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 300px)">
        <el-table-column label="统计范围" align="center">
          <el-table-column label="日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="车场基础信息" align="center">
          <!-- <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column> -->
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="进场" align="center">
          <el-table-column label="长租进场" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停进场" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_car_in }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合计" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.total_car_in }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="出场" align="center">
          <el-table-column label="长租出场" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_car_out }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停出场" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_car_out }}</span>
            </template>
          </el-table-column>
          <el-table-column label="合计" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.total_car_out }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="进出场合计" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.total_car_in_out }}</span>
          </template>
        </el-table-column>
        <el-table-column label="临停停车费" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.temp_car_amount }}</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar :tableData="tableData"></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%; height: 600px; background: #fff; line-height: 600px; text-align: center">请选择停车场进行统计</div>
    </div>
           <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30,100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
     </div>
  </el-card>
</template>

<script name="InflowAndOutflowTable" setup>
import inflowAndOutflowTableService from '@/service/statisticalReport/InflowAndOutflowTableService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';

import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted } from 'vue';
import echartbar from './barChart.vue';

const tabPosition = ref('列表');
const tableData = ref([]);
const total=ref(0)
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
       limit:30
  }
});
const parkId = ref(null);

const newdata = ref();
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
onMounted(() => {
  getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
   getList({})
}
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({})
}
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(14);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
    data.queryParams =  {...data.queryParams,...params};
  inflowAndOutflowTableService.pagingInflowAndOutflow(data.queryParams ).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id;
      total.value= Number(response.data.total);
      tableData.value = response.data.rows
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end
}
</style>
