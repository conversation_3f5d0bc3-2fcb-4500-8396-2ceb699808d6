import * as parkGateway from '@/api/park/ParkGatewayApi';

/**
 * 通道
 */
export default {
  /**
   * 分页查询
   */
  pagingParkGateways(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.pagingParkGateways(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通道列表-子场id
   */
  listParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.listParkGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通道列表  岗亭id查询
   */
  listParkSentryGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.listParkSentryGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通道列表  岗亭处查找带回
   */
  listGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.listGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增通道
   */
  createParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.createParkGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改通道
   */
  updateParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.updateParkGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除通道
   */
  deleteParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkGateway.deleteParkGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
