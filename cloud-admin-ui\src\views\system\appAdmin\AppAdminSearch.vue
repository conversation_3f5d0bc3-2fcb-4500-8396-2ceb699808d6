<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset" can-fold>
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="应用名称" /></form-search-item>
    <form-search-item>
      <el-row :gutter="5">
        <el-col :span="7">
          <el-select v-model="form.queryParams.dateType" placeholder="时间类型" style="width: 100%">
            <el-option label="更新时间" value="1"></el-option>
          </el-select>
        </el-col>
        <el-col :span="17">
          <el-date-picker
            v-model="form.dateRange"
            type="daterange"
            style="width: 100%"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
          />
        </el-col>
      </el-row>
    </form-search-item>
  </FormSearch>
</template>

<script setup name="AppAdminSearch">
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { ElMessage } from 'element-plus';
import { reactive } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: undefined,
    dateType: undefined,
    from_date: undefined,
    to_date: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

const handleDataSearch = () => {
  if (form.dateRange !== null && form.dateRange.length !== 0 && form.queryParams.dateType === undefined) {
    ElMessage({
      message: '请选择时间类型',
      type: 'warning'
    });
    return;
  }
  if (form.queryParams.dateType === '1') {
    if (undefined != form.dateRange && form.dateRange.length > 0) {
      form.queryParams.from_date = form.dateRange[0];
      form.queryParams.to_date = form.dateRange[1];
    }
    if (null === form.dateRange) {
      form.queryParams.from_date = undefined;
      form.queryParams.to_date = undefined;
    }
  }

  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};

const handleAllReset = () => {
  (form.dateRange = []),
    (form.queryParams = {
      name: undefined,
      dateType: undefined,
      from_date: undefined,
      to_date: undefined,
      page: 1,
      limit: 30
    });
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
