/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询停车缴费
export const pagingParkFee = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/pagingParkPayRecords',
    method: 'post',
    data
  });
};

// 查询各项金额合计
export const countParkPayRecord = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/countParkPayRecord',
    method: 'post',
    data
  });
};

// 申请退款
export const applyRefund = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/refundApply',
    method: 'post',
    data
  });
};
// 导出停车缴费
export const exportParkFee = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/exportParkPayRecords',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};

// 通道列表
export const listParkGateway = (parkRegionId) => {
  return $({
    url: '/console/park/gateway/listParkGateway/' + parkRegionId,
    method: 'get'
  });
};
// 通道列表
export const listParkAccount = (parkId) => {
  return $({
    url: `/console/park/account/listParkAccount/${parkId}`,
    method: 'get'
  });
};