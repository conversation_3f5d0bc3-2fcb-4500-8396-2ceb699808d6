<template>
  <div class="container">
    <cancel-release-search @form-search="searchCancelReleaseList" @reset="resetParamsAndData" />
    <cancel-release-table ref="table" />
  </div>
</template>

<script setup name="CancelRelease">
import CancelReleaseSearch from './cancelRelease/CancelReleaseSearch.vue';
import CancelReleaseTable from './cancelRelease/CancelReleaseTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageCancelRelease = (queryParams) => {
  table.value.getList(queryParams);
};

const searchCancelReleaseList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageCancelRelease
});
</script>
