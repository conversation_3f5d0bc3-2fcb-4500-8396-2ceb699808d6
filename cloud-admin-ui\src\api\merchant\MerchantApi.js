/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 商户表格数据查询
export const pagingMerchants = (data) => {
  return $({
    url: '/console/coupon/merchant/pagingMerchants',
    method: 'post',
    data
  });
};

// 商户表格数据查找带回
export const findBackMerchants = (data) => {
  return $({
    url: '/console/coupon/merchant/findBackMerchants',
    method: 'post',
    data
  });
};
//新增商户;
export const createMerchant = (data) => {
  return $({
    url: '/console/coupon/merchant/createMerchant',
    method: 'post',
    data
  });
};

// 修改商户
export const updateMerchant = (data) => {
  return $({
    url: '/console/coupon/merchant/updateMerchant',
    method: 'post',
    data
  });
};

// 启用
export const enableMerchant = (id) => {
  return $({
    url: '/console/coupon/merchant/enableMerchant/' + id,
    method: 'post'
  });
};

// 停用
export const disableMerchant = (id) => {
  return $({
    url: '/console/coupon/merchant/disableMerchant/' + id,
    method: 'post'
  });
};

// 重置密码
export const resetPassword = (id) => {
  return $({
    url: '/console/coupon/merchant/resetPassword/' + id,
    method: 'post'
  });
};
// 根据车场id关联应用
export const parkByAppList = (parkId) => {
  return $({
    url: '/console/park/app/parkByAppList/' + parkId,
    method: 'post'
  });
};
