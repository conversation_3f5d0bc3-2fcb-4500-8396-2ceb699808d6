<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card">
      <!-- <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick"> -->
      <el-tab-pane label="临停收入全览" name="temporaryParkMoneyOverview">
        <temporary-park-money-overview ref="temporary" />
      </el-tab-pane>
      <el-tab-pane label="长租收入全览" name="longRentMoneyOverview">
        <long-rent-money-overview ref="longRent" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="MoneyOverview" setup>
import temporaryParkMoneyOverview from './TemporaryParkMoneyOverview.vue';
import longRentMoneyOverview from './LongRentMoneyOverview.vue';
import { ref, reactive } from 'vue';

const activeName = ref('temporaryParkMoneyOverview');
const temporary = ref(null);
const longRent = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

// onMounted(() => {
//   temporary.value.pageTemporaryPark(params);
// });

const handleClick = (tab) => {
  if (tab.props.name === 'temporaryParkMoneyOverview') {
    temporary.value.pageTemporaryPark(params);
  }
  if (tab.props.name === 'longRentMoneyOverview') {
    longRent.value.pageLongRent(params);
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 10px 10px 0px 10px;
  background-color: #f6f6f6;
}
</style>
