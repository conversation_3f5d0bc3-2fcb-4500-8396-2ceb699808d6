<template>
  <el-card class="table" shadow="never">
    <el-radio-group v-model="tabPosition">
      <el-radio-button value="列表">列表</el-radio-button>
      <el-radio-button value="图表">图表</el-radio-button>
    </el-radio-group>

    <div v-if="tabPosition == '列表'" ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <DownloadButton btnType="default" :exportFunc="ParkingSpaceOccupationService.exportData"
            :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
          </DownloadButton>
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 317px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="统计日期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ scope.row.statistics_date }}</span>
          </template>
        </el-table-column>
        <el-table-column label="星期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ scope.row.chinese_week }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车场名称" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.park_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车场ID" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.park_id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="大区" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.region_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="城市分公司" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.organizational_structure }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所在省份" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.province_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所在城市" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.city_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="所在区域" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.district_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="临停占用（%）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.temp_occupy_percent }}</span>
          </template>
        </el-table-column>
        <el-table-column label="长租占用（%）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.rent_occupy_percent }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总占用（%）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.total_occupy_percent }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar :tableData='tableData' :title="data.queryParams.park_name"></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%;height: 600px;background: #fff;line-height: 600px;text-align: center;">
        请选择停车场进行统计
      </div>
    </div>
  </el-card>
</template>

<script name="TimedAccessTable" setup>
import DownloadButton from '@/components/DownloadButton.vue';
import ParkingSpaceOccupationService from '@/service/statisticalReport/ParkingSpaceOccupationService';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import echartbar from './barChart.vue';
const tableData = ref([]);
const loading = ref(false);
const tabPosition = ref('列表')
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },

});
const parkId = ref(null);
// onMounted(() => {
//   getList(data.queryParams);
// });
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  ParkingSpaceOccupationService.getPlaceOccupyTablePage(params).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id;
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell>.cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
