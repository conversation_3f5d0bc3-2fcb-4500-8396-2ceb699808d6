/*
 * @Description:vite配置
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-01-25 14:41:47
 */
import { fileURLToPath, URL } from 'node:url';

import { defineConfig, loadEnv } from 'vite';
import { generateVitePlugins } from './config/vite/plugin';
import { wrapperEnv } from './config/utils';

// https://vitejs.dev/config/
export default defineConfig(({ command, mode }) => {
  const root = process.cwd();
  const isBuild = command === 'build';
  const env = loadEnv(mode, root);
  const viteEnv = wrapperEnv(env);
  return {
    plugins: [generateVitePlugins(viteEnv, isBuild)],
    integrity: true,
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      }
    },
    build: {
      chunkSizeWarningLimit: 1500,
      rollupOptions: {
        output: {
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
          assetFileNames: 'assets/[ext]/[name]-[hash].[ext]',
          sourcemap: !isBuild
        },
        // 插件打包做处理
        manualChunks(id) {
          if (id.includes('node_modules')) {
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
          return;
        }
      }
    },
    server: {
      port: 3000,
      open: true,
      host: true,
      proxy: {
        '/console': {
          target: env.VITE_PROXY_URL,
          changeOrigin: true
        },
        '/pay': {
          target: env.VITE_PROXY_URL,
          changeOrigin: true
        }
      }
    }
  };
});
