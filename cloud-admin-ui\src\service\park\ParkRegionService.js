import * as parkRegion from '@/api/park/ParkRegionApi';

/**
 * 车场
 */
export default {
  /**
   * 分页查询
   */
  pagingParkRegions(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRegion.pagingParkRegions(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 子场列表
   */
  listParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRegion.listParkRegion(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增子场
   */
  createParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRegion.createParkRegion(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改子场
   */
  updateParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRegion.updateParkRegion(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除子场
   */
  deleteParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRegion.deleteParkRegion(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
