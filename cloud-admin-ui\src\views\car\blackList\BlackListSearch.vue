<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item><el-input v-model="form.queryParams.park_name" placeholder="车场名称" readonly="true"
      @click="authCharge(true)" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="人员姓名" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.black_list_types" placeholder="黑名单类型" multiple clearable>
        <el-option v-for="item in blackListType" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="状态" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
    <!-- 关联车场 -->
    <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag"
      @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="BlackListSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import dictService from '@/service/system/DictService';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '@/views/car/ParkFindBack.vue';
import { reactive, ref, onMounted } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    plate_no: '',
    name: '',
    black_list_types: [],
    states: [],
    page: 1,
    limit: 30
  }
});
const blackListType = ref([]);
const states = ref([]);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumBlackWhiteListState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });

  dictService.getDictsList('BLACK_LIST_TYPE').then((response) => {
    blackListType.value = response;
  });
};

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: '',
    plate_no: '',
    name: '',
    black_list_types: [],
    states: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  console.log(val[0].park_name)
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
