<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="有车型" name="vehicleModel">
      <vehicle-model ref="hasVehicleModel" />
    </el-tab-pane>
    <el-tab-pane label="无车型" name="noVehicleModel">
      <no-vehicle-model ref="hasNoVehicleModel" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="PlateNoInAndOutTimes" setup>
import vehicleModel from './VehicleModel.vue';
import noVehicleModel from './NoVehicleModel.vue';
import { ref, reactive } from 'vue';

const activeName = ref('vehicleModel');
const hasVehicleModel = ref(null);
const hasNoVehicleModel = ref(null);
const params = reactive({});

// onMounted(() => {
//   hasVehicleModel.value.pageHasVehicleModel(params);
// });

const handleClick = (tab) => {
  if (tab.props.name === 'vehicleModel') {
    hasVehicleModel.value.runSearchRef();
  }
  if (tab.props.name === 'noVehicleModel') {
    hasNoVehicleModel.value.runSearchRef();
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
