import * as terminal from '@/api/park/ParkSentryTerminalApi';

/**
 * 终端
 */
export default {
  /**
   * 分页查询
   */
  pagingSentryTerminals(data) {
    return new Promise((resolve, reject) => {
      try {
        terminal.pagingSentryTerminals(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增终端
   */
  createSentryTerminal(data) {
    return new Promise((resolve, reject) => {
      try {
        terminal.createSentryTerminal(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改终端
   */
  updateSentryTerminal(data) {
    return new Promise((resolve, reject) => {
      try {
        terminal.updateSentryTerminal(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除终端
   */
  deleteSentryTerminal(data) {
    return new Promise((resolve, reject) => {
      try {
        terminal.deleteSentryTerminal(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
