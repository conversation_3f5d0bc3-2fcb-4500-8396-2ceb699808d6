/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找车场信息
export const pagingParks = (data) => {
  return $({
    url: '/console/park/park/pagingParks',
    method: 'post',
    data
  });
};

//车场详情数据查询;
export const getParkById = (id) => {
  return $({
    url: '/console/park/park/getParkById/' + id,
    method: 'get'
  });
};

// 新增车场信息
export const createPark = (data) => {
  return $({
    url: '/console/park/park/createPark',
    method: 'post',
    data
  });
};

// 车场信息修改
export const updatePark = (data) => {
  return $({
    url: '/console/park/park/updatePark',
    method: 'post',
    data
  });
};

// 启用车场
export const enablePark = (id) => {
  return $({
    url: '/console/park/park/enablePark/' + id,
    method: 'post'
  });
};

// 禁用车场
export const disablePark = (id) => {
  return $({
    url: '/console/park/park/disablePark/' + id,
    method: 'post'
  });
};
//获取小程序月卡办理枚举信息
export const getMiniProgramMonthCardEnum = (data) => {
  return $.post('/console/common/findEnums/park', data);
};
