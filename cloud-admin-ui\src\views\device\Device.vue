<template>
  <div class="container">
    <device-search @form-search="searchDeviceList" @reset="resetParamsAndData" />
    <device-table ref="table" />
  </div>
</template>

<script name="Device" setup>
import DeviceSearch from './device/DeviceSearch.vue';
import DeviceTable from './device/DeviceTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchDeviceList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
