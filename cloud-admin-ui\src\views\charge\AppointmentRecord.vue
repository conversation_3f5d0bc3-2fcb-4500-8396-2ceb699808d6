<template>
  <div class="container">
    <appointment-record-search @form-search="searchAppointmentRecordList" @reset="resetParamsAndData" />
    <appointment-record-table ref="table" />
  </div>
</template>

<script setup name="AppointmentRecord">
import AppointmentRecordSearch from './appointmentRecord/AppointmentRecordSearch.vue';
import AppointmentRecordTable from './appointmentRecord/AppointmentRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchAppointmentRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
