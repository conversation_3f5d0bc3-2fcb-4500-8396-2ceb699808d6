/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-s */
.el-input .el-input__inner {
  line-height: 29px;
}
/* 修复 Chrome 浏览器输入框内选中字符行高异常的bug-e */
.el-input__wrapper {
  border-radius: 2px;
}

.el-input-group__append,
.el-input-group__prepend {
  border-radius: 2px;
}

/* 按钮 */
.el-button {
  border-radius: 2px;
}

/* 日期选择 */
.datetime-picker {
  height: 32px;
  padding-top: 0;
  padding-bottom: 0;
}
.el-divider__text.is-center {
  transform: translateX(-50%) translateY(-62%);
}

/* 菜单 */
.el-menu {
  user-select: none;
}

/* 表格 */
.el-table .cell {
  font-size: 14px;
}

.el-table thead .el-table__cell {
  text-align: center;
  color: rgba(0, 0, 0, 0.5);
  background-color: #fafafa;
}

.el-table .el-button.is-link {
  line-height: 23px;
  height: 5px;
  padding: 0px 0px 2px 0px;
}

.el-table .el-button + .el-button {
  margin-left: 6px;
}

/* 卡片 */
.el-card {
  border: none;
  border-radius: 2px;
}
.el-card__header {
  padding: 5px 10px;
}
.el-card__body {
  padding: 10px;
}
.el-textarea__inner {
  padding: 5px 11px;
}

/* dialog滚动条-s */
.el-overlay-dialog {
  scrollbar-width: none;
  &::-webkit-scrollbar {
    width: 5px;
  }
  &::-webkit-scrollbar-thumb {
    background: #eaeaea;
    border-radius: var(--el-border-radius-base);
    box-shadow: none;
    -webkit-box-shadow: none;
  }
  &:hover {
    &::-webkit-scrollbar-thumb:hover {
      background: #c8c9cc;
    }
  }
}
/* dialog滚动条-e */

/* 弹出框 */
.el-popper__arrow::before {
  display: none;
}

/* 分页 */
.el-pagination__editor {
  min-width: min-content;
}

.el-pagination .el-select .el-input {
  width: 98px;
}

.el-pagination.is-background .el-pager li:not(.is-disabled).is-active {
  font-weight: 400;
}

/* 弹出层 */
.el-select__popper {
  border-radius: 2px;
}

/* 下拉菜单 */
.el-dropdown__popper {
  border-radius: 2px;
}
