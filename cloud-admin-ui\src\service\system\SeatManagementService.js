import * as seatManagementApi from '@/api/system/SeatManagementApi';

export default {
  /**
   * 坐席管理-列表
   */
  watchSeatsList(data) {
    return seatManagementApi.watchSeatsList(data);
  },
  /**
   * 坐席管理-新增
   */
  watchSeatsAdd(data) {
    return seatManagementApi.watchSeatsAdd(data);
  },
  /**
   * 坐席管理-启用/禁用
   */
  watchSeatsEditStatus(data) {
    return seatManagementApi.watchSeatsEditStatus(data);
  },
  /**
   * 坐席管理-分配值守员工
   */
  watchSeatsAssignEmployees(data) {
    return seatManagementApi.watchSeatsAssignEmployees(data);
  },
  /**
   * 坐席管理-查看详情
   */
  watchSeatsDetail(data) {
    return seatManagementApi.watchSeatsDetail(data);
  },
  /**
   * 坐席管理-查看详情
   */
  employeeList() {
    return seatManagementApi.employeeList();
  }
};
