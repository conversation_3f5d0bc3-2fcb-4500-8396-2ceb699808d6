<template>
  <api-search @form-search="searchApiList" @reset="resetParamsAndData" />
  <api-table ref="table" />  
</template>

<script name="Api" setup>
import ApiSearch from './api/ApiSearch.vue';
import ApiTable from './api/ApiTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchApiList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
