{"version": 3, "sources": ["../../print-js/dist/webpack:/printJS/webpack/universalModuleDefinition", "../../print-js/dist/webpack:/printJS/webpack/bootstrap", "../../print-js/dist/webpack:/printJS/src/index.js", "../../print-js/dist/webpack:/printJS/src/js/browser.js", "../../print-js/dist/webpack:/printJS/src/js/functions.js", "../../print-js/dist/webpack:/printJS/src/js/html.js", "../../print-js/dist/webpack:/printJS/src/js/image.js", "../../print-js/dist/webpack:/printJS/src/js/init.js", "../../print-js/dist/webpack:/printJS/src/js/json.js", "../../print-js/dist/webpack:/printJS/src/js/modal.js", "../../print-js/dist/webpack:/printJS/src/js/pdf.js", "../../print-js/dist/webpack:/printJS/src/js/print.js", "../../print-js/dist/webpack:/printJS/src/js/raw-html.js", "../../print-js/dist/webpack:/printJS/src/sass/index.scss"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"printJS\"] = factory();\n\telse\n\t\troot[\"printJS\"] = factory();\n})(window, function() {\nreturn ", " \t// The module cache\n \tvar installedModules = {};\n\n \t// The require function\n \tfunction __webpack_require__(moduleId) {\n\n \t\t// Check if module is in cache\n \t\tif(installedModules[moduleId]) {\n \t\t\treturn installedModules[moduleId].exports;\n \t\t}\n \t\t// Create a new module (and put it into the cache)\n \t\tvar module = installedModules[moduleId] = {\n \t\t\ti: moduleId,\n \t\t\tl: false,\n \t\t\texports: {}\n \t\t};\n\n \t\t// Execute the module function\n \t\tmodules[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n \t\t// Flag the module as loaded\n \t\tmodule.l = true;\n\n \t\t// Return the exports of the module\n \t\treturn module.exports;\n \t}\n\n\n \t// expose the modules object (__webpack_modules__)\n \t__webpack_require__.m = modules;\n\n \t// expose the module cache\n \t__webpack_require__.c = installedModules;\n\n \t// define getter function for harmony exports\n \t__webpack_require__.d = function(exports, name, getter) {\n \t\tif(!__webpack_require__.o(exports, name)) {\n \t\t\tObject.defineProperty(exports, name, { enumerable: true, get: getter });\n \t\t}\n \t};\n\n \t// define __esModule on exports\n \t__webpack_require__.r = function(exports) {\n \t\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n \t\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n \t\t}\n \t\tObject.defineProperty(exports, '__esModule', { value: true });\n \t};\n\n \t// create a fake namespace object\n \t// mode & 1: value is a module id, require it\n \t// mode & 2: merge all properties of value into the ns\n \t// mode & 4: return value when already ns object\n \t// mode & 8|1: behave like require\n \t__webpack_require__.t = function(value, mode) {\n \t\tif(mode & 1) value = __webpack_require__(value);\n \t\tif(mode & 8) return value;\n \t\tif((mode & 4) && typeof value === 'object' && value && value.__esModule) return value;\n \t\tvar ns = Object.create(null);\n \t\t__webpack_require__.r(ns);\n \t\tObject.defineProperty(ns, 'default', { enumerable: true, value: value });\n \t\tif(mode & 2 && typeof value != 'string') for(var key in value) __webpack_require__.d(ns, key, function(key) { return value[key]; }.bind(null, key));\n \t\treturn ns;\n \t};\n\n \t// getDefaultExport function for compatibility with non-harmony modules\n \t__webpack_require__.n = function(module) {\n \t\tvar getter = module && module.__esModule ?\n \t\t\tfunction getDefault() { return module['default']; } :\n \t\t\tfunction getModuleExports() { return module; };\n \t\t__webpack_require__.d(getter, 'a', getter);\n \t\treturn getter;\n \t};\n\n \t// Object.prototype.hasOwnProperty.call\n \t__webpack_require__.o = function(object, property) { return Object.prototype.hasOwnProperty.call(object, property); };\n\n \t// __webpack_public_path__\n \t__webpack_require__.p = \"\";\n\n\n \t// Load entry module and return exports\n \treturn __webpack_require__(__webpack_require__.s = 0);\n", "import './sass/index.scss'\nimport print from './js/init'\n\nconst printJS = print.init\n\nif (typeof window !== 'undefined') {\n  window.printJS = printJS\n}\n\nexport default printJS\n", "const Browser = {\n  // Firefox 1.0+\n  isFirefox: () => {\n    return typeof InstallTrigger !== 'undefined'\n  },\n  // Internet Explorer 6-11\n  isIE: () => {\n    return navigator.userAgent.indexOf('MSIE') !== -1 || !!document.documentMode\n  },\n  // Edge 20+\n  isEdge: () => {\n    return !Browser.isIE() && !!window.StyleMedia\n  },\n  // Chrome 1+\n  isChrome: (context = window) => {\n    return !!context.chrome\n  },\n  // At least Safari 3+: \"[object HTMLElementConstructor]\"\n  isSafari: () => {\n    return Object.prototype.toString.call(window.HTMLElement).indexOf('Constructor') > 0 ||\n        navigator.userAgent.toLowerCase().indexOf('safari') !== -1\n  },\n  // IOS Chrome\n  isIOSChrome: () => {\n    return navigator.userAgent.toLowerCase().indexOf('crios') !== -1\n  }\n}\n\nexport default Browser\n", "import Modal from './modal'\nimport Browser from './browser'\n\nexport function addWrapper (htmlData, params) {\n  const bodyStyle = 'font-family:' + params.font + ' !important; font-size: ' + params.font_size + ' !important; width:100%;'\n  return '<div style=\"' + bodyStyle + '\">' + htmlData + '</div>'\n}\n\nexport function capitalizePrint (obj) {\n  return obj.charAt(0).toUpperCase() + obj.slice(1)\n}\n\nexport function collectStyles (element, params) {\n  const win = document.defaultView || window\n\n  // String variable to hold styling for each element\n  let elementStyle = ''\n\n  // Loop over computed styles\n  const styles = win.getComputedStyle(element, '')\n\n  for (let key = 0; key < styles.length; key++) {\n    // Check if style should be processed\n    if (params.targetStyles.indexOf('*') !== -1 || params.targetStyle.indexOf(styles[key]) !== -1 || targetStylesMatch(params.targetStyles, styles[key])) {\n      if (styles.getPropertyValue(styles[key])) elementStyle += styles[key] + ':' + styles.getPropertyValue(styles[key]) + ';'\n    }\n  }\n\n  // Print friendly defaults (deprecated)\n  elementStyle += 'max-width: ' + params.maxWidth + 'px !important; font-size: ' + params.font_size + ' !important;'\n\n  return elementStyle\n}\n\nfunction targetStylesMatch (styles, value) {\n  for (let i = 0; i < styles.length; i++) {\n    if (typeof value === 'object' && value.indexOf(styles[i]) !== -1) return true\n  }\n  return false\n}\n\nexport function addHeader (printElement, params) {\n  // Create the header container div\n  const headerContainer = document.createElement('div')\n\n  // Check if the header is text or raw html\n  if (isRawHTML(params.header)) {\n    headerContainer.innerHTML = params.header\n  } else {\n    // Create header element\n    const headerElement = document.createElement('h1')\n\n    // Create header text node\n    const headerNode = document.createTextNode(params.header)\n\n    // Build and style\n    headerElement.appendChild(headerNode)\n    headerElement.setAttribute('style', params.headerStyle)\n    headerContainer.appendChild(headerElement)\n  }\n\n  printElement.insertBefore(headerContainer, printElement.childNodes[0])\n}\n\nexport function cleanUp (params) {\n  // If we are showing a feedback message to user, remove it\n  if (params.showModal) Modal.close()\n\n  // Check for a finished loading hook function\n  if (params.onLoadingEnd) params.onLoadingEnd()\n\n  // If preloading pdf files, clean blob url\n  if (params.showModal || params.onLoadingStart) window.URL.revokeObjectURL(params.printable)\n\n  // Run onPrintDialogClose callback\n  let event = 'mouseover'\n\n  if (Browser.isChrome() || Browser.isFirefox()) {\n    // Ps.: Firefox will require an extra click in the document to fire the focus event.\n    event = 'focus'\n  }\n\n  const handler = () => {\n    // Make sure the event only happens once.\n    window.removeEventListener(event, handler)\n\n    params.onPrintDialogClose()\n\n    // Remove iframe from the DOM\n    const iframe = document.getElementById(params.frameId)\n\n    if (iframe) {\n      iframe.remove()\n    }\n  }\n\n  window.addEventListener(event, handler)\n}\n\nexport function isRawHTML (raw) {\n  const regexHtml = new RegExp('<([A-Za-z][A-Za-z0-9]*)\\\\b[^>]*>(.*?)</\\\\1>')\n  return regexHtml.test(raw)\n}\n", "import { collectStyles, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Get the DOM printable element\n    const printElement = isHtmlElement(params.printable) ? params.printable : document.getElementById(params.printable)\n\n    // Check if the element exists\n    if (!printElement) {\n      window.console.error('Invalid HTML element id: ' + params.printable)\n      return\n    }\n\n    // Clone the target element including its children (if available)\n    params.printableElement = cloneElement(printElement, params)\n\n    // Add header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Print html element contents\n    Print.send(params, printFrame)\n  }\n}\n\nfunction cloneElement (element, params) {\n  // Clone the main node (if not already inside the recursion process)\n  const clone = element.cloneNode()\n\n  // Loop over and process the children elements / nodes (including text nodes)\n  const childNodesArray = Array.prototype.slice.call(element.childNodes)\n  for (let i = 0; i < childNodesArray.length; i++) {\n    // Check if we are skipping the current element\n    if (params.ignoreElements.indexOf(childNodesArray[i].id) !== -1) {\n      continue\n    }\n\n    // Clone the child element\n    const clonedChild = cloneElement(childNodesArray[i], params)\n\n    // Attach the cloned child to the cloned parent node\n    clone.appendChild(clonedChild)\n  }\n\n  // Get all styling for print element (for nodes of type element only)\n  if (params.scanStyles && element.nodeType === 1) {\n    clone.setAttribute('style', collectStyles(element, params))\n  }\n\n  // Check if the element needs any state processing (copy user input data)\n  switch (element.tagName) {\n    case 'SELECT':\n      // Copy the current selection value to its clone\n      clone.value = element.value\n      break\n    case 'CANVAS':\n      // Copy the canvas content to its clone\n      clone.getContext('2d').drawImage(element, 0, 0)\n      break\n  }\n\n  return clone\n}\n\nfunction isHtmlElement (printable) {\n  // Check if element is instance of HTMLElement or has nodeType === 1 (for elements in iframe)\n  return typeof printable === 'object' && printable && (printable instanceof HTMLElement || printable.nodeType === 1)\n}\n", "import { addHeader } from './functions'\nimport Print from './print'\nimport Browser from './browser'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we are printing one image or multiple images\n    if (params.printable.constructor !== Array) {\n      // Create array with one image\n      params.printable = [params.printable]\n    }\n\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n\n    // Create all image elements and append them to the printable container\n    params.printable.forEach(src => {\n      // Create the image element\n      const img = document.createElement('img')\n      img.setAttribute('style', params.imageStyle)\n\n      // Set image src with the file url\n      img.src = src\n\n      // The following block is for Firefox, which for some reason requires the image's src to be fully qualified in\n      // order to print it\n      if (Browser.isFirefox()) {\n        const fullyQualifiedSrc = img.src\n        img.src = fullyQualifiedSrc\n      }\n\n      // Create the image wrapper\n      const imageWrapper = document.createElement('div')\n\n      // Append image to the wrapper element\n      imageWrapper.appendChild(img)\n\n      // Append wrapper to the printable element\n      params.printableElement.appendChild(imageWrapper)\n    })\n\n    // Check if we are adding a print header\n    if (params.header) addHeader(params.printableElement, params)\n\n    // Print image\n    Print.send(params, printFrame)\n  }\n}\n", "'use strict'\n\nimport Browser from './browser'\nimport Modal from './modal'\nimport Pdf from './pdf'\nimport Html from './html'\nimport RawHtml from './raw-html'\nimport Image from './image'\nimport J<PERSON> from './json'\n\nconst printTypes = ['pdf', 'html', 'image', 'json', 'raw-html']\n\nexport default {\n  init () {\n    const params = {\n      printable: null,\n      fallbackPrintable: null,\n      type: 'pdf',\n      header: null,\n      headerStyle: 'font-weight: 300;',\n      maxWidth: 800,\n      properties: null,\n      gridHeaderStyle: 'font-weight: bold; padding: 5px; border: 1px solid #dddddd;',\n      gridStyle: 'border: 1px solid lightgray; margin-bottom: -1px;',\n      showModal: false,\n      onError: (error) => { throw error },\n      onLoadingStart: null,\n      onLoadingEnd: null,\n      onPrintDialogClose: () => {},\n      onIncompatibleBrowser: () => {},\n      modalMessage: 'Retrieving Document...',\n      frameId: 'printJS',\n      printableElement: null,\n      documentTitle: 'Document',\n      targetStyle: ['clear', 'display', 'width', 'min-width', 'height', 'min-height', 'max-height'],\n      targetStyles: ['border', 'box', 'break', 'text-decoration'],\n      ignoreElements: [],\n      repeatTableHeader: true,\n      css: null,\n      style: null,\n      scanStyles: true,\n      base64: false,\n\n      // Deprecated\n      onPdfOpen: null,\n      font: 'TimesNewRoman',\n      font_size: '12pt',\n      honorMarginPadding: true,\n      honorColor: false,\n      imageStyle: 'max-width: 100%;'\n    }\n\n    // Check if a printable document or object was supplied\n    const args = arguments[0]\n    if (args === undefined) {\n      throw new Error('printJS expects at least 1 attribute.')\n    }\n\n    // Process parameters\n    switch (typeof args) {\n      case 'string':\n        params.printable = encodeURI(args)\n        params.fallbackPrintable = params.printable\n        params.type = arguments[1] || params.type\n        break\n      case 'object':\n        params.printable = args.printable\n        params.fallbackPrintable = typeof args.fallbackPrintable !== 'undefined' ? args.fallbackPrintable : params.printable\n        params.fallbackPrintable = params.base64 ? `data:application/pdf;base64,${params.fallbackPrintable}` : params.fallbackPrintable\n        for (var k in params) {\n          if (k === 'printable' || k === 'fallbackPrintable') continue\n\n          params[k] = typeof args[k] !== 'undefined' ? args[k] : params[k]\n        }\n        break\n      default:\n        throw new Error('Unexpected argument type! Expected \"string\" or \"object\", got ' + typeof args)\n    }\n\n    // Validate printable\n    if (!params.printable) throw new Error('Missing printable information.')\n\n    // Validate type\n    if (!params.type || typeof params.type !== 'string' || printTypes.indexOf(params.type.toLowerCase()) === -1) {\n      throw new Error('Invalid print type. Available types are: pdf, html, image and json.')\n    }\n\n    // Check if we are showing a feedback message to the user (useful for large files)\n    if (params.showModal) Modal.show(params)\n\n    // Check for a print start hook function\n    if (params.onLoadingStart) params.onLoadingStart()\n\n    // To prevent duplication and issues, remove any used printFrame from the DOM\n    const usedFrame = document.getElementById(params.frameId)\n\n    if (usedFrame) usedFrame.parentNode.removeChild(usedFrame)\n\n    // Create a new iframe for the print job\n    const printFrame = document.createElement('iframe')\n\n    if (Browser.isFirefox()) {\n      // Set the iframe to be is visible on the page (guaranteed by fixed position) but hidden using opacity 0, because\n      // this works in Firefox. The height needs to be sufficient for some part of the document other than the PDF\n      // viewer's toolbar to be visible in the page\n      printFrame.setAttribute('style', 'width: 1px; height: 100px; position: fixed; left: 0; top: 0; opacity: 0; border-width: 0; margin: 0; padding: 0')\n    } else {\n      // Hide the iframe in other browsers\n      printFrame.setAttribute('style', 'visibility: hidden; height: 0; width: 0; position: absolute; border: 0')\n    }\n\n    // Set iframe element id\n    printFrame.setAttribute('id', params.frameId)\n\n    // For non pdf printing, pass an html document string to srcdoc (force onload callback)\n    if (params.type !== 'pdf') {\n      printFrame.srcdoc = '<html><head><title>' + params.documentTitle + '</title>'\n\n      // Attach css files\n      if (params.css) {\n        // Add support for single file\n        if (!Array.isArray(params.css)) params.css = [params.css]\n\n        // Create link tags for each css file\n        params.css.forEach(file => {\n          printFrame.srcdoc += '<link rel=\"stylesheet\" href=\"' + file + '\">'\n        })\n      }\n\n      printFrame.srcdoc += '</head><body></body></html>'\n    }\n\n    // Check printable type\n    switch (params.type) {\n      case 'pdf':\n        // Check browser support for pdf and if not supported we will just open the pdf file instead\n        if (Browser.isIE()) {\n          try {\n            console.info('Print.js doesn\\'t support PDF printing in Internet Explorer.')\n            const win = window.open(params.fallbackPrintable, '_blank')\n            win.focus()\n            params.onIncompatibleBrowser()\n          } catch (error) {\n            params.onError(error)\n          } finally {\n            // Make sure there is no loading modal opened\n            if (params.showModal) Modal.close()\n            if (params.onLoadingEnd) params.onLoadingEnd()\n          }\n        } else {\n          Pdf.print(params, printFrame)\n        }\n        break\n      case 'image':\n        Image.print(params, printFrame)\n        break\n      case 'html':\n        Html.print(params, printFrame)\n        break\n      case 'raw-html':\n        RawHtml.print(params, printFrame)\n        break\n      case 'json':\n        Json.print(params, printFrame)\n        break\n    }\n  }\n}\n", "import { capitalizePrint, addHeader } from './functions'\nimport Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we received proper data\n    if (typeof params.printable !== 'object') {\n      throw new Error('Invalid javascript data object (JSON).')\n    }\n\n    // Validate repeatTableHeader\n    if (typeof params.repeatTableHeader !== 'boolean') {\n      throw new Error('Invalid value for repeatTableHeader attribute (JSON).')\n    }\n\n    // Validate properties\n    if (!params.properties || !Array.isArray(params.properties)) {\n      throw new Error('Invalid properties array for your JSON data.')\n    }\n\n    // We will format the property objects to keep the JSON api compatible with older releases\n    params.properties = params.properties.map(property => {\n      return {\n        field: typeof property === 'object' ? property.field : property,\n        displayName: typeof property === 'object' ? property.displayName : property,\n        columnSize: typeof property === 'object' && property.columnSize ? property.columnSize + ';' : 100 / params.properties.length + '%;'\n      }\n    })\n\n    // Create a print container element\n    params.printableElement = document.createElement('div')\n\n    // Check if we are adding a print header\n    if (params.header) {\n      addHeader(params.printableElement, params)\n    }\n\n    // Build the printable html data\n    params.printableElement.innerHTML += jsonToHTML(params)\n\n    // Print the json data\n    Print.send(params, printFrame)\n  }\n}\n\nfunction jsonToHTML (params) {\n  // Get the row and column data\n  const data = params.printable\n  const properties = params.properties\n\n  // Create a html table\n  let htmlData = '<table style=\"border-collapse: collapse; width: 100%;\">'\n\n  // Check if the header should be repeated\n  if (params.repeatTableHeader) {\n    htmlData += '<thead>'\n  }\n\n  // Add the table header row\n  htmlData += '<tr>'\n\n  // Add the table header columns\n  for (let a = 0; a < properties.length; a++) {\n    htmlData += '<th style=\"width:' + properties[a].columnSize + ';' + params.gridHeaderStyle + '\">' + capitalizePrint(properties[a].displayName) + '</th>'\n  }\n\n  // Add the closing tag for the table header row\n  htmlData += '</tr>'\n\n  // If the table header is marked as repeated, add the closing tag\n  if (params.repeatTableHeader) {\n    htmlData += '</thead>'\n  }\n\n  // Create the table body\n  htmlData += '<tbody>'\n\n  // Add the table data rows\n  for (let i = 0; i < data.length; i++) {\n    // Add the row starting tag\n    htmlData += '<tr>'\n\n    // Print selected properties only\n    for (let n = 0; n < properties.length; n++) {\n      let stringData = data[i]\n\n      // Support nested objects\n      const property = properties[n].field.split('.')\n      if (property.length > 1) {\n        for (let p = 0; p < property.length; p++) {\n          stringData = stringData[property[p]]\n        }\n      } else {\n        stringData = stringData[properties[n].field]\n      }\n\n      // Add the row contents and styles\n      htmlData += '<td style=\"width:' + properties[n].columnSize + params.gridStyle + '\">' + stringData + '</td>'\n    }\n\n    // Add the row closing tag\n    htmlData += '</tr>'\n  }\n\n  // Add the table and body closing tags\n  htmlData += '</tbody></table>'\n\n  return htmlData\n}\n", "const Modal = {\n  show (params) {\n    // Build modal\n    const modalStyle = 'font-family:sans-serif; ' +\n        'display:table; ' +\n        'text-align:center; ' +\n        'font-weight:300; ' +\n        'font-size:30px; ' +\n        'left:0; top:0;' +\n        'position:fixed; ' +\n        'z-index: 9990;' +\n        'color: #0460B5; ' +\n        'width: 100%; ' +\n        'height: 100%; ' +\n        'background-color:rgba(255,255,255,.9);' +\n        'transition: opacity .3s ease;'\n\n    // Create wrapper\n    const printModal = document.createElement('div')\n    printModal.setAttribute('style', modalStyle)\n    printModal.setAttribute('id', 'printJS-Modal')\n\n    // Create content div\n    const contentDiv = document.createElement('div')\n    contentDiv.setAttribute('style', 'display:table-cell; vertical-align:middle; padding-bottom:100px;')\n\n    // Add close button (requires print.css)\n    const closeButton = document.createElement('div')\n    closeButton.setAttribute('class', 'printClose')\n    closeButton.setAttribute('id', 'printClose')\n    contentDiv.appendChild(closeButton)\n\n    // Add spinner (requires print.css)\n    const spinner = document.createElement('span')\n    spinner.setAttribute('class', 'printSpinner')\n    contentDiv.appendChild(spinner)\n\n    // Add message\n    const messageNode = document.createTextNode(params.modalMessage)\n    contentDiv.appendChild(messageNode)\n\n    // Add contentDiv to printModal\n    printModal.appendChild(contentDiv)\n\n    // Append print modal element to document body\n    document.getElementsByTagName('body')[0].appendChild(printModal)\n\n    // Add event listener to close button\n    document.getElementById('printClose').addEventListener('click', function () {\n      Modal.close()\n    })\n  },\n  close () {\n    const printModal = document.getElementById('printJS-Modal')\n\n    if (printModal) {\n      printModal.parentNode.removeChild(printModal)\n    }\n  }\n}\n\nexport default Modal\n", "import Print from './print'\nimport { cleanUp } from './functions'\n\nexport default {\n  print: (params, printFrame) => {\n    // Check if we have base64 data\n    if (params.base64) {\n      const bytesArray = Uint8Array.from(atob(params.printable), c => c.charCodeAt(0))\n      createBlobAndPrint(params, printFrame, bytesArray)\n      return\n    }\n\n    // Format pdf url\n    params.printable = /^(blob|http|\\/\\/)/i.test(params.printable)\n      ? params.printable\n      : window.location.origin + (params.printable.charAt(0) !== '/' ? '/' + params.printable : params.printable)\n\n    // Get the file through a http request (Preload)\n    const req = new window.XMLHttpRequest()\n    req.responseType = 'arraybuffer'\n\n    req.addEventListener('error', () => {\n      cleanUp(params)\n      params.onError(req.statusText, req)\n\n      // Since we don't have a pdf document available, we will stop the print job\n    })\n\n    req.addEventListener('load', () => {\n      // Check for errors\n      if ([200, 201].indexOf(req.status) === -1) {\n        cleanUp(params)\n        params.onError(req.statusText, req)\n\n        // Since we don't have a pdf document available, we will stop the print job\n        return\n      }\n\n      // Print requested document\n      createBlobAndPrint(params, printFrame, req.response)\n    })\n\n    req.open('GET', params.printable, true)\n    req.send()\n  }\n}\n\nfunction createBlobAndPrint (params, printFrame, data) {\n  // Pass response or base64 data to a blob and create a local object url\n  let localPdf = new window.Blob([data], { type: 'application/pdf' })\n  localPdf = window.URL.createObjectURL(localPdf)\n\n  // Set iframe src with pdf document url\n  printFrame.setAttribute('src', localPdf)\n\n  Print.send(params, printFrame)\n}\n", "import Browser from './browser'\nimport { cleanUp } from './functions'\n\nconst Print = {\n  send: (params, printFrame) => {\n    // Append iframe element to document body\n    document.getElementsByTagName('body')[0].appendChild(printFrame)\n\n    // Get iframe element\n    const iframeElement = document.getElementById(params.frameId)\n\n    // Wait for iframe to load all content\n    iframeElement.onload = () => {\n      if (params.type === 'pdf') {\n        // Add a delay for Firefox. In my tests, 1000ms was sufficient but 100ms was not\n        if (Browser.isFirefox()) {\n          setTimeout(() => performPrint(iframeElement, params), 1000)\n        } else {\n          performPrint(iframeElement, params)\n        }\n        return\n      }\n\n      // Get iframe element document\n      let printDocument = (iframeElement.contentWindow || iframeElement.contentDocument)\n      if (printDocument.document) printDocument = printDocument.document\n\n      // Append printable element to the iframe body\n      printDocument.body.appendChild(params.printableElement)\n\n      // Add custom style\n      if (params.type !== 'pdf' && params.style) {\n        // Create style element\n        const style = document.createElement('style')\n        style.innerHTML = params.style\n\n        // Append style element to iframe's head\n        printDocument.head.appendChild(style)\n      }\n\n      // If printing images, wait for them to load inside the iframe\n      const images = printDocument.getElementsByTagName('img')\n\n      if (images.length > 0) {\n        loadIframeImages(Array.from(images)).then(() => performPrint(iframeElement, params))\n      } else {\n        performPrint(iframeElement, params)\n      }\n    }\n  }\n}\n\nfunction performPrint (iframeElement, params) {\n  try {\n    iframeElement.focus()\n\n    // If Edge or IE, try catch with execCommand\n    if (Browser.isEdge() || Browser.isIE()) {\n      try {\n        iframeElement.contentWindow.document.execCommand('print', false, null)\n      } catch (e) {\n        iframeElement.contentWindow.print()\n      }\n    } else {\n      // Other browsers\n      iframeElement.contentWindow.print()\n    }\n  } catch (error) {\n    params.onError(error)\n  } finally {\n    if (Browser.isFirefox()) {\n      // Move the iframe element off-screen and make it invisible\n      iframeElement.style.visibility = 'hidden'\n      iframeElement.style.left = '-1px'\n    }\n\n    cleanUp(params)\n  }\n}\n\nfunction loadIframeImages (images) {\n  const promises = images.map(image => {\n    if (image.src && image.src !== window.location.href) {\n      return loadIframeImage(image)\n    }\n  })\n\n  return Promise.all(promises)\n}\n\nfunction loadIframeImage (image) {\n  return new Promise(resolve => {\n    const pollImage = () => {\n      !image || typeof image.naturalWidth === 'undefined' || image.naturalWidth === 0 || !image.complete\n        ? setTimeout(pollImage, 500)\n        : resolve()\n    }\n    pollImage()\n  })\n}\n\nexport default Print\n", "import Print from './print'\n\nexport default {\n  print: (params, printFrame) => {\n    // Create printable element (container)\n    params.printableElement = document.createElement('div')\n    params.printableElement.setAttribute('style', 'width:100%')\n\n    // Set our raw html as the printable element inner html content\n    params.printableElement.innerHTML = params.printable\n\n    // Print html contents\n    Print.send(params, printFrame)\n  }\n}\n", "// extracted by mini-css-extract-plugin"], "mappings": ";;;;;AAAA;;KAAA,SAAA,iCAAA,MAAA,SAAA;AACA,UAAA,OAAA,YAAA,YAAA,OAAA,WAAA;AACA,eAAA,UAAA,QAAA;eACA,OAAA,WAAA,cAAA,OAAA;AACA,eAAA,CAAA,GAAA,OAAA;eACA,OAAA,YAAA;AACA,gBAAA,aAAA,QAAA;;AAEA,aAAA,aAAA,QAAA;IACA,GAAC,QAAA,WAAA;AACD,aAAA,SAAA,SAAA;ACTA,YAAA,mBAAA,CAAA;AAGA,iBAAA,oBAAA,UAAA;AAGA,cAAA,iBAAA,WAAA;AACA,mBAAA,iBAAA,UAAA;UACA;AAEA,cAAAA,UAAA,iBAAA,YAAA;YACA,GAAA;YACA,GAAA;YACA,SAAA,CAAA;UACA;AAGA,kBAAA,UAAA,KAAAA,QAAA,SAAAA,SAAAA,QAAA,SAAA,mBAAA;AAGA,UAAAA,QAAA,IAAA;AAGA,iBAAAA,QAAA;QACA;AAIA,4BAAA,IAAA;AAGA,4BAAA,IAAA;AAGA,4BAAA,IAAA,SAAAC,UAAA,MAAA,QAAA;AACA,cAAA,CAAA,oBAAA,EAAAA,UAAA,IAAA,GAAA;AACA,mBAAA,eAAAA,UAAA,MAAA,EAA0C,YAAA,MAAA,KAAA,OAAA,CAAgC;UAC1E;QACA;AAGA,4BAAA,IAAA,SAAAA,UAAA;AACA,cAAA,OAAA,WAAA,eAAA,OAAA,aAAA;AACA,mBAAA,eAAAA,UAAA,OAAA,aAAA,EAAwD,OAAA,SAAA,CAAkB;UAC1E;AACA,iBAAA,eAAAA,UAAA,cAAA,EAAiD,OAAA,KAAA,CAAc;QAC/D;AAOA,4BAAA,IAAA,SAAA,OAAA,MAAA;AACA,cAAA,OAAA;AAAA,oBAAA,oBAAA,KAAA;AACA,cAAA,OAAA;AAAA,mBAAA;AACA,cAAA,OAAA,KAAA,OAAA,UAAA,YAAA,SAAA,MAAA;AAAA,mBAAA;AACA,cAAA,KAAA,uBAAA,OAAA,IAAA;AACA,8BAAA,EAAA,EAAA;AACA,iBAAA,eAAA,IAAA,WAAA,EAAyC,YAAA,MAAA,MAAA,CAAiC;AAC1E,cAAA,OAAA,KAAA,OAAA,SAAA;AAAA,qBAAA,OAAA;AAAA,kCAAA,EAAA,IAAA,KAAA,SAAAC,MAAA;AAAgH,uBAAA,MAAAA;cAAmB,EAAE,KAAA,MAAA,GAAA,CAAA;AACrI,iBAAA;QACA;AAGA,4BAAA,IAAA,SAAAF,SAAA;AACA,cAAA,SAAAA,WAAAA,QAAA,aACA,SAAA,aAAA;AAA2B,mBAAAA,QAAA;UAA0B,IACrD,SAAA,mBAAA;AAAiC,mBAAAA;UAAe;AAChD,8BAAA,EAAA,QAAA,KAAA,MAAA;AACA,iBAAA;QACA;AAGA,4BAAA,IAAA,SAAA,QAAA,UAAA;AAAsD,iBAAA,OAAA,UAAA,eAAA,KAAA,QAAA,QAAA;QAA+D;AAGrH,4BAAA,IAAA;AAIA,eAAA,oBAAA,oBAAA,IAAA,CAAA;;;;AClFA,8BAAA,EAAA,mBAAA;AAAA,cAAA,gDAAA,oBAAA,uBAAA;AAAA,cAAA,wDAAA,oBAAA,EAAA,6CAAA;AAAA,cAAA,wCAAA,oBAAA,kBAAA;AAGA,cAAMG,UAAUC,sCAAAA,WAAMC;AAEtB,cAAI,OAAOC,WAAW,aAAa;AACjCA,mBAAOH,UAAUA;UAClB;AAEcA,8BAAAA,aAAAA;;;;ACTf,8BAAA,EAAA,mBAAA;AAAA,cAAMI,UAAU;YAEdC,WAAW,SAAA,YAAM;AACf,qBAAO,OAAOC,mBAAmB;YAClC;YAEDC,MAAM,SAAA,OAAM;AACV,qBAAOC,UAAUC,UAAUC,QAAQ,MAA5B,MAAwC,MAAM,CAAC,CAACC,SAASC;YACjE;YAEDC,QAAQ,SAAA,SAAM;AACZ,qBAAO,CAACT,QAAQG,KAAR,KAAkB,CAAC,CAACJ,OAAOW;YACpC;YAEDC,UAAU,SAAA,WAAsB;AAAA,kBAArBC,UAAqB,UAAA,SAAA,KAAA,UAAA,OAAA,SAAA,UAAA,KAAXb;AACnB,qBAAO,CAAC,CAACa,QAAQC;YAClB;YAEDC,UAAU,SAAA,WAAM;AACd,qBAAOC,OAAOC,UAAUC,SAASC,KAAKnB,OAAOoB,WAAtC,EAAmDb,QAAQ,aAA3D,IAA4E,KAC/EF,UAAUC,UAAUe,YAApB,EAAkCd,QAAQ,QAA1C,MAAwD;YAC7D;YAEDe,aAAa,SAAA,cAAM;AACjB,qBAAOjB,UAAUC,UAAUe,YAApB,EAAkCd,QAAQ,OAA1C,MAAuD;YAC/D;UAzBa;AA4BDN,8BAAAA,aAAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzBR,mBAASsB,WAAYC,UAAUC,QAAQ;AAC5C,gBAAMC,YAAY,iBAAiBD,OAAOE,OAAO,6BAA6BF,OAAOG,YAAY;AACjG,mBAAO,iBAAiBF,YAAY,OAAOF,WAAW;UACvD;AAEM,mBAASK,gBAAiBC,KAAK;AACpC,mBAAOA,IAAIC,OAAO,CAAX,EAAcC,YAAd,IAA8BF,IAAIG,MAAM,CAAV;UACtC;AAEM,mBAASC,cAAeC,SAASV,QAAQ;AAC9C,gBAAMW,MAAM5B,SAAS6B,eAAerC;AAGpC,gBAAIsC,eAAe;AAGnB,gBAAMC,SAASH,IAAII,iBAAiBL,SAAS,EAA9B;AAEf,qBAASvC,MAAM,GAAGA,MAAM2C,OAAOE,QAAQ7C,OAAO;AAE5C,kBAAI6B,OAAOiB,aAAanC,QAAQ,GAA5B,MAAqC,MAAMkB,OAAOkB,YAAYpC,QAAQgC,OAAO3C,IAAlC,MAA4C,MAAMgD,kBAAkBnB,OAAOiB,cAAcH,OAAO3C,IAA7B,GAAoC;AACpJ,oBAAI2C,OAAOM,iBAAiBN,OAAO3C,IAA/B;AAAsC0C,kCAAgBC,OAAO3C,OAAO,MAAM2C,OAAOM,iBAAiBN,OAAO3C,IAA/B,IAAuC;cACtH;YACF;AAGD0C,4BAAgB,gBAAgBb,OAAOqB,WAAW,+BAA+BrB,OAAOG,YAAY;AAEpG,mBAAOU;UACR;AAED,mBAASM,kBAAmBL,QAAQQ,OAAO;AACzC,qBAASC,IAAI,GAAGA,IAAIT,OAAOE,QAAQO,KAAK;AACtC,kBAAI,QAAOD,KAAP,MAAiB,YAAYA,MAAMxC,QAAQgC,OAAOS,EAArB,MAA6B;AAAI,uBAAO;YAC1E;AACD,mBAAO;UACR;AAEM,mBAASC,UAAWC,cAAczB,QAAQ;AAE/C,gBAAM0B,kBAAkB3C,SAAS4C,cAAc,KAAvB;AAGxB,gBAAIC,UAAU5B,OAAO6B,MAAR,GAAiB;AAC5BH,8BAAgBI,YAAY9B,OAAO6B;YACpC,OAAM;AAEL,kBAAME,gBAAgBhD,SAAS4C,cAAc,IAAvB;AAGtB,kBAAMK,aAAajD,SAASkD,eAAejC,OAAO6B,MAA/B;AAGnBE,4BAAcG,YAAYF,UAA1B;AACAD,4BAAcI,aAAa,SAASnC,OAAOoC,WAA3C;AACAV,8BAAgBQ,YAAYH,aAA5B;YACD;AAEDN,yBAAaY,aAAaX,iBAAiBD,aAAaa,WAAW,EAAnE;UACD;AAEM,mBAASC,QAASvC,QAAQ;AAE/B,gBAAIA,OAAOwC;AAAWC,kDAAAA,WAAMC,MAAN;AAGtB,gBAAI1C,OAAO2C;AAAc3C,qBAAO2C,aAAP;AAGzB,gBAAI3C,OAAOwC,aAAaxC,OAAO4C;AAAgBrE,qBAAOsE,IAAIC,gBAAgB9C,OAAO+C,SAAlC;AAG/C,gBAAIC,QAAQ;AAEZ,gBAAIxE,sCAAAA,WAAQW,SAAR,KAAsBX,sCAAAA,WAAQC,UAAR,GAAqB;AAE7CuE,sBAAQ;YACT;AAED,gBAAMC,UAAU,SAAVA,WAAgB;AAEpB1E,qBAAO2E,oBAAoBF,OAAOC,QAAlC;AAEAjD,qBAAOmD,mBAAP;AAGA,kBAAMC,SAASrE,SAASsE,eAAerD,OAAOsD,OAA/B;AAEf,kBAAIF,QAAQ;AACVA,uBAAOG,OAAP;cACD;YACF;AAEDhF,mBAAOiF,iBAAiBR,OAAOC,OAA/B;UACD;AAEM,mBAASrB,UAAW6B,KAAK;AAC9B,gBAAMC,YAAY,IAAIC,OAAO,6CAAX;AAClB,mBAAOD,UAAUE,KAAKH,GAAf;UACR;;;;;;;;;;;;;;;;;;;;ACnGc,8BAAA,aAAA;YACbpF,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,kBAAMpC,eAAeqC,cAAc9D,OAAO+C,SAAR,IAAqB/C,OAAO+C,YAAYhE,SAASsE,eAAerD,OAAO+C,SAA/B;AAG1E,kBAAI,CAACtB,cAAc;AACjBlD,uBAAOwF,QAAQC,MAAM,8BAA8BhE,OAAO+C,SAA1D;AACA;cACD;AAGD/C,qBAAOiE,mBAAmBC,aAAazC,cAAczB,MAAf;AAGtC,kBAAIA,OAAO6B,QAAQ;AACjBL,uBAAAA,wCAAAA,YAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;cACV;AAGDmE,kDAAAA,WAAMC,KAAKpE,QAAQ6D,UAAnB;YACD;UArBY;AAwBf,mBAASK,aAAcxD,SAASV,QAAQ;AAEtC,gBAAMqE,QAAQ3D,QAAQ4D,UAAR;AAGd,gBAAMC,kBAAkBC,MAAMhF,UAAUgB,MAAMd,KAAKgB,QAAQ4B,UAAnC;AACxB,qBAASf,IAAI,GAAGA,IAAIgD,gBAAgBvD,QAAQO,KAAK;AAE/C,kBAAIvB,OAAOyE,eAAe3F,QAAQyF,gBAAgBhD,GAAGmD,EAAjD,MAAyD,IAAI;AAC/D;cACD;AAGD,kBAAMC,cAAcT,aAAaK,gBAAgBhD,IAAIvB,MAArB;AAGhCqE,oBAAMnC,YAAYyC,WAAlB;YACD;AAGD,gBAAI3E,OAAO4E,cAAclE,QAAQmE,aAAa,GAAG;AAC/CR,oBAAMlC,aAAa,SAAS1B,OAAAA,wCAAAA,gBAAAA,EAAcC,SAASV,MAAV,CAAzC;YACD;AAGD,oBAAQU,QAAQoE,SAAhB;cACE,KAAK;AAEHT,sBAAM/C,QAAQZ,QAAQY;AACtB;cACF,KAAK;AAEH+C,sBAAMU,WAAW,IAAjB,EAAuBC,UAAUtE,SAAS,GAAG,CAA7C;AACA;YARJ;AAWA,mBAAO2D;UACR;AAED,mBAASP,cAAef,WAAW;AAEjC,mBAAO,QAAOA,SAAP,MAAqB,YAAYA,cAAcA,qBAAqBpD,eAAeoD,UAAU8B,aAAa;UAClH;;;;ACrED,8BAAA,EAAA,mBAAA;AAAA,cAAA,0CAAA,oBAAA,uBAAA;AAAA,cAAA,sCAAA,oBAAA,mBAAA;AAAA,cAAA,wCAAA,oBAAA,qBAAA;AAIe,8BAAA,aAAA;YACbxG,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,kBAAI7D,OAAO+C,UAAUkC,gBAAgBT,OAAO;AAE1CxE,uBAAO+C,YAAY,CAAC/C,OAAO+C,SAAR;cACpB;AAGD/C,qBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAG1B3B,qBAAO+C,UAAUmC,QAAQ,SAAAC,KAAO;AAE9B,oBAAMC,MAAMrG,SAAS4C,cAAc,KAAvB;AACZyD,oBAAIjD,aAAa,SAASnC,OAAOqF,UAAjC;AAGAD,oBAAID,MAAMA;AAIV,oBAAI3G,sCAAAA,WAAQC,UAAR,GAAqB;AACvB,sBAAM6G,oBAAoBF,IAAID;AAC9BC,sBAAID,MAAMG;gBACX;AAGD,oBAAMC,eAAexG,SAAS4C,cAAc,KAAvB;AAGrB4D,6BAAarD,YAAYkD,GAAzB;AAGApF,uBAAOiE,iBAAiB/B,YAAYqD,YAApC;cACD,CAvBD;AA0BA,kBAAIvF,OAAO6B;AAAQL,uBAAAA,wCAAAA,YAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;AAG5BmE,kDAAAA,WAAMC,KAAKpE,QAAQ6D,UAAnB;YACD;UA1CY;;;;ACJf,8BAAA,EAAA,mBAAA;AAAA,cAAA,wCAAA,oBAAA,qBAAA;AAAA,cAAA,sCAAA,oBAAA,mBAAA;AAAA,cAAA,oCAAA,oBAAA,iBAAA;AAAA,cAAA,qCAAA,oBAAA,kBAAA;AAAA,cAAA,yCAAA,oBAAA,sBAAA;AAAA,cAAA,sCAAA,oBAAA,mBAAA;AAAA,cAAA,qCAAA,oBAAA,kBAAA;;;;;;;;;;;;;;AAUA,cAAM2B,aAAa,CAAC,OAAO,QAAQ,SAAS,QAAQ,UAAjC;AAEJ,8BAAA,aAAA;YACblH,MADa,SAAA,OACL;AACN,kBAAM0B,SAAS;gBACb+C,WAAW;gBACX0C,mBAAmB;gBACnBC,MAAM;gBACN7D,QAAQ;gBACRO,aAAa;gBACbf,UAAU;gBACVsE,YAAY;gBACZC,iBAAiB;gBACjBC,WAAW;gBACXrD,WAAW;gBACXsD,SAAS,SAAA,QAAC9B,OAAU;AAAE,wBAAMA;gBAAO;gBACnCpB,gBAAgB;gBAChBD,cAAc;gBACdQ,oBAAoB,SAAA,qBAAM;gBAAE;gBAC5B4C,uBAAuB,SAAA,wBAAM;gBAAE;gBAC/BC,cAAc;gBACd1C,SAAS;gBACTW,kBAAkB;gBAClBgC,eAAe;gBACf/E,aAAa,CAAC,SAAS,WAAW,SAAS,aAAa,UAAU,cAAc,YAAnE;gBACbD,cAAc,CAAC,UAAU,OAAO,SAAS,iBAA3B;gBACdwD,gBAAgB,CAAA;gBAChByB,mBAAmB;gBACnBC,KAAK;gBACLC,OAAO;gBACPxB,YAAY;gBACZyB,QAAQ;gBAGRC,WAAW;gBACXpG,MAAM;gBACNC,WAAW;gBACXoG,oBAAoB;gBACpBC,YAAY;gBACZnB,YAAY;cAnCC;AAuCf,kBAAMoB,OAAOC,UAAU;AACvB,kBAAID,SAASE,QAAW;AACtB,sBAAM,IAAIC,MAAM,uCAAV;cACP;AAGD,sBAAA,QAAeH,IAAf,GAAA;gBACE,KAAK;AACHzG,yBAAO+C,YAAY8D,UAAUJ,IAAD;AAC5BzG,yBAAOyF,oBAAoBzF,OAAO+C;AAClC/C,yBAAO0F,OAAOgB,UAAU,MAAM1G,OAAO0F;AACrC;gBACF,KAAK;AACH1F,yBAAO+C,YAAY0D,KAAK1D;AACxB/C,yBAAOyF,oBAAoB,OAAOgB,KAAKhB,sBAAsB,cAAcgB,KAAKhB,oBAAoBzF,OAAO+C;AAC3G/C,yBAAOyF,oBAAoBzF,OAAOqG,SAAP,+BAAA,OAA+CrG,OAAOyF,iBAAtD,IAA4EzF,OAAOyF;AAC9G,2BAASqB,KAAK9G,QAAQ;AACpB,wBAAI8G,MAAM,eAAeA,MAAM;AAAqB;AAEpD9G,2BAAO8G,KAAK,OAAOL,KAAKK,OAAO,cAAcL,KAAKK,KAAK9G,OAAO8G;kBAC/D;AACD;gBACF;AACE,wBAAM,IAAIF,MAAM,kEAAA,QAAyEH,IAAzE,CAAV;cAjBV;AAqBA,kBAAI,CAACzG,OAAO+C;AAAW,sBAAM,IAAI6D,MAAM,gCAAV;AAG7B,kBAAI,CAAC5G,OAAO0F,QAAQ,OAAO1F,OAAO0F,SAAS,YAAYF,WAAW1G,QAAQkB,OAAO0F,KAAK9F,YAAZ,CAAnB,MAAkD,IAAI;AAC3G,sBAAM,IAAIgH,MAAM,qEAAV;cACP;AAGD,kBAAI5G,OAAOwC;AAAWC,oDAAAA,WAAMsE,KAAK/G,MAAX;AAGtB,kBAAIA,OAAO4C;AAAgB5C,uBAAO4C,eAAP;AAG3B,kBAAMoE,YAAYjI,SAASsE,eAAerD,OAAOsD,OAA/B;AAElB,kBAAI0D;AAAWA,0BAAUC,WAAWC,YAAYF,SAAjC;AAGf,kBAAMnD,aAAa9E,SAAS4C,cAAc,QAAvB;AAEnB,kBAAInD,sCAAAA,WAAQC,UAAR,GAAqB;AAIvBoF,2BAAW1B,aAAa,SAAS,iHAAjC;cACD,OAAM;AAEL0B,2BAAW1B,aAAa,SAAS,wEAAjC;cACD;AAGD0B,yBAAW1B,aAAa,MAAMnC,OAAOsD,OAArC;AAGA,kBAAItD,OAAO0F,SAAS,OAAO;AACzB7B,2BAAWsD,SAAS,wBAAwBnH,OAAOiG,gBAAgB;AAGnE,oBAAIjG,OAAOmG,KAAK;AAEd,sBAAI,CAAC3B,MAAM4C,QAAQpH,OAAOmG,GAArB;AAA2BnG,2BAAOmG,MAAM,CAACnG,OAAOmG,GAAR;AAG7CnG,yBAAOmG,IAAIjB,QAAQ,SAAAmC,MAAQ;AACzBxD,+BAAWsD,UAAU,kCAAkCE,OAAO;kBAC/D,CAFD;gBAGD;AAEDxD,2BAAWsD,UAAU;cACtB;AAGD,sBAAQnH,OAAO0F,MAAf;gBACE,KAAK;AAEH,sBAAIlH,sCAAAA,WAAQG,KAAR,GAAgB;AAClB,wBAAI;AACFoF,8BAAQuD,KAAK,6DAAb;AACA,0BAAM3G,MAAMpC,OAAOgJ,KAAKvH,OAAOyF,mBAAmB,QAAtC;AACZ9E,0BAAI6G,MAAJ;AACAxH,6BAAO+F,sBAAP;oBACD,SAAQ/B,OAAP;AACAhE,6BAAO8F,QAAQ9B,KAAf;oBACD,UAPD;AASE,0BAAIhE,OAAOwC;AAAWC,4DAAAA,WAAMC,MAAN;AACtB,0BAAI1C,OAAO2C;AAAc3C,+BAAO2C,aAAP;oBAC1B;kBACF,OAAM;AACL8E,sDAAAA,WAAIpJ,MAAM2B,QAAQ6D,UAAlB;kBACD;AACD;gBACF,KAAK;AACH6D,sDAAAA,WAAMrJ,MAAM2B,QAAQ6D,UAApB;AACA;gBACF,KAAK;AACH8D,qDAAAA,WAAKtJ,MAAM2B,QAAQ6D,UAAnB;AACA;gBACF,KAAK;AACH+D,yDAAAA,WAAQvJ,MAAM2B,QAAQ6D,UAAtB;AACA;gBACF,KAAK;AACHgE,qDAAAA,WAAKxJ,MAAM2B,QAAQ6D,UAAnB;AACA;cA/BJ;YAiCD;UA1JY;;;;;;;;;;;;;;;;;;;;ACTA,8BAAA,aAAA;YACbxF,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,kBAAI,QAAO7D,OAAO+C,SAAd,MAA4B,UAAU;AACxC,sBAAM,IAAI6D,MAAM,wCAAV;cACP;AAGD,kBAAI,OAAO5G,OAAOkG,sBAAsB,WAAW;AACjD,sBAAM,IAAIU,MAAM,uDAAV;cACP;AAGD,kBAAI,CAAC5G,OAAO2F,cAAc,CAACnB,MAAM4C,QAAQpH,OAAO2F,UAArB,GAAkC;AAC3D,sBAAM,IAAIiB,MAAM,8CAAV;cACP;AAGD5G,qBAAO2F,aAAa3F,OAAO2F,WAAWmC,IAAI,SAAAC,UAAY;AACpD,uBAAO;kBACLC,OAAO,QAAOD,QAAP,MAAoB,WAAWA,SAASC,QAAQD;kBACvDE,aAAa,QAAOF,QAAP,MAAoB,WAAWA,SAASE,cAAcF;kBACnEG,YAAY,QAAOH,QAAP,MAAoB,YAAYA,SAASG,aAAaH,SAASG,aAAa,MAAM,MAAMlI,OAAO2F,WAAW3E,SAAS;gBAH1H;cAKR,CANmB;AASpBhB,qBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAG1B,kBAAI3B,OAAO6B,QAAQ;AACjBL,uBAAAA,wCAAAA,YAAAA,EAAUxB,OAAOiE,kBAAkBjE,MAA1B;cACV;AAGDA,qBAAOiE,iBAAiBnC,aAAaqG,WAAWnI,MAAD;AAG/CmE,kDAAAA,WAAMC,KAAKpE,QAAQ6D,UAAnB;YACD;UAvCY;AA0Cf,mBAASsE,WAAYnI,QAAQ;AAE3B,gBAAMoI,OAAOpI,OAAO+C;AACpB,gBAAM4C,aAAa3F,OAAO2F;AAG1B,gBAAI5F,WAAW;AAGf,gBAAIC,OAAOkG,mBAAmB;AAC5BnG,0BAAY;YACb;AAGDA,wBAAY;AAGZ,qBAASsI,IAAI,GAAGA,IAAI1C,WAAW3E,QAAQqH,KAAK;AAC1CtI,0BAAY,sBAAsB4F,WAAW0C,GAAGH,aAAa,MAAMlI,OAAO4F,kBAAkB,OAAOxF,OAAAA,wCAAAA,kBAAAA,EAAgBuF,WAAW0C,GAAGJ,WAAf,IAA8B;YACjJ;AAGDlI,wBAAY;AAGZ,gBAAIC,OAAOkG,mBAAmB;AAC5BnG,0BAAY;YACb;AAGDA,wBAAY;AAGZ,qBAASwB,IAAI,GAAGA,IAAI6G,KAAKpH,QAAQO,KAAK;AAEpCxB,0BAAY;AAGZ,uBAASuI,IAAI,GAAGA,IAAI3C,WAAW3E,QAAQsH,KAAK;AAC1C,oBAAIC,aAAaH,KAAK7G;AAGtB,oBAAMwG,WAAWpC,WAAW2C,GAAGN,MAAMQ,MAAM,GAA1B;AACjB,oBAAIT,SAAS/G,SAAS,GAAG;AACvB,2BAASyH,IAAI,GAAGA,IAAIV,SAAS/G,QAAQyH,KAAK;AACxCF,iCAAaA,WAAWR,SAASU;kBAClC;gBACF,OAAM;AACLF,+BAAaA,WAAW5C,WAAW2C,GAAGN;gBACvC;AAGDjI,4BAAY,sBAAsB4F,WAAW2C,GAAGJ,aAAalI,OAAO6F,YAAY,OAAO0C,aAAa;cACrG;AAGDxI,0BAAY;YACb;AAGDA,wBAAY;AAEZ,mBAAOA;UACR;;;;AC5GD,8BAAA,EAAA,mBAAA;AAAA,cAAM0C,QAAQ;YACZsE,MADY,SAAA,KACN/G,QAAQ;AAEZ,kBAAM0I,aAAa;AAenB,kBAAMC,aAAa5J,SAAS4C,cAAc,KAAvB;AACnBgH,yBAAWxG,aAAa,SAASuG,UAAjC;AACAC,yBAAWxG,aAAa,MAAM,eAA9B;AAGA,kBAAMyG,aAAa7J,SAAS4C,cAAc,KAAvB;AACnBiH,yBAAWzG,aAAa,SAAS,kEAAjC;AAGA,kBAAM0G,cAAc9J,SAAS4C,cAAc,KAAvB;AACpBkH,0BAAY1G,aAAa,SAAS,YAAlC;AACA0G,0BAAY1G,aAAa,MAAM,YAA/B;AACAyG,yBAAW1G,YAAY2G,WAAvB;AAGA,kBAAMC,UAAU/J,SAAS4C,cAAc,MAAvB;AAChBmH,sBAAQ3G,aAAa,SAAS,cAA9B;AACAyG,yBAAW1G,YAAY4G,OAAvB;AAGA,kBAAMC,cAAchK,SAASkD,eAAejC,OAAOgG,YAA/B;AACpB4C,yBAAW1G,YAAY6G,WAAvB;AAGAJ,yBAAWzG,YAAY0G,UAAvB;AAGA7J,uBAASiK,qBAAqB,MAA9B,EAAsC,GAAG9G,YAAYyG,UAArD;AAGA5J,uBAASsE,eAAe,YAAxB,EAAsCG,iBAAiB,SAAS,WAAY;AAC1Ef,sBAAMC,MAAN;cACD,CAFD;YAGD;YACDA,OApDY,SAAA,QAoDH;AACP,kBAAMiG,aAAa5J,SAASsE,eAAe,eAAxB;AAEnB,kBAAIsF,YAAY;AACdA,2BAAW1B,WAAWC,YAAYyB,UAAlC;cACD;YACF;UA1DW;AA6DClG,8BAAAA,aAAAA;;;;AC7Df,8BAAA,EAAA,mBAAA;AAAA,cAAA,sCAAA,oBAAA,mBAAA;AAAA,cAAA,0CAAA,oBAAA,uBAAA;AAGe,8BAAA,aAAA;YACbpE,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B,kBAAI7D,OAAOqG,QAAQ;AACjB,oBAAM4C,aAAaC,WAAWC,KAAKC,KAAKpJ,OAAO+C,SAAR,GAAoB,SAAAsG,GAAC;AAAA,yBAAIA,EAAEC,WAAW,CAAb;gBAAJ,CAAzC;AACnBC,mCAAmBvJ,QAAQ6D,YAAYoF,UAArB;AAClB;cACD;AAGDjJ,qBAAO+C,YAAY,qBAAqBa,KAAK5D,OAAO+C,SAAjC,IACf/C,OAAO+C,YACPxE,OAAOiL,SAASC,UAAUzJ,OAAO+C,UAAUzC,OAAO,CAAxB,MAA+B,MAAM,MAAMN,OAAO+C,YAAY/C,OAAO+C;AAGnG,kBAAM2G,MAAM,IAAInL,OAAOoL,eAAX;AACZD,kBAAIE,eAAe;AAEnBF,kBAAIlG,iBAAiB,SAAS,WAAM;AAClCjB,uBAAAA,wCAAAA,UAAAA,EAAQvC,MAAD;AACPA,uBAAO8F,QAAQ4D,IAAIG,YAAYH,GAA/B;cAGD,CALD;AAOAA,kBAAIlG,iBAAiB,QAAQ,WAAM;AAEjC,oBAAI,CAAC,KAAK,GAAN,EAAW1E,QAAQ4K,IAAII,MAAvB,MAAmC,IAAI;AACzCvH,yBAAAA,wCAAAA,UAAAA,EAAQvC,MAAD;AACPA,yBAAO8F,QAAQ4D,IAAIG,YAAYH,GAA/B;AAGA;gBACD;AAGDH,mCAAmBvJ,QAAQ6D,YAAY6F,IAAIK,QAAzB;cACnB,CAZD;AAcAL,kBAAInC,KAAK,OAAOvH,OAAO+C,WAAW,IAAlC;AACA2G,kBAAItF,KAAJ;YACD;UAzCY;AA4Cf,mBAASmF,mBAAoBvJ,QAAQ6D,YAAYuE,MAAM;AAErD,gBAAI4B,WAAW,IAAIzL,OAAO0L,KAAK,CAAC7B,IAAD,GAAQ;cAAE1C,MAAM;YAAR,CAAxB;AACfsE,uBAAWzL,OAAOsE,IAAIqH,gBAAgBF,QAA3B;AAGXnG,uBAAW1B,aAAa,OAAO6H,QAA/B;AAEA7F,gDAAAA,WAAMC,KAAKpE,QAAQ6D,UAAnB;UACD;;;;ACxDD,8BAAA,EAAA,mBAAA;AAAA,cAAA,wCAAA,oBAAA,qBAAA;AAAA,cAAA,0CAAA,oBAAA,uBAAA;AAGA,cAAMM,QAAQ;YACZC,MAAM,SAAA,KAACpE,QAAQ6D,YAAe;AAE5B9E,uBAASiK,qBAAqB,MAA9B,EAAsC,GAAG9G,YAAY2B,UAArD;AAGA,kBAAMsG,gBAAgBpL,SAASsE,eAAerD,OAAOsD,OAA/B;AAGtB6G,4BAAcC,SAAS,WAAM;AAC3B,oBAAIpK,OAAO0F,SAAS,OAAO;AAEzB,sBAAIlH,sCAAAA,WAAQC,UAAR,GAAqB;AACvB4L,+BAAW,WAAA;AAAA,6BAAMC,aAAaH,eAAenK,MAAhB;oBAAlB,GAA2C,GAA5C;kBACX,OAAM;AACLsK,iCAAaH,eAAenK,MAAhB;kBACb;AACD;gBACD;AAGD,oBAAIuK,gBAAiBJ,cAAcK,iBAAiBL,cAAcM;AAClE,oBAAIF,cAAcxL;AAAUwL,kCAAgBA,cAAcxL;AAG1DwL,8BAAcG,KAAKxI,YAAYlC,OAAOiE,gBAAtC;AAGA,oBAAIjE,OAAO0F,SAAS,SAAS1F,OAAOoG,OAAO;AAEzC,sBAAMA,QAAQrH,SAAS4C,cAAc,OAAvB;AACdyE,wBAAMtE,YAAY9B,OAAOoG;AAGzBmE,gCAAcI,KAAKzI,YAAYkE,KAA/B;gBACD;AAGD,oBAAMwE,SAASL,cAAcvB,qBAAqB,KAAnC;AAEf,oBAAI4B,OAAO5J,SAAS,GAAG;AACrB6J,mCAAiBrG,MAAM2E,KAAKyB,MAAX,CAAD,EAAqBE,KAAK,WAAA;AAAA,2BAAMR,aAAaH,eAAenK,MAAhB;kBAAlB,CAA1C;gBACD,OAAM;AACLsK,+BAAaH,eAAenK,MAAhB;gBACb;cACF;YACF;UA9CW;AAiDd,mBAASsK,aAAcH,eAAenK,QAAQ;AAC5C,gBAAI;AACFmK,4BAAc3C,MAAd;AAGA,kBAAIhJ,sCAAAA,WAAQS,OAAR,KAAoBT,sCAAAA,WAAQG,KAAR,GAAgB;AACtC,oBAAI;AACFwL,gCAAcK,cAAczL,SAASgM,YAAY,SAAS,OAAO,IAAjE;gBACD,SAAQC,GAAP;AACAb,gCAAcK,cAAcnM,MAA5B;gBACD;cACF,OAAM;AAEL8L,8BAAcK,cAAcnM,MAA5B;cACD;YACF,SAAQ2F,OAAP;AACAhE,qBAAO8F,QAAQ9B,KAAf;YACD,UAhBD;AAiBE,kBAAIxF,sCAAAA,WAAQC,UAAR,GAAqB;AAEvB0L,8BAAc/D,MAAM6E,aAAa;AACjCd,8BAAc/D,MAAM8E,OAAO;cAC5B;AAED3I,qBAAAA,wCAAAA,UAAAA,EAAQvC,MAAD;YACR;UACF;AAED,mBAAS6K,iBAAkBD,QAAQ;AACjC,gBAAMO,WAAWP,OAAO9C,IAAI,SAAAsD,OAAS;AACnC,kBAAIA,MAAMjG,OAAOiG,MAAMjG,QAAQ5G,OAAOiL,SAAS6B,MAAM;AACnD,uBAAOC,gBAAgBF,KAAD;cACvB;YACF,CAJgB;AAMjB,mBAAOG,QAAQC,IAAIL,QAAZ;UACR;AAED,mBAASG,gBAAiBF,OAAO;AAC/B,mBAAO,IAAIG,QAAQ,SAAAE,SAAW;AAC5B,kBAAMC,YAAY,SAAZA,aAAkB;AACtB,iBAACN,SAAS,OAAOA,MAAMO,iBAAiB,eAAeP,MAAMO,iBAAiB,KAAK,CAACP,MAAMQ,WACtFvB,WAAWqB,YAAW,GAAZ,IACVD,QAAO;cACZ;AACDC,wBAAS;YACV,CAPM;UAQR;AAEcvH,8BAAAA,aAAAA;;;;ACrGf,8BAAA,EAAA,mBAAA;AAAA,cAAA,sCAAA,oBAAA,mBAAA;AAEe,8BAAA,aAAA;YACb9F,OAAO,SAAA,MAAC2B,QAAQ6D,YAAe;AAE7B7D,qBAAOiE,mBAAmBlF,SAAS4C,cAAc,KAAvB;AAC1B3B,qBAAOiE,iBAAiB9B,aAAa,SAAS,YAA9C;AAGAnC,qBAAOiE,iBAAiBnC,YAAY9B,OAAO+C;AAG3CoB,kDAAAA,WAAMC,KAAKpE,QAAQ6D,UAAnB;YACD;UAXY;;;;;;;;;;;", "names": ["module", "exports", "key", "printJS", "print", "init", "window", "Browser", "isFirefox", "InstallTrigger", "isIE", "navigator", "userAgent", "indexOf", "document", "documentMode", "isEdge", "StyleMedia", "isChrome", "context", "chrome", "<PERSON><PERSON><PERSON><PERSON>", "Object", "prototype", "toString", "call", "HTMLElement", "toLowerCase", "isIOSChrome", "addWrapper", "htmlData", "params", "bodyStyle", "font", "font_size", "capitalizePrint", "obj", "char<PERSON>t", "toUpperCase", "slice", "collectStyles", "element", "win", "defaultView", "elementStyle", "styles", "getComputedStyle", "length", "targetStyles", "targetStyle", "targetStylesMatch", "getPropertyValue", "max<PERSON><PERSON><PERSON>", "value", "i", "addHeader", "printElement", "headerContainer", "createElement", "isRawHTML", "header", "innerHTML", "headerElement", "headerNode", "createTextNode", "append<PERSON><PERSON><PERSON>", "setAttribute", "headerStyle", "insertBefore", "childNodes", "cleanUp", "showModal", "Modal", "close", "onLoadingEnd", "onLoadingStart", "URL", "revokeObjectURL", "printable", "event", "handler", "removeEventListener", "onPrintDialogClose", "iframe", "getElementById", "frameId", "remove", "addEventListener", "raw", "regexHtml", "RegExp", "test", "printFrame", "isHtmlElement", "console", "error", "printableElement", "cloneElement", "Print", "send", "clone", "cloneNode", "childNodesArray", "Array", "ignoreElements", "id", "clone<PERSON><PERSON><PERSON><PERSON>", "scanStyles", "nodeType", "tagName", "getContext", "drawImage", "constructor", "for<PERSON>ach", "src", "img", "imageStyle", "fullyQualifiedSrc", "imageWrapper", "printTypes", "fallbackPrintable", "type", "properties", "gridHeaderStyle", "gridStyle", "onError", "onIncompatibleBrowser", "modalMessage", "documentTitle", "repeatTableHeader", "css", "style", "base64", "onPdfOpen", "honorMarginPadding", "honorColor", "args", "arguments", "undefined", "Error", "encodeURI", "k", "show", "usedFrame", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "srcdoc", "isArray", "file", "info", "open", "focus", "Pdf", "Image", "Html", "RawHtml", "Json", "map", "property", "field", "displayName", "columnSize", "jsonToHTML", "data", "a", "n", "stringData", "split", "p", "modalStyle", "printModal", "contentDiv", "closeButton", "spinner", "messageNode", "getElementsByTagName", "bytesArray", "Uint8Array", "from", "atob", "c", "charCodeAt", "createBlobAndPrint", "location", "origin", "req", "XMLHttpRequest", "responseType", "statusText", "status", "response", "localPdf", "Blob", "createObjectURL", "iframeElement", "onload", "setTimeout", "performPrint", "printDocument", "contentWindow", "contentDocument", "body", "head", "images", "loadIframeImages", "then", "execCommand", "e", "visibility", "left", "promises", "image", "href", "loadIframeImage", "Promise", "all", "resolve", "pollImage", "naturalWidth", "complete"]}