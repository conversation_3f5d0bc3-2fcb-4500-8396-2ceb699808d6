<template>
  <div class="container">
    <prevent-list-search @form-search="searchPreventList" @reset="resetParamsAndData" />
    <prevent-list-table ref="table" />  
  </div>
</template>

<script setup name="PreventList">
import PreventListSearch from './preventList/PreventListSearch.vue';
import PreventListTable from './preventList/PreventListTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchPreventList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};

defineExpose({
  searchPreventList
});
</script>
