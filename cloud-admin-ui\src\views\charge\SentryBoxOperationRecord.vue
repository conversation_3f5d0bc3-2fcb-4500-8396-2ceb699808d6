<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="特殊放行" name="specialRelease">
        <special-release ref="special_release" />
      </el-tab-pane>
      <el-tab-pane label="取消放行" name="cancelRelease">
        <cancel-release ref="cancel_release" />
      </el-tab-pane>
      <el-tab-pane label="车牌号矫正" name="plateNoCorrect">
        <plate-no-correct ref="plate_no_correct" />
      </el-tab-pane>
      <el-tab-pane label="入口抬杆" name="entranceRangePole">
        <entrance-range-pole ref="entrance_range_pole" />
      </el-tab-pane>
      <el-tab-pane label="出口抬杆" name="exitRangePole">
        <exit-range-pole ref="exit_range_pole" />
      </el-tab-pane>
      <el-tab-pane label="手动匹配出场" name="manualMatchingExit">
        <manual-matching-exit ref="manual_matching_exit" />
      </el-tab-pane>
      <el-tab-pane label="重复入场" name="repeatEntrance">
        <repeat-entrance ref="repeat_entrance" />
      </el-tab-pane>
      <el-tab-pane label="被冲车辆" name="rushedCar">
        <rushed-car ref="rushed_car" />
      </el-tab-pane>
      <el-tab-pane label="切换费率" name="changeRate">
        <change-rate ref="change_rate" />
      </el-tab-pane>
      <el-tab-pane label="手动补录" name="manualEntry">
        <manual-entry ref="manual_entry" />
      </el-tab-pane>
      <!-- <el-tab-pane label="强制出场" name="forceExit">
        <force-exit ref="force_exit" />
      </el-tab-pane> -->
    </el-tabs>
  </div>
</template>

<script name="SentryBoxOperationRecord" setup>
import { dayjs } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import cancelRelease from './CancelRelease.vue';
import changeRate from './ChangeRate.vue';
import entranceRangePole from './EntranceRangePole.vue';
import exitRangePole from './ExitRangePole.vue';
import manualEntry from './ManualEntry.vue';
import manualMatchingExit from './ManualMatchingExit.vue';
import plateNoCorrect from './PlateNoCorrect.vue';
import repeatEntrance from './RepeatEntrance.vue';
import rushedCar from './RushedCar.vue';
import specialRelease from './SpecialRelease.vue';
const activeName = ref('specialRelease');
const special_release = ref(null);
const cancel_release = ref(null);
const plate_no_correct = ref(null);
const entrance_range_pole = ref(null);
const exit_range_pole = ref(null);
const manual_matching_exit = ref(null);
const repeat_entrance = ref(null);
const rushed_car = ref(null);
const change_rate = ref(null);
const force_exit = ref(null);
const manual_entry = ref(null);

const params = reactive({
  page: 1,
  limit: 30,
  in_end_time: dayjs().format('YYYY-MM-DD 23:59:59'),
  in_start_time: dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00')
});

onMounted(() => {
  special_release.value.pageSpecialRelease(params);
});

const handleClick = (tab) => {
  if (tab.props.name === 'specialRelease') {
    special_release.value.pageSpecialRelease(params);
  }
  if (tab.props.name === 'cancelRelease') {
    cancel_release.value.pageCancelRelease(params);
  }
  if (tab.props.name === 'plateNoCorrect') {
    plate_no_correct.value.pagePlateNoCorrect(params);
  }
  if (tab.props.name === 'entranceRangePole') {
    entrance_range_pole.value.pageEntranceRangePole(params);
  }
  if (tab.props.name === 'exitRangePole') {
    exit_range_pole.value.pageExitRangePole(params);
  }
  if (tab.props.name === 'manualMatchingExit') {
    manual_matching_exit.value.pageManualMatchingExit(params);
  }
  if (tab.props.name === 'repeatEntrance') {
    repeat_entrance.value.pageRepeatEntrance(params);
  }
  if (tab.props.name === 'rushedCar') {
    rushed_car.value.pageRushedCar(params);
  }
  if (tab.props.name === 'changeRate') {
    change_rate.value.pageChangeRate(params);
  }
  if (tab.props.name === 'forceExit') {
    force_exit.value.pageForceExit(params);
  }
  if (tab.props.name === 'manualEntry') {
    manual_entry.value.pageManualEntry(params);
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 10px 10px 0px 10px;
  background-color: #f6f6f6;
}
</style>
