<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-28 15:27:48
 * @LastEditors: 达万安 段世煜
 * @Description: 通行效率
 * @FilePath: \cloud-admin-ui\src\views\home\group\trafficEfficiency.vue
-->
<template>
  <warp-card size="mini" title="通行效率">
    <tab-button :options="tabOptions" v-model="activeKey" @change="handleTabChange" />
    <div class="container">
      <div class="chart left center">
        <div class="turnover-rate-container center">
          <div class="value">{{ turnoverRate }}</div>
          <div class="title">平均周转率</div>
        </div>
      </div>
      <div class="chart right">
        <traffic-card
          v-for="item in data"
          :key="item.label"
          :label="(activeKey === 0 ? '临停' : '长租') + item.label"
          :value="item.value"
          :unit="item.unit"
        />
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import warpCard from './components/warpCard.vue';
import trafficCard from './components/trafficCard.vue';
import tabButton from './components/tabButton.vue';
import { ref, reactive } from 'vue';
import { truncateDecimal } from '@/utils/utils';

import { groupHomepageParkTrafficEfficiency } from '@/api/home/<USER>';

// tab数据源
const tabOptions = [
  {
    value: 0,
    label: '临停数据'
  },
  {
    value: 1,
    label: '长租数据'
  }
];
// 当前激活tab
const activeKey = ref(0);
// 总体支付分类数据
const data = reactive([
  { label: '车次', value: '--', unit: '次' },
  { label: '通行时长', value: '--', unit: '秒' },
  { label: '平均通行效率', value: '--', unit: '秒/次' }
]);
const turnoverRate = ref(0);
// tab切换
const handleTabChange = (val) => {
  dealData(val);
};
const trafficData = ref({});
/**
 * @description 获取数据
 */
const fetchData = async (val) => {
  try {
    const { data: resData } = await groupHomepageParkTrafficEfficiency(val);
    trafficData.value = resData;
  } finally {
    dealData(activeKey.value);
  }
};
/**
 * @description 处理已获取数据, 赋值给页面
 */
const dealData = (index) => {
  // const resData = trafficData.value ? trafficData.value[index] : undefined;
  if (index === 0) {
    data[0].value = trafficData.value?.parking_pass_amount || '--';
    data[1].value = trafficData.value?.parking_pass_time || '--';
    data[2].value = truncateDecimal(trafficData.value?.parking_ratio, 2) || '--';
  } else {
    data[0].value = trafficData.value?.rent_pass_amount || '--';
    data[1].value = trafficData.value?.rent_pass_time || '--';
    data[2].value = truncateDecimal(trafficData.value?.rent_ratio, 2) || '--';
  }
  turnoverRate.value = truncateDecimal(trafficData.value?.park_turnover_rate || 0, 2);
};
defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: calc(100% - 34px);
  display: flex;
  margin-top: 6px;
  .chart {
    width: 40%;
    height: 100%;
  }
  .right {
    width: 60%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 5px 0;
    margin-left: 10px;
  }
  .left {
    .turnover-rate-container {
      width: 134px;
      height: 134px;
      background-image: url('@/assets/groupImage/turnover_bg.png');
      background-size: 100% 100%;
      color: #fff;
      flex-direction: column;
      .value {
        font-size: 24px;
        font-weight: bold;
        height: 32px;
      }
      .title {
        font-size: 14px;
      }
    }
  }
  .center {
    display: flex;
    justify-content: center;
    align-items: center;
  }
}
</style>
