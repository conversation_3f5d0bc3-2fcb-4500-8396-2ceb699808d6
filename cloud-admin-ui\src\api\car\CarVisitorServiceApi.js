/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询访客车申请
export const pagingVisitorApply = (data) => {
  return $({
    url: '/console/park/visitor/apply/pagingVisitorApply',
    method: 'post',
    data
  });
};

// 新增访客车申请
export const createVisitorApply = (data) => {
  return $({
    url: '/console/park/visitor/apply/createVisitorApply',
    method: 'post',
    data
  });
};
// 查看访客车申请详情
export const visitorApplyDetail = (data) => {
  return $({
    url: '/console/park/visitor/apply/visitorApplyDetail?id=' + data,
    method: 'post'
  });
};
// 修改访客车申请
export const updateVisitorApply = (data) => {
  return $({
    url: '/console/park/visitor/apply/updateVisitorApply',
    method: 'post',
    data
  });
};

// 删除访客车申请
export const deleteVisitorApply = (data) => {
  return $({
    url: '/console/park/visitor/apply/deleteVisitorApply',
    method: 'post',
    data
  });
};

// 提交审核
export const submitAuditVisitorApply = (id) => {
  return $({
    url: '/console/park/visitor/apply/submitAuditVisitorApply/' + id,
    method: 'post'
  });
};
// 导出
export const exportVisitorApply = (data) => {
  return $({
    url: '/console/park/visitor/apply/exportVisitorApply',
    method: 'post',
    data
  });
};
