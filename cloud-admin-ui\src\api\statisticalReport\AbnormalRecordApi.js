/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询异常操作记录
export const pagingAbnormalRecord = (data) => {
  return $({
    url: '/console/statistics/abnormal/operate/record/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/abnormal/operate/record/exportByPeriod',
    method: 'post',
    data
  });
};
export const exportDataAll = (data) => {
  return $.post("/console/statistics/abnormal/operate/record/exportSummaryByPeriod", data)
}
