<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-25 16:23:02
 * @LastEditors: 达万安 段世煜
 * @Description: 地图
 * @FilePath: \cloud-admin-ui\src\views\home\group\mapInfo.vue
-->
<template>
  <warp-card size="large" title="全国地图">
    <div class="container">
      <div class="filter">
        <div></div>
        <div class="right">
          <el-select
            popper-class="group-index-popper"
            :teleported="false"
            v-model="queryParam.area"
            placeholder="大区"
            class="search-select"
            @change="handleFilter($event, 'area')"
            size="small"
          >
            <el-option v-for="item in props.data" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
          <el-select
            popper-class="group-index-popper"
            v-model="queryParam.city"
            :teleported="false"
            placeholder="公司"
            class="search-select"
            @change="handleFilter($event, 'city')"
            size="small"
          >
            <el-option v-for="item in options.city" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
          <el-select
            popper-class="group-index-popper"
            v-model="queryParam.park"
            :teleported="false"
            placeholder="广场"
            class="search-select"
            @change="handleFilter($event, 'park')"
            size="small"
          >
            <el-option v-for="item in options.park" :key="item.id" :label="item.label" :value="item.id" />
          </el-select>
          <!-- <el-select
            popper-class="group-index-popper"
            v-model="queryParam.time_unit"
            placeholder="类型"
            class="search-select"
            @change="handleFilter($event, 'time_unit')"
            size="small"
          >
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select> -->
          <time-range
            v-model:date="queryParam.date"
            v-model:unit="queryParam.time_unit"
            :teleported="false"
            @change="handleFilter($event, 'date_range')"
            size="small"
            style="margin-right: 10px; width: 355px"
            popper-class="group-index-popper"
          />
          <el-button @click="setQuery" size="small">确认</el-button>
          <el-button @click="resetQuery" size="small">重置</el-button>
        </div>
      </div>
      <div class="chart">
        <map-chart ref="mapChartRef" />
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import warpCard from './components/warpCard.vue';
import mapChart from './components/mapChart.vue';
import timeRange from '@/components/timeRange.vue';
import { ref, reactive, computed } from 'vue';
import { dayjs } from 'element-plus';
import { fetchMapData } from '@/api/home/<USER>';

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
});
const emits = defineEmits(['filter']);
const mapChartRef = ref(null);
const timeOptions = {
  2: 'month',
  3: 'date',
  5: 'week'
};
const getDefaultDate = (val) => {
  return [dayjs().startOf(timeOptions[val]).format('YYYY-MM-DD'), dayjs().endOf(timeOptions[val]).format('YYYY-MM-DD')];
};
const queryParam = reactive({
  area: '',
  city: '',
  park: '',
  time_unit: 3,
  // date: getDefaultDate(3)
  date: [dayjs(new Date().getTime() - 86400000).format('YYYY-MM-DD'), dayjs(new Date().getTime() - 86400000).format('YYYY-MM-DD')]
});

const options = computed(() => {
  const data = {
    city: [],
    park: []
  };
  if (props.data && props.data.length) {
    const activeCity = props.data.find((item) => item.id === queryParam.area);
    if (activeCity) {
      if (activeCity && activeCity.children) data.city = activeCity.children;
      if (data.city) {
        const activePark = data.city.find((item) => item.id === queryParam.city);
        if (activePark && activePark.children) data.park = activePark.children;
      }
    }
  }
  return data;
});

const fetchData = async (val) => {
  const { data: resData } = await fetchMapData(val);
  mapChartRef.value.setData(resData);
};

const handleFilter = (val, type) => {
  if (type === 'area') {
    queryParam.city = '';
    queryParam.park = '';
  } else if (type === 'city') {
    queryParam.park = '';
  }
  if (type === 'time_unit') {
    queryParam.date = getDefaultDate(queryParam.time_unit);
  }
};

const setQuery = () => {
  emits('filter', dealEmitData());
};
const resetQuery = () => {
  for (let i in queryParam) {
    queryParam[i] = '';
  }
  queryParam.time_unit = 3;
  queryParam.date = getDefaultDate(queryParam.time_unit);
  emits('filter', dealEmitData());
};

const dealEmitData = () => {
  const useQueryParam = { ...queryParam };
  useQueryParam.org_id = useQueryParam.park || useQueryParam.city || useQueryParam.area;
  if (useQueryParam.date) {
    useQueryParam.start_date = useQueryParam.date[0];
    useQueryParam.end_date = useQueryParam.date[1];
  }
  return useQueryParam;
};

const initVisaual = () => {
  emits('filter', dealEmitData());
};

defineExpose({
  fetchData,
  initVisaual
});
</script>

<style lang="scss" scoped>
.container {
  height: 100%;
  width: 100%;

  .filter {
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-input {
      width: 200px;
      height: 32px;
    }

    :deep(.search-select) {
      width: 95px;
      margin-right: 10px;
    }

    .right {
      display: flex;
      align-items: center;
    }

    :deep(.el-input-group__append) {
      border: 1px solid #0b73ca;
      box-shadow: none;
    }

    :deep(.el-input__wrapper) {
      box-shadow: 0 0 15px #90c2ff50 inset;
      border: 1px solid #0b73ca;
    }

    :deep(.el-input-group__append) {
      background: linear-gradient(rgb(8, 61, 124) 0%, rgba(8, 61, 124, 0.1) 50%, rgb(8, 61, 124) 100%);
      color: #90c2ff;
      font-size: 16px;

      :deep(button) {
        border: none;
      }
    }

    :deep(.el-input__icon) {
      color: #81c4ff;
    }

    :deep(.el-select__caret),
    :deep(.el-button) {
      color: #90c2ff;
    }

    :deep(.el-button:hover) {
      background: none;
    }
  }

  :deep(.filter) {
    --el-fill-color-blank: none;
    --el-border-color: #90c2ff;
    --el-text-color-regular: #fff;
  }

  :deep(.el-input__inner::placeholder) {
    color: #90c2ff;
  }

  :deep(.el-range-input::placeholder) {
    color: #90c2ff;
  }

  :deep(.el-range-separator) {
    color: #90c2ff;
  }

  :deep(.line) {
    color: #90c2ff;
  }

  .chart {
    height: calc(100% - 40px);
    width: 100%;
    overflow: hidden;
  }

  :deep(.el-range-editor--small) {
    height: 26px;
  }
}
</style>
<style lang="scss">
.group-index-popper {
  box-shadow: 0 0 15px #90c2ff50 inset !important;
  --el-bg-color-overlay: rgba(12, 51, 110, 0.8) !important;
  color: #00fff4 !important;
  border: 1px solid #0b73ca !important;
  --el-fill-color-light: rgba(12, 51, 110, 1) !important;

  .el-select-dropdown__item {
    color: #fff !important;
  }

  .el-select-dropdown__item.hover,
  .el-select-dropdown__item:hover {
    background-color: #90c2ff50;
    color: #fff !important;
  }
}

.group-index-popper .el-picker-panel__body-wrapper {
  color: #fff;
  --el-text-color-regular: #81c4ff;
  --el-datepicker-inrange-bg-color: #90c2ff50;

  .el-picker-panel__sidebar,
  .el-picker-panel__content el-date-range-picker__content is-left {
    border-color: #81c4ff;
  }

  th {
    border-color: #81c4ff;
    color: #fff;
  }

  .el-date-table td.available:hover {
    color: var(--el-datepicker-hover-text-color) !important;
  }

  .el-icon,
  .el-picker-panel__shortcut,
  .el-month-table td .cell,
  .el-year-table td .cell {
    color: #fff;
  }

  .el-month-table td.current:not(.disabled) .cell,
  .el-year-table td.current:not(.disabled) .cell {
    color: #409eff;
  }
}
</style>
