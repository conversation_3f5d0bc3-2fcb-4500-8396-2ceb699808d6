<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="parkingTimeService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="100" />
        <el-table-column prop="park_id" label="车场id" align="center" min-width="100" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="100" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="100" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" min-width="100" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="100" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="100" />
        <el-table-column prop="district_name" label="所在区县" align="center" min-width="100" />
        <el-table-column prop="first_time" label="0-15分钟" align="center" min-width="130" />
        <el-table-column prop="first_money" label="0-15分钟金额" align="center" min-width="130" />
        <el-table-column prop="second_time" label="15分钟(不包含)-30分钟" align="center" min-width="130" />
        <el-table-column prop="second_money" label="15分钟(不包含)-30分钟金额" align="center" min-width="130" />
        <el-table-column prop="thirdly_time" label="30分钟(不包含)-1小时" align="center" min-width="130" />
        <el-table-column prop="thirdly_money" label="30分钟(不包含)-1小时金额" align="center" min-width="130" />
        <el-table-column prop="fourthly_time" label="1小时(不包含)-2小时" align="center" min-width="130" />
        <el-table-column prop="fourthly_money" label="1小时(不包含)-2小时金额" align="center" min-width="130" />
        <el-table-column prop="fifth_time" label="2小时(不包含)-3小时" align="center" min-width="130" />
        <el-table-column prop="fifth_money" label="2小时(不包含)-3小时金额" align="center" min-width="130" />
        <el-table-column prop="sixth_time" label="3小时(不包含)-4小时" align="center" min-width="130" />
        <el-table-column prop="sixth_money" label="3小时(不包含)-4小时金额" align="center" min-width="130" />
        <el-table-column prop="seventh_time" label="4小时(不包含)-5小时" align="center" min-width="130" />
        <el-table-column prop="seventh_money" label="4小时(不包含)-5小时金额" align="center" min-width="130" />
        <el-table-column prop="eighth_time" label="5小时(不包含)-6小时" align="center" min-width="130" />
        <el-table-column prop="eighth_money" label="5小时(不包含)-6小时金额" align="center" min-width="130" />
        <el-table-column prop="ninth_time" label="6小时(不包含)-7小时" align="center" min-width="130" />
        <el-table-column prop="ninth_money" label="6小时(不包含)-7小时金额" align="center" min-width="130" />
        <el-table-column prop="tenth_time" label="7小时(不包含)-8小时" align="center" min-width="130" />
        <el-table-column prop="tenth_money" label="7小时(不包含)-8小时金额" align="center" min-width="130" />
        <el-table-column prop="eleventh_time" label="8小时(不包含)-9小时" align="center" min-width="130" />
        <el-table-column prop="eleventh_money" label="8小时(不包含)-9小时金额" align="center" min-width="130" />
        <el-table-column prop="twelfth_time" label="9小时(不包含)-10小时" align="center" min-width="130" />
        <el-table-column prop="twelfth_money" label="9小时(不包含)-10小时金额" align="center" min-width="130" />
        <el-table-column prop="thirteenth_time" label="10小时以上" align="center" min-width="130" />
        <el-table-column prop="thirteenth_money" label="10小时以上金额" align="center" min-width="130" />
        <!-- <el-table-column prop="average_parking_time" label="日平均停车时长（小时）" align="center" /> -->
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkingTimeTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkingTimeService from '@/service/statisticalReport/ParkingTimeService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  parkingTimeService.pagingParkingTime(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
