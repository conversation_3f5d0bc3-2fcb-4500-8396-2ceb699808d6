<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item>
      <el-select v-model="form.queryParams.car_types" placeholder="车场类型" multiple clearable>
        <el-option v-for="item in car_types" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.valid_states" placeholder="生效状态" multiple clearable>
        <el-option v-for="item in valid_states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" placeholder="审核状态" multiple clearable>
        <el-option v-for="item in audit_states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="ParkFeeSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    car_types: [],
    valid_states: [],
    audit_states: [],
    page: 1,
    limit: 30
  }
});
const car_types = ref([]);
const valid_states = ref([]);
const audit_states = ref([]);

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'car_types', enum_value: 'EnumCarType' },
    { enum_key: 'valid_states', enum_value: 'EnumFeeModelValidState' }
  ];
  commonService.findEnums('park', param).then((response) => {
    car_types.value = response.data.car_types;
    valid_states.value = response.data.valid_states;
  });

  const auditParam = [{ enum_key: 'audit_states', enum_value: 'EnumAuditState' }];
  commonService.findEnums('audit', auditParam).then((response) => {
    audit_states.value = response.data.audit_states;
  });
};
const handleDataSearch = () => {
  const params = {
    ...form.queryParams,
    park_id: route.query.parkId
  };
  const query = Object.assign(params, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    car_types: [],
    valid_states: [],
    audit_states: [],
    page: 1,
    limit: 30
  };
  // emits('reset', form.queryParams);
  handleDataSearch();
};

defineExpose({
  handleAllReset
});
</script>
<style lang="scss" scoped></style>
