<template>
  <div class="seatBox">
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
      <!-- <form-search-item>
        <el-input v-model="queryParams.park_name" placeholder="车场名称" readonly="true" @click="authCharge2(true)"
      /></form-search-item> -->
      <form-search-item>
        <el-input placeholder="请输入车场名称" v-model="queryParams.park_name"></el-input>
      </form-search-item>
      <form-search-item>
        <el-input placeholder="请输入员工姓名" v-model="queryParams.user_name"></el-input>
      </form-search-item>
      <form-search-item>
        <el-input placeholder="请输入手机号" v-model="queryParams.mobile"></el-input>
      </form-search-item>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <!-- <el-button type="default" @click="downLoadSeatTmp">下载模板</el-button>
          <el-button type="default" @click="importSeat">导入坐席</el-button> -->
          <el-button type="default" @click="addSeat">添加坐席</el-button>
        </el-space>
      </div>
      <div ref="table">
        <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 284px)">
          <el-table-column align="center" type="selection"></el-table-column>
          <el-table-column align="center" label="序号" type="index" width="80"></el-table-column>
          <el-table-column align="center" label="分机号" prop="login_user"></el-table-column>
          <el-table-column align="center" label="分机密码" prop="login_password"></el-table-column>
          <el-table-column align="center" label="云端值守员工姓名" prop="user_name" width="150"></el-table-column>
          <el-table-column align="center" label="值守员工手机号" prop="mobile"></el-table-column>
          <el-table-column align="center" label="云端值守车场" prop="park_name"></el-table-column>
          <el-table-column align="center" label="被托管车场" prop="managed_park_name"></el-table-column>
          <el-table-column align="center" label="启用状态">
            <template v-slot="{ row }">
              <el-button :type="row.enabled === 1 ? 'primary' : 'danger'" link>{{ row.enabled === 1 ? '已启用' : '已禁用' }}</el-button>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="400">
            <template v-slot="{ row }">
              <div class="buttonbox">
                <!-- <el-button type="primary" link @click="checkSeatInfo(row)">查看</el-button> -->
                <el-button v-if="row.enabled === 1 && row.user_name" type="primary" link @click="allocation(row)">修改坐席</el-button>
                <el-button v-if="row.enabled === 1 && !row.user_name" type="primary" link @click="allocation(row)">分配坐席</el-button>
                <el-button v-if="row.enabled === 0" type="primary" link @click="setAbledSeat(row.id, 1)">启用</el-button>
                <el-button v-if="row.enabled === 0" type="danger" link @click="setAbledSeat(row.id, -1)">删除</el-button>
                <el-button v-if="row.enabled === 1" type="danger" link @click="setAbledSeat(row.id, 0)">禁用</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
  <!-- 添加坐席弹出框 -->
  <el-dialog v-model="addSeatDialog" width="420" title="添加坐席">
    <template #default>
      <div class="formClass">
        <el-form ref="addSeatFormRef" :rules="addSeatRules" :model="addSeatForm" label-position="right" label-width="120px">
          <el-form-item prop="login_user" label="分机号">
            <el-input v-model="addSeatForm.login_user" placeholder="请输入分机号"></el-input>
          </el-form-item>
          <el-form-item prop="login_password" label="分机密码">
            <el-input v-model="addSeatForm.login_password" placeholder="请输入分机密码"></el-input>
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="addSeatDialog = false"> 取消 </el-button>
        <el-button type="primary" @click="okaddSeatDialog">确定</el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 查看坐席信息弹出框 -->
  <el-dialog v-model="lookInfoDialog" width="420" title="查看">
    <template #default>
      <div class="formClass">
        <el-form label-position="right" label-width="auto">
          <el-form-item label="分机号">
            <span>{{ lookInfoForm.login_user }}</span>
          </el-form-item>
          <el-form-item label="分机密码">
            <span>{{ lookInfoForm.login_password }}</span>
          </el-form-item>
          <el-form-item label="坐席人员">
            <span>{{ lookInfoForm.user_name }}</span>
          </el-form-item>
          <el-form-item label="手机号">
            <span>{{ lookInfoForm.mobile }}</span>
          </el-form-item>
        </el-form>
      </div>
    </template>
  </el-dialog>
  <!-- 启动席弹出框 -->
  <el-dialog v-model="useSeatDialog" width="420" title="启用">
    <template #default>
      <div class="formClass">确定启动吗？</div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="okUseSeatDialog">确定</el-button>
        <el-button @click="useSeatDialog = false"> 取消 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 分配值守员工弹出框 -->
  <el-dialog v-model="allocationInfoDialog" width="820" title="分配值守员工">
    <template #default>
      <div class="formClass">
        <el-form
          style="width: 100%"
          ref="assignEmployeeFormRef"
          :rules="assignEmployeeRules"
          :model="assignEmployeeParams"
          label-position="right"
          label-width="180px"
        >
          <el-form-item label="分机号">
            <span>{{ assignEmployeeParams.login_user }}</span>
          </el-form-item>
          <el-form-item label="分机密码">
            <span>{{ assignEmployeeParams.login_password }}</span>
          </el-form-item>
          <el-form-item label="云端值守员工">
            <el-select
              v-model="assignEmployeeParams.user_id"
              placeholder="请选择云端值守员工"
              filterable
              remote
              reserve-keyword
              :remote-method="remoteSearchEmployees"
              :loading="employeeSearchLoading"
              clearable
            >
              <el-option :label="item.name" :value="item.id" v-for="item in employeeLists" :key="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="云端值守车场(单选)">
            <ClearableChargeInput
              v-model="assignEmployeeParams.park_name"
              @charge="authCharge2(true)"
              @clear="clearDepartment"
              placeholder=" 云端值守车场(单选)"
            />
          </el-form-item>
          <el-form-item label="被托管停车场(多选)">
            <ClearableChargeInput
              v-model="assignEmployeeParams.park_names"
              @charge="authCharge(true)"
              @clear="clearDepartment"
              placeholder="被托管停车场(多选)"
            />
          </el-form-item>
        </el-form>
      </div>
    </template>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="okAllocationInfoDialog">确定</el-button>
        <el-button @click="allocationInfoDialog = false"> 取消 </el-button>
      </div>
    </template>
  </el-dialog>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场1" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_names" :park_id="park_ids" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
  <!-- 关联车场2 -->
  <el-dialog v-if="relatedParkDialogVisible2" width="80%" title="关联车场2" v-model="relatedParkDialogVisible2">
    <park-find-back2 :park_name="park_name" :park_id="park_id" @authCharge="authCharge2(false)" :mode="flag" @renderTableInput="renderTableInput2" />
  </el-dialog>
</template>

<script setup>
import ClearableChargeInput from '@/components/ClearableChargeInput.vue'; //引入组件
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import seatManagementService from '@/service/system/SeatManagementService';
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { useUser } from '@/stores/user';
import ParkFindBack2 from '@/views/car/ParkFindBack.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onActivated, ref } from 'vue'; //引入“vue”
import ParkFindBack from './ParkFindBack.vue'; //引入"关联车场"组件
const addSeatDialog = ref(false); //控制“添加坐席”弹出框
const lookInfoDialog = ref(false); //控制“查看坐席信息”弹出框
const lookInfoForm = ref({
  login_user: '',
  login_password: '',
  user_name: '',
  phone: ''
});
const user = useUser();
const duty = useDuty();
const useSeatDialog = ref(false); //控制“启动”弹出框
const allocationInfoDialog = ref(false); //控制“值守员工”弹出框

const park_name = ref(''); // 车场名称
const park_id = ref(''); // 车场ID
const park_names = ref(''); // 被托管车场名称
const park_ids = ref(''); // 被托管车场ID

const queryParams = ref({
  park_name: '',
  page: 1,
  limit: 30,
  user_name: '',
  mobile: ''
});
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

const employeeLists = ref([]); // 员工列表
const employeeSearchLoading = ref(false); // 员工搜索加载状态
let searchTimeout = null; // 搜索防抖定时器
onActivated(() => {
  handleDataSearch(); // 页面加载时获取数据
  loadInitialEmployees(); // 加载初始员工列表
});

// 通用的员工加载方法
const loadEmployees = (searchName = '', showLoading = false) => {
  if (showLoading) {
    employeeSearchLoading.value = true;
  }

  const searchParams = {};
  if (searchName && searchName.trim()) {
    searchParams.name = searchName.trim();
  }

  return seatManagementService
    .watchListEmployees(searchParams.name)
    .then((response) => {
      if (response.success === true) {
        // 根据实际API响应结构处理数据
        const employees = response.data.rows || response.data || [];
        employeeLists.value = employees.map((item) => {
          return {
            id: parseInt(item.id),
            name: item.name
          };
        });
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
      if (showLoading) {
        employeeSearchLoading.value = false;
      }
    })
    .catch((error) => {
      console.error('加载员工列表失败:', error);
      ElMessage({
        message: searchName ? '搜索员工失败，请稍后重试' : '加载员工列表失败，请稍后重试',
        type: 'error'
      });
      if (showLoading) {
        employeeSearchLoading.value = false;
      }
    });
};

// 加载初始员工列表
const loadInitialEmployees = () => {
  loadEmployees(); // 不传搜索参数，加载所有员工
};

// 远程搜索员工（带防抖）
const remoteSearchEmployees = (query) => {
  // 清除之前的定时器
  if (searchTimeout) {
    clearTimeout(searchTimeout);
  }

  if (query && query.trim().length >= 1) {
    // 设置防抖，300ms后执行搜索
    searchTimeout = setTimeout(() => {
      loadEmployees(query.trim(), true); // 传入搜索关键词和显示加载状态
    }, 300);
  } else {
    // 如果搜索为空或字符数不足，重新加载初始列表
    employeeSearchLoading.value = false;
    loadInitialEmployees();
  }
};

const addSeatFormRef = ref(null); // 用于表单验证
const addSeatRules = {
  login_user: [{ required: true, message: '请输入分机号', trigger: 'blur' }],
  login_password: [{ required: true, message: '请输入分机密码', trigger: 'blur' }]
};
const addSeatForm = ref({
  login_user: '',
  login_password: ''
});
const downLoadSeatTmp = () => {
  console.log('downLoadSeatTmp');
};
const importSeat = () => {
  console.log('importSeat');
};
// 点击了“添加坐席”按钮
const addSeat = () => {
  addSeatDialog.value = true;
};
// 点击了添加坐席“确定”按钮
const okaddSeatDialog = () => {
  addSeatFormRef.value.validate().then(() => {
    // 调用服务添加坐席
    seatManagementService.watchSeatsAdd(addSeatForm.value).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '添加成功',
          type: 'success'
        });
        addSeatDialog.value = false; // 关闭弹窗
        handleDataSearch(); // 刷新数据
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
    });
  });
};
//点击了启动坐席“确定”按钮
const okUseSeatDialog = () => {
  console.log('启用');
};
//点击了分配值守员工“确定”按钮
const assignEmployeeFormRef = ref(null); // 用于表单验证
const assignEmployeeRules = {
  user_id: [{ required: true, message: '请选择值守员工', trigger: 'change' }]
};
const okAllocationInfoDialog = () => {
  console.log('分配');
  assignEmployeeFormRef.value.validate().then(() => {
    // 调用服务添加坐席
    let params = {
      ...assignEmployeeParams.value
    };
    delete params.park_name;
    delete params.park_names;
    seatManagementService.watchSeatsAssignEmployees(params).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '分配成功',
          type: 'success'
        });
        allocationInfoDialog.value = false; // 关闭弹窗
        handleDataSearch(); // 刷新数据
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
    });
  });
};
//点击了“查看”按钮
const checkSeatInfo = (row) => {
  seatManagementService.watchSeatsDetail(row.id).then((response) => {
    if (response.success === true) {
      lookInfoForm.value = response.data;
      lookInfoDialog.value = true;
    } else {
      ElMessage({
        message: response.detail_message || response.message,
        type: 'error'
      });
    }
  });
};
// 点击了“禁用”按钮
const setAbledSeat = (id, status) => {
  const params = {
    seats_id: id,
    status // 1：启用  0：禁用  -1:删除
  };
  if (status === 1) {
    seatManagementService.watchSeatsEditStatus(params).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '启用成功',
          type: 'success'
        });
        handleDataSearch(); // 刷新数据
        getDhtQx();
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
    });
  } else if (status === 0) {
    ElMessageBox.confirm('禁用后，将无法正常使用话务功能，确认是否禁用？', '禁用', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      seatManagementService.watchSeatsEditStatus(params).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '禁用成功',
            type: 'success'
          });
          handleDataSearch(); // 刷新数据
          getDhtQx();
        } else {
          ElMessage({
            message: response.detail_message || response.message,
            type: 'error'
          });
        }
      });
    });
  } else {
    ElMessageBox.confirm('删除后，用户登录将无法使用话务功能，确认是否删除？', '删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      seatManagementService.watchSeatsEditStatus(params).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          });
          handleDataSearch(); // 刷新数据
          getDhtQx();
        } else {
          ElMessage({
            message: response.detail_message || response.message,
            type: 'error'
          });
        }
      });
    });
  }
};
const getDhtQx = () => {
  // 查询是否有坐席号
  UnattendedApi.getSeatsByUserId(user.user_id).then((res) => {
    if (res.success) {
      // callWard.value.login_user = res.data.login_user;
      // callWard.value.login_password = res.data.login_password;
      duty.callWard = {
        login_user: res.data?.login_user || null,
        login_password: res.data?.login_password || null
      };
      if (!duty.callWard.login_user) {
        window.holly?.exit(true);
      }
    }
  });
};
const assignEmployeeParams = ref({
  seats_id: undefined,
  user_id: undefined,
  login_user: undefined,
  login_password: undefined,
  park_id: undefined,
  park_name: undefined,
  park_ids: undefined,
  park_names: undefined
});

//点击“分配值守员工”按钮
const allocation = (row) => {
  allocationInfoDialog.value = true;
  assignEmployeeParams.value.login_user = row.login_user; // 传入当前行的分机号
  assignEmployeeParams.value.login_password = row.login_password; // 传入当前行的分机密码
  assignEmployeeParams.value.seats_id = row.id; // 传入当前行的ID
  assignEmployeeParams.value.user_id = row.user_id; // 传入当前行的用户ID
  assignEmployeeParams.value.park_name = row.park_name;
  assignEmployeeParams.value.park_id = row.park_id;
  assignEmployeeParams.value.park_names = row.managed_park_name;
  assignEmployeeParams.value.park_ids = row.managed_park_ids;

  // 重新加载员工列表，确保数据是最新的
  loadInitialEmployees();
};

const handleDataSearch = (params) => {
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  seatManagementService.watchSeatsList(queryParams.value).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
  queryParams.value = {
    park_name: '',
    page: 1,
    limit: 30,
    user_name: '',
    mobile: ''
  };
  handleDataSearch();
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};
const relatedParkDialogVisible = ref(false); //控制“关联车场”显示
const relatedParkDialogVisible2 = ref(false); //控制“关联车场”显示
// 显示“车场”
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_names.value = assignEmployeeParams.value.park_names.split('、');
    park_ids.value = assignEmployeeParams.value.park_ids;
    relatedParkDialogVisible.value = true;
  }
};
//选择“车场”
const renderTableInput = (val) => {
  console.log(val, 'xxxxxxxxxxxxxxxx');
  if (val && val.length > 0) {
    assignEmployeeParams.value.park_ids = val.map((item) => item.park_id);
    assignEmployeeParams.value.park_names = val.map((item) => item.park_name).join('、');
  }
};
// 显示“车场”
const authCharge2 = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible2.value = false;
  } else {
    park_name.value = assignEmployeeParams.value.park_name;
    park_id.value = assignEmployeeParams.value.park_id;
    relatedParkDialogVisible2.value = true;
  }
};
//选择“车场”
const renderTableInput2 = (val) => {
  console.log(val, 'xxxxxxxxxxxxxxxx');
  if (val && val.length > 0) {
    assignEmployeeParams.value.park_id = val[0].park_id;
    assignEmployeeParams.value.park_name = val[0].park_name;
  }
};
</script>

<style lang="scss" scoped>
.formClass {
  // height: 150px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-input {
    width: 100%;
  }
}
</style>
