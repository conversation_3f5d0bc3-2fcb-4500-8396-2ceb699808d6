<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item>
      <el-input v-model="form.queryParams.rule_name" placeholder="规则名称" clearable />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.prk_rent_rule_types" placeholder="长租类型" multiple clearable>
        <el-option v-for="item in rentTypes" :key="item.value" :label="item.name" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.product_types" placeholder="产品类型" multiple clearable>
        <el-option v-for="item in productTypes" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" placeholder="审核状态" multiple clearable>
        <el-option v-for="item in auditTypes" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="启用状态" multiple clearable>
        <el-option v-for="item in stateTypes" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.public_opens" placeholder="是否对小程序开放" multiple clearable>
        <el-option v-for="item in openTypes" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="LongRentRuleSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import dictService from '@/service/system/DictService';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    rule_name: '',
    prk_rent_rule_types: [],
    product_types: [],
    audit_states: [],
    states: [],
    public_opens: []
  }
});
const rentTypes = ref([]);
const productTypes = ref([]);
const auditTypes = ref([]);
const stateTypes = ref([]);
const openTypes = ref([
  {
    key: '是',
    value: 1
  },
  {
    key: '否',
    value: 0
  }
]);

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = async () => {
  // 长租类型
  const param1 = 'LONG_RENT_TYPE';
  // 产品类型+启用状态
  const param2 = [
    { enum_key: 'product_type_list', enum_value: 'EnumRentProductType' },
    { enum_key: 'states', enum_value: 'EnumRuleState' }
  ];
  // 审核状态
  const param3 = [{ enum_key: 'auditStates', enum_value: 'EnumAuditState' }];
  const res = await Promise.all([
    dictService.getDictsList(param1),
    commonService.findEnums('park', param2),
    commonService.findEnums('audit', param3)
  ]);
  rentTypes.value = res[0];
  let arr1 = res[1].data.product_type_list.filter((item) => item.value !== 6 && item.value !== 7 && item.value !== 8 && item.value !== 9);
  let arr2 = res[1].data.product_type_list.filter((item) => item.value == 8 || item.value == 9);
  productTypes.value = arr2.concat(arr1);
  auditTypes.value = res[2].data.auditStates;
  stateTypes.value = res[1].data.states;
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    page: 1,
    limit: 30,
    rule_name: '',
    prk_rent_rule_types: [],
    product_types: [],
    audit_states: [],
    states: [],
    public_opens: []
  };
};

defineExpose({
  handleAllReset
});
</script>
