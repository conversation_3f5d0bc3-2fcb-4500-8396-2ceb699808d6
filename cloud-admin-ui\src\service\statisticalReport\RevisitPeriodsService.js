import * as RevisitPeriodsApi from '@/api/statisticalReport/RevisitPeriodsApi';

export default {
  /**
   * 查询回访周期
   * @param {Object} params - 查询参数
   * @param {number} params.park_id - 车场id
   * @param {string} params.statistics_start_date - 开始日期
   * @param {string} params.statistics_end_date - 结束日期
   * @param {number} params.org_department_id - 组织 ID
   * @return {Promise} - Promise对象
   */
  getRevisitPeriods(data) {
    return new Promise((resolve, reject) => {
      try {
        RevisitPeriodsApi.getRevisitPeriods(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        RevisitPeriodsApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
