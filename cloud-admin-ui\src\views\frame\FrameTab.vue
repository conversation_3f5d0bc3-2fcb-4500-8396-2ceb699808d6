<template>
  <div class="frame-tab" @contextmenu.prevent="openCustomMenu">
    <div class="tab-container">
      <div v-for="(tab, index) in tabList" :key="index" class="tab" :class="index === activedTabIndex ? 'active' : ''" @click="clickTab(tab, index)">
        {{ tab.title }}
        <el-icon v-if="tabList.length > 1 && tab.title !== '仪表盘'" :size="14" @click.stop="closeTab(index)" class="close">
          <Close />
        </el-icon>
      </div>
    </div>
    <el-dropdown ref="menuRef">
      <div class="more">
        <el-icon :size="13">
          <ArrowDown />
        </el-icon>
      </div>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item @click="closeCurrentTab">关闭当前标签</el-dropdown-item>
          <el-dropdown-item @click="closeOtherTabs">关闭其他标签</el-dropdown-item>
          <el-dropdown-item @click="closeAllTabs">关闭全部标签</el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
    <div id="hd-menu-popper" class="el-popper is-pure is-light el-dropdown__popper">
      <ul class="el-dropdown-menu">
        <li @click="closeCurrentTab" class="el-dropdown-menu__item hd-menu-item">关闭当前标签</li>
        <li @click="closeOtherTabs" class="el-dropdown-menu__item hd-menu-item">关闭其他标签</li>
        <li @click="closeAllTabs" class="el-dropdown-menu__item hd-menu-item">关闭全部标签</li>
      </ul>
    </div>
  </div>
</template>

<script name="FrameTab" setup>
import { computed, onMounted, onBeforeUnmount } from 'vue';
import { useTabs } from '@/stores/tabs';
import router from '@/router';
import { ElMessage } from 'element-plus';
import { activeRouteTab } from '@/utils/tabKit';
import { useMenu } from '@/stores/menu';
import { loadMenusAndRoutes } from '@/utils/menuKit';

const tabs = useTabs();
const menu = useMenu();

const tabList = computed({
  get() {
    return tabs.state.tabList;
  },
  set(val) {
    tabs.state.tabList = val;
  }
});

const activedTabIndex = computed({
  get() {
    return tabs.state.activedTabIndex;
  },
  set(val) {
    tabs.state.activedTabIndex = val;
  }
});

const clickTab = (tab, index) => {
  tabs.state.activedTabIndex = index;
  menu.state.activedMenuIndex = tab.id;

  activeRouteTab({
    path: tab.path
  });
};

const closeTab = (index) => {
  if (tabs.state.tabList.length >= 1) {
    // 当前选中 tab 被删除
    tabs.state.tabList.splice(index, 1);
    // 设置路由
    router.push({ name: tabs.state.tabList[tabs.state.tabList.length - 1].name });
    // 设置选中下标
    tabs.state.activedTabIndex = tabs.state.tabList.length - 1;
  } else {
    router.push('/');
  }
};

// 关闭当前标签
const closeCurrentTab = () => {
  if (tabs.state.tabList.length === 1) {
    ElMessage({
      message: '当前仅有一个标签页，系统不允许关闭',
      type: 'warning'
    });
    return;
  }
  closeTab(tabs.state.activedTabIndex);
};

// 关闭其它标签
const closeOtherTabs = () => {
  tabs.state.tabList = [tabs.state.tabList[tabs.state.activedTabIndex]];
  tabs.state.activedTabIndex = 0;
};

// 关闭全部标签
const closeAllTabs = () => {
  tabs.state.tabList = [];
  activeRouteTab({ path: '/' });
};
const openCustomMenu = (event) => {
  const menuDom = document.querySelector('#hd-menu-popper');
  if (!menuDom) return;
  menuDom.style.display = 'block';
  menuDom.style.zIndex = 1000;
  menuDom.style.left = event.clientX + 'px';
  menuDom.style.top = event.clientY + 'px';
};
onMounted(() => {
  window.addEventListener('click', closeCustomMenu);
});
onBeforeUnmount(() => {
  window.removeEventListener('click', closeCustomMenu);
});
const closeCustomMenu = () => {
  const menuDom = document.querySelector('#hd-menu-popper');
  menuDom && (menuDom.style.display = 'none');
};
</script>

<style lang="scss" scoped>
$tab-actived-color: #1e9fff;

.frame-tab {
  margin: 0 10px;
  padding: 6px 6px;
  width: calc(100% - 24px);
  height: 43px;
  display: flex;
  justify-content: space-between;
  background-color: #fafafa;
  border-bottom: 1px solid #dcdfe6;

  .tab-container {
    width: 100%;
    display: flex;
    overflow-x: scroll;
    height: 36px;
  }

  .tab-container::-webkit-scrollbar {
    height: 2px;
  }
  .tab-container::-webkit-scrollbar-thumb {
    border-radius: 2px;
    background-color: #1e9fff;
  }

  .tab {
    flex-shrink: 0;
    user-select: none;
    display: inline-block;
    height: 31px;
    padding: 6px 8px;
    margin: 0px 3px;
    min-width: 90px;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    color: rgba(0, 0, 0, 0.6);
    font-weight: 400;
    border: 1px solid #1e9fff;
    color: #1e9fff;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .tab:hover {
    font-weight: bold;
  }
  .active {
    background-color: $tab-actived-color;
    color: #fff;
    font-weight: 400;
  }

  .active:hover {
    background-color: $tab-actived-color;
  }

  .more {
    display: inline-block;
    padding: 8px;
    margin: 0px 3px;
    vertical-align: middle;
    background-color: #fff;
    text-align: center;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 8px #f0f1f2;
    height: 31px;

    .tab-oper:hover {
      font-weight: 600;
    }
  }
}

.el-icon {
  top: 2px;
}

.close {
  transition: all 0.5s ease 0s;
  transform: rotateZ(0turn);
  transform-origin: 50% 50%;
}

.close:hover {
  transition: all 0.3s ease 0s;
  transform: rotateZ(0.25turn);
  transform-origin: 50% 50%;
}
#hd-menu-popper {
  display: none;
  .hd-menu-item:hover {
    background-color: #ecf5ff;
    color: #409eff;
  }
}
</style>
