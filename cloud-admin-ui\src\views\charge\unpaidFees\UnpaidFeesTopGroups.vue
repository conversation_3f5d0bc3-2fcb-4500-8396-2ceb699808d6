<template>
  <div>
    <div class="search-btn-group" v-loading="loading">
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data0.sum_money }}元 / {{ data0.count_order }}笔</p>
        <span class="search-btn-group-total-label">欠缴总额/笔数</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data1.sum_money }}元 / {{ data1.count_order }}笔</p>
        <span class="search-btn-group-total-label">实缴金额/笔数</span>
      </div>
      &ensp;
      <!-- <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data2.sum_money }}元 / {{ data2.count_order }}笔</p>
        <span class="search-btn-group-total-label">未缴金额/笔数</span>
      </div> -->
    </div>
  </div>
</template>

<script name="ParkFeeTopGroups" setup>
import unpaidFeesService from '@/service/charge/UnpaidFeesService';
import '@/styles/searchBtnGroup.scss';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
const loading = ref(false);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  countData: {
    count_order_money: 0,
    count_debate_money: 0,
    count_should_pay_money: 0
  }
});
const data0 = ref({})
const data1 = ref({})
const data2 = ref({})
onMounted(() => {
  data.queryParams.order_states = [1, 2];
  // countParkPayRecord(data.queryParams);
});

const countParkPayRecord = (queryParams) => {
  // loading.value = true;
  data.queryParams = queryParams;
  const { park_name, ...newQueryParams } = queryParams; ''
  unpaidFeesService.parkRecoveryPayRecordsdata({
    ...newQueryParams,
    order_state: ''
  }).then((response) => {
    if (response.success === true) {
      data0.value = response.data;
      // loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  unpaidFeesService.parkRecoveryPayRecordsdata({
    ...newQueryParams,
    order_state: '2'
  }).then((response) => {
    if (response.success === true) {
      data1.value = response.data;
      // loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  unpaidFeesService.parkRecoveryPayRecordsdata({
    ...newQueryParams,
    order_state: '4'
  }).then((response) => {
    if (response.success === true) {
      data2.value = response.data;
      // loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  countParkPayRecord
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
</style>
