<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="产权方名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.contact_name" placeholder="联系人" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.contact_mobile" placeholder="联系人电话" /></form-search-item>
  </FormSearch>
</template>

<script name="PropertyOwnerSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: '',
    contact_name: '',
    contact_mobile: '',
    page: 1,
    limit: 30
  }
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    contact_name: '',
    contact_mobile: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
