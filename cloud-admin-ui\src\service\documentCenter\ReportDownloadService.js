import * as reportDownloadApi from '@/api/documentCenter/ReportDownloadApi';

/**
 * 报表下载
 */
export default {
  /**
   * 分页查询报表下载
   */
  pagingReportExportRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        reportDownloadApi.pagingReportExportRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 下载报表
   */
  downloadRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        reportDownloadApi.downloadRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除报表导出记录
   */
  deleteReportExportRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        reportDownloadApi.deleteReportExportRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
