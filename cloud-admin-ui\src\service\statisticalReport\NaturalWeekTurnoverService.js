import * as naturalWeekTurnoverApi from '@/api/statisticalReport/NaturalWeekTurnoverApi';

/**
 * 自然周周转率
 */
export default {
  /**
   * 分页查询自然周周转率
   */
  pagingNaturalWeek(data) {
    return new Promise((resolve, reject) => {
      try {
        naturalWeekTurnoverApi.pagingNaturalWeek(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        naturalWeekTurnoverApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
