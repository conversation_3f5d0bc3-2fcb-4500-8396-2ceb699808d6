/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找车位
export const pagingParkSpace = (data) => {
  return $({
    url: '/console/park/space/pagingParkSpace',
    method: 'post',
    data
  });
};

// 新建车位
export const createParkSpace = (data) => {
  return $({
    url: '/console/park/space/createParkSpace',
    method: 'post',
    data
  });
};

// 修改车位
export const updateParkSpace = (data) => {
  return $({
    url: '/console/park/space/updateParkSpace',
    method: 'post',
    data
  });
};

// 删除车位
export const deleteParkSpace = (id) => {
  return $({
    url: '/console/park/space/deleteParkSpace/' + id,
    method: 'post'
  });
};

// 车场列表
export const parkList = (data) => {
  return $({
    url: '/console/park/park/listPark',
    method: 'post',
    data
  });
};

// 子场列表
export const listParkRegion = (parkId) => {
  return $({
    url: '/console/park/region/listParkRegion/' + parkId,
    method: 'get'
  });
};

//通过车场id查询车位信息
export const listParkSpace = (parkId) => {
  return $({
    url: '/console/park/space/listParkSpace/' + parkId,
    method: 'get'
  });
};

//导入
export const importExcel = (data) => {
  return $({
    url: '/console/park/space/importExcel',
    method: 'post',
    data
  });
};

//通过车场id查询新增时车位信息
export const listAvailableLongRentSpace = (parkId) => {
  return $({
    url: '/console/park/rent/space/apply/listAvailableLongRentSpace?parkId=' + parkId,
    method: 'get'
  });
};

//查询编辑时车位信息
export const listRentSpaceById = (parkId, rentApplyId) => {
  return $({
    url: `/console/park/rent/space/apply/listRentSpaceById?parkId=${parkId}&rentApplyId=${rentApplyId}`,
    method: 'get'
  });
};
