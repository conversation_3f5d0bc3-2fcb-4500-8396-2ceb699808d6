<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="longRentPayService.exportLongRentPay"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        >
        </DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 324px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button
              link
              type="primary"
              v-if="
                scope.row.pay_state === 2 &&
                (scope.row.refund_state === 3 || scope.row.refund_state === 4) &&
                (scope.row.rent_state === 1 || scope.row.rent_state === 0) &&
                scope.row.payed_money > 0
              "
              @click="refundMoney(scope.row)"
            >
              申请退款
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="订单编号" align="center" width="180">
          <template #default="scope"> {{ scope.row.order_no || '--' }} </template>
        </el-table-column>
        <el-table-column prop="renew_state_desc" label="续费类型" align="center" width="180" />
        <el-table-column prop="park_name" label="停车场名称" align="center" width="180" />
        <el-table-column prop="park_code" label="车位编号" align="center" width="100" />
        <el-table-column prop="rent_rule_name" label="规则名称" align="center" width="100" />
        <el-table-column prop="product_type_desc" label="产品类型" align="center" width="180" />
        <el-table-column prop="product_range" label="产品周期" align="center" min-width="120">
          <template v-slot="scope">
            <span v-if="scope.row.product_range">{{
              formatRentProductRangeText(rentProductRanges, scope.row.product_range, scope.row.product_type)
            }}</span>
            <span v-else> --</span>
          </template>
        </el-table-column>
        <el-table-column prop="rent_time" label="长租时段" align="center" min-width="120">
          <template #default="{ row }">
            <el-tooltip v-if="row.rent_time" effect="dark" placement="top-start">
              <span style="white-space: pre">{{ row.rent_time }}</span>
              <template #content>
                <span style="white-space: pre">{{ row.rent_time }}</span>
              </template>
            </el-tooltip>
            <span v-else>全时段</span>
          </template>
        </el-table-column>
        <el-table-column prop="payed_money" label="支付金额" align="center" width="100" />
        <el-table-column prop="payed_time" label="支付时间" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.payed_time || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.pay_method_desc }} {{ scope.row.pay_method === 4 ? '（' + scope.row.pay_type_desc + '）' : '' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="prk_rent_rule_type_desc" label="长租类型" align="center" width="100" />
        <el-table-column label="长租有效期" align="center" width="300">
          <template #default="scope">
            <span v-if="scope.row.valid_start_time != null && scope.row.valid_end_time != null"
              >{{ scope.row.valid_start_time }}~{{ scope.row.valid_end_time }}</span
            >
            <span v-else></span>
          </template>
        </el-table-column>
        <el-table-column prop="pay_state_desc" label="支付状态" align="center" width="100" />
        <el-table-column prop="rent_state_desc" label="长租状态" align="center" width="100" />
        <el-table-column prop="plate_nos" label="车牌号" align="center" width="180" show-overflow-tooltip />
        <el-table-column prop="member_name" label="车主姓名" align="center" width="100" />
        <el-table-column prop="member_mobile" label="手机号" align="center" width="120" />
        <el-table-column prop="user_identity_desc" label="用户身份" align="center" width="120" />
        <el-table-column prop="channel_desc" label="申请来源" align="center" width="100" />
        <el-table-column prop="invoice_state_desc" label="发票状态" align="center" width="100" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="100" />
        <el-table-column prop="refund_money" label="退款金额" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.refund_money || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audit_reason" label="驳回原因" align="center" width="100" />
        <el-table-column prop="payed_voucher_url" label="缴费凭证" align="center" width="100">
          <template v-slot="scope">
            <template v-if="!!scope.row.payed_voucher_url">
              <el-link
                v-for="(url, index) in JSON.parse(scope.row.payed_voucher_url)"
                :key="index"
                type="primary"
                @click="exportAuditData(url.audit_data_url)"
                >{{ url.audit_data_name }}</el-link
              >
            </template>
            <template v-else></template>
          </template>
        </el-table-column>
        <el-table-column prop="payed_memo" label="付款备注" align="center" width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 申请退款 -->
    <el-dialog v-if="refundDialogVisible" width="600px" title="申请退款" v-model="refundDialogVisible" @close="closeDialog(refund)">
      <el-form ref="refund" label-width="110px" :rules="data.rules" :model="data.refundForm">
        <el-form-item prop="valid_end_time" label="原长租结束时间" v-if="data.refundForm.rent_state == 1">
          <el-input v-model="data.refundForm.valid_end_time" disabled />
        </el-form-item>
        <el-form-item prop="payed_money" label="原支付金额">
          <el-input v-model="data.refundForm.payed_money" disabled />
        </el-form-item>
        <el-form-item prop="pay_method_desc" label="原支付方式">
          <el-input v-model="data.refundForm.pay_method_desc" disabled />
        </el-form-item>
        <el-form-item prop="rent_state_desc" label="当前长租状态">
          <el-input v-model="data.refundForm.rent_state_desc" disabled />
        </el-form-item>
        <el-form-item prop="time" label="长租终止日期" v-if="data.refundForm.rent_state == 1">
          <el-date-picker
            v-model="data.refundForm.time"
            placeholder="请选择提前终止时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            :disabled-date="disabledDateFn"
            @change="handleDateChange"
          />
        </el-form-item>
        <el-form-item prop="refund_days" label="可退天数" v-if="data.refundForm.rent_state == 1">
          <el-input v-model="data.refundForm.refund_days" placeholder="系统自动计算" disabled />
        </el-form-item>
        <el-form-item prop="refund_money" label="可退金额">
          <el-input v-model="data.refundForm.refund_money" placeholder="系统自动计算" :disabled="data.refundForm.rent_state != 1" />
        </el-form-item>
        <el-form-item label="退款方式">
          <el-input v-if="data.refundForm.pay_method === 4" model-value="原支付路径退回" disabled />
          <el-input v-else model-value="线下人工退款" disabled />
        </el-form-item>
        <template v-if="data.refundForm.pay_method != 4">
          <el-form-item prop="card_bank" label="收款银行">
            <el-select v-model="data.refundForm.card_bank" placeholder="请选择车主收款银行" filterable style="width: 100%">
              <el-option v-for="item in bankList" :key="item.key" :label="item.key" :value="item.key" />
            </el-select>
          </el-form-item>
          <el-form-item prop="refund_account" label="收款账号">
            <el-input
              v-model="data.refundForm.refund_account"
              :formatter="(value) => `${value}`.replace(/(\d{4})(?=\d)/g, '$1 ').trim()"
              :parser="(value) => value.replace(/\$\s?|( *)/g, '')"
              placeholder="请输入车主收款账号"
            />
          </el-form-item>
        </template>
        <el-form-item prop="refund_reason" label="退款原因">
          <div style="display: flex; flex-direction: column; align-items: flex-start; width: 100%">
            <el-input v-model="data.refundForm.refund_reason" type="textarea" placeholder="请输入退款原因，限定50字以内" :rows="3" :maxlength="50" />
            <span style="color: #f56c6c">{{ '提示：请认真核对' + (data.refundForm.pay_method != 4 ? '收款账号与' : '') + '金额信息' }}</span>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">取 消</el-button>
          <el-button type="primary" :loading="refundLoading" @click="applyRefund(refund)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script name="LongRentPayTable" setup>
import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted } from 'vue';
import longRentPayService from '@/service/charge/LongRentPayService';
import commonService from '@/service/common/CommonService';
import { useRoute } from 'vue-router';
import { getToken } from '@/utils/common';
import { useUser } from '@/stores/user';
import { ElMessageBox } from 'element-plus';
import { GetIamTokenOpenFlag, getOpenUrl } from '@/utils/iamFlow';
import DownloadButton from '@/components/DownloadButton.vue';
import { dayjs } from 'element-plus';
import { rentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';

const user = useUser();
const validateRefundMoney = (rule, value, callback) => {
  if (data.refundForm.refund_money > data.refundForm.payed_money) {
    callback(new Error('退款金额不能大于支付金额!'));
  }
  callback();
};
const validateRefundAccount = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^\d+$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的退款账号!'));
    }
  }
  callback();
};

const route = useRoute();
const tableData = ref([]);
const refundList = ref([]);
// 收款银行枚举
const bankList = ref([]);
const loading = ref(false);
const total = ref(0);
const refund = ref();
const refundDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    member_id: '',
    page: 1,
    limit: 30
  },
  refundForm: {
    time: undefined,
    refund_money: undefined,
    refund_channel: undefined,
    card_bank: undefined,
    refund_account: undefined,
    refund_reason: undefined
  },
  rules: {
    time: [
      {
        required: true,
        message: '请选择长租截止日期',
        trigger: 'blur'
      }
    ],
    refund_days: [
      {
        required: true,
        message: '请输入可退天数',
        trigger: 'blur'
      }
    ],
    refund_money: [
      {
        required: true,
        message: '请输入退款金额',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateRefundMoney
      }
    ],
    refund_channel: [
      {
        required: true,
        message: '请选择退款渠道',
        trigger: 'blur'
      }
    ],
    card_bank: [
      {
        required: true,
        message: '请选择退款银行',
        trigger: 'change'
      }
    ],
    refund_account: [
      {
        required: true,
        message: '请输入退款账号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateRefundAccount
      }
    ],
    refund_reason: [
      {
        required: true,
        message: '请输入退款原因',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  if ({} !== route.query && undefined !== route.query.member_id) {
    data.queryParams.member_id = route.query.member_id;
  }
  initSelects();
  // getList(data.queryParams);
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const disabledDateFn = (time) => {
  let valid_end_time = dayjs(data.refundForm.valid_end_time).subtract(1, 'day').endOf('day');
  return time.getTime() >= valid_end_time || time.getTime() < dayjs().startOf('day');
};

const handleDateChange = (value) => {
  if (value) {
    data.refundForm.time = dayjs(value).endOf('day').format('YYYY-MM-DD HH:mm:ss');
    longRentPayService.calculateRefundAmount({ id: data.refundForm.id, stop_date: data.refundForm.time }).then((response) => {
      if (response.data?.detailMessage) {
        ElMessage.error(response.data.detailMessage);
      } else {
        data.refundForm.refund_days = response.data.refund_days;
        data.refundForm.refund_money = response.data.refund_amount;
        refund.value.validateField('refund_money');
      }
    });
  }
};

const initSelects = () => {
  const param = [{ enum_key: 'refundList', enum_value: 'EnumRefundChannelType' }];
  commonService.findEnums('park', param).then((response) => {
    refundList.value = response.data.refundList;
  });
  const bankListParam = [{ enum_key: 'bankList', enum_value: 'EnumRefundBankState' }];
  commonService.findEnums('fee', bankListParam).then((response) => {
    bankList.value = response.data.bankList;
  });
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  longRentPayService
    .pagingLongRentPay(params)
    .then((response) => {
      if (response.data?.detailMessage) {
        ElMessage.error(response.data.detailMessage);
      } else {
        tableData.value = response.data.rows;
        total.value = parseInt(response.data.total);
      }
      loading.value = false;
    })
    .catch(() => {
      loading.value = false;
    });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 申请退款
const refundMoney = (row) => {
  data.refundForm = {
    id: row.id,
    park_id: row.park_id,
    mobile: row.member_mobile,
    member_name: row.member_name,
    time: row.time,
    valid_start_time: row.valid_start_time,
    valid_end_time: row.valid_end_time,
    refund_money: row.refund_money,
    refund_channel: row.refund_channel,
    refund_account: row.refund_account,
    refund_reason: row.refund_reason,
    payed_money: row.payed_money,
    pay_method: row.pay_method,
    pay_method_desc: row.pay_method_desc + (row.pay_method === 4 ? '（' + row.pay_type_desc + '）' : ''),
    rent_state: row.rent_state,
    rent_state_desc: row.rent_state_desc
  };
  if (data.refundForm.rent_state != 1) {
    data.refundForm.refund_money = row.payed_money;
    data.refundForm.time = row.valid_end_time;
  }
  if (row.pay_method === 4) {
    // 易宝原路返回
    data.refundForm.refund_channel = 7;
    data.refundForm.refund_account = row.member_name;
  } else {
    // 线下人工退款
    data.refundForm.refund_channel = 8;
  }
  refundDialogVisible.value = true;
};
const refundLoading = ref(false);
const applyRefund = (refund) => {
  console.log(refund);
  if (data.refundForm.refund_money == 0) {
    ElMessage({
      message: '退款金额为0元，无需退款',
      type: 'error'
    });
    return;
  }
  refund.validate().then(() => {
    refundLoading.value = true;
    longRentPayService
      .applyRefund(data.refundForm)
      .then((response) => {
        refundLoading.value = false;
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          refundDialogVisible.value = false;
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        refundLoading.value = false;
        // getList(data.queryParams);
      })
      .finally(() => {
        refundLoading.value = false;
      });
  });
};
const closeDialog = (refund) => {
  refund.resetFields();
};

const exportAuditData = (val) => {
  window.open(val, '_blank');
};

// 产品周期内容格式化
const formatRentProductRangeText = (rentProductRanges, product_range, product_type) => {
  if (product_type == 8) {
    return product_range + '天';
  } else if (product_type == 9) {
    return product_range + '周';
  } else {
    return getRentProductRangeText(rentProductRanges, product_range) || '--';
  }
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
