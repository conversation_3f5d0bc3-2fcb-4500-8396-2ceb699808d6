<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly @click="authCharge(true, '')" placeholder="请选择停车场"
    /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.merchant_name" placeholder="商户名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.coupon_meta_name" placeholder="优免券名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" placeholder="优免券类型" multiple clearable>
        <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" placeholder="审核状态" multiple clearable>
        <el-option v-for="item in enableds" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>

    <!-- 车场查找带回 -->
    <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
      <park-find-back mode="search" @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
    </el-dialog>
  </FormSearch>
</template>

<script name="CouponSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '../ParkFindBack.vue';
import { reactive, ref, onActivated } from 'vue';

const parkInfoDialogVisible = ref(false);
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    merchant_name: '',
    coupon_meta_name: '',
    types: [],
    audit_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const enableds = ref([]);
const types = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'enableds', enum_value: 'EnumAuditState' },
    { enum_key: 'types', enum_value: 'EnumCouponMetaType' }
  ];
  commonService.findEnums('coupon', param).then((response) => {
    enableds.value = response.data.enableds;
    types.value = response.data.types;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_id: '',
    merchant_name: '',
    coupon_meta_name: '',
    types: [],
    audit_states: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
//车场查找带回;
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
