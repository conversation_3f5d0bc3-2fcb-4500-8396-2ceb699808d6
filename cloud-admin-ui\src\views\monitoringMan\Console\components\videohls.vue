<template>
  <div class="monitor-player">
    <!-- <div class="video-wrapper">
      <video ref="videoElement" controls autoplay muted playsinline class="video-element"></video>
    </div>
    <div class="player-controls">
      <button @click="togglePlay" class="control-button">
        {{ isPlaying ? '暂停' : '播放' }}
      </button>
      <button @click="toggleMute" class="control-button">
        {{ isMuted ? '取消静音' : '静音' }}
      </button>
      <input type="range" min="0" max="1" step="0.1" v-model="volume" class="volume-slider" />
      <span class="player-status">{{ playerStatus }}</span>
    </div> -->
  </div>
</template>

<script setup>
import { onActivated, onDeactivated, ref } from 'vue';
// import Hls from 'hls.js';

const props = defineProps({
  videoUrl: {
    type: String,
    default:
      'https://open.ys7.com/v3/openlive/GB0757511_1_2.m3u8?expire=1785656732&id=873592707413454848&t=e7cccccffa8c1844fc4b8c541de196c68742e6bfce6943603831e5544a873274&ev=100'
  }
});

const videoElement = ref(null);
const hlsInstance = ref(null);
const isPlaying = ref(false);
const isMuted = ref(true);
const volume = ref(0.5);
const playerStatus = ref('初始化播放器...');

const initPlayer = () => {
  return
  if (Hls.isSupported()) {
    hlsInstance.value = new Hls({
      enableWorker: true,
      lowLatencyMode: true,
      backBufferLength: 30
    });

    hlsInstance.value.loadSource(props.videoUrl);
    hlsInstance.value.attachMedia(videoElement.value);

    hlsInstance.value.on(Hls.Events.MANIFEST_PARSED, () => {
      playerStatus.value = '视频加载完成';
      videoElement.value
        .play()
        .then(() => {
          isPlaying.value = true;
          playerStatus.value = '正在播放';
        })
        .catch((err) => {
          playerStatus.value = `播放失败: ${err.message}`;
        });
    });

    hlsInstance.value.on(Hls.Events.ERROR, (event, data) => {
      if (data.fatal) {
        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            playerStatus.value = '网络错误，尝试恢复...';
            hlsInstance.value.startLoad();
            break;
          case Hls.ErrorTypes.MEDIA_ERROR:
            playerStatus.value = '媒体错误，尝试恢复...';
            hlsInstance.value.recoverMediaError();
            break;
          default:
            playerStatus.value = '不可恢复的错误';
            destroyPlayer();
            break;
        }
      }
    });
  } else if (videoElement.value.canPlayType('application/vnd.apple.mpegurl')) {
    // 原生支持HLS的浏览器（如Safari）
    videoElement.value.src = props.videoUrl;
    videoElement.value.addEventListener('loadedmetadata', () => {
      videoElement.value
        .play()
        .then(() => {
          isPlaying.value = true;
          playerStatus.value = '正在播放';
        })
        .catch((err) => {
          playerStatus.value = `播放失败: ${err.message}`;
        });
    });
  } else {
    playerStatus.value = '您的浏览器不支持播放此视频流';
  }
};

const togglePlay = () => {
  if (isPlaying.value) {
    videoElement.value.pause();
    playerStatus.value = '已暂停';
  } else {
    videoElement.value
      .play()
      .then(() => {
        playerStatus.value = '正在播放';
      })
      .catch((err) => {
        playerStatus.value = `播放失败: ${err.message}`;
      });
  }
  isPlaying.value = !isPlaying.value;
};

const toggleMute = () => {
  videoElement.value.muted = !videoElement.value.muted;
  isMuted.value = videoElement.value.muted;
  playerStatus.value = videoElement.value.muted ? '已静音' : '取消静音';
};

const destroyPlayer = () => {
  if (hlsInstance.value) {
    hlsInstance.value.destroy();
  }
};

onActivated(() => {
  destroyPlayer();
  setTimeout(() => {
    initPlayer();
    videoElement.value.volume = volume.value;
  }, 500);
});

onDeactivated(() => {
  destroyPlayer();
});
</script>

<style scoped>
.monitor-player {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.video-wrapper {
  position: relative;
  padding-bottom: 56.25%;
  /* 16:9 比例 */
  height: 0;
  overflow: hidden;
  background-color: #000;
  margin-bottom: 15px;
}

.video-element {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: contain;
}

.player-controls {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  background-color: #eee;
  border-radius: 4px;
}

.control-button {
  padding: 5px 10px;
  background-color: #4caf50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.control-button:hover {
  background-color: #45a049;
}

.volume-slider {
  flex-grow: 1;
  max-width: 100px;
}

.player-status {
  margin-left: auto;
  font-size: 0.9em;
  color: #666;
}
</style>
