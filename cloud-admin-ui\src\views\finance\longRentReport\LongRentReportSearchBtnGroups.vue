<template>
  <div class="search-btn-group" v-loading="loading">
    <div class="search-btn-group-total" @click="handleSearchTotal" v-bind:class="{ onActiv: totalActive }">
      <p class="search-btn-group-total-num">{{ total_money }}元</p>
      <p class="search-btn-group-total-num">{{ total_count }}笔</p>
      <span class="search-btn-group-total-label">长租总收入</span>
    </div>
    &ensp;
    <div class="search-btn-group-total" @click="handleSearchTotal" v-bind:class="{ onActiv: totalActive }">
      <p class="search-btn-group-total-num">{{ new_money }}元</p>
      <p class="search-btn-group-total-num">{{ new_count }}笔</p>
      <span class="search-btn-group-total-label">新开通总收入</span>
    </div>
    &ensp;
    <div class="search-btn-group-total" @click="handleSearchTotal" v-bind:class="{ onActiv: totalActive }">
      <p class="search-btn-group-total-num">{{ renew_money }}元</p>
      <p class="search-btn-group-total-num">{{ renew_count }}笔</p>
      <span class="search-btn-group-total-label">续费总收入</span>
    </div>
  </div>
</template>

<script name="LongRentReportSearchBtnGroups" setup>
import longRentReportService from '@/service/finance/LongRentReportService';
import { reactive, getCurrentInstance, ref } from 'vue';
import '@/styles/searchBtnGroup.scss';

const { proxy } = getCurrentInstance();
const loading = ref(false);
const totalActive = ref(true);
const new_money = ref(0);
const new_count = ref(0);
const renew_money = ref(0);
const renew_count = ref(0);
const total_count = ref(0);
const total_money = ref(0);
const data = reactive({
  queryParams: {
      park_name: undefined,
      park_id: undefined,
    page: 1,
    limit: 30
  },
  onActive: [null, null]
});

const findLongRentReportByMoney = (queryParams) => {
  loading.value = true;
  longRentReportService.getMoney(queryParams).then((response) => {
    new_money.value = response.data.new_money;
    new_count.value = response.data.new_count;
    renew_money.value = response.data.renew_money;
    renew_count.value = response.data.renew_count;
    total_count.value = response.data.total_count;
    total_money.value = response.data.total_money;
  });
  loading.value = false;
};

const handleSearchTotal = () => {
  totalActive.value = true;
  data.onActive = [null, null];
  // proxy.$emit('search', data.queryParams);
};

defineExpose({
  findLongRentReportByMoney
});
</script>
<style lang="scss" scoped></style>
