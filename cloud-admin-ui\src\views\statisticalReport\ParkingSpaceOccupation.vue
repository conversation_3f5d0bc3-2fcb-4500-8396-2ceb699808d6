<template>
  <div class="container">
    <timed-access-search @form-search="searchTimedAccessList" @reset="resetParamsAndData" />
    <timed-access-table ref="table" />
  </div>
</template>

<script name="TimedAccess" setup>
import { reactive, ref } from 'vue';
import TimedAccessSearch from './parkingSpaceOccupation/parkingSpaceOccupationSearch.vue';
import TimedAccessTable from './parkingSpaceOccupation/parkingSpaceOccupationTable.vue';

const table = ref(null);
const params = reactive({});

const searchTimedAccessList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
