<template>
  <div class="container">
    <coupon-record-search @form-search="searchCouponRecord" @reset="resetParamsAndData" />
    <coupon-record-table ref="table" />  
  </div>
</template>

<script setup name="CouponRecord">
import CouponRecordSearch from './coupon/CouponRecordSearch.vue';
import CouponRecordTable from './coupon/CouponRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchCouponRecord = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
