<template>
  <div class="container">
    <el-card>
      <!-- navbar -->
      <div class="navbox">
        <div v-for="(i, index) in navbardata" :key="index" :class="{ activ: navindex == index }" @click="changeNav(index)">{{ i }}</div>
      </div>
    </el-card>
    <!-- coupon-record-search组件 -->
    <coupon-record-search @form-search="searchCouponRecord" :type="navindex" @reset="resetParamsAndData" :isreset="navindex" />
    <!-- coupon-record-table组件 -->
    <coupon-record-table v-show="navindex" ref="table" />  
    <!-- 引入CouponRecordmaiTable组件 -->
    <CouponRecordmaiTable v-show="!navindex" ref="maiTbale" />
  </div>
</template>

<script setup name="CouponRecord">
import CouponRecordSearch from './coupon/CouponRecordSearch.vue'; //引入coupon-record-search组件
import CouponRecordTable from './coupon/CouponRecordTable.vue'; //引入coupon-record-table组件
import CouponRecordmaiTable from './coupon/CouponRecordmaiTable.vue'; //引入CouponRecordmaiTable组件
import { ref, reactive } from 'vue'; //引入vue
const navindex = ref(0); //控制导航栏
const maiTbale = ref(null); //coupon-record-table组件实例
const table = ref(null); //CouponRecordmaiTable组件实例
//初始化数据用来进行"重置"
const params = reactive({
  park_id: '',
  park_name: '',
  organization_ids: '',
  start_time: '',
  end_time: '',
  page: 1,
  limit: 30
});
const navbardata = ['优免券售卖数据', '优免券核销数据']; //navbar数据
// 当子组件自定义事件"form-search"被触发时，调用
const searchCouponRecord = (queryParams) => {
  //判断导航栏是否为核销数据，如果是则调用核销数据的搜索方法，否则调用售卖数据的搜索方法
  if (navindex.value) {
    table.value.getList(queryParams);
    console.log('核销数据');
    return;
  }
  console.log('售卖数据');
  maiTbale.value.getList(queryParams);
};
// 当子组件自定义事件"reset"被触发时，调用
const resetParamsAndData = () => {
  //判断导航栏是否为核销数据，如果是则调用核销数据的重置方法，否则调用售卖数据的重置方法
  if (navindex.value) {
    return table.value.getList(params);
  }
  maiTbale.value.getList(params);
};
// 切换导航栏
const changeNav = (index) => {
  navindex.value = index;
};
</script>
<style lang="scss" scoped>
.navbox {
  height: 25px;
  display: flex;
  gap: 40px;
  align-items: center;
  padding-left: 10px;
  > div {
    cursor: pointer;
  }
}
.activ {
  position: relative;
  width: fit-content;
  color: #409eff;
  &::after {
    content: '';
    position: absolute;
    height: 3px;
    width: 60%;
    background-color: #409eff;
    border-radius: 3px;
    bottom: -5px;
    left: 50%;
    transform: translateX(-50%);
  }
}
</style>
