<template>
  <el-card shadow="never" class="tree-card">
    <template #header>
      <div class="card-header">
        <span>菜单树</span>
        <el-select v-model="category" style="width: 120px" @change="handleChangeMenuCategory">
          <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </div>
    </template>
    <div style="max-height: calc(100vh - 180px); overflow: auto; padding-bottom: 10px">
      <el-tree
        ref="treeRef"
        :data="treeData"
        :props="defaultProps"
        node-key="id"
        show-checkbox
        :default-expand-all="true"
        check-strictly
        @check="handleCheckMenuNode"
      />
    </div>
  </el-card>
</template>

<script name="MenuTree" setup>
import menuService from '@/service/system/MenuService';
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElTree } from 'element-plus';

const emits = defineEmits(['checkMenuNode']);
const treeRef = ref();
const categories = ref([]);
const category = ref('');
const treeData = ref([]);
const defaultProps = reactive({
  children: 'children',
  label: 'name'
});

onActivated(() => {
  listMenuCategories();
});

// 查询菜单分类
const listMenuCategories = () => {
  menuService.listMenuCategories().then((response) => {
    if (response.success === true) {
      categories.value = response.data;
      if (categories.value.length > 0) {
        category.value = categories.value[0].id;
        getMenuTree();
      }
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
// 获取菜单树
const getMenuTree = () => {
  treeRef.value.setCheckedKeys([]);
  emits('checkMenuNode', {
    id: undefined,
    name: undefined
  });
  menuService.getMenuTree(category.value).then((response) => {
    if (response.success === true) {
      treeData.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
// 当改变菜单分类，菜单树也对应改变
const handleChangeMenuCategory = () => {
  getMenuTree();
};
// 检查菜单节点
const handleCheckMenuNode = (data) => {
  const isCheck = treeRef.value.getCheckedNodes().indexOf(data) > -1;
  if (isCheck === true) {
    treeRef.value.setCheckedKeys([data.id]);
    emits('checkMenuNode', data);
  }
};
// 父组件调用子组件需要引入并添加defineExpose
defineExpose({
  getMenuTree
});
</script>
<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 6px 10px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.tree-card {
  height: 100%;
}
</style>
