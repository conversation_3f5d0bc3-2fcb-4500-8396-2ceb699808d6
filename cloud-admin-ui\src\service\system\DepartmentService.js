import * as department from '@/api/system/DepartmentApi';

/**
 * 部门服务层
 */
export default {
  /**
   * 部门树查询
   */
  departmentTree() {
    return new Promise((resolve, reject) => {
      try {
        department.departmentTree().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询部门
   */
  pagingDepartment(data) {
    return new Promise((resolve, reject) => {
      try {
        department.pagingDepartment(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增部门
   */
  addDepartment(data) {
    return new Promise((resolve, reject) => {
      try {
        department.addDepartment(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改部门
   */
  updateDepartment(data) {
    return new Promise((resolve, reject) => {
      try {
        department.updateDepartment(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除部门
   */
  deleteDepartment(data) {
    return new Promise((resolve, reject) => {
      try {
        department.deleteDepartment(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  // /**
  //  * 员工下拉框
  //  */
  //  listEmployees () {
  //   return new Promise((resolve, reject) => {
  //     try {
  //       department.listEmployees().then(function (res) {
  //         resolve(res.data);
  //       });
  //     } catch (error) {
  //       reject(error);
  //     }
  //   });
  // },

  /**
   * 分页查询员工(按部门ID)
   * @param {*} data
   * @returns
   */
  pagingEmployeesByDepartmentId(data) {
    return new Promise((resolve, reject) => {
      try {
        department.pagingEmployeesByDepartmentId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
