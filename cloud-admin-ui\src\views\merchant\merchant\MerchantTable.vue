<template>
  <el-card class="table" shadow="never">
    <!-- 添加商户按钮 -->
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加商户</el-button>
      </el-space>
    </div>
    <div ref="table">
      <!-- 搜索的商户table -->
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link v-if="scope.row.state == 0" type="success" @click="enable(scope.row.id)"> 启用 </el-button>
            <el-button link v-if="scope.row.state == 1" type="danger" @click="disable(scope.row.id)"> 停用 </el-button>
            <el-button link type="warning" @click="resetPassword(scope.row.id)"> 重置密码 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="login_name" label="商户账号" align="center" />
        <el-table-column prop="name" label="商户名称" align="center" />
        <el-table-column prop="first_category_name" label="商户类型" align="center" />
        <el-table-column prop="address" label="详细地址" align="center" />
        <el-table-column prop="contact" label="联系人姓名" align="center" />
        <el-table-column prop="contact_mobile" label="联系人手机号" align="center" />
        <el-table-column prop="state_desc" label="商户状态" align="center" />
        <el-table-column prop="logo" label="商户logo" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleImage(scope.row.logo)"> 查看 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="商户描述" align="center" />
      </el-table>
      <!-- 分页组件 -->
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <!-- 添加商户弹出框 -->
      <el-dialog title="添加商户" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="1000px">
        <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="name" label="商户名称">
                <el-input v-model="data.form.name" show-word-limit maxlength="20" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择停车场" prop="park_id">
                <el-input v-model="data.form.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'add')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="contact" label="联系人姓名">
                <el-input v-model="data.form.contact" show-word-limit maxlength="10" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.form.contact_mobile" show-word-limit maxlength="11" placeholder="请输入联系人手机号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="login_name" label="商户账号">
                <el-input v-model="data.form.login_name" show-word-limit maxlength="20" placeholder="请输入商户账号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="password" label="商户密码">
                <el-input type="password" v-model="data.form.password" show-word-limit maxlength="20" minlength="6" placeholder="请输入商户密码">
                  <template #append>
                    <el-checkbox v-model="data.form.default_password" true-label="1" false-label="0" @change="boxChange" style="display: inline">
                      默认密码
                    </el-checkbox>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.form.address" show-word-limit maxlength="20" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商户描述">
                <el-input v-model="data.form.memo" type="textarea" show-word-limit maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="merchant_type" label="商户类型">
                <el-select
                  v-model="data.form.merchant_type"
                  placeholder="商户类型"
                  @change="handleSelectChange(data.form.merchant_type, 'add')"
                  clearable
                >
                  <el-option v-for="item in merchantType" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择应用" v-if="appList.length > 0">
                <el-select v-model="data.form.app_id" placeholder="关联应用" clearable>
                  <el-option v-for="item in appList" :key="item.app_id" :label="item.app_name" :value="item.app_id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row> -->
          <!-- 111 -->
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="first_category_id" label="商户一级分类">
                <div style="position: relative; width: 100%">
                  <div style="position: absolute; left: -105px; color: red">*</div>
                  <el-select
                    v-model="data.form.first_category_id"
                    :placeholder="data.form.first_category ? data.form.first_category.name : '商户一级分类'"
                    clearable
                    @clear="cleareditFirstCategory"
                    @change="firstCategoryChange(data.form.first_category_id, 0)"
                  >
                    <el-option v-for="item in firstCategoryIddata" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contract_terms" label="合同约定" style="display: flex; align-items: center">
                <el-radio-group v-model="data.form.contract_terms">
                  <el-radio value="1" size="large">是</el-radio>
                  <el-radio value="0" size="large">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- // -->
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="second_category_id" label="商户二级分类">
                <div style="position: relative; width: 100%">
                  <div style="position: absolute; left: -105px; color: red">*</div>
                  <el-select
                    v-model="data.form.second_category_id"
                    :placeholder="data.form.second_category ? data.form.second_category.name : '商户二级分类'"
                    clearable
                    @clear="cleareditsecCategory"
                    @change="secendCategoryChange(data.form.second_category_id, 1)"
                  >
                    <el-option v-for="item in secondCategoryIddata" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="收费规则简述" prop="charging_rules">
                <el-input
                  v-model="data.form.charging_rules"
                  style="width: 400px"
                  :rows="4"
                  type="textarea"
                  maxlength="5000"
                  placeholder="请将合同上关于收费的内容填写至此处"
                />
              </el-form-item>
            </el-col>
          </el-row>
          <!--  -->
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="商户logo">
                <el-upload
                  class="avatar-uploader"
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  :on-success="onSuccessUpload"
                >
                  <img v-if="data.form.logo" :src="imageUrl" class="avatar" />
                  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createMerchant(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 编辑商户弹出框 -->
      <el-dialog title="编辑商户" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="1000px">
        <el-form ref="editForm" label-width="120px" :rules="data.rules" :model="data.updateForm">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="name" label="商户名称">
                <el-input v-model="data.updateForm.name" show-word-limit maxlength="20" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择停车场" prop="park_id">
                <el-input v-model="data.updateForm.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'edit')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="contact" label="联系人姓名">
                <el-input v-model="data.updateForm.contact" show-word-limit maxlength="10" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.updateForm.contact_mobile" show-word-limit maxlength="11" placeholder="请输入联系人手机号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="login_name" label="商户账号">
                <el-input v-model="data.updateForm.login_name" show-word-limit maxlength="20" placeholder="请输入商户账号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.updateForm.address" show-word-limit maxlength="20" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="first_category_id" label="商户一级分类">
                <div style="position: relative; width: 100%">
                  <div style="position: absolute; left: -105px; color: red">*</div>
                  <el-select
                    v-model="data.updateForm.first_category_id"
                    :placeholder="data.form.first_category ? data.form.first_category.name : '商户一级分类'"
                    clearable
                    @clear="cleareditFirstCategory"
                    @change="firstCategoryChange(data.updateForm.first_category_id, 0)"
                  >
                    <el-option v-for="item in firstCategoryIddata" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contract_terms" label="合同约定" style="display: flex; align-items: center">
                <el-radio-group v-model="data.updateForm.contract_terms">
                  <el-radio value="1" size="large">是</el-radio>
                  <el-radio value="0" size="large">否</el-radio>
                </el-radio-group>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- // -->
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="second_category_id" label="商户二级分类">
                <div style="position: relative; width: 100%">
                  <div style="position: absolute; left: -105px; color: red">*</div>
                  <el-select
                    v-model="data.updateForm.second_category_id"
                    :placeholder="data.form.second_category ? data.form.second_category.name : '商户二级分类'"
                    clearable
                    @clear="cleareditsecCategory"
                    @change="secendCategoryChange(data.updateForm.second_category_id, 1)"
                  >
                    <el-option v-for="item in secondCategoryIddata" :key="item.value" :label="item.name" :value="item.value" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商户logo">
                <el-upload
                  class="avatar-uploader"
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  :on-success="onEditSuccessUpload"
                >
                  <img v-if="data.updateForm.logo" :src="imageUrl" class="avatar" />
                  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="merchant_type" label="商户类型">
                <el-select
                  v-model="data.updateForm.merchant_type"
                  placeholder="商户类型"
                  @change="handleSelectChange(data.updateForm.merchant_type, 'edit')"
                  clearable
                >
                  <el-option v-for="item in merchantType" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择应用" v-if="appList.length > 0">
                <el-select v-model="data.updateForm.app_id" placeholder="关联应用" clearable>
                  <el-option v-for="item in appList" :key="item.app_id" :label="item.app_name" :value="item.app_id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateMerchant(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
      <!-- logo图片弹出框 -->
      <el-dialog v-model="dialogVisible" title="商户logo" width="40%">
        <img w-full :src="imageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
      </el-dialog>
    </div>
  </el-card>
  <!-- 自定义弹出框 -->
  <el-dialog
    v-model="centerDialogVisible"
    :title="firsttitle ? '自定义二级分类' : '自定义一级分类'"
    width="500"
    align-center
    @open="centerDialogOpen"
  >
    <el-input v-model="Categorydata"></el-input>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="centerDialogCancel">取消</el-button>
        <el-button type="primary" @click="centerDialogOk">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script name="MerchantTable" setup>
import { reactive, ref, onActivated } from 'vue'; //引入vue
import { ElMessage, ElMessageBox } from 'element-plus'; //element组件
import { Plus } from '@element-plus/icons-vue'; //element图标
// import dictService from '@/service/system/DictService'; //请求api
import merchantService from '@/service/merchant/MerchantService'; //请求api
import { getfirstCategoryId, getsecondCategoryId } from '@/api/system/DictApi'; //直接引入接口
import ParkFindBack from '../ParkFindBack.vue'; //引入选择车场组件
import { getToken } from '@/utils/common'; //获取用户token
const addForm = ref(); //添加商户弹出框中的form组件实例
const editForm = ref(); //修改商户弹出框中的form组件实例
const tableData = ref([]); //获取的商户列表数据
const loading = ref(false); //控制商户列表加载动画
const total = ref(0); //商户总共数量
const createDialogVisible = ref(false); //控制添加商户弹出框
const updateDialogVisible = ref(false); //控制修改商户弹出框
const parkInfoDialogVisible = ref(false); //控制选择停车场弹出框
const firsttitle = ref(''); //用于标识添加的是一级or二级(0:一级,1:二级)
const Categorydata = ref(''); //要添加的分类名称
const imageUrl = ref(''); //商户logo图片
const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/coupon/merchant/uploadMerchantLogo'); //商户logo上传地址
//请求头token
const headers = reactive({
  Authorization: getToken()
});
//satrt都是选择停车场的数据
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
//end都是选择停车场的数据
const dialogVisible = ref(false); //控制商户logo弹出框
const firstCategoryIddata = ref([]); //一级分类数据
const secondCategoryIddata = ref([]); //二级分类数据
const centerDialogVisible = ref(false); //控制自定义一级分类弹出框
// const merchantType = ref([]); //获取的商户类型
let appList = ref([]); //不知道干什么的
// 手机号自定义验证规则1
const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
//数据
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  //添加商户接口请求数据
  form: {
    park_id: '', //停车场id
    park_name: '', //停车场名称
    name: '', //商户名称
    address: '', //详细地址
    contact: '', //联系人姓名
    contact_mobile: '', //联系人手机号
    login_name: '', //商户账号
    password: '', //商户密码
    default_password: '', //默认密码
    logo: '', //商户logo
    memo: '', //商户描述
    merchant_type: '', //商户类型，
    first_category_id: null, //一级分类
    second_category_id: null, //二级分类
    contract_terms: '', //合同约定，
    charging_rules: '', //收费规则描述
    second_category: null,
    first_category: null
  },
  updateForm: {},
  //添加/编辑商户from规则
  rules: {
    contract_terms: [{ required: true, message: '请选择合同约定', trigger: 'blur' }],
    charging_rules: [{ required: true, message: '请输入收费规则描述', trigger: 'blur' }],
    contact: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      {
        required: true,
        message: '请输入联系人手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入商户名称',
        trigger: 'blur'
      }
    ],
    address: [
      {
        required: true,
        message: '请输入详细地址',
        trigger: 'blur'
      }
    ],
    login_name: [
      {
        required: true,
        message: '请输入商户账号',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入商户密码',
        trigger: 'change'
      }
    ],
    merchant_type: [
      {
        required: true,
        message: '请选择商户类型',
        trigger: 'blur'
      }
    ]
  }
});
//在最外层keep-alive包裹着路由 所以有这个生命周期
onActivated(() => {
  // 数据初始化
  // initSelects();
  getfirstCategoryIddata(); //获取商户一级分类
  getList(data.queryParams); //获取商户列表数据
  appList.value = [];
});
// start清楚一二分类
const cleareditFirstCategory = () => {
  data.form.first_category = null;
  data.updateForm.first_category_id = null;
  data.form.second_category = null;
  data.updateForm.second_category_id = null;
};
const cleareditsecCategory = () => {
  data.updateForm.second_category = null;
  data.updateForm.second_category_id = null;
};
//end 清楚一二分类
//获取商户一级分类
const getfirstCategoryIddata = async () => {
  try {
    const rudata = await getfirstCategoryId();
    if (rudata.code == 200) {
      firstCategoryIddata.value = rudata.data.map((e) => {
        return {
          value: e.id,
          name: e.name
        };
      });
      //添加自定义选项
      firstCategoryIddata.value.push({
        value: '99999',
        name: '自定义'
      });
    }
  } catch (error) {
    console.error('获取商户一级分类失败:', error);
  }
};
//选择一级标题的时候
const firstCategoryChange = async (datas, type) => {
  data.form.second_category = null;
  data.form.second_category_id = null;
  data.updateForm.second_category = null;
  data.updateForm.second_category_id = null;
  if (!datas) {
    return;
  }
  //选择了自定义
  if (datas == '99999') {
    centerDialogVisible.value = true;
    firsttitle.value = type;
    return;
  }
  try {
    const rudata = await getsecondCategoryId(datas);
    if (rudata.code == 200) {
      secondCategoryIddata.value = rudata.data.map((e) => {
        return {
          value: e.id,
          name: e.name
        };
      });
    }
    secondCategoryIddata.value.push({
      value: '99999',
      name: '自定义'
    });
  } catch (error) {
    console.error('获取商户二级分类失败:', error);
  }
};
//获取的商户类型
// const initSelects = () => {
//   dictService.getDictsList('MERCHANT_TYPE').then((response) => {
//     merchantType.value = response;
//   });
// };
//获取商户列表
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  merchantService.pagingMerchants(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//点击了添加商户按钮
const handleCreate = () => {
  //数据清空
  data.form = {
    park_id: '',
    park_name: '',
    name: '',
    address: '',
    contact: '',
    contact_mobile: '',
    login_name: '',
    password: '',
    default_password: '0',
    logo: '',
    memo: ''
  };
  secondCategoryIddata.value = [];
  //显示添加商户
  createDialogVisible.value = true;
};
//分页切换limit
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
//分页切换页面
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
//点击添加商户按钮
const createMerchant = (addForm) => {
  if (!data.form.first_category_id && !data.form.first_category) {
    return ElMessage({
      type: 'error',
      message: '请选择商户一级分类'
    });
  }
  if (!data.form.second_category_id && !data.form.second_category) {
    return ElMessage({
      type: 'error',
      message: '请选择商户二级分类'
    });
  }
  addForm.validate().then(() => {
    merchantService
      .createMerchant(data.form)
      .then((response) => {
        if (response.data?.detailMessage) {
          ElMessage.error(response.data.detailMessage);
        } else {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
          getfirstCategoryIddata(); //获取商户一级分类
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

///////////////////
//点击"添加商家"按钮
// const createMerchant = (addForm) => {
//   if (!data.form.first_category_id && !data.form.first_category) {
//     return ElMessage({
//       type: 'error',
//       message: '请选择商户一级分类'
//     });
//   }
//   if (!data.form.second_category_id && !data.form.second_category) {
//     return ElMessage({
//       type: 'error',
//       message: '请选择商户二级分类'
//     });
//   }
//   //校验from规则
//   addForm.validate().then(() => {
//     //校验通过
//     //调用添加商户接口
//     console.log(data.form);
//     merchantService
//       .createMerchant(data.form)
//       .then((response) => {
//         if (response.success === true) {
//           ElMessage({
//             message: response.message,
//             type: 'success'
//           });
//           //添加成功 重新获取商户
//           getList(data.queryParams);
//           getfirstCategoryIddata(); //获取商户一级分类
//           addForm.resetFields(); //清空表单并移除校验提示信息
//           createDialogVisible.value = false; //关闭添加商户窗口
//         } else {
//           ElMessage({
//             message: response.detail_message != '' ? response.detail_message : response.message,
//             type: 'error'
//           });
//         }
//       })
//       .catch(() => {
//         getList(data.queryParams);
//       });
//   });
// };
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService
      .deleteBlackList(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
//点击"编辑"按钮
const handleEdit = async (val) => {
  cleareditFirstCategory();
  appList.value = [];
  //start把当前数据赋值显示
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    park_name: val.park_name,
    name: val.name,
    address: val.address,
    contact: val.contact,
    contact_mobile: val.contact_mobile,
    login_name: val.login_name,
    logo: val.logo,
    memo: val.memo,
    merchant_type: val.merchant_type,
    app_id: val.app_id,
    //
    //start新增 2025/5/19
    first_category_id: val.first_category_id, //一级分类
    second_category_id: val.second_category_id, //二级分类
    contract_terms: String(val.contract_terms), //合同约定，
    second_category: null,
    first_category: null
  };
  /////////////
  imageUrl.value = val.logo;
  updateDialogVisible.value = true;
  if (val.merchant_type == 9) {
    //显示第三方
    merchantService.parkByAppList(data.updateForm.park_id).then((response) => {
      if (response.success === true) {
        //刷新页面数据
        appList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  }
  /////////////////
  //根据当前一级分类获取相应的二级标题
  if (!val.first_category_id) return;
  try {
    const rudata = await getsecondCategoryId(val.first_category_id);
    if (rudata.code == 200) {
      secondCategoryIddata.value = rudata.data.map((e) => {
        return {
          value: e.id,
          name: e.name
        };
      });
    }
    secondCategoryIddata.value.push({
      value: '99999',
      name: '自定义'
    });
    console.log('二级分类', rudata);
  } catch (error) {
    console.error('获取商户二级分类失败:', error);
  }
};
//////////////////////
const updateMerchant = (editForm) => {
  data.updateForm.first_category = data.form.first_category;
  data.updateForm.second_category = data.form.second_category;
  if (!data.updateForm.first_category_id && !data.updateForm.first_category) {
    return ElMessage({
      type: 'error',
      message: '请选择商户一级分类'
    });
  }
  if (!data.updateForm.second_category_id && !data.updateForm.second_category) {
    return ElMessage({
      type: 'error',
      message: '请选择商户二级分类'
    });
  }
  editForm.validate().then(() => {
    merchantService
      .updateMerchant(data.updateForm)
      .then((response) => {
        if (response.data?.detailMessage) {
          ElMessage.error(response.data.detailMessage);
        } else {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
          getfirstCategoryIddata(); //获取商户一级分类
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

/////////////////////
//点击更新商户"确定"按钮
// const updateMerchant = (editForm) => {
//   data.updateForm.first_category = data.form.first_category;
//   data.updateForm.second_category = data.form.second_category;
//   if (!data.updateForm.first_category_id && !data.updateForm.first_category) {
//     return ElMessage({
//       type: 'error',
//       message: '请选择商户一级分类'
//     });
//   }
//   if (!data.updateForm.second_category_id && !data.updateForm.second_category) {
//     return ElMessage({
//       type: 'error',
//       message: '请选择商户二级分类'
//     });
//   }
//   console.log(data);
//   //from校验规则
//   editForm.validate().then(() => {
//     //更新
//     merchantService
//       .updateMerchant(data.updateForm)
//       .then((response) => {
//         if (response.success === true) {
//           ElMessage({
//             message: response.message,
//             type: 'success'
//           });
//           getList(data.queryParams); //重新获取商户数据
//           getfirstCategoryIddata(); //获取商户一级分类
//           editForm.resetFields(); //清空规则和字段内容
//           updateDialogVisible.value = false;
//         } else {
//           ElMessage({
//             message: response.detail_message != '' ? response.detail_message : response.message,
//             type: 'error'
//           });
//         }
//       })
//       .catch(() => {
//         getList(data.queryParams);
//       });
//   });
// };

const boxChange = (val) => {
  if (val == 1) {
    data.form.password = '12345678';
  } else {
    data.form.password = '';
  }
};

//启用
const enable = (val) => {
  ElMessageBox.confirm('是否要启用该商户？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService.enableMerchant(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 禁用
const disable = (val) => {
  ElMessageBox.confirm('是否要停用该商户？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService.disableMerchant(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
//重制密码
const resetPassword = (val) => {
  ElMessageBox.confirm('确定重置密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService
      .resetPassword(val)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.message,
            type: 'warning'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
//获取车场信息
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
    if (flag.value == 'add') {
      handleSelectChange(data.form.merchant_type, 'add');
    } else if (flag.value == 'edit') {
      handleSelectChange(data.updateForm.merchant_type, 'edit');
    }
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
  appList.value = [];
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
  }
};
//上传商户logo前钩子处理
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
//上传商户logo成功处理钩子
const onSuccessUpload = (response) => {
  if (response.success == true) {
    console.log(response);
    data.form.logo = response.data;
    imageUrl.value = response.data;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
//上传商户logo成功处理钩子(编辑商户中logo成功处理钩子)
const onEditSuccessUpload = (response) => {
  if (response.success == true) {
    data.updateForm.logo = response.data;
    imageUrl.value = response.data;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
//点击查看logo
const handleImage = (logo) => {
  if (logo == null || logo == '') {
    ElMessage({
      message: '商户没有Logo',
      type: 'warning'
    });
  } else {
    imageUrl.value = logo;
    dialogVisible.value = true;
  }
};

// 点击添加商户中"取消"按钮
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
  appList.value = [];
};
/// 点击编辑商户中"取消"按钮
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
//点击弹出的添加商户弹出框中的"取消"
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
//点击弹出的编辑商户弹出框中的"取消"
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const handleSelectChange = (val, mode) => {
  console.log('商户类型', val);
  let parkid = '';
  if (mode == 'add') {
    parkid = data.form.park_id;
  } else if (mode == 'edit') {
    parkid = data.updateForm.park_id;
  }
  if (val == 9 && !!parkid) {
    merchantService.parkByAppList(parkid).then((response) => {
      if (response.success === true) {
        //刷新页面数据
        appList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    appList.value = [];
  }
};
//点击自定义“确定”按钮
const centerDialogOk = async () => {
  //如果二级就是添加二级分类
  if (firsttitle.value) {
    data.form.second_category_id = null;
    data.updateForm.second_category_id = null;
    data.form.second_category = {
      name: '',
      level: 2
    };
    data.form.second_category.name = Categorydata.value;
  } else {
    data.form.first_category_id = null;
    data.updateForm.first_category_id = null;
    data.form.first_category = {
      name: '',
      level: 1
    };
    data.form.first_category.name = Categorydata.value;
    secondCategoryIddata.value = [];
    secondCategoryIddata.value.push({
      value: '99999',
      name: '自定义'
    });
  }
  centerDialogVisible.value = false;
  console.log(data.form);
};
//点击自定义取消按钮
const centerDialogCancel = () => {
  if (firsttitle.value) {
    data.form.second_category_id = '';
    centerDialogVisible.value = false;
    data.form.first_category = null;
    return;
  }
  data.form.first_category_id = '';
  centerDialogVisible.value = false;
  data.form.second_category = null;
};
//选择二级分类的时候
const secendCategoryChange = (id, type) => {
  if (id == '99999') {
    centerDialogVisible.value = true;
    firsttitle.value = type;
  }
};
//自定义弹出框打开的时候
const centerDialogOpen = () => {
  Categorydata.value = '';
};
//暴露getlist方法
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
