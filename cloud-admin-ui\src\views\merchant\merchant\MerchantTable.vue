<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加商户</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="160">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link v-if="scope.row.state == 0" type="success" @click="enable(scope.row.id)"> 启用 </el-button>
            <el-button link v-if="scope.row.state == 1" type="danger" @click="disable(scope.row.id)"> 停用 </el-button>
            <el-button link type="warning" @click="resetPassword(scope.row.id)"> 重置密码 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="login_name" label="商户账号" align="center" />
        <el-table-column prop="name" label="商户名称" align="center" />
        <el-table-column prop="merchant_type_desc" label="商户类型" align="center" />
        <el-table-column prop="address" label="详细地址" align="center" />
        <el-table-column prop="contact" label="联系人姓名" align="center" />
        <el-table-column prop="contact_mobile" label="联系人手机号" align="center" />
        <el-table-column prop="state_desc" label="商户状态" align="center" />
        <el-table-column prop="logo" label="商户logo" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleImage(scope.row.logo)"> 查看 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="memo" label="商户描述" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加商户" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="1000px">
        <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="name" label="商户名称">
                <el-input v-model="data.form.name" show-word-limit maxlength="20" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择停车场" prop="park_id">
                <el-input v-model="data.form.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'add')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="contact" label="联系人姓名">
                <el-input v-model="data.form.contact" show-word-limit maxlength="10" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.form.contact_mobile" show-word-limit maxlength="11" placeholder="请输入联系人手机号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="login_name" label="商户账号">
                <el-input v-model="data.form.login_name" show-word-limit maxlength="20" placeholder="请输入商户账号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="password" label="商户密码">
                <el-input type="password" v-model="data.form.password" show-word-limit maxlength="20" minlength="6" placeholder="请输入商户密码">
                  <template #append>
                    <el-checkbox v-model="data.form.default_password" true-label="1" false-label="0" @change="boxChange" style="display: inline">
                      默认密码
                    </el-checkbox>
                  </template>
                </el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.form.address" show-word-limit maxlength="20" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商户描述">
                <el-input v-model="data.form.memo" type="textarea" show-word-limit maxlength="50" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="merchant_type" label="商户类型">
                <el-select
                  v-model="data.form.merchant_type"
                  placeholder="商户类型"
                  @change="handleSelectChange(data.form.merchant_type, 'add')"
                  clearable
                >
                  <el-option v-for="item in merchantType" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择应用" v-if="appList.length > 0">
                <el-select v-model="data.form.app_id" placeholder="关联应用" clearable>
                  <el-option v-for="item in appList" :key="item.app_id" :label="item.app_name" :value="item.app_id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="商户logo">
                <el-upload
                  class="avatar-uploader"
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  :on-success="onSuccessUpload"
                >
                  <img v-if="data.form.logo" :src="imageUrl" class="avatar" />
                  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createMerchant(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="编辑商户" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="1000px">
        <el-form ref="editForm" label-width="120px" :rules="data.rules" :model="data.updateForm">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="name" label="商户名称">
                <el-input v-model="data.updateForm.name" show-word-limit maxlength="20" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择停车场" prop="park_id">
                <el-input v-model="data.updateForm.park_name" placeholder="请选择停车场" readonly @click="authCharge(true, 'edit')" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="contact" label="联系人姓名">
                <el-input v-model="data.updateForm.contact" show-word-limit maxlength="10" placeholder="请输入联系人姓名" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.updateForm.contact_mobile" show-word-limit maxlength="11" placeholder="请输入联系人手机号" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="login_name" label="商户账号">
                <el-input v-model="data.updateForm.login_name" show-word-limit maxlength="20" placeholder="请输入商户账号" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.updateForm.address" show-word-limit maxlength="20" placeholder="请输入详细地址" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item label="商户描述">
                <el-input v-model="data.updateForm.memo" type="textarea" show-word-limit maxlength="50" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="商户logo">
                <el-upload
                  class="avatar-uploader"
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :before-upload="beforeUpload"
                  :show-file-list="false"
                  :on-success="onEditSuccessUpload"
                >
                  <img v-if="data.updateForm.logo" :src="imageUrl" class="avatar" />
                  <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
                </el-upload>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="12">
              <el-form-item prop="merchant_type" label="商户类型">
                <el-select
                  v-model="data.updateForm.merchant_type"
                  placeholder="商户类型"
                  @change="handleSelectChange(data.updateForm.merchant_type, 'edit')"
                  clearable
                >
                  <el-option v-for="item in merchantType" :key="item.value" :label="item.name" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="选择应用" v-if="appList.length > 0">
                <el-select v-model="data.updateForm.app_id" placeholder="关联应用" clearable>
                  <el-option v-for="item in appList" :key="item.app_id" :label="item.app_name" :value="item.app_id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateMerchant(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>

      <el-dialog v-model="dialogVisible" title="商户logo" width="40%">
        <img w-full :src="imageUrl" style="max-width: 100%; max-height: 100%" alt="Preview Image" />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="MerchantTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { Plus } from '@element-plus/icons-vue';
import dictService from '@/service/system/DictService';
import merchantService from '@/service/merchant/MerchantService';
import ParkFindBack from '../ParkFindBack.vue';
import { getToken } from '@/utils/common';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const parkInfoDialogVisible = ref(false);
const imageUrl = ref('');
const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/coupon/merchant/uploadMerchantLogo');
const headers = reactive({
  Authorization: getToken()
});

const park_id = ref('');
const park_name = ref('');
const flag = ref('');

const dialogVisible = ref(false);
const merchantType = ref([]);
let appList = ref([]);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: '',
    park_name: '',
    name: '',
    address: '',
    contact: '',
    contact_mobile: '',
    login_name: '',
    password: '',
    default_password: '',
    logo: '',
    memo: '',
    merchant_type: ''
  },
  updateForm: {},
  rules: {
    contact: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      {
        required: true,
        message: '请输入联系人手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入商户名称',
        trigger: 'blur'
      }
    ],
    address: [
      {
        required: true,
        message: '请输入详细地址',
        trigger: 'blur'
      }
    ],
    login_name: [
      {
        required: true,
        message: '请输入商户账号',
        trigger: 'blur'
      }
    ],
    password: [
      {
        required: true,
        message: '请输入商户密码',
        trigger: 'change'
      }
    ],
    merchant_type: [
      {
        required: true,
        message: '请选择商户类型',
        trigger: 'blur'
      }
    ]
  }
});
onActivated(() => {
  // 数据初始化
  initSelects();
  getList(data.queryParams);
  appList.value = [];
});

const initSelects = () => {
  dictService.getDictsList('MERCHANT_TYPE').then((response) => {
    merchantType.value = response;
  });
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  merchantService.pagingMerchants(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: '',
    park_name: '',
    name: '',
    address: '',
    contact: '',
    contact_mobile: '',
    login_name: '',
    password: '',
    default_password: '0',
    logo: '',
    memo: ''
  };
  createDialogVisible.value = true;
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createMerchant = (addForm) => {
  addForm.validate().then(() => {
    merchantService
      .createMerchant(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService
      .deleteBlackList(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (val) => {
  appList.value = [];
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    park_name: val.park_name,
    name: val.name,
    address: val.address,
    contact: val.contact,
    contact_mobile: val.contact_mobile,
    login_name: val.login_name,
    logo: val.logo,
    memo: val.memo,
    merchant_type: val.merchant_type,
    app_id: val.app_id
  };
  imageUrl.value = import.meta.env.VITE_MERCHANT_URL + val.logo;
  updateDialogVisible.value = true;
  if (val.merchant_type == 9) {
    //显示第三方
    merchantService.parkByAppList(data.updateForm.park_id).then((response) => {
      if (response.success === true) {
        //刷新页面数据
        appList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  }
};
const updateMerchant = (editForm) => {
  editForm.validate().then(() => {
    merchantService
      .updateMerchant(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const boxChange = (val) => {
  if (val == 1) {
    data.form.password = '12345678';
  } else {
    data.form.password = '';
  }
};

//启用
const enable = (val) => {
  ElMessageBox.confirm('是否要启用该商户？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService.enableMerchant(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 禁用
const disable = (val) => {
  ElMessageBox.confirm('是否要停用该商户？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService.disableMerchant(val).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
const resetPassword = (val) => {
  ElMessageBox.confirm('确定重置密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    merchantService
      .resetPassword(val)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.message,
            type: 'warning'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
    if (flag.value == 'add') {
      handleSelectChange(data.form.merchant_type, 'add');
    } else if (flag.value == 'edit') {
      handleSelectChange(data.updateForm.merchant_type, 'edit');
    }
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
  appList.value = [];
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
  }
};

const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
const onSuccessUpload = (response) => {
  if (response.success == true) {
    data.form.logo = response.data;
    imageUrl.value = import.meta.env.VITE_MERCHANT_URL + response.data;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};

const onEditSuccessUpload = (response) => {
  if (response.success == true) {
    data.updateForm.logo = response.data;
    imageUrl.value = import.meta.env.VITE_MERCHANT_URL + response.data;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
const handleImage = (logo) => {
  if (logo == null || logo == '') {
    ElMessage({
      message: '商户没有Logo',
      type: 'warning'
    });
  } else {
    imageUrl.value = import.meta.env.VITE_MERCHANT_URL + logo;
    dialogVisible.value = true;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
  appList.value = [];
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const handleSelectChange = (val, mode) => {
  console.log('商户类型', val);
  let parkid = '';
  if (mode == 'add') {
    parkid = data.form.park_id;
  } else if (mode == 'edit') {
    parkid = data.updateForm.park_id;
  }
  if (val == 9 && !!parkid) {
    merchantService.parkByAppList(parkid).then((response) => {
      if (response.success === true) {
        //刷新页面数据
        appList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    appList.value = [];
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
}
</style>
