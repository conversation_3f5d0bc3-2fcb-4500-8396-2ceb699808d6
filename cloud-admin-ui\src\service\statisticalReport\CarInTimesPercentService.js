import * as carInTimesPercentApi from '@/api/statisticalReport/CarInTimesPercentApi';

/**
 * 车辆进场次数占比
 */
export default {
  /**
   * 分页查询车辆进场次数占比
   */
  pagingCarInTimesPercent(data) {
    return new Promise((resolve, reject) => {
      try {
        carInTimesPercentApi.pagingCarInTimesPercent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        carInTimesPercentApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
