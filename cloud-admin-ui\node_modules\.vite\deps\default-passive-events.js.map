{"version": 3, "sources": ["../../default-passive-events/src/index.js", "../../default-passive-events/src/utils.js"], "sourcesContent": ["import { eventListenerOptionsSupported } from './utils';\n\nconst defaultOptions = {\n  passive: true,\n  capture: false\n};\nconst supportedPassiveTypes = [\n  'scroll', 'wheel',\n  'touchstart', 'touchmove', 'touchenter', 'touchend', 'touchleave',\n  'mouseout', 'mouseleave', 'mouseup', 'mousedown', 'mousemove', 'mouseenter', 'mousewheel', 'mouseover'\n];\nconst getDefaultPassiveOption = (passive, eventName) => {\n  if (passive !== undefined) return passive;\n\n  return supportedPassiveTypes.indexOf(eventName) === -1 ? false : defaultOptions.passive;\n};\n\nconst getWritableOptions = (options) => {\n  const passiveDescriptor = Object.getOwnPropertyDescriptor(options, 'passive');\n    \n  return passiveDescriptor && passiveDescriptor.writable !== true && passiveDescriptor.set === undefined\n    ? Object.assign({}, options)\n    : options;\n};\n\nconst overwriteAddEvent = (superMethod) => {\n  EventTarget.prototype.addEventListener = function (type, listener, options) {\n    const usesListenerOptions = typeof options === 'object' && options !== null;\n    const useCapture          = usesListenerOptions ? options.capture : options;\n\n    options         = usesListenerOptions ? getWritableOptions(options) : {};\n    options.passive = getDefaultPassiveOption(options.passive, type);\n    options.capture = useCapture === undefined ? defaultOptions.capture : useCapture;\n\n    superMethod.call(this, type, listener, options);\n  };\n\n  EventTarget.prototype.addEventListener._original = superMethod;\n};\n\nconst supportsPassive = eventListenerOptionsSupported();\n\nif (supportsPassive) {\n  const addEvent = EventTarget.prototype.addEventListener;\n  overwriteAddEvent(addEvent);\n}\n", "export const eventListenerOptionsSupported = () => {\n  let supported = false;\n\n  try {\n    const opts = Object.defineProperty({}, 'passive', {\n      get() {\n        supported = true;\n      }\n    });\n\n    window.addEventListener('test', null, opts);\n    window.removeEventListener('test', null, opts);\n  } catch (e) {}\n\n  return supported;\n}\n"], "mappings": ";IAyB2BA;IAnBrBC,IAAwB,CAC5B,UAAU,SACV,cAAc,aAAa,cAAc,YAAY,cACrD,YAAY,cAAc,WAAW,aAAa,aAAa,cAAc,cAAc,WAAA;AAiC7F,IC1C6C,WAAA;AAC3C,MAAIC,KAAAA;AAEJ,MAAA;AACE,QAAMC,KAAOC,OAAOC,eAAe,CAAA,GAAI,WAAW,EAChDC,KAAAA,WAAAA;AACEJ,MAAAA,KAAAA;IAAY,EAAA,CAAA;AAIhBK,WAAOC,iBAAiB,QAAQ,MAAML,EAAAA,GACtCI,OAAOE,oBAAoB,QAAQ,MAAMN,EAAAA;EAAAA,SAClCO,IAAP;EAAOA;AAET,SAAOR;AAAAA,ED0BeS,GAEH;AACbC,MAAWC,YAAYC,UAAUN;AAlBdR,MAmBPY,GAlBlBC,YAAYC,UAAUN,mBAAmB,SAAUO,IAAMC,GAAUC,GAAAA;AACjE,QAhB6BC,GAgBvBC,IAAyC,YAAA,OAAZF,KAAoC,SAAZA,GACrDG,IAAsBD,IAAsBF,EAAQI,UAAUJ;AAAAA,KAEpEA,IAAkBE,IAbK,SAACF,IAAAA;AAC1B,UAAMK,KAAoBlB,OAAOmB,yBAAyBN,IAAS,SAAA;AAEnE,aAAOK,MAAAA,SAAqBA,GAAkBE,YAAAA,WAAqBF,GAAkBG,MACjFrB,OAAOsB,OAAO,CAAA,GAAIT,EAAAA,IAClBA;IAAAA,EAQyDA,CAAAA,IAAW,CAAA,GAC9DC,UAAAA,YApBqBA,IAoBaD,EAAQC,WAnBlBA,IAAAA,OAE3BjB,EAAsB0B,QAiBgCZ,EAAAA,KAAAA,MAC3DE,EAAQI,UAAAA,WAAUD,KAAoDA,GAEtEpB,EAAY4B,KAAKC,MAAMd,IAAMC,GAAUC,CAAAA;EAAAA,GAGzCJ,YAAYC,UAAUN,iBAAiBsB,YAAY9B;AAAAA;AAM7CY;", "names": ["superMethod", "supportedPassiveTypes", "supported", "opts", "Object", "defineProperty", "get", "window", "addEventListener", "removeEventListener", "e", "eventListenerOptionsSupported", "addEvent", "EventTarget", "prototype", "type", "listener", "options", "passive", "usesListenerOptions", "useCapture", "capture", "passiveDescriptor", "getOwnPropertyDescriptor", "writable", "set", "assign", "indexOf", "call", "this", "_original"]}