<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="sentryBoxRecordService.exportPlateNoCorrect"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 384px)">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="180" />
        <el-table-column label="车牌号码（改前）" align="center" min-width="180">
          <template #default="scope">
            <span style="margin-left: 10px">{{ scope.row.old_plate_no }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车牌号码（改后）" align="center" min-width="180">
          <template #default="scope">
            <span style="margin-left: 10px">{{ scope.row.new_plate_no }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="event_context" label="车牌号矫正详情" align="center" min-width="180">
          <template #default="scope">
            <span style="margin-left: 10px">{{ scope.row.out_reason }}</span>
          </template></el-table-column
        >
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" />
        <el-table-column label="入场图片" align="center" min-width="100">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="updator_name" label="操作人" align="center" min-width="100" />
        <el-table-column prop="updated_at" label="操作时间" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="PlateNoCorrectTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import sentryBoxRecordService from '@/service/charge/SentryBoxRecordService';
import DownloadButton from '@/components/DownloadButton.vue';

const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  sentryBoxRecordService.pagingPlateNoCorrect(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 查看入场图片
const checkInPicture = (row) => {
  sentryBoxRecordService.getByCarInRecordId(`${row.park_id}/${row.car_in_biz_no}`).then((response) => {
    if (!response.data || response.data.car_photo_url === '') {
      ElMessage({
        message: '暂无图片可以查看',
        type: 'error'
      });
    } else {
      dialogVisible.value = true;
      title.value = '出场图片';
      dialogImageUrl.value = response.data.car_photo_url;
    }
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
