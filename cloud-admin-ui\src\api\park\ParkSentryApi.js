/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找岗亭信息
export const pagingParkSentries = (data) => {
  return $({
    url: '/console/park/sentry/pagingParkSentries',
    method: 'post',
    data
  });
};

// 新增岗亭信息
export const createParkSentry = (data) => {
  return $({
    url: '/console/park/sentry/createParkSentry',
    method: 'post',
    data
  });
};

// 岗亭信息修改
export const updateParkSentry = (data) => {
  return $({
    url: '/console/park/sentry/updateParkSentry',
    method: 'post',
    data
  });
};

// 删除岗亭
export const deleteParkSentry = (id) => {
  return $({
    url: '/console/park/sentry/deleteParkSentry/' + id,
    method: 'post'
  });
};

//通道授权;
export const accreditGateway = (data) => {
  return $({
    url: '/console/park/sentry/accreditGateway',
    method: 'post',
    data
  });
};
