# cloud-admin-ui

智慧停车管理云平台

## 运行环境

Node版本：v16.15.0

Npm版本：7.7.6

## 插件

### Element-Plus

`npm install element-plus --save`

### Element-Plus 图标库

`npm install @element-plus/icons-vue`

### axios

`npm install axios --save`

### mockjs

`npm install mockjs`

### sass

`npm install sass --save-dev`

### nprogress

`npm install nprogress --save`

### lodash

`npm install lodash --save`

### pinia-plugin-persistedstate

`npm install pinia-plugin-persistedstate`

### mitt

`npm install mitt`

### wangEditor

`npm install @wangeditor/editor --save`
`npm install @wangeditor/editor-for-vue@next --save`

### print

`npm install print-js --save`
