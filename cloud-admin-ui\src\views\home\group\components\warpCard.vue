<template>
  <div class="card-container" :class="props.size">
    <div class="title">{{ props.title }}</div>
    <div class="main-container">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  size: {
    type: String, // 尺寸，可选值为 large、middle、small、default、mini
    default: 'default'
  },
  title: {
    type: String,
    default: '标题'
  }
});
</script>

<style scoped lang="scss">
.card-container {
  background-size: 100% 100%;
  position: relative;
  background-image: url('@/assets/groupImage/card-default.png');
  height: 244px;
  width: 932px;
  .title {
    height: 20px;
    font-weight: 550;
    font-size: 22px;
    color: #00d5ff;
    line-height: 20px;
    text-align: left;
    position: absolute;
    top: 10px;
    left: 20px;
    font-family: 'RuiZi';
  }
  .main-container {
    width: 100%;
    margin-top: 42px;
    height: calc(100% - 42px);
    padding: 10px 20px;
  }
}
.large {
  background-image: url('@/assets/groupImage/card-large.png');
  height: 500px;
  width: 850px;
}
.middle {
  background-image: url('@/assets/groupImage/card-middle.png');
  height: 500px;
  width: 502px;
}
.mini {
  background-image: url('@/assets/groupImage/card-mini.png');
  height: 244px;
  width: 502px;
}
</style>
