{"name": "cloud-admin-ui", "version": "0.0.0", "scripts": {"dev": "vite", "build": "if-env NODE_ENV=production && npm run build:pro || if-env NODE_ENV=test && npm run build:test || npm run build:dev", "build:dev": "vite build --mode development", "build:pro": "vite build --mode production", "build:test": "vite build --mode test", "build:pre": "vite build --mode pre", "preview": "vite preview --port 4173", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "ant-design-vue": "^3.2.20", "axios": "^0.27.2", "decimal.js": "^10.6.0", "default-passive-events": "^2.0.0", "echarts": "^5.5.0", "element-plus": "^2.2.14", "lodash": "^4.17.21", "node-rtsp-stream": "^0.0.9", "nprogress": "^0.2.0", "pinia": "^2.0.17", "pinia-plugin-persistedstate": "^2.1.1", "print-js": "^1.6.0", "qrcode.vue": "^3.3.3", "save": "^2.9.0", "uuid": "^9.0.0", "vue": "^3.2.37", "vue-happy-bus": "^2.0.1-vue3", "vue-router": "^4.1.3"}, "devDependencies": {"@rushstack/eslint-patch": "^1.1.4", "@vitejs/plugin-vue": "^3.0.1", "@vue/eslint-config-prettier": "^7.0.0", "eslint": "^8.21.0", "eslint-plugin-vue": "^9.3.0", "if-env": "^1.0.4", "prettier": "^2.7.1", "rollup-plugin-visualizer": "^5.12.0", "sass": "^1.54.5", "vite": "^3.0.4", "vite-plugin-compression": "^0.5.1", "vite-plugin-imagemin": "^0.6.1"}}