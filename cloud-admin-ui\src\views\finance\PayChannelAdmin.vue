<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="微信渠道" name="wechat">
      <we-chat-pay-channel ref="wechatPay" />
    </el-tab-pane>
    <el-tab-pane label="支付宝渠道" name="ali">
      <ali-pay-channel ref="aliPay" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="PayChannelAdmin" setup>
import WeChatPayChannel from './WeChatPayChannel.vue';
import AliPayChannel from './AliPayChannel.vue';
import { ref, reactive, onMounted } from 'vue';

const activeName = ref('wechat');
const wechatPay = ref(null);
const aliPay = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

onMounted(() => {
  wechatPay.value.searchWeChatPayChannelList(params);
});

const handleClick = (tab) => {
  if (tab.props.name === 'wechat') {
    wechatPay.value.searchWeChatPayChannelList(params);
  }
  if (tab.props.name === 'ali') {
    aliPay.value.searchAilPayChannelList(params);
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
