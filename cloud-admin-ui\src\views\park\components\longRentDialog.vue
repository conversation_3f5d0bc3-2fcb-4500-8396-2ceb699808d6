<script setup>
import { ref, reactive, computed } from 'vue';
import commonService from '@/service/common/CommonService';
import dictService from '@/service/system/DictService';
import { ElMessage, ElMessageBox } from 'element-plus';
import longRentRuleService from '@/service/park/LongRentRuleService';
import groupRentRuleService from '@/service/park/GroupRentRuleService';
import { QuestionFilled } from '@element-plus/icons-vue';
import { rentProductRanges, dailyRentProductRanges, weeklyRentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';
import { debounce, template } from 'lodash';

const props = defineProps({
  parkingType: {
    type: String,
    default: 'headquarter' //  headquarter-总部 branch-分部
  },
  parkId: {
    type: String,
    default: undefined
  }
});
const emits = defineEmits(['submit', 'cancel']);

// 弹窗显示控制
const dialogVisable = ref(false);
// 是否新建
const isAddRule = ref(true);
// 表单
const roleForm = ref();
// 表单数据
const formData = ref();
// 长租类型
const rentType = ref([]);
// 产品类型
const productType = ref([]);
// 规则名称是否发生了改变
const changeInput = ref(false);

const loading = ref(false);

const isDisabled = ref(false);
// 自定义产品周期
const productRangeText = ref();

// 表单字段是否展示
const showItem = computed(() => {
  const commonSet = [5, 6, 7, 8, 9].includes(formData.value.product_type);
  return {
    public_open: formData.value.type === 3,
    product_range: commonSet,
    start_time: formData.value.product_type === 5,
    time_type: commonSet,
    week_day: formData.value.product_type === 7,
    days: formData.value.product_type === 7,
    month_range: formData.value.product_type === 7
  };
});

const validateRangeTime = (rule, value, callback) => {
  if (formData.value.product_type_list[0]?.start_time && formData.value.product_type_list[0]?.end_time) {
    if (formData.value.product_type_list[0]?.start_time == formData.value.product_type_list[0]?.end_time) {
      callback(new Error('分段时长开始时间不能等于结束时间'));
    }
    callback();
  } else {
    callback(new Error('请选择长租时段'));
  }
};

const validateRestRangeTime = (rule, value, callback) => {
  if (formData.value.product_type_list[0]?.rest_start_time && formData.value.product_type_list[0]?.rest_end_time) {
    if (formData.value.product_type_list[0]?.rest_start_time == formData.value.product_type_list[0]?.rest_end_time) {
      callback(new Error('分段时长开始时间不能等于结束时间'));
    }
    callback();
  } else {
    callback(new Error('请选择长租时段'));
  }
};

const productRangeTime = (rule, value, callback) => {
  if (formData.value.product_type === 8 || formData.value.product_type === 9) {
    if (value === 'zdy') {
      if (productRangeText.value === undefined || productRangeText.value === '') {
        callback(new Error('请输入自定义产品周期'));
      } else if (!/^[1-9]\d*$/.test(productRangeText.value)) {
        callback(new Error('请输入正确的正整数'));
      } else {
        callback();
      }
    } else if (value === undefined) {
      callback(new Error('请选择产品周期'));
    } else {
      callback();
    }
  } else {
    callback();
  }
};
// 表单校验规则
const rules = reactive({
  name: [
    {
      required: true,
      validator: async (_rule, value, callback) => {
        if (!value) {
          return callback(new Error('请输入规则名称'));
        }
        if (changeInput.value) {
          const params = {
            park_id: props.parkId,
            name: value
          };
          const res = await longRentRuleService.checkRepeatRentRuleName(params);
          if (res.data) {
            return callback(new Error('规则名称已存在,请重新命名'));
          }
        }
        callback();
      },
      trigger: 'blur'
    }
  ],
  type: [
    {
      required: true,
      message: '请选择长租类型',
      trigger: 'change'
    }
  ],
  public_open: [
    {
      required: true,
      message: '请选择是否对小程序开放',
      trigger: 'blur'
    }
  ],
  product_type: {
    required: true,
    message: '请选择产品类型',
    trigger: 'blur'
  },
  money: {
    required: true,
    message: '请输入产品金额',
    trigger: 'blur'
  },
  product_type_list: {
    0: {
      start_time: {
        trigger: 'blur',
        validator: validateRangeTime
      },
      end_time: {
        required: true,
        message: '请选择时段结束时间',
        trigger: 'blur'
      },
      rest_start_time: {
        trigger: 'blur',
        validator: validateRestRangeTime
      },
      rest_end_time: {
        required: true,
        message: '请选择时段结束时间',
        trigger: 'blur'
      },
      time_type: {
        required: true,
        message: '请选择时段类型',
        trigger: 'blur'
      },
      product_range: [
        {
          required: true,
          message: '请选择产品周期',
          trigger: 'change'
        },
        {
          trigger: 'change',
          validator: productRangeTime
        }
      ],
      rent_mode: {
        required: true,
        message: '请选择长租模式',
        trigger: 'blur'
      },
      month_range: {
        required: true,
        message: '请选择月周期',
        trigger: 'blur'
      }
    }
  }
});

/**
 * @description 显示弹窗
 */
const showDialog = (data) => {
  isDisabled.value = data ? data.audit_state === 2 : false;
  initFormData(data);
  changeInput.value = false;
  dialogVisable.value = true;
};

/**
 * @description 重置表单
 */
const closeForm = () => {
  dialogVisable.value = false;
  emits('cancel');
};

/**
 * @description 初始化表单数据
 */
const initFormData = (data) => {
  loading.value = false;
  isAddRule.value = !data;
  if (data) {
    formData.value = data;
    if (props.parkingType === 'branch') {
      if (!formData.value.product_type_list[0]?.days) {
        formData.value.product_type_list[0].days = [];
      }
      if (!formData.value.product_type_list[0]?.week_day) {
        formData.value.product_type_list[0].week_day = [];
      }
    }
  } else {
    formData.value = {
      park_id: props.parkId,
      name: undefined,
      type: undefined,
      public_open: undefined,
      product_type: undefined,
      money: undefined,
      product_type_list: [
        {
          product_range: undefined,
          rent_mode: 1, // 默认按星期模型',
          workday: undefined,
          rest_day: undefined,
          rest_start_time: undefined,
          rest_end_time: undefined,
          start_time: undefined,
          end_time: undefined,
          time_type: undefined,
          week_day: [],
          days: [],
          month_range: undefined
        }
      ]
    };
    productRangeText.value = undefined;
  }
  initSelects();
  initProductRange();
};

/**
 * @desc 长租类型和产品类型切换时 重置表单数据
 * @param {*} type 'product_type' | 'type'
 */
const clearFormData = (type) => {
  if (type === 'type') {
    formData.value.public_open = undefined;
  } else {
    formData.value.product_type_list = [
      {
        product_range: undefined,
        rent_mode: 1, // 默认按星期模型
        workday: undefined,
        rest_day: undefined,
        rest_start_time: undefined,
        rest_end_time: undefined,
        start_time: undefined,
        end_time: undefined,
        time_type: [5, 6, 7, 8, 9].includes(type) ? 0 : undefined,
        week_day: [],
        days: [],
        month_range: undefined
      }
    ];
  }
};

/**
 * @description 通过code查询字典
 */
const initSelects = () => {
  // 长租类型
  dictService.getDictsList('LONG_RENT_TYPE').then((response) => {
    rentType.value = response;
  });
  // 产品类型
  const param = [
    { enum_key: 'product_type_list', enum_value: props.parkingType === 'headquarter' ? 'EnumGroupRentProductType' : 'EnumRentProductType' }
  ];
  commonService.findEnums('park', param).then((response) => {
    let arr1 = response.data.product_type_list.filter((item) => item.value !== 6 && item.value !== 7 && item.value !== 8 && item.value !== 9);
    let arr2 = response.data.product_type_list.filter((item) => item.value == 8 || item.value == 9);
    productType.value = [...arr2, ...arr1];
  });
};
const initProductRange = () => {
  // 产品周期
  if (formData.value.product_type === 8) {
    if (getRentProductRangeText(dailyRentProductRanges, formData.value.product_type_list[0].product_range) == '--') {
      productRangeText.value = formData.value.product_type_list[0].product_range;
      formData.value.product_type_list[0].product_range = 'zdy';
    }
  } else if (formData.value.product_type === 9) {
    if (getRentProductRangeText(weeklyRentProductRanges, formData.value.product_type_list[0].product_range) == '--') {
      productRangeText.value = formData.value.product_type_list[0].product_range;
      formData.value.product_type_list[0].product_range = 'zdy';
    }
  }
};

/**
 * @description 校验自定义日期
 */
const validatorDays = () => {
  // 判断产品类型是否为自定义日期
  if (formData.value.product_type !== 7) return true;
  // 判断自定义日期和自定义星期是否都为空
  if (formData.value.product_type_list[0].week_day.length === 0 && formData.value.product_type_list[0].days.length === 0) {
    // 如果都为空，则报错
    ElMessage.error('请选择自定义星期或自定义日期');
    return false;
  } else {
    // 如果不为空，则返回true
    return true;
  }
};

/**
 * @description 保存表单数据
 */
const saveData = async () => {
  // 验证自定义日期
  if (validatorDays()) {
    // 加载中
    loading.value = true;
    let apiFunc;
    // 判断停车场类型
    if (props.parkingType === 'headquarter') {
      // 判断是否为新增
      apiFunc = isAddRule.value ? groupRentRuleService.createGroupRentRule : groupRentRuleService.updateRentRule;
      formData.value.product_list = [
        {
          money: formData.value.money,
          type: formData.value.product_type
        }
      ];
    } else {
      // 判断是否为新增
      apiFunc = isAddRule.value ? longRentRuleService.createRentRule : longRentRuleService.updateRentRule;
    }
    try {
      // 调用接口
      let params = {
        ...formData.value,
        product_type_list: formData.value.product_type_list.map((item) => {
          return {
            ...item,
            product_range: item.product_range === 'zdy' ? productRangeText.value : item.product_range
          };
        })
      };
      const { success, message } = await apiFunc(params);
      // 判断是否成功
      if (success) {
        // 触发submit事件
        emits('submit', params);
        // 提示成功
        ElMessage.success(message);
        // 关闭表单
        closeForm();
      } else {
        // 提示失败
        ElMessage.error(message || '保存失败');
      }
    } catch (e) {
      console.log('e', e);
    } finally {
      // 加载结束
      loading.value = false;
    }
  }
};

const handleProductRange = (value) => {
  // 判断产品类型是否为自定义日期
  if (formData.value.product_type === 8 || formData.value.product_type === 9) {
    if (value === 'zdy') {
      return productRangeText.value;
    } else {
      return value;
    }
  } else {
    return value;
  }
};
/**
 * @description 提交表单
 */
const handleSubmit = debounce(() => {
  // 判断表单是否为空
  if (!roleForm.value) return;
  if (formData.value.product_type === 5 && !formData.value.product_type_list[0].workday && !formData.value.product_type_list[0].rest_day) {
    if (formData.value.product_type_list[0].rent_mode === 1) {
      ElMessage.error('周内和周六日的长租时段不能同时为无');
    } else {
      ElMessage.error('法定工作日和法定节假日的长租时段不能同时为无');
    }
    return;
  }
  // 验证表单
  roleForm.value.validate().then(async () => {
    let params = {
      park_id: props.parkId,
      type: formData.value.type,
      product_type: formData.value.product_type,
      product_range: handleProductRange(formData.value.product_type_list[0].product_range),
      public_open: formData.value.public_open
    };
    if (!isAddRule.value) {
      params = {
        ...params,
        rule_id: formData.value.id
      };
    }
    const res = await longRentRuleService.checkRentRule(params);
    if (formData.value.public_open && res.success && res.data?.detailMessage) {
      ElMessageBox.confirm(res.data?.detailMessage, '温馨提示').then(() => {
        saveData();
      });
    } else {
      saveData();
    }
  });
}, 600);

defineExpose({ showDialog });
</script>

<template>
  <el-dialog :title="`${isAddRule ? '添加' : '修改'}长租规则`" v-model="dialogVisable" :close-on-click-modal="false" destroy-on-close width="1080px">
    <el-form ref="roleForm" label-width="120px" :rules="rules" :model="formData" class="grid-form">
      <el-form-item prop="name" label="规则名称">
        <el-input v-model="formData.name" placeholder="请输入套餐规则名称,限12字以内" @input="changeInput = true" :maxlength="12" clearable />
      </el-form-item>
      <el-form-item prop="type" label="长租类型">
        <el-select
          v-model="formData.type"
          placeholder="请选择长租类型（即规则适用用户）"
          @change="clearFormData('type')"
          style="width: 100%"
          :disabled="isDisabled"
        >
          <el-option v-for="item in rentType" :key="item.value" :label="item.name" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="product_type" label="产品类型">
        <el-select
          v-model="formData.product_type"
          placeholder="请选择产品类型"
          @change="clearFormData('product_type')"
          style="width: 100%"
          :disabled="isDisabled"
        >
          <el-option v-for="item in productType" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="money" label="产品金额">
        <el-input-number
          v-model="formData.money"
          controls-position="right"
          :precision="2"
          :step="0.1"
          :min="0.01"
          :max="999999"
          style="width: 100%"
          :disabled="isDisabled"
        />
      </el-form-item>
      <el-form-item prop="public_open" label-width="150px">
        <template #label>
          <span class="label-flex">
            是否对小程序开放
            <el-tooltip content="选择'是'，将在小程序端对用户可见，用户可购买该套餐；选择'否'，则反之。" placement="top">
              <el-icon>
                <QuestionFilled />
              </el-icon>
            </el-tooltip>
          </span>
        </template>
        <el-radio-group v-model="formData.public_open">
          <el-radio :value="1">是</el-radio>
          <el-radio :value="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item prop="public_open" label="是否对外开放" v-if="showItem.public_open">
        <el-radio-group v-model="formData.public_open" :disabled="isDisabled">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item prop="product_type_list[0].product_range" label="产品周期" v-if="showItem.product_range">
        <el-radio-group v-if="[5, 6, 7].includes(formData.product_type)" v-model="formData.product_type_list[0].product_range" :disabled="isDisabled">
          <el-radio :label="item.label" v-for="item in rentProductRanges" :key="item.label">{{ item.text }}</el-radio>
        </el-radio-group>
        <template v-else-if="formData.product_type === 8">
          <el-radio-group v-model="formData.product_type_list[0].product_range" :disabled="isDisabled">
            <el-radio v-for="item in dailyRentProductRanges" :key="item.label" :value="item.label">{{ item.text }}</el-radio>
            <el-radio value="zdy"
              ><el-input v-model="productRangeText" style="width: 60px; margin-right: 4px" placeholder="自定义" :disabled="isDisabled" />天</el-radio
            >
          </el-radio-group>
        </template>
        <template v-else-if="formData.product_type === 9">
          <el-radio-group v-model="formData.product_type_list[0].product_range" :disabled="isDisabled">
            <el-radio v-for="item in weeklyRentProductRanges" :key="item.label" :value="item.label">{{ item.text }}</el-radio>
            <el-radio value="zdy"
              ><el-input v-model="productRangeText" style="width: 60px; margin-right: 4px" placeholder="自定义" :disabled="isDisabled" />周</el-radio
            >
          </el-radio-group>
        </template>
      </el-form-item>
      <template v-if="showItem.start_time">
        <div>
          <el-form-item prop="product_type_list[0].rent_mode" label="长租模式">
            <el-radio-group v-model="formData.product_type_list[0].rent_mode" :disabled="isDisabled">
              <el-radio :label="1">按星期模式</el-radio>
              <el-radio :label="2">按法定日模式</el-radio>
            </el-radio-group>
          </el-form-item>
        </div>
        <div style="display: flex; align-items: center; grid-column-start: span 2">
          <el-form-item prop="product_type_list[0].workday" label-width="200px">
            <template #label v-if="formData.product_type_list[0].rent_mode === 1">
              <span class="label-flex">
                周内是否有长租时段
                <el-tooltip placement="top">
                  <template #content>
                    <span>周内是指周一至周五，当选择'无'，即产品周期内如遇周一至周五停车全天按临停收费；</span>
                    <br />
                    <span>当选择'有',即产品周期内如遇周一至周五在长租时段内停车免费，长租时段以外按临停收费。</span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <template #label v-else>
              <span class="label-flex">
                法定工作日是否有长租时段
                <el-tooltip placement="top">
                  <template #content>
                    <span>根据当年国家公布的年历进行日期认定，当选择"无"，即产品周期内如遇法定工作日、法定节假日(含周末)停车全天按临停收费；</span>
                    <br />
                    <span>当选择"有",即产品周期内如遇法定工作日、法定节假日(含周末)在长租时段内停车则免费，长租时段以外按临停收费。</span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-radio-group v-model="formData.product_type_list[0].workday" :disabled="isDisabled">
              <el-radio :value="0">无</el-radio>
              <el-radio :value="1">有</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="formData.product_type_list[0].workday === 1">
            <el-form-item prop="product_type_list[0].start_time" label="长租时段">
              <template #label>
                <div class="label-flex">
                  <span>长租时段</span>
                  <el-tooltip content="每天长租时段内停车免费，长租时段以外按临停计费" placement="top">
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="rime-range-picker">
                <el-time-picker v-model="formData.product_type_list[0].start_time" value-format="HH:mm:ss" :disabled="isDisabled" />
                <span>至</span>
                <el-time-picker v-model="formData.product_type_list[0].end_time" value-format="HH:mm:ss" :disabled="isDisabled" />
              </div>
            </el-form-item>
          </div>
        </div>
        <div style="display: flex; align-items: center; grid-column-start: span 2">
          <el-form-item prop="product_type_list[0].rest_day" label-width="200px">
            <template #label v-if="formData.product_type_list[0].rent_mode === 1">
              <span class="label-flex">
                周六日是否有长租时段
                <el-tooltip placement="top">
                  <template #content>
                    <span>周六日是指周六、周日，当选择'无'，即产品周期内如遇周六、周日停车全天按临停收费；</span>
                    <br />
                    <span>当选择'有',即产品周期内如遇周六、周日在长租时段内停车免费，长租时段以外按临停收费。</span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <template #label v-else>
              <span class="label-flex">
                法定节假日是否有长租时段
                <el-tooltip placement="top">
                  <template #content>
                    <span>根据当年国家公布的年历进行日期认定，当选择"无"，即产品周期内如遇法定工作日、法定节假日(含周末)停车全天按临停收费；</span>
                    <br />
                    <span>当选择"有",即产品周期内如遇法定工作日、法定节假日(含周末)在长租时段内停车则免费，长租时段以外按临停收费。</span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </span>
            </template>
            <el-radio-group v-model="formData.product_type_list[0].rest_day" :disabled="isDisabled">
              <el-radio :value="0">无</el-radio>
              <el-radio :value="1">有</el-radio>
            </el-radio-group>
          </el-form-item>
          <div v-if="formData.product_type_list[0].rest_day === 1">
            <el-form-item prop="product_type_list[0].rest_start_time" label="长租时段">
              <template #label>
                <div class="label-flex">
                  <span>长租时段</span>
                  <el-tooltip content="每天长租时段内停车免费，长租时段以外按临停计费" placement="top">
                    <el-icon>
                      <QuestionFilled />
                    </el-icon>
                  </el-tooltip>
                </div>
              </template>
              <div class="rime-range-picker">
                <el-time-picker v-model="formData.product_type_list[0].rest_start_time" value-format="HH:mm:ss" :disabled="isDisabled" />
                <span>至</span>
                <el-time-picker v-model="formData.product_type_list[0].rest_end_time" value-format="HH:mm:ss" :disabled="isDisabled" />
              </div>
            </el-form-item>
          </div>
        </div>
      </template>
      <el-form-item prop="product_type_list[0].week_day" label="星期设定" v-if="showItem.week_day">
        <el-checkbox-group v-model="formData.product_type_list[0].week_day" :disabled="isDisabled">
          <el-checkbox
            v-for="(item, index) in ['周日', '周一', '周二', '周三', '周四', '周五', '周六']"
            :label="index + 1"
            :key="index"
            :disabled="formData.product_type_list[0].days && formData.product_type_list[0].days.length != 0"
          >
            {{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="product_type_list[0].days" label="日期设定" v-if="showItem.week_day">
        <el-checkbox-group v-model="formData.product_type_list[0].days" :disabled="isDisabled">
          <el-checkbox
            v-for="item in 31"
            :label="item"
            :key="item"
            :disabled="formData.product_type_list[0].week_day && formData.product_type_list[0].week_day.length != 0"
          >
            {{ item }}
          </el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item prop="product_type_list[0].month_range" label="月周期设定" v-if="showItem.month_range">
        <el-radio-group v-model="formData.product_type_list[0].month_range" :disabled="isDisabled">
          <el-radio :label="2">30天</el-radio>
          <el-radio :label="1">31天</el-radio>
          <el-radio :label="0">自然月</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="closeForm">取 消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<style scoped>
.grid-form {
  display: grid;
  grid-template-columns: 1fr 1fr;
}

.rime-range-picker {
  width: 360px;
  display: flex;
  align-items: center;

  & > span {
    margin: 0 10px;
  }
}

.label-flex {
  display: flex;
  align-items: center;
}

:deep(.el-form-item__content) {
  align-items: flex-start;
}
</style>
