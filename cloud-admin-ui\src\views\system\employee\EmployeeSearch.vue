<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name_or_mobile" placeholder="用户ID/姓名/手机号" /></form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        range-separator="至"
        start-placeholder="创建开始时间"
        end-placeholder="创建结束时间"
        style="width: 100%"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
    <form-search-item>
      <org-filter v-model="form.queryParams.department_id" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.enable" placeholder="启用状态" clearable>
        <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="EmployeeSearch" setup>
import { dayjs } from 'element-plus';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';
import orgFilter from '@/components/orgFilter.vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name_or_mobile: undefined,
    department_id: undefined,
    enable: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const options = [
  {
    value: 1,
    label: '启用'
  },
  {
    value: 0,
    label: '禁用'
  }
];

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.created_at_start = form.dateRange[0];
    form.queryParams.created_at_end = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.created_at_start = undefined;
    form.queryParams.created_at_end = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    name_or_mobile: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
