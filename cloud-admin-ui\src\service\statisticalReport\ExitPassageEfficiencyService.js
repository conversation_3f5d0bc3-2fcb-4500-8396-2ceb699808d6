import * as exitPassageEfficiencyApi from '@/api/statisticalReport/ExitPassageEfficiencyApi';

/**
 * 出口通行效率
 */
export default {
  /**
   * 分页查询出口通行效率
   */
  pagingExitPassageEfficiency(data) {
    return new Promise((resolve, reject) => {
      try {
        exitPassageEfficiencyApi.pagingExitPassageEfficiency(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按日导出
   */
  exportDataByDay(data) {
    return new Promise((resolve, reject) => {
      try {
        exitPassageEfficiencyApi.exportDataByDay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 汇总导出
   */
  exportDataByGather(data) {
    return new Promise((resolve, reject) => {
      try {
        exitPassageEfficiencyApi.exportDataByGather(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 汇总统计
   */
  parkExitTrafficEfficienciesCount(data) {
    return new Promise((resolve, reject) => {
      try {
        exitPassageEfficiencyApi.parkExitTrafficEfficienciesCount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
