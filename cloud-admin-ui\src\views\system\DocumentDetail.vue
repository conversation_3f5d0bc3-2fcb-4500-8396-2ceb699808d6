<template>
    <el-card shadow="never" style="margin: 10px 0px">
        <template #header>
            <div style="height: 45px">
                <div style="display: flex; justify-content: space-between; height: 45px">
                    <div style="line-height: 45px">
                        <span>{{ docTypeName }}</span>
                    </div>
                    <div style="line-height: 45px"><el-button type="primary" @click="cancelAndClose()">关 闭</el-button></div>
                </div>
            </div>
        </template>
        <div class="opers">
            <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
                <el-form-item prop="title" label="文档标题" style="width: 30%">
                    <span>{{ data.form.title }}</span>
                </el-form-item>
                <el-form-item prop="content">
                    <div style="border: 1px solid #ccc">
                        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
                        <Editor
                                style="height: 500px; overflow-y: hidden"
                                v-model="data.form.content"
                                :defaultConfig="editorConfig"
                                :mode="mode"
                                @onCreated="handleCreated"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="上传附件">
                    <el-space direction="vertical" style="align-items: left">
                        <el-link @click="handleDetail(data.form.attachment_path)" type="primary">{{ data.form.attachment_name }}</el-link>
                    </el-space>
                </el-form-item>
            </el-form>
        </div>
    </el-card>
</template>

<script name="DocumentEdit" setup>
    import '@wangeditor/editor/dist/css/style.css';
    import documentService from '@/service/system/DocumentService';
    import { ref, shallowRef, reactive, onActivated, onBeforeUnmount } from 'vue';
    import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
    import { ElMessage } from 'element-plus';
    import { useRoute } from 'vue-router';
    import { closeCurrentTab } from '@/utils/tabKit';
    {
        Editor, Toolbar;
    }
    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef();
    // 内容 HTML
    const route = useRoute();
    const mode = ref('default');
    const saveBtn = ref(false);
    const docTypeName = ref('');
    const data = reactive({
        queryParams: {
            id: undefined
        },
        form: {
            id: undefined,
            title: undefined,
            content: undefined,
            doc_type_id: undefined,
            attachment_name: undefined,
            attachment_path: undefined
        },
        rules: {
            title: [
                {
                    required: true,
                    message: '请填写文档标题',
                    trigger: 'blur'
                }
            ]
        }
    });

    onActivated(() => {
        if ({} !== route.query && undefined !== route.query.id) {
            data.form.id = route.query.id;
            getDocumentById(data.form.id);
        }
        if ({} !== route.query && undefined !== route.query.docTypeName) {
            docTypeName.value = route.query.docTypeName;
        }
    });

    const toolbarConfig = {};
    const editorConfig = { placeholder: '请输入内容...' };

    // 组件销毁时，也及时销毁编辑器
    onBeforeUnmount(() => {
        const editor = editorRef.value;
        if (editor == null) return;
        editor.destroy();
    });

    const handleCreated = (editor) => {
        editorRef.value = editor; // 记录 editor 实例，重要！
    };

    // 通过文档ID获取文档信息
    const getDocumentById = (id) => {
        documentService.getDocumentById(id).then((response) => {
            if (response.success === true) {
                data.form.id = response.data.id;
                data.form.title = response.data.title;
                data.form.content = response.data.content;
                data.form.doc_type_id = response.data.doc_type_id;
                data.form.attachment_name = response.data.attachment_name;
                data.form.attachment_path = response.data.attachment_path;
                saveBtn.value = false;
                const editor = editorRef.value;
                editor.disable();
            } else {
                ElMessage({
                    message: response.message,
                    type: 'error'
                });
                return false;
            }
        });
    };

    const handleDetail = (row) => {
        window.open(import.meta.env.VITE_BASE_URL + row, '_blank');
    };

    // 关闭页面
    const cancelAndClose = () => {
        closeCurrentTab({
            path: '/system/documentCenter'
        });
    };
</script>
<style lang="scss" scoped>
  .footer {
    padding-top: 50px;
    padding-bottom: 50px;
    text-align: center;
  }
</style>
