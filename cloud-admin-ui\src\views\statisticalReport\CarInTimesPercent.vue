<template>
  <div class="container">
    <car-in-times-percent-search @form-search="searchParkCarInTimesPercentList" @reset="resetParamsAndData" />
    <car-in-times-percent-table ref="table" />
  </div>
</template>

<script name="CarInTimesPercent" setup>
import CarInTimesPercentSearch from './carInTimesPercent/CarInTimesPercentSearch.vue';
import CarInTimesPercentTable from './carInTimesPercent/CarInTimesPercentTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkCarInTimesPercentList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
