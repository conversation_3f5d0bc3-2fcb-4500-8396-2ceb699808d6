<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="time" label="日期" align="center" min-width="120" fixed="left" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="200" fixed="left" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column label="所在地" prop="location" align="center" min-width="180" />
        <el-table-column prop="should_pay_money_cnt" label="应收（次）" align="center" min-width="180" />
        <el-table-column prop="payed_money_cnt" label="实收（次）" align="center" min-width="180" />
        <el-table-column prop="electronic_money_cnt" label="电子支付（次）" align="center" min-width="180" />
        <el-table-column prop="ali_money_cnt" label="线上交易（次）" align="center" min-width="180" />
        <el-table-column prop="ali_money" label="线上交易（元）" align="center" min-width="180" />
        <el-table-column prop="cash_money_cnt" label="ETC支付（次）" align="center" min-width="180" />
        <el-table-column prop="ali_money_cnt" label="支付宝支付（次）" align="center" min-width="180" />
        <el-table-column prop="wx_money_cnt" label="微信支付（次）" align="center" min-width="180" />
        <el-table-column prop="parking_third_party_num" label="第三方会员收入（次）" align="center" min-width="180" />
        <el-table-column prop="cash_money_cnt" label="现金支付（次）" align="center" min-width="180" />
        <el-table-column prop="special_money_cnt" label="特殊处理（次）" align="center" min-width="180" />
        <el-table-column prop="debate_money_cnt" label="优免抵扣（次）" align="center" min-width="180" />
        <el-table-column prop="special_loss_money_cnt" label="特殊处理损失（次）" align="center" min-width="180" />
        <el-table-column prop="flush_loss_money_cnt" label="被冲车辆损失（次）" align="center" min-width="180" />
        <el-table-column prop="manual_money_cnt" label="手动抬杆（次）" align="center" min-width="180" />
        <el-table-column prop="park_in_num" label="入场临停（次）" align="center" min-width="180" />
        <el-table-column prop="rent_in_num" label="入场长租（次）" align="center" min-width="180" />
        <el-table-column prop="park_out_num" label="出场临停（次）" align="center" min-width="180" />
        <el-table-column prop="rent_out_num" label="出场长租（次）" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="DayReportByTimesTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import dayReportService from '@/service/finance/DayReportService';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  dayReportService.pagingDayReportByTimes(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
