<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.terminal_code" placeholder="终端编码" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="终端名称" /></form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="更新开始时间"
        end-placeholder="更新结束时间"
        style="width: 100%"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
      />
    </form-search-item>
  </FormSearch>
</template>

<script name="TerminalSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, onActivated, ref } from 'vue';
import { useRoute } from 'vue-router';

const emits = defineEmits(['form-search']);
const route = useRoute();
const park_id = ref('');
const form = reactive({
  queryParams: {
    name: '',
    park_id: '',
    terminal_code: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});

onActivated(() => {
  park_id.value = route.query.park_id;
  form.queryParams.park_id = route.query.park_id;
});

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    park_id: park_id.value,
    terminal_code: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
