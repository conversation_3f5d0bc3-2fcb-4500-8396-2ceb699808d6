<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <div class="opers">
        <el-space>
          <div></div>
        </el-space>
        <el-space>
          <!-- <div class="uodataClass">
            <el-tooltip>
              <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
              <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
            </el-tooltip>
            <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
          </div> -->
        </el-space>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 317px)">
        <el-table-column label="日期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ scope.row.statistics_date }}</span>
          </template>
        </el-table-column>
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车场名称" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.park_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="车场ID" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.park_id }}</span>
          </template>
        </el-table-column>
        <el-table-column label="大区" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.region_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="城市分公司" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.organizational_structure }}</span>
          </template>
        </el-table-column>
        <el-table-column label="通道名称" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.gateway_name }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本地呼叫次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.local_call_num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本地接听次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.local_accept_num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本地呼叫本地未接听次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.local_refuse_num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本地呼叫等待平均时长（秒）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.call_duration_avg }}</span>
          </template>
        </el-table-column>
        <el-table-column label="本地通话平均时长（秒）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.accept_duration_avg }}</span>
          </template>
        </el-table-column>
        <el-table-column label="远程值守模式本地呼叫次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.remote_call_num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="远程值守模式本地接听次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.remote_accept_num }}</span>
          </template>
        </el-table-column>

        <el-table-column label="远程值守模式本地呼叫远程未接听次数" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.remote_refuse_num }}</span>
          </template>
        </el-table-column>
        <el-table-column label="远程值守模式呼叫等待平均时长" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.remote_call_duration_avg }}</span>
          </template>
        </el-table-column>
        <el-table-column label="远程值守模式通话平均时长（秒）" align="center" min-width="90">
          <template #default="scope">
            <span>{{ scope.row.remote_accept_duration_avg }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="TimedAccessTable" setup>
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { getAnswerList } from '@/api/answer/index.js';
import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted } from 'vue';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const parkId = ref(null);
// onMounted(() => {
//   getList(data.queryParams);
// });
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(24);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  getAnswerList(params).then((response) => {
    if (response.success === true) {
      parkId.value = data.queryParams.park_id;
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
</style>
