<!-- 停车场交易汇总 -->
<template>
  <div class="transaction_summary">
    <div class="title">停车场交易汇总</div>
    <el-table :data="tableData" v-loading="loading" border style="width: 100%">
      <el-table-column prop="statement_date" label="账单日期" align="center" />
      <el-table-column prop="merchant_no" label="商户编号" align="center" />
      <el-table-column prop="total_transaction_amount" label="交易总金额" align="center" />
      <el-table-column prop="total_refund_amount" label="退款总金额" align="center" />
      <el-table-column prop="commission_amount" label="交易手续费金额" align="center" />
      <el-table-column prop="refund_commission_amount" label="退款退回手续费金额" align="center" />
      <el-table-column prop="cash_amount" label="现金收款" align="center" />
      <el-table-column prop="account_balance" label="账户余额" align="center" />
    </el-table>
    <!-- <el-pagination
      background
      :current-page="pagination.page"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="pagination.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    /> -->
  </div>
</template>

<script setup>
import requestApi from '@/service/finance/TransactionReconciliation';
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';

const total = ref(0);
const loading = ref(false);
const tableData = ref([]);
const pagination = reactive({
  page: 1,
  limit: 30
});
const query = ref({
  park_id: '',
  park_name: '',
  stl_date: ''
});

const init = (data) => {
  loading.value = true;
  if (data) {
    query.value = data;
  }
  let params = {
    ...query.value,
    page: pagination.page,
    limit: pagination.limit
  };
  requestApi
    .getParkTransactionSummary(params)
    .then((res) => {
      if (res.success) {
        tableData.value = [res.data];
        loading.value = false;
      } else {
        ElMessage({
          message: res.detail_message != '' ? res.detail_message : res.message,
          type: 'error'
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
const handleCurrentChange = (val) => {
  pagination.page = val;
  init();
};
const handleSizeChange = (val) => {
  pagination.limit = val;
  init();
};

defineExpose({
  init
});
</script>
<style lang="scss" scoped></style>
