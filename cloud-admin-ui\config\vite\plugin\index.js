import vue from '@vitejs/plugin-vue';
import { configImageminPlugin } from './imagemin';
import { configCompressPlugin } from './compress';
import { configVisualizerConfig } from './visualizer';

export function generateVitePlugins(viteEnv, isBuild) {
  const { VITE_USE_IMAGEMIN, VITE_BUILD_COMPRESS, VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE, VITE_SHOW_REPORT } = viteEnv;
  const vitePlugins = [
    // have to
    vue()
  ];
  if (isBuild) {
    VITE_USE_IMAGEMIN && vitePlugins.push(configImageminPlugin());
    VITE_SHOW_REPORT && vitePlugins.push(configVisualizerConfig());
    vitePlugins.push(configCompressPlugin(VITE_BUILD_COMPRESS, VITE_BUILD_COMPRESS_DELETE_ORIGIN_FILE));
  }
  return vitePlugins;
}
