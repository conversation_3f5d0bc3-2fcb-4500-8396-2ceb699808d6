<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <!-- 导出exel表格按钮 组件实际就是el-button  "btnType"参数为el-button按钮类型  "exportFunc"为点击进行生成exel的api   "rules"为是"params"规则  -->
        <DownloadButton
          btnType="default"
          :exportFunc="exportSale"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <!-- table表格 -->
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 320px)">
        <el-table-column label="车场基本信息" align="center">
          <el-table-column prop="created_at" label="统计日期" align="center" width="130"> </el-table-column>
          <el-table-column prop="park_name" label="车场名称" align="center" min-width="160" />
          <el-table-column prop="park_id" label="车场ID" align="center" />
          <el-table-column prop="region_name" label="大区" align="center" />
          <el-table-column prop="organizational_structure" label="城市分公司" align="center" />
          <el-table-column prop="province_name" label="省份名称" align="center" />
          <el-table-column label="城市名称" prop="city_name" align="center"> </el-table-column>
          <el-table-column prop="first_category_name" label="商家一级类别" align="center" />
          <el-table-column label="商家二级类别" align="center">
            <!-- 对于旧数据没有二级  所以有二级进行显示  没有的显示"/" -->
            <template #="{ row, $index }">
              <div class="second_category_desc">
                <div v-if="row.second_category_name">{{ row.second_category_name }}</div>
                <div v-else>/</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type_desc" label="优免种类" align="center" />
        </el-table-column>
        <el-table-column label="业务指标" align="center">
          <el-table-column prop="money_category" label="金额类别" align="center">
            <template #header>
              <div class="money_categorycalss">
                金额类别
                <div>
                  <el-tooltip placement="top-end" effect="light">
                    <template #content>
                      <strong>商家从广场物业处购买优惠券的折扣幅度。计算公<br />式:</strong>
                      折扣率=实际销售金额/总原价x100%<br />全免卷和时段券以十折进行展示。</template
                    >
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
            <template #="{ row }">{{ preciseMultiply(row.money_category,10) }}折 </template>
          </el-table-column>
          <el-table-column prop="contract_terms_desc" label="合同约定" align="center" />
          <el-table-column prop="valid_day" label="有效期(天)" align="center" />
        </el-table-column>
        <el-table-column label="售卖指标" align="center">
          <el-table-column prop="total_count" label="总数量" align="center" />
          <el-table-column prop="total_price" label="总金额" align="center" />
        </el-table-column>
      </el-table>
      <!-- 分页组件 -->
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CouponMetaTable" setup>
import { reactive, ref, onActivated } from 'vue'; //引入vue
import decimal from 'decimal.js'
import { ElMessage } from 'element-plus'; //引入element-plus
import { maidata, exportSale } from '@/api/merchant/CouponStatsApi'; //直接引入api    'maidata'为获取优免卷售卖数据的api   "exportSale"为导出exel表格的api
import DownloadButton from '@/components/DownloadButton.vue'; //调用公共封装的“导出”按钮
const tableData = ref([]); //table数据
const loading = ref(false); //table加载
const total = ref(0); //分页总数
//查询的数据
const data = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    organization_ids: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  }
});
//这个不知道之前的人为什么这样写  在最外用到了keep-alive组件
onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
});
//页面加载时候获取数据params是传过来的参数
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  const { park_name, ...otherdata } = params; //解构赋值发送的参数没有"park_name" 解构出来
  data.queryParams = otherdata;
  maidata(otherdata).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
function preciseMultiply(num, multiplier) {
    return decimal(num).mul(multiplier)     
}
//分页组件进行更改获取数量
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
//分页组件进行更改页数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

//暴露给父组件使用如父组件点击了“查询”和“重置”按钮
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.second_category_desc {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
.money_categorycalss {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    cursor: pointer;
  }
}
</style>
