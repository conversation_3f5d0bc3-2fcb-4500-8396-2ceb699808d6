<template>
  <div class="container">
    <property-owner-search @form-search="searchPropertyOwnerList" @reset="resetParamsAndData" />
    <property-owner-table ref="table" />
  </div>
</template>

<script name="PropertyOwner" setup>
import PropertyOwnerSearch from './propertyOwner/PropertyOwnerSearch.vue';
import PropertyOwnerTable from './propertyOwner/PropertyOwnerTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchPropertyOwnerList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
