<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-29 14:00:59
 * @LastEditors: 达万安 段世煜
 * @Description: 时间组件
 * @FilePath: \vue-admin-template-master\src\components\timeRange.vue
-->
<template>
  <div class="time-range-warp">
    <!-- 选择器 选择要展示的日期 -->
    <el-select
      :popper-class="props.popperClass"
      v-model="selectedUnit"
      placeholder="类型"
      class="search-select"
      @change="handleTypeChange"
      :size="props.size"
      style="max-width: 100px"
      :teleported="props.teleported"
      v-if="props.showSelect"
    >
      <template v-for="item in props.typeOptions">
        <el-option
          :key="item.value"
          :label="item.label"
          :value="item[props.typeValue]"
          v-if="props.showType.includes(item.type)"
          :disabled="props.disabled.includes(item.type)"
        >
          <div class="select-class">
            <span>{{ item.label }}</span>
            <el-tooltip v-if="item.value == 6" placement="right">
              <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
              <template #content>指上月26日至本月25日为一个月度数据，如 <br />统计8月月度数据则为7月26日至8月25日。</template>
            </el-tooltip>
            <el-tooltip v-if="item.value == 2" placement="right">
              <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
              <template #content
                >指该月实际日历天数为一个月度数据，即 <br />
                从1号开始至该月月底,算作为一个自然月。</template
              >
            </el-tooltip>
          </div>
        </el-option>
      </template>
    </el-select>
    <div class="time-range-container">
      <!-- 日 -->
      <template v-if="unitType == 'date'">
        <el-date-picker
          v-model="state.date_range"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          style="width: 100%"
          @change="handleChangeDay"
          :clearable="false"
          :teleported="props.teleported"
          :value-format="props.formatter"
          :popper-class="props.popperClass"
          :size="props.size"
          :disabled-date="props.disabledDate"
        >
        </el-date-picker>
      </template>
      <!-- 周 -->
      <template v-if="unitType == 'week'">
        <el-row>
          <el-col :span="11">
            <el-date-picker
              v-model="state.start_date"
              :teleported="props.teleported"
              type="week"
              :clearable="false"
              format="YYYY[年]ww[周]"
              :value-format="props.formatter"
              placeholder="开始周"
              style="width: 100%"
              @change="handleChangeWeek"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            >
            </el-date-picker>
          </el-col>
          <el-col class="line text-center" :span="2"> 至 </el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="state.end_date"
              type="week"
              :clearable="false"
              :teleported="props.teleported"
              format="YYYY[年]ww[周]"
              :value-format="props.formatter"
              placeholder="结束周"
              style="width: 100%"
              @change="handleChangeWeek"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            >
            </el-date-picker>
          </el-col>
        </el-row>
      </template>
      <!-- 月(自然月) -->
      <template v-if="unitType == 'month'">
        <el-date-picker
          type="monthrange"
          :clearable="false"
          v-model="state.date_range"
          @change="handleChangeMonth"
          style="width: 100%"
          format="YYYY[年]MM[月]"
          :teleported="props.teleported"
          :value-format="props.formatter"
          range-separator="至"
          start-placeholder="开始月"
          end-placeholder="结束月"
          :popper-class="props.popperClass"
          :size="props.size"
          :disabled-date="props.disabledDate"
        >
        </el-date-picker>
      </template>
      <!-- 月(财务月) -->
      <template v-if="unitType == 'fiscalMonth'">
        <el-date-picker
          type="month"
          :clearable="false"
          v-model="fiscalMonth"
          @change="handleChangefiscalMonth"
          style="width: 300px"
          format="YYYY[年]MM[月]"
          :teleported="props.teleported"
          :value-format="props.formatter"
          :popper-class="props.popperClass"
          :size="props.size"
          :disabled-date="props.disabledDate"
        >
        </el-date-picker>
      </template>
      <!-- 季 -->
      <template v-if="unitType == 'quarter'">
        <el-row :gutter="5">
          <el-col :span="11">
            <el-row :gutter="5">
              <el-col :span="12">
                <el-date-picker
                  type="year"
                  :clearable="false"
                  v-model="state.start_year"
                  @change="handleChangeQuarter"
                  :teleported="props.teleported"
                  format="YYYY年"
                  :value-format="props.formatter"
                  placeholder="开始年"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                  :disabled-date="props.disabledDate"
                ></el-date-picker>
              </el-col>
              <el-col :span="12">
                <el-select
                  placeholder="开始季度"
                  v-model="state.start_quarter"
                  @change="handleChangeQuarter"
                  style="width: 100%"
                  clearable
                  :teleported="props.teleported"
                  :size="props.size"
                >
                  <el-option label="一季度" value="1"></el-option>
                  <el-option label="二季度" value="2"></el-option>
                  <el-option label="三季度" value="3"></el-option>
                  <el-option label="四季度" value="4"></el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="2" align="center" style="padding-top: 5px; font-size: 14px">至</el-col>
          <el-col :span="11">
            <el-row :gutter="5">
              <el-col :span="12">
                <el-date-picker
                  type="year"
                  v-model="state.end_year"
                  format="YYYY年"
                  :value-format="props.formatter"
                  :teleported="props.teleported"
                  @change="handleChangeQuarter"
                  placeholder="结束年"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                  :disabled-date="props.disabledDate"
                ></el-date-picker>
              </el-col>
              <el-col :span="12">
                <el-select
                  placeholder="结束季度"
                  v-model="state.end_quarter"
                  :teleported="props.teleported"
                  @change="handleChangeQuarter"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                >
                  <el-option label="一季度" value="1"></el-option>
                  <el-option label="二季度" value="2"></el-option>
                  <el-option label="三季度" value="3"></el-option>
                  <el-option label="四季度" value="4"></el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </template>
      <!-- 年 -->
      <template v-if="unitType == 'year'">
        <el-row>
          <el-col :span="11">
            <el-date-picker
              :clearable="false"
              v-model="state.start_date"
              type="year"
              format="YYYY年"
              :value-format="props.formatter"
              :teleported="props.teleported"
              @change="handleChangeYear"
              placeholder="开始年"
              style="width: 100%"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            ></el-date-picker>
          </el-col>
          <el-col :span="2" class="text-center">至</el-col>
          <el-col :span="11">
            <el-date-picker
              :clearable="false"
              v-model="state.end_date"
              type="year"
              format="YYYY年"
              :value-format="props.formatter"
              :teleported="props.teleported"
              @change="handleChangeYear"
              placeholder="结束年"
              style="width: 100%"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            ></el-date-picker>
          </el-col>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script setup>
import { watch, reactive, onMounted, ref, computed } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
const fiscalMonth = ref();
const shortcuts = [
  {
    text: '昨天',
    value: () => {
      // const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      return [start, start];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];

const props = defineProps({
  // 时间单位
  //默认selcet选择的值
  unit: {
    type: String,
    default: 'date' // year month date quarter week
  },
  // 时间值
  date: {
    type: Array,
    default: () => {
      return [];
    }
  },
  // 禁用时间单位数组
  disabled: {
    type: Array,
    default: () => {
      return [];
    }
  },
  // 时间单位选择器数据
  typeOptions: {
    type: Array,
    default: () => {
      return [
        {
          value: 3,
          label: '按日',
          type: 'date'
        },
        {
          value: 5,
          label: '按周',
          type: 'week'
        },
        {
          value: 2,
          label: '按月(自然月)',
          type: 'month'
        },
        {
          value: 6,
          label: '按月(财务月)',
          type: 'fiscalMonth'
        }
      ];
    }
  },
  // 禁用时间
  disabledDate: {
    type: Function,
    default: (time) => {
      return time.getTime() > Date.now();
    }
  },
  // 组件大小
  size: String,
  // 弹出框自定义类
  popperClass: String,
  // 是否展示时间类型选择器
  showSelect: {
    type: Boolean,
    default: true
  },
  teleported: {
    type: Boolean,
    default: true
  },
  // 时间类型(不展示选择器时使用)
  type: String,
  // 时间格式化
  formatter: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  // 展示哪些选择器
  showType: {
    type: Array,
    default: () => {
      return ['year', 'month', 'fiscalMonth', 'date', 'quarter', 'week'];
    }
  },
  // 展示的选择器value使用key
  typeValue: {
    type: String,
    default: 'value'
  }
});

const emits = defineEmits(['update:date', 'update:unit', 'change']);
// 当前选中的时间选择器类型  好好好这么写看死我吧 总的来说返回selected组件选择的type
const unitType = computed(() => {
  // 如果不展示选择器，则直接返回传入的type
  if (!props.showSelect) return props.type;
  //如果没有选择 选择器组件(默认为date)，则返回false
  if (!selectedUnit.value) return false;
  // 如果你传来的数据为type，则select组件中value就是取typeOptions中的type属性，则返回type对应的值
  if (props.typeValue === 'type') return selectedUnit.value;
  if (props.typeValue === 'value') return props.typeOptions.find((item) => item.value == selectedUnit.value)?.type;
  return false;
});

watch(
  () => props.date,
  (val) => {
    dealDefault(val);
  }
);

onMounted(() => {
  dealDefault(props.date);
});

const dealDefault = (val) => {
  switch (unitType.value) {
    case 'year':
      state.start_year = val[0];
      state.end_year = val[1];
      break;
    case 'month':
    case 'fiscalMonth':
    case 'date':
      state.date_range = val;
      break;
    case 'week':
      state.start_date = val[0];
      state.end_date = val[1];
      break;
    case 'quarter':
      state.start_quarter = val[0];
      state.end_quarter = val[1];
      break;
    default:
      break;
  }
};
watch(
  () => props.unit,
  (val) => {
    selectedUnit.value = val;
  }
);
const selectedUnit = ref(props.unit);
const state = reactive({
  start_date: undefined,
  end_date: undefined,
  start_year: undefined,
  end_year: undefined,
  start_quarter: undefined,
  end_quarter: undefined,
  date_range: []
});
const handleChangefiscalMonth = (e) => {
  emitsValueChange(e);
};
// 日
const handleChangeDay = () => {
  emitsValueChange();
};

// 周
const handleChangeWeek = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束周必须大于开始周',
        type: 'warning'
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束周');
  }
};

// 月
const handleChangeMonth = () => {
  emitsValueChange();
};

// 季
const handleChangeQuarter = () => {
  const start_year = state.start_year;
  const start_quarter = state.start_quarter;
  const end_year = state.end_year;
  const end_quarter = state.end_quarter;

  if (start_year && start_quarter && end_year && end_quarter) {
    if (start_year > end_year) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning'
      });
    } else {
      if (start_year == end_year && start_quarter > end_quarter) {
        ElMessage({
          message: '结束季度必须大于开始季度',
          type: 'warning'
        });
      } else {
        state.date_range = [start_year + '年' + start_quarter + '季度', end_year + '年' + end_quarter + '季度'];
        emitsValueChange();
      }
    }
  } else {
    ElMessage.warning('请选择开始结束季度');
  }
};

// 年
const handleChangeYear = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning'
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束年');
  }
};
// 根据当前选择type获取相应时间
const getDefaultDate = () => {
  // 如果选择的是财务月  调用getCustomMonthRange方法获取财务月时间
  return [dayjs().startOf(unitType.value).format(props.formatter), dayjs().endOf(unitType.value).format(props.formatter)];
};
// 选择器变化
const handleTypeChange = (val) => {
  // 获取当前选择的type类型获取相应的时间 进行存储
  if (val == 'fiscalMonth') {
    fiscalMonth.value = getCustomMonthRange().currentDate;
  } else {
    state.date_range = getDefaultDate();
  }
  // 存储当前选择的type
  selectedUnit.value = val;
  emitsValueChange(dayjs().startOf('month').format('YYYY-MM-DD'));
};

const emitsValueChange = (time) => {
  console.log(time);
  const useRange = state.date_range;
  if (selectedUnit.value == 'fiscalMonth') {
    emits('update:date', getCustomMonthRange(time).customRange);
    emits('update:unit', selectedUnit.value);
    emits('change', useRange);
    return;
  }
  if (selectedUnit.value !== 3) {
    useRange[1] = dayjs(useRange[1]).endOf(unitType.value).format(props.formatter);
  }
  emits('update:date', useRange);
  emits('update:unit', selectedUnit.value);
  emits('change', useRange);
};
// 如果选择的是财务月则返回相应的财务月时间范围
const getCustomMonthRange = (input) => {
  // 情况1：传入的是时间范围对象 {0: startDate, 1: endDate}
  if (input && typeof input === 'object' && input[0] && input[1]) {
    // 计算财务月开始（上个月26号）
    const fiscalStart = dayjs(input[0]).subtract(1, 'month').date(26);

    // 计算财务月结束（本月25号）
    const fiscalEnd = dayjs(input[1]).date(25);

    return {
      currentDate: dayjs(input[0]).format('YYYY-MM-DD'),
      customRange: [
        fiscalStart.format('YYYY-MM-DD'), // 财务月开始
        fiscalEnd.format('YYYY-MM-DD') // 财务月结束
      ]
    };
  }

  // 情况2：传入的是单个日期字符串 "YYYY-MM-DD"
  if (typeof input === 'string' && dayjs(input).isValid()) {
    const date = dayjs(input);
    let fiscalStart, fiscalEnd;

    if (date.date() >= 26) {
      // 如果日期≥26日，则财务月是「本月26日-下月25日」
      fiscalStart = date.date(26);
      fiscalEnd = date.add(1, 'month').date(25);
    } else {
      // 如果日期<26日，则财务月是「上月26日-本月25日」
      fiscalStart = date.subtract(1, 'month').date(26);
      fiscalEnd = date.date(25);
    }

    return {
      currentDate: date.format('YYYY-MM-DD'), // 传入的日期
      customRange: [fiscalStart.format('YYYY-MM-DD'), fiscalEnd.format('YYYY-MM-DD')]
    };
  }

  // 默认情况：没有传值或传值无效，使用当前日期
  const currentDate = dayjs();
  return getCustomMonthRange(currentDate.format('YYYY-MM-DD'));
};
</script>

<style scoped lang="scss">
.time-range-warp {
  display: flex;
}
.search-select {
  margin-right: 10px;
  max-width: 130px !important;
}
.text-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
.select-class {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}
</style>
