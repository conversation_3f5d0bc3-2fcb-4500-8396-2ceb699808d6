<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-29 14:00:59
 * @LastEditors: 达万安 段世煜
 * @Description: 时间组件
 * @FilePath: \vue-admin-template-master\src\components\timeRange.vue
-->
<template>
  <div class="time-range-warp">
    <el-select
      :popper-class="props.popperClass"
      v-model="selectedUnit"
      placeholder="类型"
      class="search-select"
      @change="handleTypeChange"
      :size="props.size"
      style="max-width: 100px"
      :teleported="props.teleported"
      v-if="props.showSelect"
    >
      <template v-for="item in props.typeOptions">
        <el-option
          :key="item.value"
          :label="item.label"
          :value="item[props.typeValue]"
          v-if="props.showType.includes(item.type)"
          :disabled="props.disabled.includes(item.type)"
        />
      </template>
    </el-select>
    <div class="time-range-container">
      <!-- 日 -->
      <template v-if="unitType == 'date'">
        <el-date-picker
          v-model="state.date_range"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          style="width: 100%"
          @change="handleChangeDay"
          :clearable="false"
          :teleported="props.teleported"
          :value-format="props.formatter"
          :popper-class="props.popperClass"
          :size="props.size"
          :disabled-date="props.disabledDate"
        >
        </el-date-picker>
      </template>
      <!-- 周 -->
      <template v-if="unitType == 'week'">
        <el-row>
          <el-col :span="11">
            <el-date-picker
              v-model="state.start_date"
              :teleported="props.teleported"
              type="week"
              :clearable="false"
              format="YYYY[年]ww[周]"
              :value-format="props.formatter"
              placeholder="开始周"
              style="width: 100%"
              @change="handleChangeWeek"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            >
            </el-date-picker>
          </el-col>
          <el-col class="line text-center" :span="2"> 至 </el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="state.end_date"
              type="week"
              :clearable="false"
              :teleported="props.teleported"
              format="YYYY[年]ww[周]"
              :value-format="props.formatter"
              placeholder="结束周"
              style="width: 100%"
              @change="handleChangeWeek"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            >
            </el-date-picker>
          </el-col>
        </el-row>
      </template>
      <!-- 月 -->
      <template v-if="unitType == 'month'">
        <el-date-picker
          type="monthrange"
          :clearable="false"
          v-model="state.date_range"
          @change="handleChangeMonth"
          style="width: 100%"
          format="YYYY[年]MM[月]"
          :teleported="props.teleported"
          :value-format="props.formatter"
          range-separator="至"
          start-placeholder="开始月"
          end-placeholder="结束月"
          :popper-class="props.popperClass"
          :size="props.size"
          :disabled-date="props.disabledDate"
        >
        </el-date-picker>
      </template>
      <!-- 季 -->
      <template v-if="unitType == 'quarter'">
        <el-row :gutter="5">
          <el-col :span="11">
            <el-row :gutter="5">
              <el-col :span="12">
                <el-date-picker
                  type="year"
                  :clearable="false"
                  v-model="state.start_year"
                  @change="handleChangeQuarter"
                  :teleported="props.teleported"
                  format="YYYY年"
                  :value-format="props.formatter"
                  placeholder="开始年"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                  :disabled-date="props.disabledDate"
                ></el-date-picker>
              </el-col>
              <el-col :span="12">
                <el-select
                  placeholder="开始季度"
                  v-model="state.start_quarter"
                  @change="handleChangeQuarter"
                  style="width: 100%"
                  clearable
                  :teleported="props.teleported"
                  :size="props.size"
                >
                  <el-option label="一季度" value="1"></el-option>
                  <el-option label="二季度" value="2"></el-option>
                  <el-option label="三季度" value="3"></el-option>
                  <el-option label="四季度" value="4"></el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="2" align="center" style="padding-top: 5px; font-size: 14px">至</el-col>
          <el-col :span="11">
            <el-row :gutter="5">
              <el-col :span="12">
                <el-date-picker
                  type="year"
                  v-model="state.end_year"
                  format="YYYY年"
                  :value-format="props.formatter"
                  :teleported="props.teleported"
                  @change="handleChangeQuarter"
                  placeholder="结束年"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                  :disabled-date="props.disabledDate"
                ></el-date-picker>
              </el-col>
              <el-col :span="12">
                <el-select
                  placeholder="结束季度"
                  v-model="state.end_quarter"
                  :teleported="props.teleported"
                  @change="handleChangeQuarter"
                  style="width: 100%"
                  :popper-class="props.popperClass"
                  :size="props.size"
                >
                  <el-option label="一季度" value="1"></el-option>
                  <el-option label="二季度" value="2"></el-option>
                  <el-option label="三季度" value="3"></el-option>
                  <el-option label="四季度" value="4"></el-option>
                </el-select>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </template>
      <!-- 年 -->
      <template v-if="unitType == 'year'">
        <el-row>
          <el-col :span="11">
            <el-date-picker
              :clearable="false"
              v-model="state.start_date"
              type="year"
              format="YYYY年"
              :value-format="props.formatter"
              :teleported="props.teleported"
              @change="handleChangeYear"
              placeholder="开始年"
              style="width: 100%"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            ></el-date-picker>
          </el-col>
          <el-col :span="2" class="text-center">至</el-col>
          <el-col :span="11">
            <el-date-picker
              :clearable="false"
              v-model="state.end_date"
              type="year"
              format="YYYY年"
              :value-format="props.formatter"
              :teleported="props.teleported"
              @change="handleChangeYear"
              placeholder="结束年"
              style="width: 100%"
              :popper-class="props.popperClass"
              :size="props.size"
              :disabled-date="props.disabledDate"
            ></el-date-picker>
          </el-col>
        </el-row>
      </template>
    </div>
  </div>
</template>

<script setup>
import { watch, reactive, onMounted, ref, computed } from 'vue';
import { ElMessage, dayjs } from 'element-plus';

const shortcuts = [
  {
    text: '昨天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 1);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];

const props = defineProps({
  // 时间单位
  unit: {
    type: String,
    default: 'date' // year month date quarter week
  },
  // 时间值
  date: {
    type: Array,
    default: () => {
      return [];
    }
  },
  // 禁用时间单位数组
  disabled: {
    type: Array,
    default: () => {
      return [];
    }
  },
  // 时间单位选择器数据
  typeOptions: {
    type: Array,
    default: () => {
      return [
        {
          value: 2,
          label: '按月',
          type: 'month'
        },
        {
          value: 5,
          label: '按周',
          type: 'week'
        },
        {
          value: 3,
          label: '按日',
          type: 'date'
        }
      ];
    }
  },
  // 禁用时间
  disabledDate: {
    type: Function,
    default: (time) => {
      return time.getTime() > Date.now();
    }
  },
  // 组件大小
  size: String,
  // 弹出框自定义类
  popperClass: String,
  // 是否展示时间类型选择器
  showSelect: {
    type: Boolean,
    default: true
  },
  teleported: {
    type: Boolean,
    default: true
  },
  // 时间类型(不展示选择器时使用)
  type: String,
  // 时间格式化
  formatter: {
    type: String,
    default: 'YYYY-MM-DD'
  },
  // 展示哪些选择器
  showType: {
    type: Array,
    default: () => {
      return ['year', 'month', 'date', 'quarter', 'week'];
    }
  },
  // 展示的选择器value使用key
  typeValue: {
    type: String,
    default: 'value'
  }
});

const emits = defineEmits(['update:date', 'update:unit', 'change']);
// 当前选中的时间选择器类型
const unitType = computed(() => {
  if (!props.showSelect) return props.type;
  if (!selectedUnit.value) return false;
  if (props.typeValue === 'type') return selectedUnit.value;
  if (props.typeValue === 'value') return props.typeOptions.find((item) => item.value == selectedUnit.value)?.type;
  return false;
});

watch(
  () => props.date,
  (val) => {
    dealDefault(val);
  }
);

onMounted(() => {
  dealDefault(props.date);
});

const dealDefault = (val) => {
  switch (unitType.value) {
    case 'year':
      state.start_year = val[0];
      state.end_year = val[1];
      break;
    case 'month':
    case 'date':
      state.date_range = val;
      break;
    case 'week':
      state.start_date = val[0];
      state.end_date = val[1];
      break;
    case 'quarter':
      state.start_quarter = val[0];
      state.end_quarter = val[1];
      break;
    default:
      break;
  }
};
watch(
  () => props.unit,
  (val) => {
    selectedUnit.value = val;
  }
);
const selectedUnit = ref(props.unit);
const state = reactive({
  start_date: undefined,
  end_date: undefined,
  start_year: undefined,
  end_year: undefined,
  start_quarter: undefined,
  end_quarter: undefined,
  date_range: []
});

// 日
const handleChangeDay = () => {
  emitsValueChange();
};

// 周
const handleChangeWeek = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束周必须大于开始周',
        type: 'warning'
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束周');
  }
};

// 月
const handleChangeMonth = () => {
  emitsValueChange();
};

// 季
const handleChangeQuarter = () => {
  const start_year = state.start_year;
  const start_quarter = state.start_quarter;
  const end_year = state.end_year;
  const end_quarter = state.end_quarter;

  if (start_year && start_quarter && end_year && end_quarter) {
    if (start_year > end_year) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning'
      });
    } else {
      if (start_year == end_year && start_quarter > end_quarter) {
        ElMessage({
          message: '结束季度必须大于开始季度',
          type: 'warning'
        });
      } else {
        state.date_range = [start_year + '年' + start_quarter + '季度', end_year + '年' + end_quarter + '季度'];
        emitsValueChange();
      }
    }
  } else {
    ElMessage.warning('请选择开始结束季度');
  }
};

// 年
const handleChangeYear = () => {
  const start_date = state.start_date;
  const end_date = state.end_date;

  if (start_date && end_date) {
    if (start_date > end_date) {
      ElMessage({
        message: '结束年必须大于开始年',
        type: 'warning'
      });
    } else {
      state.date_range = [start_date, end_date];
      emitsValueChange();
    }
  } else {
    ElMessage.warning('请选择开始结束年');
  }
};

const getDefaultDate = () => {
  return [dayjs().startOf(unitType.value).format(props.formatter), dayjs().endOf(unitType.value).format(props.formatter)];
};
const handleTypeChange = (val) => {
  console.log('handleTypeChange', val);
  state.date_range = getDefaultDate();
  selectedUnit.value = val;
  emitsValueChange();
};

const emitsValueChange = () => {
  const useRange = state.date_range;
  if (selectedUnit.value !== 3) {
    useRange[1] = dayjs(useRange[1]).endOf(unitType.value).format(props.formatter);
  }
  console.log('date_range', state.date_range);
  emits('update:date', useRange);
  emits('update:unit', selectedUnit.value);
  emits('change', useRange);
};
</script>

<style scoped lang="scss">
.time-range-warp {
  display: flex;
}
.search-select {
  margin-right: 10px;
}
.text-center {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
