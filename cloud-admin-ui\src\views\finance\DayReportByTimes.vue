<template>
  <div class="container">
    <day-report-by-times-search ref="searchRef" @form-search="searchDayReportByTimesList" />
    <day-report-by-time-search-btn-groups @search="searchDayReportByTimeFromGroups" ref="search_btn" />
    <day-report-by-times-table ref="table" />
  </div>
</template>

<script name="DayReportByTimes" setup>
import DayReportByTimesSearch from './dayReport/DayReportByTimesSearch.vue';
import DayReportByTimeSearchBtnGroups from './dayReport/DayReportByTimeSearchBtnGroups.vue';
import DayReportByTimesTable from './dayReport/DayReportByTimesTable.vue';
import { ref, reactive, defineExpose } from 'vue';

const table = ref(null);
const searchRef = ref(null);
const search_btn = ref(null);

const state = reactive({
  params: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});

const runSearchRef = () => {
  searchRef.value.handleDataSearch();
};
defineExpose({
  runSearchRef
});
const searchDayReportByTimesList = (params) => {
  state.params = params;

  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findDayReportByTime(params);
  table.value.getList(params);
};

const searchDayReportByTimeFromGroups = (queryParams) => {
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findDayReportByTime(queryParams);
  table.value.getList(queryParams);
};
</script>
