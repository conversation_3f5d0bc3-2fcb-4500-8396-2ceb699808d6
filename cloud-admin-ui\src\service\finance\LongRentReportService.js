import * as longRentReportApi from '@/api/finance/LongRentReportApi';

/**
 * 长租报表
 */
export default {
  /**
   * 分页查询日报表通过金额
   */
  pagingLongRentReport(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentReportApi.pagingLongRentReport(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentReportApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 卡片数据
   */
  getMoney(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentReportApi.getMoney(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
