<!-- 汇总 -->
<template>
  <div class="summary">
    <div class="title">{{ title }}</div>
    <el-table :data="tableData" v-loading="loading" border style="width: 100%">
      <el-table-column v-for="item in tableColumn" align="center" :key="item.prop" :prop="item.prop" :label="item.label" :width="item.width" />
    </el-table>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '汇总'
  },
  tableData: {
    type: Array,
    default: []
  },
  loading: {
    type: Boolean,
    default: false
  }
});

//收入汇总
const incomeArr = [
  {
    prop: 'str_day',
    label: '日期/类别',
    width: ''
  },
  {
    prop: 'park_name',
    label: '停车场名称',
    width: ''
  },
  {
    prop: 'total_amount_receivable',
    label: '应收总金额（元）',
    width: ''
  },
  {
    prop: 'total_received_amount',
    label: '实收总金额（元）',
    width: ''
  },
  {
    prop: 'transactions_number',
    label: '交易成功笔数',
    width: ''
  }
];

//退款汇总
const refundArr = [
  {
    prop: 'str_day',
    label: '日期/类别',
    width: ''
  },
  {
    prop: 'park_name',
    label: '停车场名称',
    width: ''
  },
  {
    prop: 'total_refund_amount',
    label: '退款总金额（元）',
    width: ''
  },
  {
    prop: 'refund_number',
    label: '退款总笔数',
    width: ''
  },
  {
    prop: 'total_refuse_amount',
    label: '据付总金额（元）',
    width: ''
  },
  {
    prop: 'refuse_number',
    label: '拒付总笔数',
    width: ''
  }
];

//优免汇总
const exemptArr = [
  {
    prop: 'str_day',
    label: '日期/类别',
    width: ''
  },
  {
    prop: 'park_name',
    label: '停车场名称',
    width: ''
  },
  {
    prop: 'use_amount',
    label: '优免使用总金额（元）',
    width: ''
  },
  {
    prop: 'use_number',
    label: '优免使用总笔数',
    width: ''
  },
  {
    prop: 'total_preferential_refund_amount',
    label: '优免退款总金额（元）',
    width: ''
  },
  {
    prop: 'preferential_refund_number',
    label: '优免退款总笔数',
    width: ''
  }
];

//手续费汇总
const commissionArr = [
  {
    prop: 'str_day',
    label: '日期/类别',
    width: ''
  },
  {
    prop: 'park_name',
    label: '停车场名称',
    width: ''
  },
  {
    prop: 'rate',
    label: '费率',
    width: ''
  },
  {
    prop: 'commission_payable',
    label: '应付手续费',
    width: ''
  },
  {
    prop: 'refund_commission',
    label: '退款退回手续费',
    width: ''
  }
];

const tableColumn = computed(() => {
  let arr = [];
  if (props.title == '收入汇总') arr = incomeArr;
  if (props.title == '退款汇总') arr = refundArr;
  if (props.title == '优免汇总') arr = exemptArr;
  if (props.title == '手续费汇总') arr = commissionArr;
  return arr;
});
</script>
<style lang="scss" scoped>
.summary {
  padding-bottom: 20px;
}
</style>
