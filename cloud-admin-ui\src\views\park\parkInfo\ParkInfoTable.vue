<template>
  <el-card shadow="never" style="margin-top: 10px">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加停车场</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="220">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row.id)"> 编辑 </el-button>
            <el-button link v-if="scope.row.state == 0" type="success" @click="enable(scope.row)"> 启用 </el-button>
            <el-button link v-if="scope.row.state == 1" type="danger" @click="disable(scope.row)"> 禁用 </el-button>
            <el-button link type="primary" @click="handleManager(scope.row.id)"> 管理 </el-button>
            <el-button link type="primary" @click="handleTerminal(scope.row.id)"> 终端管理 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="停车场编号" align="center" min-width="120" />
        <el-table-column prop="name" label="停车场名称" align="center" min-width="250">
          <template v-slot="scope">
            <el-link type="primary" @click="handleDetail(scope.row.id)">{{ scope.row.name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="state_desc" label="停车场状态" align="center" min-width="100" />
        <el-table-column label="省市区" align="center" min-width="235">
          <template v-slot="scope">
            <span>{{ scope.row.province_name + '-' + scope.row.city_name + '-' + scope.row.district_name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="address" label="停车场地址" align="center" min-width="250" />
        <el-table-column prop="total_spaces" label="总车位数" align="center" min-width="100" />
        <el-table-column prop="type_desc" label="停车场类型" align="center" min-width="120" />
        <el-table-column prop="property_name" label="产权方" align="center" min-width="120" />
        <el-table-column prop="org_department_name" label="运营方" align="center" min-width="120" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkInfoTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parkInfoService from '@/service/park/ParkInfoService';
import { activeRouteTab } from '@/utils/tabKit';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
onActivated(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkInfoService.pagingParks(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  activeRouteTab({
    path: '/park/parkInfo/parkInfoAdd'
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleEdit = (id) => {
  activeRouteTab({
    path: '/park/parkInfo/parkInfoEdit',
    query: {
      parkInfoId: id
    }
  });
};
const handleDetail = (id) => {
  activeRouteTab({
    path: '/park/parkInfo/parkInfoDetail',
    query: {
      parkInfoId: id
    }
  });
};

//启用
const enable = (row) => {
  ElMessageBox.confirm('是否要启用该车场？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkInfoService.enablePark(row.id).then((response) => {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};
// 禁用
const disable = (row) => {
  ElMessageBox.confirm('是否要禁用该车场？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkInfoService.disablePark(row.id).then((response) => {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};
const handleManager = (parkId) => {
  activeRouteTab({
    path: '/park/parkAdmin',
    query: {
      parkId: parkId
    }
  });
};

const handleTerminal = (parkId) => {
  activeRouteTab({
    path: '/park/sentryTerminal',
    query: {
      park_id: parkId
    }
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
