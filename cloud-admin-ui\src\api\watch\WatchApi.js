/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 开始云端值守
export const beginWatch = (data) => {
  return $({
    url: '/console/watch/beginWatch',
    method: 'post',
    data
  });
};

// 停止云端值守
export const endWatch = (data) => {
  return $({
    url: '/console/watch/endWatch',
    method: 'post',
    data
  });
};
// 修改车牌号
export const updateCarInPlateNo = (data) => {
  return $({
    url: '/console/park/carin/updateCarInPlateNo',
    method: 'post',
    data
  });
};
// 查询通道中的车辆
export const queryCarInGateway = (data) => {
  return $({
    url: '/console/park/gateway/queryCarInGateway',
    method: 'post',
    data
  });
};
// 获取抓拍的快照
export const getCarSnapshot = (data) => {
  return $({
    url: '/console/park/gateway/getCarSnapshot',
    method: 'post',
    data
  });
};
// 触发抓拍事件
export const triggerCamera = (data) => {
  return $({
    url: '/console/watch/triggerCamera',
    method: 'post',
    data
  });
};
// 抬杆请求
export const pushOpenStrobe = (data) => {
  return $({
    url: '/console/watch/pushOpenStrobe',
    method: 'post',
    data
  });
};
// 分页查询停车缴费
export const pagingParkFee = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/pagingParkPayRecords',
    method: 'post',
    data
  });
};
// 远程放闸
export const pushCloseStrobe = (data) => {
  return $({
    url: '/console/watch/pushCloseStrobe',
    method: 'post',
    data
  });
};
