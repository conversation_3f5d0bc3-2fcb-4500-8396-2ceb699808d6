<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="parkCarDailyPaymentService.exportData"
          :rules="[{ name: 'park_id', required: false, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" min-width="180" />
        <el-table-column label="省市区" align="center" min-width="80">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="total_spaces" label="车位总数" align="center" min-width="180" />
        <el-table-column prop="parking_payed_number" label="付费临停出场车次" align="center" min-width="180" />
        <el-table-column prop="parking_payed_money" label="临停车位总收入（元）" align="center" min-width="180" />
        <el-table-column prop="parking_space_average_income_proportion" label="电子支付总收入（元）" align="center" min-width="180" />
        <el-table-column prop="parking_third_party_income" label="第三方会员总收入（元）" align="center" min-width="180" />
        <el-table-column prop="daily_average_income" label="临停车位日均收入" align="center" min-width="180" />
        <el-table-column prop="licensed_car_income" label="临停小于24小时【有牌车】总收入" align="center" min-width="180" />
        <el-table-column prop="licensed_car_average_income" label="临停小于24小时【有牌车】车位日均收入" align="center" min-width="180" />
        <el-table-column prop="unlicensed_car_income" label="临停小于24小时【无牌车】总收入" align="center" min-width="180" />
        <el-table-column prop="unlicensed_car_average_income" label="临停小于24小时【无牌车】车位日均收入" align="center" min-width="180" />
        <el-table-column prop="assessment_licensed_car_income" label="临停小于24小时【大于30分钟考核有牌车】收入" align="center" min-width="180" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkCarDailyPaymentTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkCarDailyPaymentService from '@/service/statisticalReport/ParkCarDailyPaymentService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  parkCarDailyPaymentService.pagingParkCarDailyPayment(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
