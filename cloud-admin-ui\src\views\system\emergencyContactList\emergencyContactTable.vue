<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="batchDelete()">批量删除</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange"
        style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="所属车场" align="center" min-width="100" />
        <el-table-column prop="duty_phone" label="值班室电话" align="center" min-width="180" />
        <el-table-column prop="contact_name1" label="应急联系人1" align="center" min-width="120" />
        <el-table-column prop="contact_mobile1" label="手机号" align="center" min-width="100" />
        <el-table-column prop="contact_name2" label="应急联系人2" align="center" min-width="120" />
        <el-table-column prop="contact_mobile2" label="手机号" align="center" min-width="100" />
        <el-table-column prop="contact_name3" label="应急联系人3" align="center" min-width="120" />
        <el-table-column prop="contact_mobile3" label="手机号" align="center" min-width="100" />
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      <el-dialog title="免费车申请" v-model="createDialogVisible" :close-on-click-modal="false" destroy-on-close
        @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="140px" :rules="data.rules" :model="data.form">
          <el-form-item prop="park_name" label="选择车场">
            <el-input v-model="data.form.park_name" readonly="true" @click="authCharge(true, 'add')"
              placeholder="选择车场" />
          </el-form-item>
          <el-form-item prop="space_id" label="车位编号">
            <el-select v-model="data.form.space_id" style="width: 100%" placeholder="车位编号" @change="getCode">
              <el-option v-for="item in spaceCodeList" :key="item.id" :label="item.code" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="name" label="车主姓名"> <el-input v-model="data.form.name" placeholder="车主姓名" />
          </el-form-item>
          <el-form-item prop="plate_no" label="车牌号"> <el-input v-model="data.form.plate_no" placeholder="车牌号" />
          </el-form-item>
          <el-form-item prop="business_format" label="业态属性">
            <el-select v-model="data.form.business_format" style="width: 100%" placeholder="请选择">
              <el-option v-for="item in businessFormatList" :key="item.key" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="mobile" label="手机号"> <el-input v-model="data.form.mobile" placeholder="手机号" />
          </el-form-item>
          <el-form-item prop="validity_date" label="有效期">
            <el-date-picker v-model="data.form.validity_date" type="daterange" range-separator="~"
              start-placeholder="开始日期" end-placeholder="结束日期" :size="size" format="YYYY-MM-DD"
              value-format="YYYY-MM-DD" />
          </el-form-item>
          <el-form-item prop="audit_data" label="审核资料">
            <el-upload ref="uploadRef" accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG" v-model:file-list="fileList"
              :on-preview="onPreview" list-type="picture-card" :show-file-list="false" :limit="1" :action="uploadUrl"
              :headers="headers" :before-upload="beforeUpload" :on-success="onSuccessUpload" :on-exceed="handleExceed">
              <div v-if="!fileList.length" class="upload-card">
                <el-icon>
                  <Plus />
                </el-icon>
              </div>
              <div v-else class="upload-img">
                <img :src="fileList?.[0].url" :alt="fileList?.[0].name" />
                <div class="upload-operations">
                  <span @click.stop="onPreview(fileList?.[0])">
                    <el-icon><zoom-in /></el-icon>
                  </span>
                  <span @click.stop="handleRemove(fileList?.[0], fileList)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </span>
                  <span>
                    <el-icon>
                      <Upload />
                    </el-icon>
                  </span>
                </div>
              </div>
            </el-upload>
          </el-form-item>
          <el-form-item prop="is_remind" label="车辆入场短信提醒">
            <el-switch v-model="data.form.is_remind" active-text="开启" inactive-text="关闭" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="createFreeCar(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改" v-model="updateDialogVisible" :close-on-click-modal="false" destroy-on-close
        @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="140px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="park_name" label="所属车场">
            <el-input v-model="data.updateForm.park_name" readonly="true" @click="authCharge(true, 'edit')"
              placeholder="所属车场" disabled />
          </el-form-item>
          <el-form-item prop="duty_phone" label="值班室电话">
            <el-input v-model="data.updateForm.duty_phone" readonly="true" @click="authCharge(true, 'edit')"
              placeholder="所属车场" disabled />
          </el-form-item>
          <el-form-item prop="contact_name1" label="应急联系人1"> <el-input v-model="data.updateForm.contact_name1"
              placeholder="车主姓名" />
          </el-form-item>
          <el-form-item prop="contact_mobile1" label="手机号"> <el-input v-model="data.updateForm.contact_mobile1"
              placeholder="车牌号" />
          </el-form-item>
          <el-form-item prop="contact_name2" label="应急联系人2"> <el-input v-model="data.updateForm.contact_name2"
              placeholder="车主姓名" />
          </el-form-item>
          <el-form-item prop="contact_mobile2" label="手机号"> <el-input v-model="data.updateForm.contact_mobile2"
              placeholder="车牌号" />
          </el-form-item>
          <el-form-item prop="contact_name3" label="应急联系人3"> <el-input v-model="data.updateForm.contact_name3"
              placeholder="车主姓名" />
          </el-form-item>
          <el-form-item prop="contact_mobile3" label="手机号"> <el-input v-model="data.updateForm.contact_mobile3"
              placeholder="车牌号" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="updateFreeCar(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 关联车场 -->
      <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible"
        :before-close="handleClose">
        <car-free-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false, '')" :mode="flag"
          @renderTableInput="renderTableInput" />
      </el-dialog>
      <el-dialog v-model="dialogVisible" :title="title" width="40%">
        <img w-full style="max-width: 100%; height: auto; margin: auto; display: block" :src="dialogImageUrl"
          alt="Preview Image" />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CarFreeTable" setup>
import carFreeService from '@/service/car/CarFreeService';
import commonService from '@/service/common/CommonService';
import UnattendedApi from '@/service/system/Unattended';
import { useUser } from '@/stores/user';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';
import CarFreeFindBack from './CarFreeFindBack.vue';
const user = useUser();
// import { useUser } from '@/stores/user';

// const user = useUser();



const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const spaceCodeList = ref([]);
const businessFormatList = ref([]);
commonService.findEnums('park', [{ enum_key: 'businessFormatType', enum_value: 'EnumBusinessFormatType' }]).then((response) => {
  businessFormatList.value = response.data.businessFormatType;
});
const ids = ref([]);
const addForm = ref();
const editForm = ref();
const total = ref(0);
const size = ref(0);
const relatedParkDialogVisible = ref(false);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const carFreeIds = ref([]);
const fileList = ref([]);
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const onPreview = (file) => {
  const fileUrl = file.url;
  const fileName = file.name;
  const fileExtension = fileName.split('.').pop().toLowerCase();
  if (fileExtension === 'pdf') {
    // 如果是 PDF 文件，则在新标签页中打开 URL 地址
    window.open(fileUrl, '_blank');
  } else {
    dialogVisible.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = file.response?.data.audit_data_url || file.url;
  }
};
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  uploadExcelFile: {
    space_upload_url: undefined
  },
  form: {
    park_id: '',
    park_name: '',
    space_id: undefined,
    space_code: undefined,
    plate_no: undefined,
    business_format: undefined,
    name: undefined,
    mobile: '',
    is_remind: '',
    validity_date: [],
    effective_start_time: undefined,
    effective_end_time: undefined,
    audit_data: undefined,
    audit_data_name: undefined
  },
  updateForm: {},
  reviewForm: {
    ids: []
  },
  deleteForm: {
    ids: []
  },
  cancelReviewForm: {
    id: undefined
  },
  rules: {
  }
});
onMounted(() => {
  getList(data.queryParams);
  status.value = true;
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  UnattendedApi.emergencylist(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSelectionChange = (val) => {
  carFreeIds.value = val;
};

// 选择车场
const authCharge = (visible, mode) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
    // 清空车位编号，需重新选择
    data.form.space_id = '';
    carFreeService.listParkSpace(data.form.park_id).then((response) => {
      if (response.success === true) {
        spaceCodeList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
    // 清空车位编号，需重新选择
    data.updateForm.space_id = '';
    carFreeService.listParkSpace(data.updateForm.park_id).then((response) => {
      if (response.success === true) {
        spaceCodeList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  }
};
watch(spaceCodeList, () => {
  if (spaceCodeList.value?.length > 0) {
    data.form.space_id = spaceCodeList.value[0].id;
    data.form.space_code = spaceCodeList.value[0].code;
  }
});
const createFreeCar = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.validity_date.length > 0) {
      data.form.effective_start_time = data.form.validity_date[0];
      data.form.effective_end_time = data.form.validity_date[1];
    }
    let params = JSON.parse(JSON.stringify(data.form));
    params.is_remind = params.is_remind ? '1' : '0';
    carFreeService
      .createWhiteList(params)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '创建成功',
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch((err) => {
        ElMessage({
          message: err.response.detail_message != '' ? err.response.detail_message : err.response.message,
          type: 'error'
        });
        getList(data.queryParams);
      });
  });
};

//批量删除
const batchDelete = () => {
  if (carFreeIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的数据',
      type: 'warning'
    });
    return false;
  }
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const pushIds = [];
    for (let i = 0; i < carFreeIds.value.length; i++) {
      pushIds.push(carFreeIds.value[i].id);
    }
    data.deleteForm.ids = pushIds;
    UnattendedApi
      .emergencydelete(data.deleteForm).then((response) => {
        if (response.success) {
          ElMessage({
            message: '删除成功',
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
  });
};

// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 编辑
const handleEdit = (row) => {
  console.log('handleEdit', row);
  data.updateForm = row;
  // fileList.value = [
  //   {
  //     name: row.audit_data_name,
  //     url: row.audit_data_url
  //   }
  // ];
  // data.updateForm.validity_date = [row.effective_start_time, row.effective_end_time];
  // carFreeService.listParkSpace(data.updateForm.park_id).then((response) => {
  //   if (response.success === true) {
  //     spaceCodeList.value = response.data;
  //   } else {
  //     ElMessage({
  //       message: response.detail_message != '' ? response.detail_message : response.message,
  //       type: 'error'
  //     });
  //   }
  // });
  updateDialogVisible.value = true;
};
const updateFreeCar = (editForm) => {
  editForm.validate().then(() => {
    let params = JSON.parse(JSON.stringify(data.updateForm));
    UnattendedApi
      .emergencyupdate(params)
      .then((response) => {
        if (response.success) {
          ElMessage({
            message: '修改成功',
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch((err) => {
        ElMessage({
          message: err.response.detail_message != '' ? err.response.detail_message : err.response.message,
          type: 'error'
        });
        getList(data.queryParams);
      });
  });
};
const getCode = (val) => {
  spaceCodeList.value.find((ele) => {
    if (val === ele.id) {
      data.form.space_code = ele.code;
    }
  });
};
// 删除
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    UnattendedApi
      .emergencydelete({ ids: [id] })
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 80px;
  // width: 148px;
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;

  .upload-state {
    font-size: 12px;
    text-align: center;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-operations {
    display: none;
    width: 100%;
    height: 100%;
    font-size: 20px;
    background-color: var(--el-overlay-color-lighter);
    color: #fff;
    cursor: default;
    justify-content: center;
    align-items: center;
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
  }

  .upload-operations span {
    margin: 0 7px;
    cursor: pointer;
  }
}

:deep(.el-upload--picture-card) {
  position: relative;
}

:deep(.el-upload--picture-card:hover .upload-operations) {
  display: inline-flex;
  opacity: 1;
  transition: opacity var(--el-transition-duration);
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin: 0 20px 20px 0;
}
</style>
