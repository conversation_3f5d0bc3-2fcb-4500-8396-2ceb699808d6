<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="金额" name="monthReportByMoney">
      <month-report-by-money ref="byMoney" />
    </el-tab-pane>
    <el-tab-pane label="按次" name="monthReportByTimes">
      <month-report-by-times ref="byTimes" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="MonthReport" setup>
import monthReportByMoney from './MonthReportByMoney.vue';
import monthReportByTimes from './MonthReportByTimes.vue';
import { ref, reactive, onMounted } from 'vue';

const activeName = ref('monthReportByMoney');
const byMoney = ref(null);
const byTimes = ref(null);

// onMounted(() => {
//   byMoney.value.searchList();
// });

const handleClick = (tab) => {
  // if (tab.props.name === 'monthReportByMoney') {
  //   byMoney.value.searchList();
  // }
  // if (tab.props.name === 'monthReportByTimes') {
  //   byTimes.value.searchList();
  // }
};
</script>
