<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="车场名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.code" placeholder="车场编号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.property_id" placeholder="产权方" clearable>
        <el-option v-for="item in propertyOwnerList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.org_department_name" placeholder="运营方" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="车场状态" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="ParkInfoSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import propertyOwnerService from '@/service/system/PropertyOwnerService';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: '', //车场名称
    code: '', //车场编码
    org_department_name: undefined, //运营方名称
    property_id: '', //产权方id
    states: [], //车场状态
    page: 1,
    limit: 30
  }
});
const states = ref([]);
const propertyOwnerList = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumParkState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });
  //产权方列表
  propertyOwnerService.listPropertyOwner().then((response) => {
    propertyOwnerList.value = response;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '', //车场名称
    code: '', //车场编码
    org_department_id: undefined, //运营方ID
    property_name: '', //产权方名称
    states: [], //车场状态
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
