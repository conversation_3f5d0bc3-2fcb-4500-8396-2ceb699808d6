import { useMenu } from '@/stores/menu';
import { routePush } from './router';
import { useTabs } from '@/stores/tabs';

/**
 * 激活路由页签
 * @param {*} path
 */
export const activeRouteTab = (obj) => {
  console.log('activeRouteTab', obj);
  const menu = useMenu();
  const tabs = useTabs();

  const _obj = tabs.checkAndPushTab(obj);
  if (_obj.query && Object.keys(_obj.query).length !== 0) {
    obj.query = _obj.query;
  }

  let page = {};
  if (obj.path === '/') {
    page = menu.state.pageList[0];
    obj = page;
  } else {
    page = menu.state.pageList.filter((item) => item.path === obj.path)[0];
  }
  if (page && routePush(obj)) {
    if (!tabs.state.tabList.some((item) => item.path === page.path)) {
      // 添加 frame-tab 临时去除
      tabs.state.tabList.push(page);
      tabs.state.activedTabIndex = tabs.state.tabList.length - 1;
      menu.state.activedMenuIndex = page.id;
      menu.state.activedMenuPath = page.id + '_' + page.path;
    } else {
      const index = tabs.state.tabList.findIndex((item) => item.path === page.path);
      tabs.state.activedTabIndex = index;
      menu.state.activedMenuIndex = page.id;
      menu.state.activedMenuPath = page.id + '_' + page.path;
    }
  } else {
    routePush({ path: '/404' });
  }
};

export const closeCurrentTab = (obj) => {
  const tabs = useTabs();

  if (tabs.state.tabList.length >= 1) {
    // 当前选中 tab 被删除
    tabs.state.tabList.splice(tabs.state.activedTabIndex, 1);

    const menu = useMenu();
    const page = menu.state.pageList.filter((item) => item.path === obj.path)[0];
    if (page && routePush(obj)) {
      // 添加 frame-tab
      const tabs = useTabs();

      if (!tabs.state.tabList.some((item) => item.path === page.path)) {
        tabs.state.tabList.push(page);
        tabs.state.activedTabIndex = tabs.state.tabList.length - 1;
        menu.state.activedMenuIndex = page.id;
        menu.state.activedMenuPath =  page.id+ '_' + page.path;
      } else {
        const index = tabs.state.tabList.findIndex((item) => item.path === page.path);
        tabs.state.activedTabIndex = index;
        menu.state.activedMenuIndex = page.id;
        menu.state.activedMenuPath =  page.id+ '_' + page.path;
      }
    }
  }
};
