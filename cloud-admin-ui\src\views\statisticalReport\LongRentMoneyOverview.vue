<template>
  <div class="container">
    <long-rent-money-overview-search @form-search="searchLongRentList" @reset="resetParamsAndData" />
    <long-rent-money-overview-table ref="table" />
  </div>
</template>

<script setup name="LongRentMoneyOverview">
import LongRentMoneyOverviewSearch from './longRentMoneyOverview/LongRentMoneyOverviewSearch.vue';
import LongRentMoneyOverviewTable from './longRentMoneyOverview/LongRentMoneyOverviewTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageLongRent = (queryParams) => {
  table.value.getList(queryParams);
};

const searchLongRentList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageLongRent
});
</script>
