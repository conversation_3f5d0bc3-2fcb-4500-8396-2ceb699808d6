import * as spaceRentBindPark from '@/api/car/SpaceRentBindParkApi';

/**
 * 长租车申请-关联车场
 */
export default {
  /**
   * 分页查询
   */
  pagingRentSpaceBindParks(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentBindPark.pagingRentSpaceBindParks(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 保存长租车关联车场
   */
  saveRentSpaceBindPark(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentBindPark.saveRentSpaceBindPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获得车场列表 --去除当前车场
   */
  listParkNotCurrentPark(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentBindPark.listParkNotCurrentPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 长租申请已关联的车场
   * @param {*} data
   * @returns
   */
  ListRentSpaceBindPark(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentBindPark.ListRentSpaceBindPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
