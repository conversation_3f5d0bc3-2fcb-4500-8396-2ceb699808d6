<template>
  <el-card class="summarize-table" shadow="never">
    <span class="title">停车场临停汇总（ETC数据）</span>
    <el-button class="print" type="primary" @click="handlePrint">打 印</el-button>
    <div>
      <el-table ref="table" id="table" :data="tableData" v-loading="tableLoading" border @row-click="handleRowClick">
        <el-table-column prop="stl_dat" label="类别" align="center" width="120">
          <template v-slot:header>
            <div class="cell-with-diagonal">
              <span class="category"> 类别 </span>
              <span class="diagonal-line"></span>
              <span class="dateColumn"> 日期 </span>
            </div>
            <!-- 在这里插入自定义内容 -->
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="need_total_money" label="应收金额(元)" align="center" />
        <el-table-column prop="etc_tolal_cnt" label="ETC交易成功笔数" align="center" />
        <el-table-column prop="etc_tolal_money" label="ETC交易成功(元)" align="center" />
        <el-table-column prop="coupon_tolal_money" label="优免券抵扣总额(元)" align="center" />
        <el-table-column prop="coupon_tolal_cnt" label="优免券使用数量" align="center" />
        <el-table-column label="操作" fixed="right" align="center">
          <template #default="scope">
            <el-button
              v-if="scope.row.stl_dat !== '月合计'"
              size="small"
              type="primary"
              link
              @click="handleExport(scope.row)"
              :loading="scope.row.loading"
              >导出明细</el-button
            >
            <span v-else>-</span>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="ETCReconcileSummarizeTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import printJS from 'print-js';
import etcReconcileApi from '@/service/finance/ETCReconcileService';
import commonService from '@/service/common/CommonService';
import { saveToFile } from '@/utils/utils.js';

const emits = defineEmits(['update-records', 'update-records-loading']);
const table = ref(null);
const tableData = ref([]);
const totalInfo = reactive({
  start_time: undefined, // 统计开始时间
  end_time: undefined, // 统计结束时间
  need_total_money_sum: undefined, // 应收金额合计
  etc_tolal_cnt_sum: undefined, // ETC交易成功笔数合计
  etc_tolal_money_sum: undefined, // ETC交易成功金额合计
  coupon_tolal_money_sum: undefined, // 优免券抵扣总额合计
  coupon_tolal_cnt_sum: undefined, // 优免券使用数量合计
  etc_need_total_money_sum: undefined, // 对账-应收总金额合计
  etc_real_total_money_sum: undefined, // 对账-实收总金额合计
  etc_total_cnt_sum: undefined, // 对账-交易成功笔数合计
  etc_refund_total_cnt_sum: undefined, // 对账-退款总比数合计
  etc_refund_total_money_sum: undefined, // 对账-退款总金额合计
  etc_jf_refund_total_cnt_sum: undefined, // 对账-拒付总比数合计
  etc_jf_refund_total_money_sum: undefined // 对账-拒付总金额合计
});
const tableLoading = ref(false);
const data = reactive({
  queryParams: {}
});

// 设置表格加载状态
const setTableLoading = (loading) => {
  tableLoading.value = loading;
};

// 设置合计数据
const setTotalInfo = (result) => {
  const {
    start_time,
    end_time,
    need_total_money_sum,
    etc_tolal_cnt_sum,
    etc_tolal_money_sum,
    coupon_tolal_money_sum,
    coupon_tolal_cnt_sum,
    etc_need_total_money_sum,
    etc_real_total_money_sum,
    etc_total_cnt_sum,
    etc_refund_total_cnt_sum,
    etc_refund_total_money_sum,
    etc_jf_refund_total_cnt_sum,
    etc_jf_refund_total_money_sum
  } = result;
  totalInfo.start_time = start_time;
  totalInfo.end_time = end_time;
  totalInfo.need_total_money_sum = need_total_money_sum;
  totalInfo.etc_tolal_cnt_sum = etc_tolal_cnt_sum;
  totalInfo.etc_tolal_money_sum = etc_tolal_money_sum;
  totalInfo.coupon_tolal_money_sum = coupon_tolal_money_sum;
  totalInfo.coupon_tolal_cnt_sum = coupon_tolal_cnt_sum;
  totalInfo.etc_need_total_money_sum = etc_need_total_money_sum;
  totalInfo.etc_real_total_money_sum = etc_real_total_money_sum;
  totalInfo.etc_total_cnt_sum = etc_total_cnt_sum;
  totalInfo.etc_refund_total_cnt_sum = etc_refund_total_cnt_sum;
  totalInfo.etc_refund_total_money_sum = etc_refund_total_money_sum;
  totalInfo.etc_jf_refund_total_cnt_sum = etc_jf_refund_total_cnt_sum;
  totalInfo.etc_jf_refund_total_money_sum = etc_jf_refund_total_money_sum;
};

// 设置汇总数据
const setTableData = (list) => {
  if (list?.length) {
    tableData.value = [
      {
        stl_dat: '月合计',
        park_name: '-',
        need_total_money: totalInfo.need_total_money_sum,
        etc_tolal_cnt: totalInfo.etc_tolal_cnt_sum,
        etc_tolal_money: totalInfo.etc_tolal_money_sum,
        coupon_tolal_money: totalInfo.coupon_tolal_money_sum,
        coupon_tolal_cnt: totalInfo.coupon_tolal_cnt_sum
      },
      ...list
    ];
  } else {
    tableData.value = [];
  }
  if (tableData.value?.length) {
    setRecordsTable();
  }
};

// 设置对账数据
const setRecordsTable = (originData) => {
  const transactionTableData = [
    {
      stl_dat: '月合计',
      park_name: '-',
      etc_need_total_money: totalInfo.etc_need_total_money_sum,
      etc_real_total_money: totalInfo.etc_real_total_money_sum,
      etc_total_cnt: totalInfo.etc_total_cnt_sum
    }
  ];
  const refundTableData = [
    {
      stl_dat: '月合计',
      park_name: '-',
      etc_refund_total_cnt: totalInfo.etc_refund_total_cnt_sum,
      etc_refund_total_money: totalInfo.etc_refund_total_money_sum,
      etc_jf_refund_total_cnt: totalInfo.etc_jf_refund_total_cnt_sum,
      etc_jf_refund_total_money: totalInfo.etc_jf_refund_total_money_sum
    }
  ];
  if (originData?.trades_info) {
    transactionTableData.push(originData?.trades_info);
  }
  if (originData?.refund_info) {
    refundTableData.push(originData?.refund_info);
  }
  emits('update-records', {
    transactionTableData,
    refundTableData
  });
};

// 设置对账列表loading
const setRecordsLoading = (loading) => {
  emits('update-records-loading', loading);
};

// 获取汇总数据
const getList = (params) => {
  setTableLoading(true);
  data.queryParams = params;
  etcReconcileApi.parkTempTotalByDay(params).then((response) => {
    if (response.success === true) {
      setTotalInfo(response.data);
      setTableData(response.data.day_infos ?? []);

      setTableLoading(false);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      setTableLoading(false);
    }
  });
};

// 打印
const handlePrint = async () => {
  printJS({
    printable: JSON.parse(JSON.stringify(tableData.value)),

    properties: [
      { field: 'stl_dat', displayName: '日期' },
      { field: 'park_name', displayName: '停车场名称' },
      { field: 'need_total_money', displayName: '应收金额(元)' },
      { field: 'etc_tolal_cnt', displayName: 'ETC交易成功笔数' },
      { field: 'etc_tolal_money', displayName: 'ETC交易成功(元)' },
      { field: 'coupon_tolal_money', displayName: '优免券抵扣总额(元)' },
      { field: 'coupon_tolal_cnt', displayName: '优免券使用数量' }
    ],
    type: 'json',
    header: '停车场临停汇总',
    gridHeaderStyle: ';padding: 10px 16px;font-size: 14px;font-weight: 400;border: 0.5px solid #333333;',
    gridStyle: 'padding: 10px 16px;border: 0.5px solid #333333;text-align: center;'
  });
};

// 导出明细
const handleExport = (record) => {
  record.loading = true;
  etcReconcileApi
    .exportAccFile({
      park_id: record.park_id,
      stl_date: record.stl_dat
    })
    .then((response) => {
      if (response.success == true) {
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            record.loading = false;
          })
          .catch(() => {
            record.loading = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        record.loading = false;
      }
    })
    .catch(() => {
      record.loading = false;
    });
};

// 查询明细
const handleRowClick = (record) => {
  if (record.stl_dat === '月合计') {
    return;
  }
  setRecordsLoading(true);
  etcReconcileApi
    .getEtcAccInfos({
      park_id: record.park_id,
      stl_date: record.stl_dat
    })
    .then((response) => {
      if (response.success === true) {
        setRecordsLoading(false);
        const { trades_info, refund_info } = response.data ?? {};
        setRecordsTable({
          trades_info,
          refund_info
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        setRecordsLoading(false);
      }
    });
};

defineExpose({
  getList,
  setTableData
});
</script>
<style lang="scss" scoped>
.summarize-table {
  position: relative;
  .print {
    position: absolute;
    right: 10px;
  }
  .title {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 16px;
    margin-top: 4px;
    margin-bottom: 16px;
  }
  .el-table {
    height: calc(100vh - 240px);
  }
  .cell-with-diagonal {
    display: flex;
    flex-direction: column;
    .category,
    .dateColumn {
      position: relative;
      font-weight: 500;
    }
    .category {
      top: 0px;
      right: 0px;
      align-self: flex-end;
    }
    .diagonal-line {
      width: 100%;
      height: 100%;
      border-top: 1px solid rgba(195, 195, 195, 0.5); /* 调整颜色和样式 */
      transform: rotate(24deg);
    }
    .dateColumn {
      display: inline-block;
      width: 32px;
      left: 0px;
      bottom: 0px;
    }
  }
  :deep(th.el-table-fixed-column--right) {
    background-color: #fafafa !important;
  }
}
</style>
