<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button v-if="$_has('demo:create')" type="primary">新 增</el-button>
      </el-space>
      <div>
        <el-button type="default">导 出</el-button>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border :height="tableHeight">
        <el-table-column label="操作" width="120" align="center">
          <el-button link type="success" v-if="$_has('demo:update')">修改</el-button>
          <el-button link type="danger">删除</el-button>
        </el-table-column>
        <el-table-column prop="date" label="日期" />
        <el-table-column prop="name" label="姓名" />
        <el-table-column prop="state" label="所在州" />
        <el-table-column prop="city" label="所在城市" />
        <el-table-column prop="address" label="详细地址" />
      </el-table>
      <el-pagination
        background
        :total="50"
        layout="total, sizes, prev, pager, next, jumper"
        :page-sizes="[10, 30, 50, 100]"
        class="table-pagination"
      />
    </div>
  </el-card>
</template>

<script setup>
import { onActivated, ref, nextTick } from 'vue';

const tableHeight = ref(0);

const tableData = ref([]);
const loading = ref(false);
onActivated(() => {
  for (let i = 0; i < 60; i++) {
    tableData.value.push({
      date: '2016-05-01',
      name: 'Tom',
      state: 'California',
      city: 'Los Angeles',
      address: 'No. 189, Grove St, Los Angeles'
    });
  }

  nextTick(() => {
    tableHeight.value = window.innerHeight - 330;
  });
});
</script>

<style lang="scss" scoped></style>
