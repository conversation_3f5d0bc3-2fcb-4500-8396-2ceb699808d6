<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">长租车申请</el-button>
      </el-space>
      <el-space>
        <el-button type="plain" @click="templateDownload">模板下载</el-button>
        <el-upload
          :action="uploadExcelUrl"
          accept=".xlsx"
          :headers="headers"
          :show-file-list="false"
          :before-upload="beforeUploadExcel"
          :on-success="onSuccessUploadExcel"
        >
          <el-button type="plain">批量上传</el-button>
        </el-upload>
        <el-button type="default" @click="exportData()">导 出</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 377px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="240">
          <template #default="{ row }">
            <!-- <el-button link type="primary" v-if="row.audit_state == 0 || row.audit_state == 3" @click="handleEdit(row)">
              编辑
            </el-button>
            <el-button link type="danger" v-if="row.audit_state == 0 || row.audit_state == 3" @click="handleDelete(row.id)">
              删除
            </el-button>
            <el-button link type="primary" v-if="row.audit_state == 0 || row.audit_state == 3" @click="handleAudit(row.id)">
              提交审核
            </el-button>
            <el-button link type="primary" v-if="row.audit_state == 1" @click="handleRevoke(row.id)"> 撤回 </el-button>
            <el-button link type="primary" @click="handleRenewPage(row)"> 续费记录 </el-button> -->
            <template v-if="showEmptySign(row)"> - </template>
            <template v-else>
              <el-button link type="primary" @click="handleEdit(row)" v-if="row.audit_state !== 1 && !showOpenReviewBtn(row)"> 修改 </el-button>
              <el-button link type="danger" v-if="showDelBtn(row)" @click="handleDelete(row.id)"> 删除 </el-button>
              <el-button link type="primary" v-if="showOpenReviewBtn(row)" @click="handleEdit(row, 'openReview')"> 开通审核 </el-button>
              <el-button link type="primary" v-if="showSubmitAuditBtn(row)" @click="handleAudit(row)"> 提交审核 </el-button>
              <el-button link type="danger" v-if="showRevokeBtn(row)" @click="handleRevoke(row)"> 撤回 </el-button>
              <re-rent-apply v-if="showRenewBtn(row)" :user_info="row" @submit="getList(data.queryParams)" />
              <el-button link type="primary" v-if="row.pay_state === 2 && row.audit_state === 2" @click="handleRenewPage(row)"> 续费记录 </el-button>
            </template>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="申请编号" align="center" min-width="100" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="180" />
        <el-table-column prop="space_code" label="车位编号" align="center" min-width="120" />
        <el-table-column prop="prk_rent_rule_name" label="规则名称" align="center" min-width="120" />
        <el-table-column prop="prk_rent_rule_type_desc" label="长租类型" align="center" min-width="130" />
        <el-table-column prop="product_type_desc" label="产品类型" align="center" min-width="180" />
        <el-table-column prop="order_money" label="产品金额" align="center" min-width="120" />
        <el-table-column prop="product_range" label="产品周期" align="center" min-width="120">
          <template #default="{ row }">
            <span v-if="row.product_range">{{ formatRentProductRangeText(rentProductRanges, row) }}</span>
            <span v-else> --</span>
          </template>
        </el-table-column>
        <el-table-column prop="rent_time" label="长租时段" align="center" min-width="120">
          <template #default="{ row }">
            <span>{{ row.rent_time || '全时段' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="有效期" align="center" min-width="180">
          <template #default="{ row }">
            <span v-if="row.valid_start_time == null && row.valid_end_time == null"> --</span>
            <span v-else>{{ row.valid_start_time + '~' + row.valid_end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_nos" label="车牌号" align="center" min-width="180" show-overflow-tooltip />
        <el-table-column prop="plate_number" label="车牌数量" align="center" min-width="180" show-overflow-tooltip>
          <template #="{ row }"> {{ row.plate_number }}个 </template>
        </el-table-column>
        <!-- <el-table-column prop="mbr_member_nickname" label="关联会员" align="center" min-width="120" /> -->
        <el-table-column prop="mbr_member_name" label="车主姓名" align="center" min-width="120" />
        <el-table-column prop="mbr_member_mobile" label="手机号" align="center" min-width="120" />
        <el-table-column prop="user_identity_desc" label="用户身份" align="center" min-width="120" />
        <el-table-column prop="pay_state_desc" label="支付状态" align="center" min-width="120" />
        <el-table-column prop="rent_state_desc" label="长租状态" align="center" min-width="120" />
        <el-table-column prop="open_sign_desc" label="续费类型" align="center" min-width="120" />
        <el-table-column prop="audit_state_desc" label="审核状态" align="center" min-width="120" />
        <el-table-column prop="created_at" label="申请时间" align="center" min-width="120" />
        <el-table-column prop="channel_desc" label="申请来源" align="center" min-width="120" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" min-width="120" />
        <el-table-column label="关联车场" align="center">
          <template #default="{ row }">
            <el-button link type="primary" @click="handleParkList(row.id)" v-if="row.park_count > 0"> {{ row.park_count }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="audit_data" label="审核资料" align="center" min-width="160">
          <template #default="{ row }">
            <!-- {{ row }} -->
            <template v-if="row.audit_urls != null && row.audit_urls.length > 0">
              <div style="display: flex; flex-direction: column">
                <el-link v-for="(url, index) in row.audit_urls" :key="index" type="primary" @click="exportAuditData(url.audit_data_url)">{{
                  url?.audit_data_name
                }}</el-link>
              </div>
            </template>
            <template v-else></template>
          </template>
        </el-table-column>
        <el-table-column label="驳回原因" align="center" min-width="120">
          <template #default="{ row }">
            <span v-if="row.audit_state == 3">{{ row.reject_reason }}</span>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <!-- 车场查找带回 -->
  <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
    <park-find-back :park_id="park_id" :park_name="park_name" :mode="flag" @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
  </el-dialog>

  <!-- 会员查找带回 -->
  <el-dialog v-if="memberDialogVisible" width="80%" title="选择会员" v-model="memberDialogVisible" :before-close="handleMemberClose">
    <member-find-back
      :member_id="member_id"
      :member_name="member_name"
      :member_mobile="member_mobile"
      :mode="flag"
      @authCharge="authMemberCharge(false, '')"
      @renderTableInput="renderMemberTableInput"
    />
  </el-dialog>
  <rent-apply-dialog ref="rentApplyDialogRef" @select="handleSelect" @audit="handleAudit" @submit="getList(data.queryParams)" />
</template>

<script name="SpaceRentApplyTable" setup>
import { reactive, ref, onActivated, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import spaceRentApplyService from '@/service/car/SpaceRentApplyService';
import ParkFindBack from './ParkFindBack.vue';
import MemberFindBack from './MemberFindBack.vue';
import rentApplyDialog from './components/rentApplyDialog.vue';
import reRentApply from './components/reRentApply.vue';
import { activeRouteTab } from '@/utils/tabKit';
import { getToken } from '@/utils/common';
import { getOpenUrl, getIamAndNormal } from '@/utils/iamFlow';
// import { useUser } from '@/stores/user';
import commonService from '@/service/common/CommonService';
import { saveToFile } from '@/utils/utils.js';
import { rentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';

const uploadExcelUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadImportExcel');
const headers = reactive({
  Authorization: getToken()
});
const parkInfoDialogVisible = ref(false);
const memberDialogVisible = ref(false);
const tableData = ref([]);
const loading = ref(false);
const downLoading = ref(false);
const total = ref(0);
const park_id = ref('');
const park_name = ref('');
const member_id = ref('');
const member_name = ref('');
const member_mobile = ref('');
const flag = ref('');
const rentApplyDialogRef = ref();
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  ownerForm: {
    id: '',
    mbr_member_id: '',
    mbr_member_name: '',
    mbr_member_mobile: '',
    mbr_member_nickname: '',
    plate_nos: [{ value: '' }]
  },
  validTimeForm: {
    mbr_member_name: '',
    mbr_member_mobile: '',
    id: '',
    valid_start_time: undefined,
    valid_end_time: undefined,
    plate_nos: []
  },
  saveAuditForm: {
    name: ''
  }
});

onActivated(() => {
  getList(data.queryParams);
});
onMounted(() => {
  window.addEventListener('message', handleMessage, false);
});

const showEmptySign = (row) => {
  return row.pay_state === 1 && row.rent_state === 0 && row.audit_state === 4;
};

const showDelBtn = (row) => {
  //状态为待审核 || 已驳回 || 已通过且支付状态为待支付，则显示出删除按钮
  return (
    (row.pay_state === 1 && row.rent_state === 0 && row.audit_state === 0) || row.audit_state === 3 || (row.pay_state === 1 && row.audit_state === 2)
  );
};

// 当支付状态为待支付、长租状态为待生效、审核状态为待审核，且续费类型为开卡，申请来源为小程序, 展示开通审核+删除
const showOpenReviewBtn = (row) => {
  return row.pay_state === 1 && row.rent_state === 0 && row.audit_state === 0 && row.open_sign === 1 && row.channel === 0;
};

const showSubmitAuditBtn = (row) => {
  return !showOpenReviewBtn(row) && row.pay_state === 1 && row.rent_state === 0 && [0, 3].includes(row.audit_state) && row.channel === 1;
};

const showRevokeBtn = (row) => {
  return row.pay_state === 1 && row.rent_state === 0 && row.audit_state === 1;
};

const showRenewBtn = (row) => {
  return row.pay_state === 2 && row.rent_state !== 0 && row.audit_state === 2;
};

// 产品周期内容格式化
const formatRentProductRangeText = (rentProductRanges, row) => {
  if (row.product_type == 8) {
    return row.product_range + '天';
  } else if (row.product_type == 9) {
    return row.product_range + '周';
  } else {
    return getRentProductRangeText(rentProductRanges, row.product_range) || '--';
  }
};

const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  spaceRentApplyService.pagingRentSpaceApplies(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleCreate = () => {
  rentApplyDialogRef.value.showDialog();
};

const handleSelect = (type, childData) => {
  if (type === 'parking') {
    authCharge(true, childData);
  } else {
    authMemberCharge(true, '', childData);
  }
};
//导出
const exportData = () => {
  if (!data.queryParams?.park_id) {
    ElMessage({
      message: '请选择停车场进行统计',
      type: 'warning'
    });
    return false;
  }
  spaceRentApplyService
    .exportRentSpaceApplies(data.queryParams)
    .then((response) => {
      if (response.success) {
        downLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            downLoading.value = false;
          })
          .catch(() => {
            downLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        downLoading.value = false;
      }
    })
    .catch(() => {
      downLoading.value = false;
    });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleEdit = (val, type) => {
  rentApplyDialogRef.value.showDialog(val, type);
};

const handleAudit = (row) => {
  if (!getIamAndNormal(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/Longrent?id=${row.id}&parkToken=${getToken()}`))) {
    ElMessageBox.confirm('请确认是否提交审核？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      if (row.open_sign === 2) {
        // 续费提交审核
        spaceRentApplyService.submitAuditRenewRentSpaceApply(row.id).then((response) => {
          if (response.success) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
            rentApplyDialogRef.value.mainDialogVisible = false;
          } else {
            ElMessage.error(response.detail_message || response.message);
          }
        });
      } else {
        spaceRentApplyService
          .submitAuditRentSpaceApply(row.id)
          .then((response) => {
            if (response.success === true) {
              ElMessage({
                message: response.message,
                type: 'success'
              });
              getList(data.queryParams);
              rentApplyDialogRef.value.mainDialogVisible = false;
            } else {
              ElMessage({
                message: response.detail_message != '' ? response.detail_message : response.message,
                type: 'error'
              });
            }
          })
          .catch(() => {
            getList(data.queryParams);
          });
      }
    });
  }
  // 中台审批流
  // return window.open(`http://localhost/hdwaCommonBpm/hdwaCommonBpm/export/Longrent?id=${id}&parkToken=${getToken()}`, '_blank');
  // 原审批流
};
const handleRenewPage = (row) => {
  activeRouteTab({
    path: '/charge/longRentPay',
    query: {
      park_id: row.park_id,
      park_name: row.park_name,
      plate_no: row.plate_nos
    }
  });
};

// 下载
const templateDownload = () => {
  commonService.fileDownload('template/1_parkRent.xlsx').then((res) => {
    let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
    saveToFile(res.data, decodeURIComponent(fileName));
  });
};
const beforeUploadExcel = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
// 上传
const onSuccessUploadExcel = (response) => {
  if (response.data?.detailMessage) {
    ElMessage.error(response.data.detailMessage);
  } else {
    ElMessage({
      message: response.message,
      type: 'success'
    });
    getList(data.queryParams);
  }
};

const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const params = {
      id: id
    };
    spaceRentApplyService
      .deleteRentSpaceApply(params)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleRevoke = (row) => {
  ElMessageBox.confirm('请确认是否撤回？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let response;
    if (row.open_sign === 2) {
      // 撤销续费审核
      response = await spaceRentApplyService.revokeAuditRenewRentSpaceApply(row.id);
    } else {
      response = await spaceRentApplyService.revokeAuditRentSpaceApply(row.id);
    }
    if (response.success === true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};

const authCharge = (visible, childData) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    park_id.value = childData?.park_id;
    park_name.value = childData?.park_name;
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  rentApplyDialogRef.value.dealSelect(val, 'parking');
};
//会员查找带回
const handleMemberClose = () => {
  memberDialogVisible.value = false;
};
const authMemberCharge = (visible, mode, childData) => {
  if (visible === false) {
    memberDialogVisible.value = false;
  } else {
    if (mode == 'editMem') {
      member_id.value = data.ownerForm.mbr_member_id;
      member_name.value = data.ownerForm.mbr_member_nickname;
      member_mobile.value = data.ownerForm.mbr_member_mobile;
      flag.value = 'editMem';
    } else {
      member_id.value = childData?.mbr_member_id;
      member_name.value = childData?.mbr_member_nickname;
      member_mobile.value = childData?.mbr_member_mobile;
      flag.value = '';
    }
    memberDialogVisible.value = true;
  }
};
const renderMemberTableInput = (val) => {
  if (val[0].mode == 'editMem') {
    data.ownerForm.mbr_member_id = val[0].member_id;
    data.ownerForm.mbr_member_nickname = val[0].member_name;
    data.ownerForm.mbr_member_mobile = val[0].member_mobile;
  } else {
    rentApplyDialogRef.value.dealSelect(val, 'user');
  }
};

const exportAuditData = (val) => {
  window.open(val, '_blank');
};

defineExpose({
  getList
});
</script>
