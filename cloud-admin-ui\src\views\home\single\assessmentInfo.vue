<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-06-25 17:01:16
 * @LastEditTime: 2024-06-28 14:12:04
 * @LastEditors: 达万安 段世煜
 * @Description: 考核指标
 * @FilePath: \cloud-admin-ui\src\views\home\single\assessmentInfo.vue
-->
<template>
  <warp-card height="41%" title="考核指标">
    <tab-button :options="tabOptions" v-model="activeTab" @change="handleTabChange" />
    <div class="chart-container">
      <turnover-rate v-if="activeTab === 0" ref="turnoverRateRef" />
      <parking-duration v-if="activeTab === 1" ref="parkingDurationRef" />
      <traffic-efficiency v-if="activeTab === 2" ref="trafficEfficiencyRef" />
    </div>
  </warp-card>
</template>

<script setup>
import { nextTick, ref } from 'vue';
import warpCard from './components/warpCard.vue';
import tabButton from './components/tabButton.vue';
import turnoverRate from './turnoverRate.vue';
import parkingDuration from './parkingDuration.vue';
import trafficEfficiency from './trafficEfficiency.vue';

const tabOptions = [
  {
    label: '周转率及车位利用趋势',
    value: 0
  },
  {
    label: '停车时长数据',
    value: 1
  },
  {
    label: '通行效率数据',
    value: 2
  }
];
const activeTab = ref(0);

const handleTabChange = () => {
  initChart();
};

const params = ref({});
const fetchData = (val) => {
  params.value = val;
  initChart();
};
const turnoverRateRef = ref(null);
const parkingDurationRef = ref(null);
const trafficEfficiencyRef = ref(null);
const initChart = () => {
  if (activeTab.value === 0) {
    nextTick(() => turnoverRateRef.value.fetchData(params.value));
  } else if (activeTab.value === 1) {
    nextTick(() => parkingDurationRef.value.fetchData(params.value));
  } else if (activeTab.value === 2) {
    nextTick(() => trafficEfficiencyRef.value.fetchData(params.value));
  }
};

defineExpose({
  fetchData
});
</script>
<style scoped lang="scss">
.chart-container {
  height: calc(100% - 30px);
}
</style>
