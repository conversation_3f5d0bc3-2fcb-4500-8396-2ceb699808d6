import * as parkTurnoverApi from '@/api/statisticalReport/ParkTurnoverApi';

/**
 * 车场周转率
 */
export default {
  /**
   * 分页查询车场周转率
   */
  pagingParkTurnover(data) {
    return new Promise((resolve, reject) => {
      try {
        parkTurnoverApi.pagingParkTurnover(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 车场周转率总平均日周转率
   */
  turnroundRatesCount(data) {
    return new Promise((resolve, reject) => {
      try {
        parkTurnoverApi.turnroundRatesCount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkTurnoverApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
