<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加产权方</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="ID" align="center" />
        <el-table-column prop="name" label="产权方名称" align="center" />
        <el-table-column prop="address" label="详细地址" align="center" />
        <el-table-column prop="contact_name" label="联系人" align="center" />
        <el-table-column prop="contact_mobile" label="联系人电话" align="center" />
        <el-table-column prop="updated_at" label="修改时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog
        title="添加产权方"
        v-model="propertyOwnerCreateDialogVisible"
        :close-on-click-modal="false"
        @close="closeAddDialog(addForm)"
        width="500px"
      >
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item prop="name" label="产权方名称">
            <el-input v-model="data.form.name" />
          </el-form-item>
          <el-form-item prop="address" label="详细地址">
            <el-input v-model="data.form.address" />
          </el-form-item>
          <el-form-item prop="contact_name" label="联系人">
            <el-input v-model="data.form.contact_name" />
          </el-form-item>
          <el-form-item prop="contact_mobile" label="联系人电话">
            <el-input v-model="data.form.contact_mobile" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createPropertyOwner(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="修改产权方"
        v-model="propertyOwnerUpdateDialogVisible"
        :close-on-click-modal="false"
        @close="closeEditDialog(editForm)"
        width="500px"
      >
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="name" label="产权方名称">
            <el-input v-model="data.updateForm.name" />
          </el-form-item>
          <el-form-item prop="address" label="详细地址">
            <el-input v-model="data.updateForm.address" />
          </el-form-item>
          <el-form-item prop="contact_name" label="联系人">
            <el-input v-model="data.updateForm.contact_name" />
          </el-form-item>
          <el-form-item prop="contact_mobile" label="联系人电话">
            <el-input v-model="data.updateForm.contact_mobile" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updatePropertyOwner(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="PropertyOwnerTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import propertyOwnerService from '@/service/system/PropertyOwnerService';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const propertyOwnerCreateDialogVisible = ref(false);
const propertyOwnerUpdateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    name: undefined,
    address: undefined,
    contact_name: undefined,
    contact_mobile: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入产权方名称',
        trigger: 'blur'
      }
    ],
    contact_name: [
      {
        required: true,
        message: '请输入联系人',
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      {
        required: true,
        message: '请输入联系人电话',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
  status.value = true;
});
// 分页查询产权方数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  propertyOwnerService.pagingPropertyOwner(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 新建产权方
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form = {
    name: undefined,
    address: undefined,
    contact_name: undefined,
    contact_mobile: undefined
  };
  propertyOwnerCreateDialogVisible.value = true;
  status.value = false;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建产权方
const createPropertyOwner = (addForm) => {
  addForm.validate().then(() => {
    propertyOwnerService
      .createPropertyOwner(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          propertyOwnerCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除产权方
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    propertyOwnerService
      .deletePropertyOwner(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 修改产权方
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    address: row.address,
    contact_name: row.contact_name,
    contact_mobile: row.contact_mobile
  };
  propertyOwnerUpdateDialogVisible.value = true;
};
// 提交并保存修改产权方
const updatePropertyOwner = (editForm) => {
  editForm.validate().then(() => {
    propertyOwnerService
      .updatePropertyOwner(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          editForm.resetFields();
          getList(data.queryParams);
          propertyOwnerUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  propertyOwnerCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  propertyOwnerUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
