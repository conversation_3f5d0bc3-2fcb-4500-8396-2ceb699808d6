import router from '@/router/index';
import { isNavigationFailure, NavigationFailureType } from 'vue-router';
import { ElNotification } from 'element-plus';
import { baseRoute } from '@/router/static';

/**
 * 导航失败有错误消息的路由push
 * @param name 路由name
 * @param params 路由参数
 * @param path 路由path,通过path导航无法传递
 */
export const routePush = async (obj) => {
  try {
    const failure = await router.push(
      obj.name
        ? { name: obj.name, params: obj.params === undefined ? {} : obj.params }
        : { path: obj.path, query: obj.query === undefined ? {} : obj.query }
    );

    if (isNavigationFailure(failure, NavigationFailureType.aborted)) {
      ElNotification({
        message: '导航失败，导航守卫拦截！',
        type: 'error'
      });

      return false;
    }

    return true;
  } catch (error) {
    ElNotification({
      message: '导航失败，路由无效！',
      type: 'error'
    });

    return false;
  }
};

/**
 * 添加动态路由
 */
export const addDynamicRoutes = (routes) => {
  const viewsComponent = import.meta.globEager('/src/views/**/*.vue');
  addRouteAll(viewsComponent, routes);
  router.addRoute({
    path: '/:pathMatch(.*)*',
    name: 'notFound',
    meta: {
      title: '网页走丢了'
    },
    component: () => import('@/views/error/404.vue')
  });
};

/**
 * 动态添加路由-带子路由
 */
export const addRouteAll = (viewsComponent, routes) => {
  for (const idx in routes) {
    if (routes[idx].type == 'page' && viewsComponent[routes[idx].component]) {
      addRouteItem(viewsComponent, routes[idx]);
    }

    if (routes[idx].children && routes[idx].children.length > 0) {
      addRouteAll(viewsComponent, routes[idx].children);
    }
  }
};

/**
 * 动态添加路由
 */
export const addRouteItem = (viewsComponent, route) => {
  baseRoute.children.push({
    path: route.path,
    name: route.name,
    component: viewsComponent[route.component].default,
    meta: {
      title: route.title,
      cached: route.cached || 0
    }
  });

  router.addRoute(baseRoute);
};
