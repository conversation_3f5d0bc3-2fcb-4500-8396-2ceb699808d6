/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 商家优免券表格数据查询
export const pagingCouponMerchants = (data) => {
  return $({
    url: '/console/coupon/merchant/pagingCouponMerchants',
    method: 'post',
    data
  });
};

//新增商家优免券
export const createCouponMerchant = (data) => {
  return $({
    url: '/console/coupon/merchant/createCouponMerchant',
    method: 'post',
    data
  });
};

// 修改商家优免券
export const updateCouponMerchant = (data) => {
  return $({
    url: '/console/coupon/merchant/updateCouponMerchant',
    method: 'post',
    data
  });
};

// 删除商家优免券
export const deleteCouponMerchant = (id) => {
  return $({
    url: '/console/coupon/merchant/deleteCouponMerchant/' + id,
    method: 'post'
  });
};

// 提交审核
export const submitAuditCouponMerchant = (id) => {
  return $({
    url: '/console/coupon/merchant/submitAuditCouponMerchant/' + id,
    method: 'post'
  });
};
// 撤回审核
export const revokeAuditCouponMerchant = (id) => {
  return $({
    url: '/console/coupon/merchant/revokeAuditCouponMerchant/' + id,
    method: 'post'
  });
};
