<template>
  <div class="map-warp">
    <div ref="chartRef" class="chart-warp"></div>
    <div class="scale-button">
      <div class="left btn" @click="handleScale('-')">-</div>
      <div class="value">{{ (scaleValue * 100).toFixed(0) + '%' }}</div>
      <div class="right btn" @click="handleScale('+')">+</div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, markRaw, onBeforeUnmount } from 'vue';
import * as echarts from 'echarts';
import chinaMap from '@/assets/js/china.json';

const chartRef = ref(null);
let chartInstance = null;
const scaleValue = ref(1);
let scatterData = [];
const scatterImg = new URL(`../../../../assets/groupImage/location.png`, import.meta.url).href;

const handleScale = (type) => {
  if (type === '+' && scaleValue.value < 4) {
    scaleValue.value = (Number(scaleValue.value) + 0.1).toFixed(1);
  } else if (type === '-' && scaleValue.value > 0.1) {
    scaleValue.value = (Number(scaleValue.value) - 0.1).toFixed(1);
  }
  setData();
  chartInstance.dispatchAction({
    type: 'geoRoam',
    zoom: scaleValue.value
  });
};

const setData = (data) => {
  if (data) {
    scatterData = data;
  }
  chartInstance.clear();
  const option = {
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(12,55,115,.9)',
      borderColor: 'rgba(35,255,249,.8)',
      textStyle: {
        color: '#fff'
      }
    },
    geo: [
      {
        layoutCenter: ['50%', '52%'], //位置
        layoutSize: '115%', //大小
        show: true,
        map: 'china',
        roam: true,
        zoom: scaleValue.value,
        aspectScale: 0.95,
        scaleLimit: {
          min: 0.1,
          max: 4
        },
        label: {
          show: false,
          color: '#fff'
        },
        itemStyle: {
          areaColor: {
            type: 'linear',
            x: 1200,
            y: 0,
            x2: 0,
            y2: 0,
            colorStops: [
              {
                offset: 0,
                color: 'rgba(51,84,136)' // 0% 处的颜色
              },
              {
                offset: 1,
                color: 'rgba(28,56,108)' // 50% 处的颜色
              }
            ],
            global: true // 缺省为 false
          },
          borderColor: 'rgb(160,218,253)',
          borderWidth: 1,
          opacity: 1
        },
        emphasis: {
          itemStyle: {
            areaColor: 'rgba(19,39,79)',
            borderWidth: 2
          },
          label: {
            show: false
          }
        }
      },
      {
        type: 'map',
        map: 'china',
        zlevel: -1,
        aspectScale: 0.95,
        layoutCenter: ['50%', '52%'],
        layoutSize: '115%',
        roam: false,
        silent: true,
        itemStyle: {
          borderWidth: 2,
          borderColor: 'white',
          shadowColor: 'white',
          shadowOffsetY: 0,
          shadowBlur: 5,
          areaColor: 'rgba(5,21,35,0.1)'
        }
      },
      {
        type: 'map',
        map: 'china',
        zlevel: -2,
        aspectScale: 0.95,
        layoutCenter: ['50%', '53.5%'],
        layoutSize: '115%',
        roam: false,
        silent: true,
        itemStyle: {
          borderWidth: 1,
          borderColor: 'rgba(58,149,253,0.6)',
          shadowColor: '#327093',
          shadowOffsetY: 5,
          shadowBlur: 15,
          areaColor: 'rgba(51,96,141,0.9)'
        }
      },
      {
        type: 'map',
        map: 'china',
        zlevel: -3,
        aspectScale: 0.95,
        layoutCenter: ['50%', '55%'],
        layoutSize: '115%',
        roam: false,
        silent: true,
        itemStyle: {
          borderWidth: 1,
          borderColor: 'rgba(58,149,253,0.6)',
          shadowColor: '#327093',
          shadowOffsetY: 5,
          shadowBlur: 15,
          areaColor: 'rgba(51,96,141,0.6)'
        }
      },
      {
        type: 'map',
        map: 'china',
        zlevel: -4,
        aspectScale: 0.95,
        layoutCenter: ['50%', '56.5%'],
        layoutSize: '115%',
        roam: false,
        silent: true,
        itemStyle: {
          borderWidth: 1,
          borderColor: 'rgba(58,149,253,0.4)',
          shadowColor: 'black',
          shadowOffsetY: 15,
          shadowBlur: 10,
          areaColor: 'rgba(51,96,141,0.3)'
        }
      },
      {
        type: 'map',
        map: 'china',
        zlevel: -5,
        aspectScale: 0.95,
        layoutCenter: ['50%', '58%'],
        layoutSize: '115%',
        roam: false,
        silent: true,
        itemStyle: {
          borderWidth: 0,
          borderColor: 'rgba(50,112,147,0.2)',
          shadowColor: 'black',
          shadowOffsetY: 15,
          shadowBlur: 30,
          areaColor: 'rgba(51,96,141,0.1)'
        }
      }
    ],
    series: [
      ...(scatterData.map((item) => ({
        type: 'scatter',
        coordinateSystem: 'geo',
        //自定义图片的 位置（lng, lat）
        data: [
          {
            id: item.id,
            name: item.name,
            value: [item.longitude, item.latitude],
            moeny: item.trade_money || '- -',
            count: item.trade_num || '- -'
          }
        ],
        // itemStyle: {
        //   color: colorValue.value.text
        // },
        //自定义图片的 大小
        symbolSize: [44, 34],
        symbol: 'image://' + scatterImg,
        cursor: 'pointer',
        tooltip: {
          formatter: (params) => {
            return `<div class="tip-name">${params.data.name}</div><div class="tip-info">交易金额：${params.data.moeny}</div><div class="tip-info">交易笔数：${params.data.count}</div>`;
          }
        }
      })) || [])
    ]
  };
  chartInstance.setOption(option, true);
};

const addZoomEvent = () => {
  chartInstance.on('georoam', () => {
    const currentOption = chartInstance.getOption();
    const zoom = currentOption.geo[0].zoom;
    scaleValue.value = zoom;
    for (let i = 1; i < currentOption.geo.length; i++) {
      //捕捉到缩放时
      currentOption.geo[i].zoom = zoom; //下层geo的缩放等级跟着上层的geo一起改变
      currentOption.geo[i].center = currentOption.geo[0].center; //下层的geo的中心位置随着上层geo一起改变
    }
    chartInstance.clear();
    chartInstance.setOption(currentOption, true);
  });
};

onMounted(() => {
  chartInstance = markRaw(echarts.init(chartRef.value));
  chartInstance.showLoading();
  echarts.registerMap('china', chinaMap);
  //文件加载的动画
  chartInstance.hideLoading();
  setData();
  addZoomEvent();
});

const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData
});
</script>

<style lang="scss" scoped>
:deep(.tip-name) {
  font-size: 14px;
}
:deep(.tip-info) {
  margin-top: 5px;
  font-size: 20px;
}
.map-warp {
  width: 100%;
  height: 100%;
  // position: relative;
  overflow: hidden;
  .chart-warp {
    width: 100%;
    height: 120%;
  }
  .scale-button {
    width: 146px;
    height: 30px;
    display: flex;
    position: absolute;
    bottom: 10px;
    right: 20px;
    background: url('@/assets/groupImage/scale-btn.png') no-repeat;
    background-size: 100% 100%;
    color: #fff;
    font-size: 16px;
    justify-content: space-around;
    align-items: center;
    .btn {
      cursor: pointer;
    }
  }
}
</style>
