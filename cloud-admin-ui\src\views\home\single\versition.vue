<template>
  <warp-card height="21%" title="版本信息">
    <div class="verBox" v-if="versitionData">
      <div class="verTop">
        <div class="verLeft">{{ versitionData.version_name }}</div>
        <div class="verRight">发布时间:{{ versitionData.release_date }}</div>
      </div>
      <div class="verContent">
        {{ versitionData.version_update_content }}
        <div></div>
      </div>
      <div class="verBottom">
        <div class="goHistry" @click="goHistry">
          历史版本<el-icon><ArrowRightBold /></el-icon>
        </div>
      </div>
    </div>
  </warp-card>
</template>
<script setup>
import { onMounted, ref } from 'vue'; //引入vue
import warpCard from './components/warpCard.vue'; //引入卡片组件
import { getNewVersionInfo } from '@/api/VersionInfo/VersionInfo'; //引入接口api
import { useRouter } from 'vue-router'; //引入路由
const router = useRouter(); //使用路由
const versitionData = ref();
//onMounted钩子
onMounted(() => {
  getNewVersionData();
});
//获取最新版本信息api
const getNewVersionData = async () => {
  try {
    const rudata = await getNewVersionInfo();
    if (rudata.code == 200) {
      console.log('xxxxx', rudata.data);
      versitionData.value = rudata.data;
    }
  } catch (error) {
    console.log('获取版本信息失败', error);
  }
};
//点击历史版本
const goHistry = () => {
  router.push('/VersionInfo/VersionInfo');
};
</script>

<style scoped lang="scss">
.verBox {
  height: 100%;
  display: flex;
  flex-direction: column;
  padding: 15rpx 30rpx;
  .verTop {
    height: 30%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .verLeft {
      padding: 5px 15px;
      border-radius: 1000px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 13px;
      color: #2d99fd;
      font-weight: 700;
      background-color: #deefff;
    }
    .verRight {
      color: #7b7b7b;
      font-size: 13px;
    }
  }
  .verContent {
    flex: 1;
    overflow: auto;
    white-space: pre-line;
    padding-top: 5px;
    line-height: 30px;
    font-size: 15px;
    color: #000;
  }
  .verBottom {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    color: #3da0ff;
    font-size: 13px;
    .goHistry {
      cursor: pointer;
      display: flex;
      align-items: center;
      gap: 10px;
    }
  }
}
</style>
