import * as electronicPaymentDataApi from '@/api/statisticalReport/ElectronicPaymentDataApi';

/**
 * 电子支付数据
 */
export default {
  /**
   * 分页查询电子支付数据
   */
  pagingElectronicPaymentData(data) {
    return new Promise((resolve, reject) => {
      try {
        electronicPaymentDataApi.pagingElectronicPaymentData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        electronicPaymentDataApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
