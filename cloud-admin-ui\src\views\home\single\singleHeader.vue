<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-06-25 13:40:40
 * @LastEditTime: 2024-06-25 16:37:03
 * @LastEditors: 达万安 段世煜
 * @Description: 单店头部
 * @FilePath: \cloud-admin-ui\src\views\home\single\singleHeader.vue
-->
<template>
  <div class="header">
    <div class="left">
      <div class="time">{{ time }}</div>
    </div>
    <div class="right">
      <el-select v-model="queryParam.park_id" size="small" placeholder="请选择车场" style="margin-right: 10px; width: 100px" @change="handleFilter">
        <el-option v-for="item in parkList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
      <time-range
        v-model:date="queryParam.date"
        v-model:unit="queryParam.time_unit"
        :teleported="false"
        @change="handleFilter"
        size="small"
        style="margin-right: 10px; width: 355px"
        popper-class="single-index-popper"
      />
      <el-button @click="resetQuery" size="small">重置</el-button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, reactive } from 'vue';
import { useUser } from '@/stores/user';
import { dayjs } from 'element-plus';
import timeRange from '@/components/timeRange.vue';

const parkList = ref([]);
const user = useUser();
if (user.park_ids) {
  parkList.value = user.park_ids.map((item, index) => {
    return {
      key: user.park_names[index],
      value: item
    };
  });
}

// 左上角时钟
const time = ref('----年--月--日 -- --:--:--');
let timer = null;
onMounted(() => {
  starTimer();
  emits('filter', dealEmitData());
});
onUnmounted(() => {
  destoryTimer();
});

const starTimer = () => {
  destoryTimer();
  time.value = dayjs().format('YYYY年MM月DD日 dddd HH:mm:ss');
  timer = setInterval(() => {
    time.value = dayjs().format('YYYY年MM月DD日 dddd HH:mm:ss');
  }, 1000);
};

const destoryTimer = () => {
  clearInterval(timer);
  timer = null;
};

const emits = defineEmits(['filter']);
// 筛选框
const timeOptions = {
  2: 'month',
  3: 'date',
  5: 'week'
};
const getDefaultDate = (val) => {
  return [dayjs().startOf(timeOptions[val]).format('YYYY-MM-DD'), dayjs().endOf(timeOptions[val]).format('YYYY-MM-DD')];
};
const queryParam = reactive({
  time_unit: 3,
  // date: getDefaultDate(3)
  park_id: user.park_ids[0],
  date: [dayjs(new Date().getTime() - 86400000).format('YYYY-MM-DD'), dayjs(new Date().getTime() - 86400000).format('YYYY-MM-DD')]
});

const handleFilter = (val) => {
  emits('filter', dealEmitData());
};

const resetQuery = () => {
  queryParam.time_unit = 3;
  queryParam.date = getDefaultDate(queryParam.time_unit);
};

const dealEmitData = () => {
  const useQueryParam = { ...queryParam };
  if (useQueryParam.date) {
    useQueryParam.start_date = useQueryParam.date[0];
    useQueryParam.end_date = useQueryParam.date[1];
    delete useQueryParam.date;
  }
  return useQueryParam;
};
</script>
<style scoped lang="scss">
.header {
  height: 35px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
  background-color: #fff;
  .left {
    .time {
      font-size: 16px;
      color: #333;
      margin-left: 20px;
    }
  }
  .right {
    display: flex;
  }
}
</style>
