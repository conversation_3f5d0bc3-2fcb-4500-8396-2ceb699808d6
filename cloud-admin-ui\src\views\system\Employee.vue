<template>
  <div class="container my-table-container">
    <employee-search @form-search="searchEmployeeList" @reset="resetParamsAndData" />
    <employee-table ref="table" class="table-warp" />
  </div>
</template>

<script name="Employee" setup>
import EmployeeSearch from './employee/EmployeeSearch.vue';
import EmployeeTable from './employee/EmployeeTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchEmployeeList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
