import $ from '@/utils/axios';

/**
 * 查询回访周期
 * @param {Object} params - 查询参数
 * @param {number} params.park_id - 车场id
 * @param {string} params.statistics_start_date - 开始日期
 * @param {string} params.statistics_end_date - 结束日期
 * @param {number} params.org_department_id - 组织 ID
 * @return {Promise} - Promise对象
 */
export function getRevisitPeriods(params) {
  return $({
    url: '/console/statistics/space/reVisit/getReVisitList',
    method: 'post',
    data: params
  });
}

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/space/reVisit/exportReVisitList',
    method: 'post',
    data
  });
};
//获取最近更新时间
export const getNewUpdateTmie = (type) => {
  return $.post('/console/job/getByType/' + type)
}

