<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="cashTransferService.exportWxTransfer"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 430px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button
              link
              type="primary"
              v-if="scope.row.status === 'ACCEPTED' || scope.row.status === 'WAIT_USER_CONFIRM'"
              @click="revokeTransfer(scope.row)"
            >
              撤销转账
            </el-button>
            <el-button link type="primary" v-if="scope.row.status === 'FAIL'" @click="reTransfer(scope.row)"> 重新转账 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="业务单号" align="center" min-width="150" />
        <el-table-column prop="transfer_bill_no" label="微信支付单号" align="center" min-width="150" />
        <el-table-column prop="openid" label="收款用户OpenID" align="center" min-width="150" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="150" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="150" />
        <el-table-column prop="plate_no" label="车牌号码" align="center" width="100" />
        <el-table-column prop="transfer_amount_str" label="找零金额" align="center" width="100" />
        <el-table-column prop="created_at" label="创建时间" align="center" min-width="100" />
        <el-table-column prop="state" label="找零状态" align="center" width="100">
          <template v-slot="scope">
            <span>{{ transferStateList.find((item) => item.value === scope.row.state).key }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="fail_reason" label="失败原因" align="center" width="100" />
        <el-table-column prop="last_order_update_time" label="最后状态变更时间" align="center" min-width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="CashTransferTable" setup>
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, onMounted } from 'vue';
import cashTransferService from '@/service/finance/CashTransferService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const title = ref('');
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

const transferStateList = [
  { key: '已受理', value: 'ACCEPTED' },
  { key: '处理中', value: 'PROCESSING' },
  { key: '待收款用户确认', value: 'WAIT_USER_CONFIRM' },
  { key: '转账结果未明确', value: 'TRANSFERING' },
  { key: '转账成功', value: 'SUCCESS' },
  { key: '转账失败', value: 'FAIL' },
  { key: '撤销中', value: 'CANCELING' },
  { key: '已撤销', value: 'CANCELLED' }
];

onMounted(() => {});

const getList = (params) => {
  if (!params?.park_id || params?.park_id == '') return;
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;

  const { park_name, ...newParams } = params;
  cashTransferService.getWxTransferPage(newParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const revokeTransfer = (row) => {
  ElMessageBox.confirm('请确认是否撤销转账？', '撤销转账提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    cashTransferService.cancelTransfer(row.order_no).then((response) => {
      console.log(response);
      if (response.data?.detailMessage) {
        ElMessage.error(response.data.detailMessage);
      } else {
        ElMessage.success(response.message);
        getList(data.queryParams);
      }
    });
  });
};

const reTransfer = (row) => {
  ElMessageBox.confirm('请确认是否重新发起用户转账？', '重新转账提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    cashTransferService.reTryTransfer(row.id).then((response) => {
      console.log(response);
      if (response.data?.detailMessage) {
        ElMessage.error(response.data.detailMessage);
      } else {
        ElMessage.success(response.message);
        getList(data.queryParams);
      }
    });
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
