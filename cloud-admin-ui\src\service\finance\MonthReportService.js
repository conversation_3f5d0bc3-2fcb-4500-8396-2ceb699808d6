import * as monthReportApi from '@/api/finance/MonthReportApi';

/**
 * 月报表
 */
export default {
  /**
   * 分页查询月报表通过金额
   */
  pagingMonthReportByMoney(data) {
    return new Promise((resolve, reject) => {
      try {
        monthReportApi.pagingMonthReportByMoney(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询月报表通过次数
   */
  pagingMonthReportByTimes(data) {
    return new Promise((resolve, reject) => {
      try {
        monthReportApi.pagingMonthReportByTimes(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 卡片数据查询
   */
  searchBtnData(data) {
    return new Promise((resolve, reject) => {
      try {
        monthReportApi.searchBtnData(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出月报表（金额）
   */
  exportMonthReports(data) {
    return new Promise((resolve, reject) => {
      try {
        monthReportApi.exportMonthReports(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出月报表（次数）
   */
  exportMonthReportCnt(data) {
    return new Promise((resolve, reject) => {
      try {
        monthReportApi.exportMonthReportCnt(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
