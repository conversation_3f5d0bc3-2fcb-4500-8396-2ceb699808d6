<template>
  <div>
    <WholedayFrequencySearch @form-search="searchWholedayFrequencyList" @reset="resetParamsAndData" />
    <WholedayFrequencyTable ref="table" />
  </div>
</template>

<script name="WholedayFrequency" setup>
import WholedayFrequencySearch from './wholedayFrequency/WholedayFrequencySearch.vue';
import WholedayFrequencyTable from './wholedayFrequency/WholedayFrequencyTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchWholedayFrequencyList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
