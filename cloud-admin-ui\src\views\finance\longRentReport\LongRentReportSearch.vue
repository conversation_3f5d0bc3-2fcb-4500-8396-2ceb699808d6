<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" @click="authCharge(true)" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <time-range
        v-model:date="form.dateRange"
        v-model:unit="form.dateType"
        formatter="YYYY-MM-DD HH:mm:ss"
        :show-type="['week', 'month']"
        style="width: 100%"
        type-value="type"
      />
    </form-search-item>
    <form-search-item>
      <park-type-filter v-model="form.queryParams.park_types" />
    </form-search-item>
    <template #button>
      <export-button :export-func="exportData" :params="form.queryParams"></export-button>
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="LongRentReportSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import parkTypeFilter from '@/components/parkTypeFilter.vue';
import exportButton from '@/components/exportButton.vue';
import { reactive, ref, onMounted } from 'vue';
import { useUser } from '@/stores/user';
import { dayjs } from 'element-plus';
import { exportData } from '@/api/finance/LongRentReportApi';
import timeRange from '@/components/timeRange.vue';

const defaultDateRange = [dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')];

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);

onMounted(() => {
  const user = useUser();
  if (user.role_id == 1) {
    handleDataSearch();
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    handleDataSearch();
  }
});

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    start_time: undefined,
    end_time: undefined,
    park_types: undefined,
    time_type: 2,
    page: 1,
    limit: 30
  },
  dateType: 'month',
  dateRange: defaultDateRange
});

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  form.queryParams.time_type = form.dateType === 'month' ? 2 : 5; // 2月 5周
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateType = 'month';
  form.dateRange = defaultDateRange;
  form.queryParams = {
    park_name: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
