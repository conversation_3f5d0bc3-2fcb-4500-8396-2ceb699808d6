<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.device_no" placeholder="设备序列号" /></form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.device_name" placeholder="设备名称" />
    </form-search-item>
    <form-search-item
      ><el-select v-model="form.queryParams.device_types" style="width: 100%" placeholder="设备类型" multiple>
        <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" /> </el-select
    ></form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.device_ip" placeholder="设备地址" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.device_states" style="width: 100%" placeholder="设备状态" multiple>
        <el-option v-for="item in deviceStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="DeviceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const emits = defineEmits(['form-search']);
const deviceTypeList = ref([]);
const deviceStateList = ref([]);
const form = reactive({
  queryParams: {
    device_no: '',
    device_name: '',
    device_types: [],
    device_ip: '',
    device_states: [],
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'deviceStateList',
      enum_value: 'EnumDeviceState'
    },
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    deviceStateList.value = response.data.deviceStateList;
  });
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
  });
};

const handleDataSearch = () => {
  const params = {
    ...form.queryParams,
    park_id: route.query.parkId
  };
  const query = Object.assign(params, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    device_no: '',
    device_name: '',
    device_types: [],
    device_ip: '',
    device_states: [],
    page: 1,
    limit: 30
  };
  // emits('reset', form.queryParams);
  handleDataSearch();
};

defineExpose({
  handleAllReset
});
</script>
<style lang="scss" scoped></style>
