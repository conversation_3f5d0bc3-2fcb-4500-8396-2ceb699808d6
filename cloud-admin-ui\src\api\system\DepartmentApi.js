import $ from '@/utils/axios';

/**
 * 部门接口层
 */

/**
 * 部门树查询
 * @returns {*}
 */
export const departmentTree = () => {
  return $({
    url: '/console/department/listDepartmentTree',
    method: 'get'
  });
};
// 分页查询部门
export const pagingDepartment = (data) => {
  return $({
    url: '/console/department/pagingDepartment',
    method: 'post',
    data
  });
};
// 保存部门
export const addDepartment = (data) => {
  return $({
    url: '/console/department/addDepartment',
    method: 'post',
    data
  });
};

// 修改部门
export const updateDepartment = (data) => {
  return $({
    url: '/console/department/updateDepartment',
    method: 'post',
    data
  });
};

// 删除部门
export const deleteDepartment = (departmentId) => {
  return $({
    url: '/console/department/deleteDepartment/' + departmentId,
    method: 'post'
  });
};
// // 员工下拉框
// export const listEmployees = () => {
//   return $({
//     url: '/console/employee/employeeList',
//     method: 'get'
//   });
// };

// 分页查询员工(按部门ID)
export const pagingEmployeesByDepartmentId = (data) => {
  return $({
    url: '/console/employee/pagingEmployees',
    method: 'post',
    data
  });
};
