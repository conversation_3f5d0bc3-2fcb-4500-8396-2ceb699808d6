/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:21
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\router\index.js
 * @Description: {}
 */
import { createRouter, createWebHashHistory } from 'vue-router';
import NProgress from 'nprogress';
import 'nprogress/nprogress.css';
import { useUser } from '@/stores/user';
import { staticRoutes } from './static';
import { loadMenusAndRoutes } from '@/utils/menuKit';
import { getTokenByIamToken } from '@/api/login/LoginApi';

const router = createRouter({
  history: createWebHashHistory(),
  routes: staticRoutes
});

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.configure({ showSpinner: false });
  NProgress.start();
  const user = useUser();
  const token = to.query.token;
  const searchParams = new URLSearchParams(window.location.search);
  const token2 = searchParams.get('token');
  const pid = user.user_cas_pid;

  if (to.path === '/' && token2) {
    console.log('beforeEach', token2);
    const { data, success } = await getTokenByIamToken(token2, pid === 'parkingcm' ? 1 : 2);
    if (success) {
      user.$state = {
        user_id: data.user_detail.user_id,
        username: data.user_detail.username,
        name: data.user_detail.name,
        role_id: data.user_detail.role_id
      };

      user.token = data.token;
      user.iam_token = data.iam_token;
      user.park_ids = data.user_detail.park_ids;
      user.park_names = data.user_detail.park_names;
      // 地址内容处理
      window.location.search = '';
      let old_url = location.href;
      let new_url = old_url.substring(0, old_url.indexOf('?'));
      // window.history.replaceState(null, null, new_url); //替换地址栏,直接将地址栏参数替换
      window.location.href = new_url;
      return false;
    }
  }
  if (token) {
    console.log('beforeEach', token);
    const { data, success } = await getTokenByIamToken(token, pid === 'parkingcm' ? 1 : 2);
    if (success) {
      user.$state = {
        user_id: data.user_detail.user_id,
        username: data.user_detail.username,
        name: data.user_detail.name,
        role_id: data.user_detail.role_id
      };

      user.token = data.token;
      user.iam_token = data.iam_token;
      user.park_ids = data.user_detail.park_ids;
      user.park_names = data.user_detail.park_names;
    }
  }
  if (to.path === '/login') {
    // 如果是访问登录界面，如果用户会话信息存在，代表已登录过，跳转到主页
    user.token ? next({ path: '/' }) : next();
  } else {
    if (!user.token) {
      // 如果访问非登录界面，且户会话信息不存在，代表未登录，则跳转到登录界面
      next({ path: '/login' });
    } else {
      loadMenusAndRoutes(to);
    }
  }
  next();
});

// 路由加载完成
router.afterEach((to, from) => {
  document.title = to.meta.title ? to.meta.title : '智慧停车管理云平台';
  NProgress.done();
});

export default router;
