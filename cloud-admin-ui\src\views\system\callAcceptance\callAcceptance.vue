<template>
  <div class="container">
    <car-free-search @form-search="searchParkInfoList" @reset="resetParamsAndData" />
    <car-free-table ref="table" />
  </div>
</template>

<script setup name="CarFree">
import { onActivated, reactive, ref } from 'vue';
import CarFreeSearch from './callAcceptanceSearch.vue';
import CarFreeTable from './callAcceptanceTable.vue';

const table = ref(null);
const params = reactive({});

const searchParkInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
onActivated(() => {
  table.value.getList(params);
});
</script>
