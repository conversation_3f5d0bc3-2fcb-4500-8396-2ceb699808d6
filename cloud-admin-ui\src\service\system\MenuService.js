import * as menu from '@/api/system/MenuApi';

/**
 * 菜单服务层
 */
export default {
  /**
   * 查询导航树
   */
  findNavMenus() {
    return new Promise((resolve, reject) => {
      try {
        menu.findNavMenus().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询菜单分类
   */
  listMenuCategories() {
    return new Promise((resolve, reject) => {
      try {
        menu.listMenuCategories().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 添加菜单
   */
  createMenu(param) {
    return new Promise((resolve, reject) => {
      try {
        menu.createMenu(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改菜单
   */
  updateMenu(param) {
    return new Promise((resolve, reject) => {
      try {
        menu.updateMenu(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除菜单
   */
  deleteMenu(menuId) {
    return new Promise((resolve, reject) => {
      try {
        menu.deleteMenu(menuId).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询菜单树
   */
  getMenuTree(category) {
    return new Promise((resolve, reject) => {
      try {
        menu.getMenuTree(category).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取菜单
   */
  getMenuById(id) {
    return new Promise((resolve, reject) => {
      try {
        menu.getMenuById(id).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
