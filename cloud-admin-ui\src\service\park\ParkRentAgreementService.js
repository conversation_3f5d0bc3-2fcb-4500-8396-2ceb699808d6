import * as parkRentAgreementApi from '@/api/park/ParkRentAgreementApi';

/**
 * 车场-车位长租协议
 */
export default {
  /**
   * 获取租用车位协议
   */
  getSpaceRentLicense(parkId) {
    return new Promise((resolve, reject) => {
      try {
        parkRentAgreementApi.getSpaceRentLicense(parkId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新建车位长租协议
   */
  createParkRentAgreement(data) {
    return new Promise((resolve, reject) => {
      try {
        parkRentAgreementApi.createParkRentAgreement(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
