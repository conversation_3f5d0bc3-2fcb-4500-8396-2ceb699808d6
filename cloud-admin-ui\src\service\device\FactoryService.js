import * as factory from '@/api/device/FactoryApi';

/**
 * 厂商
 */
export default {
  /**
   * 分页查询
   */
  pagingFactory(data) {
    return new Promise((resolve, reject) => {
      try {
        factory.pagingFactory(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建厂商
   */
  createFactory(data) {
    return new Promise((resolve, reject) => {
      try {
        factory.createFactory(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改厂商
   */
  updateFactory(data) {
    return new Promise((resolve, reject) => {
      try {
        factory.updateFactory(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除厂商
   */
  deleteFactory(data) {
    return new Promise((resolve, reject) => {
      try {
        factory.deleteFactory(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
