<template>
  <div class="container">
    <el-card class="card">
      <div class="clearfix">
        <span>最新版本</span>
      </div>
      <el-row :gutter="10">
        <el-col :span="19">
          <el-row :gutter="10">
            <el-col :span="6">
              <span>应用名称：{{ data.appForm.name }}</span>
            </el-col>
            <el-col :span="6">
              <span>当前版本：{{ data.appForm.currentVersion }}</span>
            </el-col>
            <el-col :span="6">
              <span>App Key：{{ data.appForm.app_key }}</span>
            </el-col>
            <el-col :span="6">
              <span>更新说明：{{ data.appForm.features }}</span>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-card>
    <el-card class="card" style="margin-top: 10px">
      <div class="clearfix">
        <span>版本信息</span>
      </div>
      <div>
        <el-table
          :header-cell-style="{ background: '#eef1f6', color: '#606266' }"
          :data="tableData"
          size="mini"
          border
          ref="multipleTable"
          tooltip-effect="dark"
          style="width: 100%; margin-bottom: 40px"
          @selection-change="handleSelectionChange"
          v-loading="tableLoading"
        >
          <el-table-column type="selection" width="40" style="text-align: center"></el-table-column>
          <el-table-column prop="version" label="版本" align="center" min-width="100"></el-table-column>
          <!-- <el-table-column prop="appFileSize" label="安装包大小" align="center" min-width="130">
            <template v-slot="scope"> {{ (scope.row.appFileSize / 1024 / 1024).toFixed(2) }} MB </template>
          </el-table-column> -->
          <el-table-column prop="features" label="版本更新说明" align="center" min-width="150"></el-table-column>
          <el-table-column prop="memo" label="备注" align="center" min-width="160"></el-table-column>
          <el-table-column prop="updator" label="更新人" align="center" min-width="100"></el-table-column>
          <el-table-column prop="updated_at" label="更新时间" align="center" min-width="120"></el-table-column>
        </el-table>
        <el-pagination
          class="fixed-bottom-page"
          small
          :current-page="data.queryParams.page"
          :page-sizes="[30, 50, 100, 200]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </el-card>
  </div>
</template>

<script setup name="AppDetail">
import { reactive, ref, onActivated } from 'vue';
import appAdminService from '@/service/system/AppAdminService';
import { useRoute } from 'vue-router';

const tableLoading = ref(false);
const route = useRoute();
const total = ref(0);
const tableData = ref([]);
const data = reactive({
  appForm: {
    app_id: undefined,
    name: undefined,
    currentVersion: undefined,
    app_key: undefined,
    features: undefined,
    memo: undefined
  },
  selectRows: [],
  queryParams: {
    page: 1,
    limit: 30
  }
});

onActivated(() => {
  if ({} !== route.query) {
    const param = route.query;
    data.appForm = {
      app_id: param.id,
      name: param.name,
      currentVersion: param.currentVersion,
      app_key: param.app_key,
      features: param.features,
      memo: param.memo
    };
  }
  getList(data.appForm);
});

const handleSelectionChange = (val) => {
  data.selectRows = val;
};

const getList = (e) => {
  tableLoading.value = true;
  appAdminService.queryHistApp(e).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    tableLoading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};

const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
</script>

<style lang="scss" scoped>
.card {
  vertical-align: middle;
  height: 100%;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  content: '* ';
  color: red;
}

.el-upload-dragger {
  width: 500px;
}
</style>
