/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:21
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\api\statisticalReport\ParkingCarFlowApi.js
 * @Description: {}
 */
/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 车场车流量-车流明细-统计金额
export const parkTrafficFlowsCount = (data) => {
  return $({
    url: '/console/statistics/traffic/flow/parkTrafficFlowsCount',
    method: 'post',
    data
  });
};

// 分页查询车场车流量
export const pagingParkingCarFlow = (data) => {
  return $({
    url: '/console/statistics/traffic/flow/pagingByPeriod',
    method: 'post',
    data
  });
};

// 按地区层级查询车场车流量
export const chartOrgParkTrafficFlows = (data) => {
  return $({
    url: '/console/report/traffic/flow/chartOrgParkTrafficFlows',
    method: 'post',
    data
  });
};

// 车场车流量趋势
export const chartTrendParkTrafficFlows = (data) => {
  return $({
    url: '/console/report/traffic/flow/chartTrendParkTrafficFlows',
    method: 'post',
    data
  });
};

// 车场车流量分时段进出流量
export const chartPeriodParkTrafficFlows = (data) => {
  return $({
    url: '/console/report/traffic/flow/chartPeriodParkTrafficFlows',
    method: 'post',
    data
  });
};

// 按日导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/traffic/flow/exportByPeriod',
    method: 'post',
    data
  });
};
// 按月导出
export const exportDataByMonth = (data) => {
  return $({
    url: '/console/statistics/traffic/flow/exportMonthParkTrafficFlows',
    method: 'post',
    data
  });
};
//汇总导出
export const exportDataBySum = (data) => {
  return $.post("/console/statistics/traffic/flow/exportSummaryByPeriod", data)
}