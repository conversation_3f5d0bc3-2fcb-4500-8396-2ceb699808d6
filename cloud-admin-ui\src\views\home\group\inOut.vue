<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-25 11:07:42
 * @LastEditors: 达万安 段世煜
 * @Description: 进出车场总次数
 * @FilePath: \cloud-admin-ui\src\views\home\group\inOut.vue
-->
<template>
  <warp-card title="进出车场总次数">
    <bar-chart ref="barChartRef" :title="title" :color="color" bar-gap="0" :gridbottom="30" />
  </warp-card>
</template>

<script setup>
import { nextTick, ref, reactive } from 'vue';

import { statParkTrafficFlowsByInterval } from '@/api/home/<USER>';

import warpCard from './components/warpCard.vue';
import barChart from './components/barChart.vue';
import { dayjs } from 'element-plus';

const color = ['#1890FF', '#00DBF2'];
const title = ref('趋势图');
const data = reactive([
  {
    name: '入场车次（次）',
    value: 0
  },
  {
    name: '出场车次（次）',
    value: 0
  }
]);
let xData = [''];
const barChartRef = ref(null);

const fetchData = async (val) => {
  title.value = `趋势图(${dealTitle(val.start_date, val.time_unit)} ~ ${dealTitle(val.end_date, val.time_unit)})`;
  try {
    const { data: resData } = await statParkTrafficFlowsByInterval(val);
    data[0].value = resData.map((item) => item.car_in_number || 0);
    data[1].value = resData.map((item) => item.car_out_number || 0);
    xData = resData.map((item) => item.time || 0);
  } finally {
    nextTick(() => {
      barChartRef.value && barChartRef.value.setData(data, xData);
    });
  }
};
const timeFormatter = {
  3: 'YYYY年MM月DD日',
  2: 'YYYY年MM月',
  5: 'YYYY年ww周'
};
const dealTitle = (val, unit) => {
  return dayjs(val).format(timeFormatter[unit]);
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped></style>
