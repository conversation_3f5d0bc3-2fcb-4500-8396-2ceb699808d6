import * as abnormalRecordApi from '@/api/statisticalReport/WholedayFrequencyApi';

/**
 * 异常操作记录
 */
export default {
  /**
   * 分页查询异常操作记录
   */
  pagingAbnormalRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        abnormalRecordApi.pagingAbnormalRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportParkingHighPayOrderRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        abnormalRecordApi.exportParkingHighPayOrderRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
