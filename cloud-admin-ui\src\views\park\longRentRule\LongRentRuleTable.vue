<template>
  <div>
    <long-rent-rule-search ref="searchRef" @form-search="searchParkFeeList" @reset="resetParamsAndData" />
    <el-card class="table" shadow="never" style="margin-bottom: 10px">
      <div class="opers">
        <el-space>
          <el-button type="primary" @click="handleCreate">添加长租规则</el-button>
        </el-space>
      </div>
      <div ref="table">
        <el-table :data="tableData" v-loading="loading" border>
          <el-table-column type="selection" style="text-align: center" width="40" />
          <el-table-column prop="action" label="操作" align="center" width="200">
            <template #default="{ row }">
              <el-button link type="primary" v-if="[0, 3].includes(row.audit_state)" @click="review(row.id)"> 提交审核 </el-button>
              <el-button link type="primary" v-if="row.state === 1 && row.audit_state === 1" @click="cancelReview(row.id)"> 撤回 </el-button>
              <el-button link type="success" v-if="row.state === 0 && row.audit_state === 2 && row.group_create === 0" @click="enabled(row)">
                启用
              </el-button>
              <el-button link type="danger" v-if="row.state === 1 && row.audit_state === 2 && row.group_create === 0" @click="disabled(row)">
                禁用
              </el-button>
              <el-button link type="primary" v-if="[0, 2, 3].includes(row.audit_state)" @click="handleEdit(row)"> 修改 </el-button>
              <el-button link type="danger" v-if="[0, 3].includes(row.audit_state)" @click="handleDelete(row.id)"> 删除 </el-button>
            </template>
          </el-table-column>
          <el-table-column prop="id" label="规则编号" align="center" />
          <el-table-column prop="name" label="规则名称" align="center" />
          <el-table-column prop="prk_rent_rule_type_desc" label="长租类型" align="center" />
          <el-table-column prop="product_type_desc" label="产品类型" align="center" />
          <el-table-column prop="money" label="产品金额" align="center" />
          <el-table-column prop="name" label="产品周期" align="center">
            <template #default="{ row }">
              <span v-if="row.product_type_list === 0">--</span>
              <span v-else>{{
                row.product_type_list[0].product_range
                  ? formatRentProductRangeText(rentProductRanges, row.product_type_list[0].product_range, row.product_type)
                  : '--'
              }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="name" label="长租时段" align="center">
            <template #default="{ row }">
              <span v-if="row.product_type_list === 0 || !row.product_type_list[0].start_time">全时段</span>
              <span v-else>{{ row.product_type_list[0].start_time }}至{{ row.product_type_list[0].end_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="public_open_desc" label="是否对小程序开放" align="center" />
          <el-table-column prop="state_desc" label="启用状态" align="center" />
          <el-table-column prop="audit_state_desc" label="审核状态" align="center" />
          <el-table-column prop="audit_comment" label="审核失败原因" align="center">
            <template #default="{ row }">
              {{ row.audit_comment || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="group_create_desc" label="是否总部创建" align="center" />
          <el-table-column prop="creator" label="创建人" align="center">
            <template #default="{ row }">
              {{ row.creator || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="created_at" label="创建时间" align="center">
            <template #default="{ row }">
              {{ row.created_at || '--' }}
            </template>
          </el-table-column>
          <el-table-column prop="updated_at" label="最后修改时间" align="center">
            <template #default="{ row }">
              {{ row.updated_at || '--' }}
            </template>
          </el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="data.queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
    <long-rent-dialog ref="rentDialog" parking-type="branch" @submit="getList(data.queryParams)" :park-id="park_id" />
  </div>
</template>

<script name="LongRentRuleTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import longRentRuleService from '@/service/park/LongRentRuleService';
import { cloneDeep } from 'lodash';
import LongRentRuleSearch from './LongRentRuleSearch.vue';
import longRentDialog from '../components/longRentDialog.vue';
import { getToken } from '@/utils/common';
import { getOpenUrl, getIamAndNormal } from '@/utils/iamFlow';
import { rentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';
import { useRoute } from 'vue-router';
// import { useUser } from '@/stores/user';

// const user = useUser();
const route = useRoute();
const searchRef = ref();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const park_id = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  }
});
onMounted(() => {
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const searchParkFeeList = (queryParams) => {
  getList({
    ...queryParams,
    park_id: park_id.value
  });
};
const resetParamsAndData = (queryParams) => {
  getList({
    ...queryParams,
    park_id: park_id.value
  });
};

const resetDataAndGetList = () => {
  searchRef.value.handleAllReset();
  getList({
    park_id: route.query.parkId
  });
};

// 产品周期内容格式化
const formatRentProductRangeText = (rentProductRanges, product_range, product_type) => {
  if (product_type == 8) {
    return product_range + '天';
  } else if (product_type == 9) {
    return product_range + '周';
  } else {
    return getRentProductRangeText(rentProductRanges, product_range) || '--';
  }
};

// 分页查询长租规则列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  if (params.park_id === undefined || params.park_id === '') {
    return;
  }
  data.queryParams = params;
  longRentRuleService.pagingRentRule(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 提交审核
const review = (row) => {
  // 中台流程表单  长租规则申请
  if (!getIamAndNormal(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/LongRule?id=${row}&parkToken=${getToken()}`))) {
    ElMessageBox.confirm('请确认是否提交审核？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      longRentRuleService.submitAuditRentRuleApply(row).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: '提交审核成功',
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: '提交审核失败',
            type: 'error'
          });
        }
      });
    });
  }
  // return  window.open(`http://localhost/hdwaCommonBpm/hdwaCommonBpm/export/LongRule?id=${row}&parkToken=${getToken()}`, '_blank');
};

// 撤回
const cancelReview = (row) => {
  ElMessageBox.confirm('请确认是否撤回审核？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const parms = { id: row };
    longRentRuleService.cancelAuditRentRuleApply(parms).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.message,
          type: 'error'
        });
      }
    });
  });
};

// 保存表单数据
const saveData = (id) => {
  longRentRuleService.enableRentRule(id).then(() => {
    ElMessage({
      message: '长租规则启用成功',
      type: 'success'
    });
    getList(data.queryParams);
  });
};

// 启用
const enabled = async (row) => {
  const { park_id, prk_rent_rule_type, id, product_type, public_open, product_type_list } = row;
  const params = {
    park_id,
    type: prk_rent_rule_type,
    rule_id: id,
    product_type,
    product_range: product_type_list[0].product_range,
    public_open
  };
  const res = await longRentRuleService.checkRentRule(params);
  if (public_open && res.success && res.data?.detailMessage) {
    ElMessageBox.confirm(res.data?.detailMessage, '温馨提示').then(() => {
      saveData(row.id);
    });
  } else {
    ElMessageBox.confirm('是否要启用该长租规则？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(async () => {
      saveData(row.id);
    });
  }
};

// 禁用
const disabled = (row) => {
  ElMessageBox.confirm('是否要禁用该长租规则？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    longRentRuleService.disableRentRule(row.id).then(() => {
      ElMessage({
        message: '长租规则禁用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 添加长租规则
const handleCreate = () => {
  rentDialog.value.showDialog();
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 删除长租规则
const handleDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    longRentRuleService.deleteRentRule(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message || response.message,
          type: 'error'
        });
      }
    });
  });
};
// 修改长租规则
const handleEdit = (row) => {
  const params = cloneDeep(row);
  rentDialog.value.showDialog({
    id: params.id,
    park_id: params.park_id,
    name: params.name,
    type: params.prk_rent_rule_type,
    public_open: params.public_open,
    money: params.money,
    audit_state: params.audit_state,
    product_type: params.product_type,
    product_list: params.details,
    product_type_list: params.product_type_list
  });
};

const rentDialog = ref();

defineExpose({
  resetDataAndGetList
});
</script>
