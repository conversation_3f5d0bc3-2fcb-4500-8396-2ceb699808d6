<template>
  <div class="container">
    <biz-audit-search @form-search="searchBizAudit" @reset="resetParamsAndData" @batch="handleBatch" />
    <biz-audit-table ref="table" />  
  </div>
</template>

<script setup name="BizAudit">
import BizAuditSearch from './bizAudit/BizAuditSearch.vue';
import BizAuditTable from './bizAudit/BizAuditTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchBizAudit = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
const handleBatch = () => {
  table.value.handleBatch();
}
</script>
