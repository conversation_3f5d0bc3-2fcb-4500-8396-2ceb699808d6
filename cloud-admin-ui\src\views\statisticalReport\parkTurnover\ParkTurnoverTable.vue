<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never" class="table-warp">
    <div class="opers" v-show="props.type === 'date'">
      <el-space>
        <div class="search-btn-group" v-loading="countLoading">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.average_turnround_rate_count }}</p>
            <span class="search-btn-group-total-label">总平均日周转率</span>
          </div>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column prop="turnround_rate_rank" label="排名" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" width="100" />
        <!-- <el-table-column prop="organizational_structure" label="组织架构" align="center" /> -->
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <el-table-column prop="parking_out_number" label="总临停车次" align="center" />
        <el-table-column prop="parking_payed_out_number" label="付费临停出场车次数" align="center" />
        <el-table-column prop="space_number" label="总车位数" align="center" />
        <el-table-column prop="average_turnround_rate" label="总平均日周转率" align="center" />
        <el-table-column prop="payed_average_turnround_rate" label="付费平均日周转率" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkTurnoverTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkTurnoverService from '@/service/statisticalReport/ParkTurnoverService';
import { pagingParkMonthTurnover } from '@/api/statisticalReport/ParkTurnoverApi';

const tableData = ref([]);
const loading = ref(false);
const countData = ref({
  average_turnround_rate_count: 0
});
const data = reactive({
  queryParams: {}
});
const props = defineProps({
  type: String
});
const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  const httpFunc = props.type === 'date' ? parkTurnoverService.pagingParkTurnover : pagingParkMonthTurnover;
  httpFunc(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });

  parkTurnoverService.turnroundRatesCount(params).then((response) => {
    if (response.success === true) {
      countData.value = response.data;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
</style>
