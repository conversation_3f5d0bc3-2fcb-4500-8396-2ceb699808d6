<template>
  <div class="container">
    <advice-search @form-search="searchMemberInfoList" @reset="resetParamsAndData" />
    <advice-table ref="table" />
  </div>
</template>

<script name="Advice" setup>
import AdviceSearch from './advice/AdviceSearch.vue';
import AdviceTable from './advice/AdviceTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchMemberInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
