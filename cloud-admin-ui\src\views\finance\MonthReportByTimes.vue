<template>
  <div class="container">
    <month-report-by-times-search @form-search="searchMonthReportByTimesList" />
    <month-report-by-time-search-btn-groups @search="searchMonthReportByTimeFromGroups" ref="search_btn" />
    <month-report-by-times-table ref="table" />
  </div>
</template>

<script name="MonthReportByTimes" setup>
import MonthReportByTimesSearch from './monthReport/MonthReportByTimesSearch.vue';
import MonthReportByTimeSearchBtnGroups from './monthReport/MonthReportByTimeSearchBtnGroups.vue';
import MonthReportByTimesTable from './monthReport/MonthReportByTimesTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const search = ref(null);
const search_btn = ref(null);

const state = reactive({
  params: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});

const searchMonthReportByTimesList = (params) => {
  state.params = params;
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findMonthReportByTime(params);
  table.value.getList(params);
};

const searchMonthReportByTimeFromGroups = (queryParams) => {
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findMonthReportByTime(queryParams);
  table.value.getList(queryParams);
};
</script>
