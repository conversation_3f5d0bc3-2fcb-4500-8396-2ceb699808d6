<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-03-15 10:56:46
 * @LastEditors: 达万安 段世煜
 * @Description: 停车统计
 * @FilePath: \cloud-admin-ui\src\views\home\single\question.vue
-->
<template>
  <warp-card height="28%" title="常见问题">
    <div class="container">
      <!-- <div class="tip">点击查看常见问题处理方案</div>
      <div class="video-container"></div> -->
      <!-- <el-button type="primary" link @click="downloadFile()">惠达停车系统用户使用手册V2.3</el-button> -->
      <a :href="urlDownLoad + '/user_manual/user_manual_V2.3.docx'" download="惠达停车系统用户使用手册V2.3.docx">惠达停车系统用户使用手册V2.3</a>
    </div>
  </warp-card>
</template>

<script setup>
import { ref } from 'vue';
import warpCard from './components/warpCard.vue';
const urlDownLoad = ref(import.meta.env.VITE_BASE_URL);

const downloadFile = () => {
  window.open(urlDownLoad.value + '/user_manual/user_manual_V2.3.docx', '_blank');
};
</script>

<style scoped lang="scss">
.container {
  margin-top: -10px;
}
.tip {
  color: #2d2d2d;
  margin-bottom: 6px;
}
.video-container {
  width: 100%;
  height: 15vh;
  background-image: url('@/assets/singleImage/question-video.png');
  background-size: 100% 100%;
  cursor: pointer;
}
</style>
