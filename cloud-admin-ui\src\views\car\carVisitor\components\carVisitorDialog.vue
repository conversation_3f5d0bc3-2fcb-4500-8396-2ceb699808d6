<template>
  <div ref="dialogRef">
    <el-dialog
      :title="`${checkabled ? '查看详情' : isAddRule ? '新建访客车申请' : '修改访客车申请'}`"
      v-model="dialogVisible"
      destroy-on-close
      :closed="dialogClosed"
      :close-on-click-modal="false"
      width="1200px"
    >
      <div v-if="checkabled" class="form-title">
        <span class="title">申请信息</span>
        <span class="status">到访状态：{{ formData.visit_status_desc }}</span>
      </div>
      <el-form ref="formRef" label-width="120px" :rules="rules" :model="formData" class="grid-form">
        <el-form-item label="停车场名称" prop="park_id">
          <el-input
            v-if="checkabled || (formData.audit_state === 2 && formData.visit_status === 1)"
            v-model="formData.park_name"
            placeholder="请选择访客预约车场"
            disabled
          />
          <el-input v-else v-model="formData.park_name" placeholder="请选择访客预约车场" readonly @click="handleSelect('parking')" />
        </el-form-item>
        <el-form-item label="访客姓名" prop="visitor_name">
          <el-input v-model="formData.visitor_name" placeholder="请输入访客姓名" maxlength="20" :disabled="checkabled" />
        </el-form-item>
        <el-form-item label="访客类型" prop="visitor_type">
          <el-select v-model="formData.visitor_type" placeholder="访客类型" style="width: 100%" :disabled="checkabled">
            <el-option v-for="item in visitorTypes" :key="item.value + 'visitor_type'" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="手机号" prop="visitor_phone">
          <el-input v-model="formData.visitor_phone" placeholder="请输入访客手机号" maxlength="20" :disabled="checkabled" />
        </el-form-item>
        <el-form-item label="车牌号" prop="visitor_plate_no">
          <template #label>
            <div class="label-flex">
              <span>车牌号</span>
              <el-tooltip placement="top">
                <template #content>
                  <span>特殊车牌须知<br /></span>
                  <span> 1) 若输入港澳本土牌、其他特殊类型车牌请切换"特殊车牌"模式；<br /></span>
                  <span> 2) 若输入大陆、粤港/粤澳车牌(如粤Z开头)请切回"大陆车牌"模式；<br /></span>
                  <span>
                    3) 若悬挂粤港/澳、港澳本土、特殊等多张号牌，请按粤港/澳(粤Z开头)->香港本土->澳门本土->特殊车牌，由此顺序择其一张号牌绑定。<br
                  /></span>
                </template>
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <div style="display: flex; width: 100%" v-show="formData.plate_no_type == 0">
            <el-input v-model="plate_no" placeholder="若多车牌以英文逗号隔开，例：粤XX,粤XX" style="width: 100%" :disabled="checkabled" />
            <el-button type="primary" @click="changePlateNoType">特殊车牌</el-button>
          </div>
          <div style="display: flex; width: 100%" v-show="formData.plate_no_type != 0">
            <el-input v-model="plate_no" placeholder="请输入港澳本土、其他车牌，仅支持添加一张" style="width: 100%" :disabled="checkabled" />
            <el-button type="primary" @click="changePlateNoType">大陆车牌</el-button>
          </div>
        </el-form-item>
        <el-form-item label="预约时间" prop="start_time">
          <el-date-picker
            v-model="dateRangeTime"
            type="datetimerange"
            range-separator="至"
            start-placeholder="预约开始时间"
            end-placeholder="预约结束时间"
            @change="dateRangeTimeChange"
            :disabled-date="(time) => time.getTime() < new Date().setHours(0, 0, 0, 0)"
            :disabled="checkabled"
          />
        </el-form-item>
        <el-row class="row">
          <el-col :span="24">
            <el-form-item label="来访事由" prop="visit_reason">
              <el-input v-model="formData.visit_reason" placeholder="请输入来访事由，字数限60字以内" maxlength="60" :disabled="checkabled" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="24">
            <div class="el-form-item is-required asterisk-left">
              <label class="el-form-item__label">
                来访证明
                <span class="el-form-item__sublabel"
                  >（支持JPG、JPEG、PNG、PDF格式文件，单份限10M,可上传3份，可上传来访事项通告文件或微信等通知截图，能辅助证明确有其事）</span
                >
              </label>
            </div>
            <div style="display: flex">
              <el-form-item prop="visit_proof">
                <el-upload
                  :disabled="checkabled"
                  :action="uploadUrl"
                  :headers="headers"
                  ref="visitorVoucherUploadRef"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG, .pdf"
                  :limit="3"
                  v-model:file-list="defaultVisitorFileList"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onVisitorUploadSuccessUpload"
                  :on-exceed="handleVisitorUploadExceed"
                >
                  <div class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传文件</div>
                  </div>
                  <template #file="{ file }">
                    <div style="width: 100%">
                      <div
                        style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center"
                        v-if="file.name.split('.').pop().toLowerCase() === 'pdf'"
                      >
                        <img :src="PDFLogo" alt="" />
                      </div>
                      <img v-else class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="onPreview(file)">
                          <el-icon><zoom-in /></el-icon>
                        </span>
                        <span class="el-upload-list__item-delete" v-if="!checkabled" @click="handleRemove(file, defaultVisitorFileList)">
                          <el-icon><Delete /></el-icon>
                        </span>
                      </span>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-form-item label="访客车入场消息提醒" prop="is_remind" label-width="160">
          <el-switch v-model="formData.is_remind" :active-value="1" :inactive-value="0" :disabled="checkabled" />
        </el-form-item>
        <el-form-item label="访客车逾期进出场提醒" prop="late_remind" label-width="190">
          <el-switch v-model="formData.late_remind" :active-value="1" :inactive-value="0" :disabled="checkabled" />
        </el-form-item>
      </el-form>
      <div v-if="checkabled" class="form-title">
        <span class="title">操作记录</span>
        <span class="status">当前审批状态：{{ formData.audit_state_desc }}</span>
      </div>
      <el-table v-if="checkabled" :data="formData.history_list" v-loading="loading" style="margin-bottom: 20px">
        <el-table-column prop="node_name" label="记录节点" align="center" min-width="100" />
        <el-table-column prop="operator" label="操作人" align="center" min-width="100" />
        <el-table-column prop="created_at" label="操作时间" align="center" min-width="100" />
        <el-table-column prop="conclusion" label="审批结论" align="center" min-width="100">
          <template v-slot="{ row }">
            <span>{{ row.conclusion || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="opinion" label="审核意见" align="center" min-width="100">
          <!-- <template v-slot="{ row }">
            <span>{{ row.opinion || '--' }}</span>
          </template> -->
        </el-table-column>
      </el-table>
      <template #footer v-if="!checkabled">
        <span class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="imgDialogVisible" title="图片预览" width="40%">
      <img w-full style="max-width: 100%; height: auto" :src="imageDialogUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup>
import commonService from '@/service/common/CommonService';
import CarVisitorService from '@/service/car/CarVisitorService';
import { getToken } from '@/utils/common';
import { ElMessage, ElMessageBox, dayjs } from 'element-plus';
import { ref, reactive, watch } from 'vue';

const PDFLogo = new URL('./PDF.svg', import.meta.url).href;
const emits = defineEmits(['select', 'submit']);
const formData = ref({
  park_id: '',
  park_name: '',
  visitor_name: '',
  visitor_type: undefined,
  visitor_phone: '',
  visitor_plate_no: [],
  plate_no_type: 0,
  start_time: '',
  end_time: '',
  visit_reason: '',
  visit_proof: '',
  is_remind: 0,
  plate_no_change: false,
  late_remind: 0
});

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadAuditData');
const headers = reactive({
  Authorization: getToken()
});

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[0123456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const validatePlateNo = (rule, value, callback) => {
  if (value?.length) {
    if (formData.value.plate_no_type == 0) {
      const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z][A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳]$/;
      const newReg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z](?:((\d{5}[A-HJK])|([A-HJK][A-HJ-NO-Z0-9][0-9]{4}))|[A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳])$/;
      for (let i = 0; i < value.length; i++) {
        const item = value[i];
        if (!reg.test(item) && !newReg.test(item) && !item.includes('使') && !item.includes('领')) {
          console.log(reg.test(item), newReg.test(item), item);
          callback(new Error(item + '为无效的车牌号'));
          break;
        }
      }
    } else {
      //最多支持输入15位以内字符，字符只允许输入数字、英文(区分大小写)、中文，需要对输入字符合法性进行校验，校验不通过的，框外红色提示语“请输入15位以内且为数字/中英文组合”
      const reg = /^[a-zA-Z0-9\u4e00-\u9fa5]{1,15}$/;
      if (!reg.test(value)) {
        callback(new Error('请输入15位以内且为数字/中英文组合'));
        return;
      }
    }
  }
  callback();
};
const rules = {
  park_id: [
    {
      required: true,
      message: '请选择车场',
      trigger: 'change'
    }
  ],
  visitor_name: [
    {
      required: true,
      message: '请输入访客姓名',
      trigger: 'blur'
    }
  ],
  visitor_type: [
    {
      required: true,
      message: '请选择访客类型',
      trigger: 'change'
    }
  ],
  visitor_phone: [
    {
      required: true,
      message: '请输入手机号',
      trigger: 'change'
    },
    {
      trigger: 'blur',
      validator: validateMobilePhone
    }
  ],
  visitor_plate_no: [
    {
      required: true,
      message: '请输入车牌号',
      trigger: 'change'
    },
    {
      trigger: 'blur',
      validator: validatePlateNo
    }
  ],
  start_time: [
    {
      required: true,
      message: '请选择预约时间',
      trigger: 'change'
    }
  ],
  visit_reason: [
    {
      required: true,
      message: '请输入来访事由',
      trigger: 'blur'
    }
  ],
  visit_proof: [
    {
      required: true,
      message: '请上传来访证明',
      trigger: 'change'
    }
  ]
};

const dateRangeTime = ref([]);
// watch(dateRangeTime, (val) => {
//   if (val.length) {
//     formData.value.start_time = dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss');
//     formData.value.end_time = dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss');
//   }
// });
const dateRangeTimeChange = (val) => {
  if (val.length) {
    formData.value.start_time = dayjs(val[0]).format('YYYY-MM-DD HH:mm:ss');
    formData.value.end_time = dayjs(val[1]).format('YYYY-MM-DD HH:mm:ss');
    if (!isWithin72Hours(formData.value.start_time, formData.value.end_time)) {
      ElMessage.error('开始时间和结束时间必须在72小时内');
      return;
    }
  }
};
const plate_no = ref('');
const init_plate_no = ref('');
watch(plate_no, (val) => {
  formData.value.visitor_plate_no = val.split(',');
});
const dialogVisible = ref(false);
const imgDialogVisible = ref(false);
const imageDialogUrl = ref('');
const loading = ref(false);
// 是否新建
const isAddRule = ref(true);
const checkabled = ref(false);
const visitorTypes = ref([]);
const formRef = ref();
const visitorVoucherUploadRef = ref();
const defaultVisitorFileList = ref([]);
watch(
  defaultVisitorFileList,
  (val) => {
    if (val.length > 0) {
      val.forEach((item) => {
        const extension = getFileExtension(item.name);
        if (item.name.indexOf('来访证明') != -1) {
          return;
        }
        item.name = '来访证明' + extension;
      });
      const auditUrl = val.map((item) => {
        return {
          audit_data_name: item.name,
          audit_data_url: item.url
        };
      });
      if (auditUrl.length > 0) {
        formData.value.visit_proof = JSON.stringify(auditUrl);
      } else {
        formData.value.visit_proof = undefined;
      }
    }
  },
  { deep: true }
);
const getFileExtension = (filename) => {
  const match = filename.match(/(\.[^.]+)$/);
  return match ? match[1] : '';
};

const showDialog = (data, type) => {
  if (type === 'check') {
    checkabled.value = true;
  } else {
    checkabled.value = false;
  }
  initFormData(data);
  dialogVisible.value = true;
};

const closeDialog = () => {
  dialogVisible.value = false;
};

const dialogClosed = () => {
  formData.value = {
    park_id: '',
    park_name: '',
    visitor_name: '',
    visitor_type: undefined,
    visitor_phone: '',
    visitor_plate_no: [],
    plate_no_type: 0,
    start_time: '',
    end_time: '',
    visit_reason: '',
    visit_proof: '',
    is_remind: 0,
    plate_no_change: false,
    late_remind: 0
  };
  defaultVisitorFileList.value = [];
  dateRangeTime.value = [];
  plate_no.value = '';
  init_plate_no.value = '';
};

/**
 * @description 初始化表单数据
 */
const initFormData = async (data) => {
  loading.value = false;
  isAddRule.value = !data;
  const param1 = [{ enum_key: 'EnumVisitorType', enum_value: 'EnumVisitorType' }];
  commonService.findEnums('park', param1).then((response) => {
    visitorTypes.value = response.data.EnumVisitorType;
  });
  if (data) {
    CarVisitorService.visitorApplyDetail(data.id).then((response) => {
      formData.value = response.data;
      formData.value.plate_no_change = false;
      if (response.data.visit_proof) {
        defaultVisitorFileList.value = JSON.parse(response.data.visit_proof);
        defaultVisitorFileList.value = defaultVisitorFileList.value.map((item) => {
          return {
            name: item.audit_data_name,
            url: item.audit_data_url
          };
        });
      }
      dateRangeTime.value = [response.data.start_time, response.data.end_time];
      plate_no.value = response.data.visitor_plate_no;
      init_plate_no.value = response.data.visitor_plate_no;
      formData.value.visitor_plate_no = response.data.visitor_plate_no.split(',');
      console.log('formData.value', formData.value);
      console.log('response', response.data);
    });
  } else {
    formData.value = {
      park_id: '',
      park_name: '',
      visitor_name: '',
      visitor_type: undefined,
      visitor_phone: '',
      visitor_plate_no: [],
      plate_no_type: 0,
      start_time: '',
      end_time: '',
      visit_reason: '',
      visit_proof: '',
      is_remind: 0,
      plate_no_change: false,
      late_remind: 0
    };
    defaultVisitorFileList.value = [];
    dateRangeTime.value = [];
    plate_no.value = '';
  }
};

const dealSelect = (val, type) => {
  if (type === 'parking') {
    formData.value.park_id = val[0].park_id;
    formData.value.park_name = val[0].park_name;
  }
};
/**
 * @description 点击选择框的选择事件 触发父组件中的弹窗
 * @param {*} type 弹窗类型 parking | user
 */
const handleSelect = (type) => {
  emits('select', type, formData.value);
};

const onPreview = (file) => {
  const fileUrl = file.url;
  const fileName = file.name;
  const fileExtension = fileName.split('.').pop().toLowerCase();
  if (fileExtension === 'pdf') {
    // 如果是 PDF 文件，则在新标签页中打开 URL 地址
    window.open(fileUrl, '_blank');
  } else {
    imgDialogVisible.value = true;
    imageDialogUrl.value = file.response?.data.audit_data_url || file.url;
  }
};

const handleRemove = (file, fileList) => {
  const index = fileList.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.splice(index, 1);
  }
};

const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage.warning('上传文件大小不能超过 25MB!');
    return false;
  }
  if (file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'image/jpg' && file.type !== 'application/pdf') {
    ElMessage.warning('上传文件格式错误!');
    return false;
  }
};

const onVisitorUploadSuccessUpload = (response, uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles);
  if (response.success == true) {
    uploadFile.url = response.data.audit_data_url;
    formRef.value.validateField('visit_proof');
    ElMessage.success(response.message);
  } else {
    ElMessage.error(response.message);
  }
};
const handleVisitorUploadExceed = (files) => {
  // visitorVoucherUploadRef.value.clearFiles();
  // visitorVoucherUploadRef.value.handleStart(files[0]);
  // visitorVoucherUploadRef.value.submit();
  ElMessage.warning('只可上传三份文件！');
};

const hasPlateNoChanged = (plate_no, init_plate_no) => {
  // 将车牌号字符串转换为数组并排序
  const plateNoArray = plate_no.split(',').sort();
  const initPlateNoArray = init_plate_no.split(',').sort();
  // 比较两个数组的内容
  if (plateNoArray.length !== initPlateNoArray.length) {
    return true;
  }
  for (let i = 0; i < plateNoArray.length; i++) {
    if (plateNoArray[i] !== initPlateNoArray[i]) {
      return true;
    }
  }
  return false;
};

const isWithin72Hours = (start_time, end_time) => {
  const start = dayjs(start_time);
  const end = dayjs(end_time);
  return end.diff(start, 'hour') <= 72;
};
const submitForm = async () => {
  loading.value = true;
  try {
    const submitFunc = isAddRule.value ? CarVisitorService.createVisitorApply : CarVisitorService.updateVisitorApply;
    const { message, data } = await submitFunc(formData.value);
    if (data?.detailMessage) {
      ElMessage.error(data.detailMessage);
    } else {
      ElMessage.success(message);
      emits('submit');
      closeDialog();
      visitorVoucherUploadRef.value?.clearFiles();
    }
  } catch (error) {
    loading.value = false;
  } finally {
    loading.value = false;
  }
};
const dialogRef = ref();
/**
 * @description: 提交表单数据
 * @param {*}
 * @return {*}
 */
const handleSubmit = () => {
  console.log(formData.value);
  formRef.value.validate().then(async () => {
    if (!isWithin72Hours(formData.value.start_time, formData.value.end_time)) {
      ElMessage.error('开始时间和结束时间必须在72小时内');
      return;
    }
    if (formData.value.audit_state === 2 && hasPlateNoChanged(plate_no.value, init_plate_no.value)) {
      ElMessageBox.confirm('对已审批通过的访客关键要素发生变更，系统将重新发起审批，请确认是否执行？', '确认提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        customClass: 'submitCarVisitorAuditClass'
      }).then(async () => {
        formData.value.plate_no_change = true;
        await submitForm();
      });
    } else {
      formData.value.plate_no_change = false;
      await submitForm();
    }
  });
};
const changePlateNoType = () => {
  formData.value.plate_no_type = formData.value.plate_no_type === 0 ? 1 : 0;
  plate_no.value = '';
  formRef.value.clearValidate('visitor_plate_no');
};

defineExpose({
  showDialog,
  dealSelect
});
</script>

<style lang="scss" scoped>
.grid-form {
  display: grid;
  grid-template-columns: 1fr 1fr;

  .row {
    grid-column-start: 1;
    grid-column-end: 3;
  }
}

::v-deep .el-form-item__label {
  justify-content: unset !important;
  padding-left: 18px;
}
.el-form-item__sublabel {
  color: #f56c6c;
  font-size: 12px;
}
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;
}
.form-title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  .title {
    font-size: 16px;
    &::before {
      content: '';
      display: inline-block;
      width: 4px;
      height: 18px;
      margin-right: 4px;
      background-color: #409eff;
      vertical-align: text-bottom;
    }
  }
  .status {
    color: #ffffff;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: #409eff;
  }
}
:global(.submitCarVisitorAuditClass) {
  vertical-align: top;
  top: calc(15vh + 200px);
}
</style>
