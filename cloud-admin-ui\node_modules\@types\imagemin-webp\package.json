{"name": "@types/imagemin-webp", "version": "7.0.3", "description": "TypeScript definitions for imagemin-webp", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin-webp", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "brettm12345", "url": "https://github.com/brettm12345"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-webp"}, "scripts": {}, "dependencies": {"@types/imagemin": "*"}, "typesPublisherContentHash": "cf03e3d23ebf8e589e117123fad9ea58c48c6ddf8886dbb6af1b850390d3ea4b", "typeScriptVersion": "4.5"}