{"name": "@types/imagemin-mozjpeg", "version": "8.0.4", "description": "TypeScript definitions for imagemin-mozjpeg", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin-mozjpeg", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "hkjeffchan", "url": "https://github.com/hkjeffchan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-mozjpeg"}, "scripts": {}, "dependencies": {"@types/imagemin": "*"}, "typesPublisherContentHash": "31496ef18dc179038cc197da236aa1816146edd4d994f12f40a45b5bbf5f5cc1", "typeScriptVersion": "4.5"}