<template>
  <black-list-search @form-search="searchBlackList" @reset="resetParamsAndData" />
  <black-list-table ref="table" />  
</template>

<script setup name="BlackList">
import BlackListSearch from './blackList/BlackListSearch.vue';
import BlackListTable from './blackList/BlackListTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchBlackList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};

defineExpose({
  searchBlackList
});
</script>
