/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-28 18:16:00
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\stores\user.js
 * @Description: {}
 */
import { defineStore } from 'pinia';

export const useUser = defineStore('user', {
  id: 'user',
  state: () => {
    return {
      user_id: 0,
      user_cas_pid: '',
      username: '',
      name: '',
      role_id: '',
      token: '',
      iam_token: '',
      park_ids: [],
      park_names: []
    };
  },
  actions: {
    removeToken() {
      this.token = '';
      this.iam_token = '';
    }
  },
  persist: {
    enable: true
  }
});
