<template>
  <div>
    <div @click="toConsole(props.info)" v-if="!props.info.tabName || props.info.tabName != '本地'" class="conbox">
      <div class="tp">
        <div>{{ props.info.name }}</div>
        <div class="red">{{ props.info.event_type_desc?.length > 0 ? '异常' : '' }}</div>
      </div>
      <div class="bt">
        <div class="bt-one">
          <!-- <div v-if="props.info.showtype == 'export'">车牌号: <span class="blue">京A123C4</span></div> -->
          <div class="blue" style="display: flex; align-items: center; justify-content: space-between">
            <!-- <div v-if="props.info.showtype == 'export'">排队时间:00:00:28</div> -->
            <div v-if="props.info.event_type_desc?.length > 0" class="bt-two">
              <el-icon><WarnTriangleFilled /></el-icon>{{ props.info.event_type_desc }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-if="props.info.tabName == '本地'" class="conbox2">
      <div class="tp">
        <div>{{ props.info.name }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDuty } from '@/stores/duty';
import { activeRouteTab } from '@/utils/tabKit';
const duty = useDuty();
const props = defineProps({
  info: {
    type: Object,
    default: () => {}
  }
});
const toConsole = (item) => {
  duty.callInfo = {
    ...duty.callInfo
  };
  duty.eventStartTime = new Date().getTime();
  activeRouteTab({
    path: '/monitoringMan/Console/Console',
    query: {
      sIp: item.id,
      gatewayType: item.type
    }
  });
};
</script>

<style lang="scss" scoped>
.conbox2 {
  height: 60px;
  width: 240px;
  border: 1px solid #dcdcdc;
  border-radius: 5px;
  font-size: 14px;
  text-align: center;
  line-height: 60px;
  border: 1px solid #f1f0f0;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  background: linear-gradient(135deg, #e2e4e5, #ccdde9);
}
.conbox {
  height: 90px;
  width: 240px;
  border-radius: 5px;
  font-size: 14px;
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  padding: 15px;
  background: linear-gradient(135deg, #e2e4e5, #ccdde9);
  box-sizing: border-box;
  border: 1px solid #f1f0f0;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  .tp {
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .red {
      padding: 0 5px;
      background-color: #ffd9d9;
      color: red;
      border-radius: 3px;
    }
  }
  .bt {
    .bt-one {
      // height: 35px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      .blue {
        color: #009dff;
      }
    }
    .bt-two {
      width: fit-content;
      display: flex;
      align-items: center;
      gap: 2px;
      padding: 0px 5px;
      background-color: #ffeed9;
      border-radius: 3px;
      color: #ff8f00;
    }
  }
}
</style>
