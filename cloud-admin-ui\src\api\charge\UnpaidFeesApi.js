/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 欠逃费订单列表查询
export const recordsPage = (data) => {
  return $({
    url: '/console/park/fee/parkRecoveryPayRecords/recordsPage',
    method: 'post',
    data
  });
};
// 导出
export const exportParkPayRecords = (data) => {
  return $({
    url: '/console/park/fee/parkRecoveryPayRecords/exportParkPayRecords',
    method: 'post',
    data
  });
};
// 保存核销记录
export const chargeoffsave = (data) => {
  return $({
    url: '/console/park/fee/chargeoff/save',
    method: 'post',
    data
  });
};

// 根据订单id查询到 核销记录
export const chargeofflistid = (id) => {
  return $({
    url: `/console/park/fee/chargeoff/list/${id}`,
    method: 'get'
  });
};
// 催缴
export const pushRemind = (id) => {
  return $({
    url: `/console/park/fee/parkRecoveryPayRecords/pushRemind/${id}`,
    method: 'get'
  });
};
// 查询欠缴总额和笔数
export const parkRecoveryPayRecordsdata = (data) => {
  return $({
    url: `/console/park/fee/parkRecoveryPayRecords/data`,
    method: 'post',
    data
  });
};

// 查询单条临停规则
export const getParkFeeById = (id) => {
  return $({
    url: '/console/park/fee/getParkFeeById/' + id,
    method: 'get'
  });
};

// 新增临停规则
export const createParkFee = (data) => {
  return $({
    url: '/console/park/fee/createParkFee',
    method: 'post',
    data
  });
};

// 临停规则修改
export const updateParkFee = (data) => {
  return $({
    url: '/console/park/fee/updateParkFee',
    method: 'post',
    data
  });
};

// 删除临停规则
export const deleteParkFee = (id) => {
  return $({
    url: '/console/park/fee/deleteParkFee/' + id,
    method: 'post'
  });
};

//提交审核临停规则
export const submitAuditParkFee = (id) => {
  return $({
    url: '/console/park/fee/submitAuditParkFee/' + id,
    method: 'post'
  });
};

// 撤销临停规则
export const cancelParkFee = (id) => {
  return $({
    url: '/console/park/fee/cancelParkFee/' + id,
    method: 'post'
  });
};

// 预览规则
export const previewCalcModel = (data) => {
  return $({
    url: '/console/park/fee/previewCalcModel',
    method: 'post',
    data
  });
};
// 申请退款
export const applyRefund = (data) => {
  return $({
    url: '/console/park/fee/parkPayRecords/refundApply',
    method: 'post',
    data
  });
};
//获取电话号码
export const getPhone = (id) => {
  return $({
    url: '/console/park/fee/parkRecoveryPayRecords/getPhone/' + id,
    method: 'get'
  });
}
//发送短信
export const sendMsg = (data) => {
  return $({
    url: '/console/park/fee/parkRecoveryPayRecords/sendRecoverMessage',
    method: 'post',
    data
  });
}
