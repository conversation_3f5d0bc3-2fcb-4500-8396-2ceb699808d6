<template>
  <div class="container">
    <car-free-search @form-search="searchParkInfoList" @reset="resetParamsAndData" />
    <car-free-table ref="table" />
  </div>
</template>

<script setup name="CarFree">
import CarFreeSearch from './emergencyContactSearch.vue';
import CarFreeTable from './emergencyContactTable.vue';
import { ref, reactive, onActivated } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
onActivated(() => {
  table.value.getList(params);
});
</script>
