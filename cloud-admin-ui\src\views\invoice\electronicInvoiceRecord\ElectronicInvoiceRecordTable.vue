<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="invoiceService.exportInvoices"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="250">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleDetail(scope.row, addForm)"> 查看明细 &ensp;</el-button>
            <el-upload
              v-if="scope.row.state !== 1"
              style="display: inline"
              :limit="1"
              :action="uploadUrl"
              :data="{ id: scope.row.id }"
              :headers="headers"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :on-success="(response, file, fileList) => onSuccessUpload(response, scope.row.id, scope.row.email)"
            >
              <el-button link type="primary"> 人工开票 </el-button>
            </el-upload>
            <br />
            <el-button link type="primary" @click="queryEInvoice(scope.row, addForm)"> 查询结果 </el-button>
            <el-button link type="primary" @click="sendingEInvoiceMail(scope.row, addForm)"> 发送邮件 </el-button>
            <el-button link type="primary" @click="cancelInvoice(scope.row, addForm)"> 红冲 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="relative_orders" label="关联订单" align="center">
          <template v-slot="scope">
            <el-link type="primary" @click="relationOrder(scope.row)">{{ scope.row.relative_orders }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="prk_park_name" label="停车场名称" align="center" width="200" />
        <el-table-column prop="money" label="开票金额" align="center" />
        <el-table-column prop="state_desc" label="开票状态" align="center" />
        <el-table-column prop="company_name" label="开票公司" align="center" />
        <el-table-column prop="name" label="会员昵称" align="center" />
        <el-table-column prop="mbr_mobile" label="会员手机号" align="center" />
        <el-table-column prop="contact_mobile" label="公司电话" align="center" />
        <el-table-column prop="title_type_desc" label="抬头类型" align="center" />
        <el-table-column prop="fee_type_desc" label="费用类型" align="center" />
        <el-table-column prop="invoice_type_desc" label="发票类型" align="center" />
        <el-table-column prop="title" label="发票抬头" align="center" />
        <el-table-column prop="attachment_name" label="附件" align="center">
          <template #default="scope">
            <el-link @click="checkInvoice(scope.row.attachment_path)" type="primary">{{ scope.row.attachment_name }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="申请时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="查看明细" v-model="handleDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="600px">
        <el-form ref="addForm" label-width="110px">
          <el-form-item label="停车场名称">
            {{ data.whiteListFrom.prk_park_name }}
          </el-form-item>
          <el-form-item label="开票金额">
            {{ data.whiteListFrom.money }}
          </el-form-item>
          <el-form-item label=" 开票状态">
            {{ data.whiteListFrom.state_desc }}
          </el-form-item>
          <el-form-item label="开票公司">
            {{ data.whiteListFrom.company_name }}
          </el-form-item>
          <el-form-item label="会员昵称">
            {{ data.whiteListFrom.name }}
          </el-form-item>
          <el-form-item label="会员手机号">
            {{ data.whiteListFrom.mbr_mobile }}
          </el-form-item>
          <el-form-item label="费用类型">
            {{ data.whiteListFrom.fee_type_desc }}
          </el-form-item>
          <el-form-item label="发票类型">
            {{ data.whiteListFrom.invoice_type_desc }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.title_type === 2" label="抬头类型">
            {{ data.whiteListFrom.title_type_desc }}
          </el-form-item>
          <el-form-item label="发票抬头">
            {{ data.whiteListFrom.title }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.invoice_type === 2" label="发票税号">
            {{ data.whiteListFrom.taxpayer_identify_no }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.invoice_type === 2" label="公司地址">
            {{ data.whiteListFrom.company_address }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.invoice_type === 2" label="公司电话">
            {{ data.whiteListFrom.contact_mobile }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.invoice_type === 2" label="公司开户行">
            {{ data.whiteListFrom.company_bank_account }}
          </el-form-item>
          <el-form-item v-if="data.whiteListFrom.title_type === 1 || data.whiteListFrom.invoice_type === 2" label="开户行账号">
            {{ data.whiteListFrom.account_no }}
          </el-form-item>
          <el-form-item label="申请时间">
            {{ data.whiteListFrom.created_at }}
          </el-form-item>
          <el-form-item label="电子邮箱">
            {{ data.whiteListFrom.email }}
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="detailCancel(addForm)">取 消</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="电子专票红冲" v-model="redDialogVisible" :close-on-click-modal="false" width="1000px">
        <el-form ref="redForm" label-width="120px" :rules="data.rules" :model="data.redForm">
          <el-form-item prop="park_name" label="申请类型">
            <el-select v-model="form.province_code" placeholder="请选择" style="width: 100%" clearable>
              <el-option key="1" label="销方申请" :value="1" />
              <el-option key="0" label="购方申请" :value="0" />
            </el-select>
          </el-form-item>
          <el-form-item prop="park_name" label="申请原因">
            <el-select v-model="form.province_code" placeholder="请选择" style="width: 100%" clearable>
              <el-option key="0" label="销方-开票有误购买方拒收" :value="2" />
              <el-option key="1" label="购方-已抵扣" :value="0" />
              <el-option key="2" label="购方-未抵扣" :value="1" />
            </el-select>
          </el-form-item>
          <el-form-item class="required" prop="invoice_title" label="应用类型">
            <el-select v-model="form.province_code" placeholder="请选择" style="width: 100%" clearable>
              <el-option key="1" label="国税" :value="1" />
              <el-option key="2" label="地税" :value="2" />
            </el-select>
          </el-form-item>
          <el-form-item prop="unit_money" label="编码表版本号">
            <el-input v-model="data.updateForm.unit_money" maxlength="50" placeholder="请输入版本号39.0" />
          </el-form-item>
          <el-form-item class="required" prop="invoice_title" label="红字信息表编码">
            <el-input v-model="data.updateForm.unit_money" maxlength="50" placeholder="请输入编码" />
            <el-button @click="longRentOrderDialogVisible = false">申请</el-button>
            <el-button @click="longRentOrderDialogVisible = false">查询</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
      <el-dialog title="关联订单" v-model="orderDialogVisible" :close-on-click-modal="false" width="1000px">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="临停费用" name="stopFee">
            <stop-fee ref="stop_fee" />
          </el-tab-pane>
          <el-tab-pane label="预约车位" name="orderCarSpace">
            <order-car-space ref="order_car_space" />
          </el-tab-pane>
        </el-tabs>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="orderDialogVisible = false">关 闭</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="关联订单" v-model="longRentOrderDialogVisible" :close-on-click-modal="false" width="1000px">
        <el-table :data="orderData" border>
          <el-table-column prop="park_name" label="停车场名称" align="center" width="200px" />
          <el-table-column prop="code" label="车位编号" align="center" width="100px" />
          <el-table-column prop="rule_name" label="规则名称" align="center" width="100px" />
          <el-table-column prop="long_rent_type_desc" label="长租类型" align="center" width="100px" />
          <el-table-column prop="product_name" label="产品名称" align="center" width="100px" />
          <el-table-column prop="product_price" label="产品金额" align="center" width="100px" />
          <el-table-column label="长租有效期" align="center" width="200px">
            <template #default="scope">
              <span>{{ scope.row.valid_start_time }}~{{ scope.row.valid_end_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="pay_state_desc" label="支付状态" align="center" width="100px" />
          <el-table-column prop="rent_state_desc" label="长租状态" align="center" width="100px" />
          <el-table-column prop="plate_no" label="车牌号" align="center" />
          <el-table-column prop="mbr_member_nickname" label="车主姓名" align="center" width="100px" />
          <el-table-column prop="mbr_member_mobile" label="手机号" align="center" />
          <el-table-column prop="renew_state_desc" label="是否续费" align="center" width="100px" />
          <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="100px" />
        </el-table>
        <el-pagination
          background
          :current-page="data.orderParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.orderParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="count"
          class="table-pagination"
          @size-change="orderHandleSizeChange"
          @current-change="orderHandleCurrentChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="longRentOrderDialogVisible = false">关 闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>
<script name="ElectronicInvoiceRecordTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getToken } from '@/utils/common';
import { useRoute } from 'vue-router';
import StopFee from './StopFee.vue';
import OrderCarSpace from './OrderCarSpace.vue';
import commonService from '@/service/common/CommonService';
import invoiceService from '@/service/invoice/InvoiceService';
import DownloadButton from '@/components/DownloadButton.vue';

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/invoice/uploadInvoiceData');
const headers = reactive({
  Authorization: getToken()
});
const route = useRoute();
const tableData = ref([]);
const loading = ref(false);
const addForm = ref();
const total = ref(0);
const count = ref(0);
const activeName = ref('stopFee');
const orderData = ref([]);
const feeType = ref('');
const stop_fee = ref(null);
const order_car_space = ref(null);
const handleDialogVisible = ref(false);
const longRentOrderDialogVisible = ref(false);
const orderDialogVisible = ref(false);
const redDialogVisible = ref(false);
const statesList = ref([]);
const params = reactive({
  park_id: undefined,
  park_name: undefined,
  page: 1,
  limit: 30
});
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  orderParams: {
    park_order_ids: undefined,
    rent_order_ids: undefined,
    reserve_order_ids: undefined,
    page: 1,
    limit: 30
  }
});

onActivated(() => {
  if ({} !== route.query && undefined !== route.query.memberId) {
    data.queryParams.member_id = route.query.memberId;
  }
  initSelects();
  // getList(data.queryParams);
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'statesList',
      enum_value: 'EnumQuestionState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    const list = response.data.statesList.filter((item) => item.value !== 0);
    statesList.value = list;
  });
};

const checkInvoice = (e) => {
  if (e === undefined || e === '' || e === null) {
    ElMessage({
      message: '发票为空！',
      type: 'error'
    });
  } else {
    window.open(e, '_blank');
  }
};

// 开票
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    this.$message.error('上传文件大小不能超过 25MB!');
  }
};
const onSuccessUpload = (response, id, email) => {
  if (response.success == true) {
    // 保存文件到电子发票记录表
    const param = {
      id: id,
      attachment_path: response.data.attachment_path,
      attachment_name: response.data.attachment_name,
      email: email
    };
    saveFile(param);
    ElMessage({
      message: response.message,
      type: 'success'
    });
    getList(data.queryParams);
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};

// 保存发票
const saveFile = (param) => {
  invoiceService.saveFile(param).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 分页查询电子发票列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  invoiceService.pagingInvoice(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 查询长租分页
const getOrderList = (params) => {
  params.page === undefined ? (params.page = 1) : (params.page = data.orderParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.orderParams.limit);
  data.orderParams = params;
  data.orderParams.id = params.id;
  data.orderParams.page = params.page;
  data.orderParams.limit = params.limit;
  invoiceService.relativeRentOrderPaging(data.orderParams).then((response) => {
    if (response.success === true) {
      orderData.value = response.data.rows;
      count.value = parseInt(response.data.total);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 获取分页参数
const orderHandleSizeChange = (val) => {
  data.orderParams.limit = val;
  getOrderList(data.orderParams);
};
// 获取分页参数
const orderHandleCurrentChange = (val) => {
  data.orderParams.page = val;
  getOrderList(data.orderParams);
};

const handleClick = (tab) => {
  if (tab.props.name === 'stopFee') {
    stop_fee.value.getList(params);
  }
  if (tab.props.name === 'orderCarSpace') {
    order_car_space.value.getList(params);
  }
};

// 查看明细
const handleDetail = (form) => {
  data.whiteListFrom = {
    prk_park_name: form.prk_park_name,
    money: form.money,
    state: form.state,
    state_desc: form.state_desc,
    company_name: form.company_name,
    name: form.name,
    contact: form.contact,
    mbr_mobile: form.mbr_mobile,
    contact_mobile: form.contact_mobile,
    title_type: form.title_type,
    title_type_desc: form.title_type_desc,
    fee_type: form.fee_type,
    fee_type_desc: form.fee_type_desc,
    invoice_type: form.invoice_type,
    invoice_type_desc: form.invoice_type_desc,
    title: form.title,
    created_at: form.created_at,
    email: form.email,
    taxpayer_identify_no: form.taxpayer_identify_no,
    company_address: form.address,
    company_bank_account: form.company_bank_account,
    account_no: form.account_no
  };
  handleDialogVisible.value = true;
};

// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const relationOrder = (row) => {
  feeType.value = row.fee_type;
  if (feeType.value === 1) {
    orderDialogVisible.value = true;
    longRentOrderDialogVisible.value = false;
    params.park_order_ids = row.park_order_id;
    params.reserve_order_ids = row.reserve_order_id;
    data.orderParams.park_order_ids = row.park_order_id;
    data.orderParams.reserve_order_ids = row.reserve_order_id;
    activeName.value = 'stopFee';
    setTimeout(function () {
      stop_fee.value.getList(data.orderParams);
    }, 100);
  } else if (feeType.value === 2) {
    data.orderParams.rent_order_ids = row.rent_order_id;
    orderDialogVisible.value = false;
    longRentOrderDialogVisible.value = true;
    getOrderList(data.orderParams);
  }
};

// 取消
const detailCancel = (addForm) => {
  addForm.resetFields();
  handleDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};

//发送邮件
const sendingEInvoiceMail = (form) => {
  if (form.state == 3) {
    invoiceService.sendingEInvoiceMail(form.id).then((response) => {
      if (response.success == true) {
        if (response.data.code == 0) {
          ElMessage({
            message: response.data.data.send_email_result_msg,
            type: 'success'
          });
        } else {
          ElMessage({
            message: response.data.message,
            type: 'error'
          });
        }
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    ElMessage({
      message: '发票未成功开具,不能发送邮件',
      type: 'error'
    });
  }
};
//查询开票结果
const queryEInvoice = (form) => {
  if (form.state == 1 || form.state == 4) {
    invoiceService.queryEInvoice(form.id).then((response) => {
      if (response.success == true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    ElMessage({
      message: '发票成功无需查询结果',
      type: 'error'
    });
  }
};
//红冲
const cancelInvoice = (form) => {
  if (form.state == 3) {
    if (form.invoice_type == 2) {
      //专票需要进行红字信息表申请
      //弹出信息框
      ElMessageBox.confirm('电子专票红冲需要否进行红字信息表申请，是否申请？', '提示', {
        confirmButtonText: '申请',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        invoiceService
          .cancelInvoice(form.id)
          .then((response) => {
            if (response.success === true) {
              ElMessage({
                message: response.message,
                type: 'success'
              });
              //1、返回申请流水号及是否提交成功的状态 terminal_code apply_sn
              //2、弹窗窗口，人工点击按钮通过流水号进行查询下载红字信息表获取编码号 获取red_form_sn编码 red_form_status处理状态
              //3.处理状态未2、审核通过，才可以进行红冲申请
            } else {
              ElMessage({
                message: response.detail_message != '' ? response.detail_message : response.message,
                type: 'error'
              });
            }
          })
          .catch(() => {
            getList(data.queryParams);
          });
      });
    } else {
      //普票直接红冲
      invoiceService.cancelInvoice(form.id).then((response) => {
        if (response.success == true) {
          if (response.data.code == 0) {
            ElMessage({
              message: response.data.message,
              type: 'success'
            });
          } else {
            ElMessage({
              message: response.data.message,
              type: 'error'
            });
          }
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    }
  } else {
    ElMessage({
      message: '发票未成功开具,不能红冲作废',
      type: 'error'
    });
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
