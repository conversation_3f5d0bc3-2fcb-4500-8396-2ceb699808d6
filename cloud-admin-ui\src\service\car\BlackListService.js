import * as blackList from '@/api/car/BlackListApi';

/**
 * 黑名单
 */
export default {
  /**
   * 分页查询
   */
  pagingBlackLists(data) {
    return new Promise((resolve, reject) => {
      try {
        blackList.pagingBlackLists(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增黑名单
   */
  createBlackList(data) {
    return new Promise((resolve, reject) => {
      try {
        blackList.createBlackList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改黑名单
   */
  updateBlackList(data) {
    return new Promise((resolve, reject) => {
      try {
        blackList.updateBlackList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除黑名单
   */
  deleteBlackList(data) {
    return new Promise((resolve, reject) => {
      try {
        blackList.deleteBlackList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 导出黑名单
   */
  exportBlackLists(data) {
    return new Promise((resolve, reject) => {
      try {
        blackList.exportBlackLists(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
