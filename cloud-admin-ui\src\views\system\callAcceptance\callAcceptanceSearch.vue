<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.park_name" placeholder="车场名称" /></form-search-item>
    <!-- <form-search-item> <el-input v-model="form.queryParams.mbr_member_name" placeholder="姓名" /></form-search-item> -->
    <!-- <form-search-item> <el-input v-model="form.queryParams.mbr_member_mobile" placeholder="手机号" /></form-search-item> -->
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarFreeSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '@/views/car/ParkFindBack.vue';
import { onMounted, reactive, ref } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    // park_id: undefined,
    // space_code: '',
    // plate_no: '',
    // mbr_member_name: '',
    // mbr_member_mobile: '',
    // audit_states: [],
    // states: [],
    page: 1,
    limit: 30
  }
});
const statesList = ref([]);
const auditStatesList = ref([]);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'auditStatesList', enum_value: 'EnumAuditState' },
    { enum_key: 'statesList', enum_value: 'EnumBlackWhiteListState' }
  ];
  commonService.findEnums('audit', param).then((response) => {
    auditStatesList.value = response.data.auditStatesList;
  });
  commonService.findEnums('park', param).then((response) => {
    statesList.value = response.data.statesList;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: '',
    // space_code: '',
    // plate_no: '',
    // mbr_member_name: '',
    // mbr_member_mobile: '',
    // audit_states: [],
    // states: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  console.log(val[0].park_name);
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
