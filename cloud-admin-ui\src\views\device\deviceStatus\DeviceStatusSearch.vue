<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="停车场名称"
    /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.sn" placeholder="设备序列号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.dev_device_name" placeholder="设备名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" multiple style="width: 100%" placeholder="设备类型">
        <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.ip" placeholder="设备地址" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" multiple style="width: 100%" placeholder="设备状态">
        <el-option v-for="item in statesList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.onlines" multiple style="width: 100%" placeholder="在线状态">
        <el-option v-for="item in onlineStatesList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
    <park-find-back :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="DeviceStatusSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onActivated } from 'vue';
const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const park_name = ref('');
const deviceTypeList = ref([]);
const statesList = ref([]);
const onlineStatesList = ref([]);
const form = reactive({
  queryParams: {
    park_name: '',
    sn: '',
    dev_device_name: '',
    types: [],
    ip: '',
    states: [],
    onlines: [],
    page: 1,
    limit: 30
  }
});
onActivated(() => {
  initSelects();
});
// 数据初始化
const initSelects = () => {
  // 设备类型
  const param = [
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    },
    {
      enum_key: 'statesList',
      enum_value: 'EnumDeviceState'
    },
    {
      enum_key: 'onlineStatesList',
      enum_value: 'EnumDeviceOnlineState'
    }
  ];
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
  });
  commonService.findEnums('park', param).then((response) => {
    statesList.value = response.data.statesList;
    onlineStatesList.value = response.data.onlineStatesList;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: '',
    sn: '',
    dev_device_name: '',
    types: [],
    ip: '',
    states: [],
    onlines: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
