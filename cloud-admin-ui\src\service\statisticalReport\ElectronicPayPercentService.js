import * as electronicPayPercentApi from '@/api/statisticalReport/ElectronicPayPercentApi';

/**
 * 电子支付占比
 */
export default {
  /**
   * 分页查询电子支付占比
   */
  pagingElectronicPay(data) {
    return new Promise((resolve, reject) => {
      try {
        electronicPayPercentApi.pagingElectronicPay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 按日导出
   */
  exportDataByDay(data) {
    return new Promise((resolve, reject) => {
      try {
        electronicPayPercentApi.exportDataByDay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 汇总导出
   */
  exportDataGather(data) {
    return new Promise((resolve, reject) => {
      try {
        electronicPayPercentApi.exportDataGather(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
