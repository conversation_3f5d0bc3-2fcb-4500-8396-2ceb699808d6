<template>
  <div class="container">
    <month-report-by-money-search @form-search="searchMonthReportByMoneyList" />
    <month-report-by-money-search-btn-groups @search="searchMonthReportByMoneyFromGroups" ref="search_btn" />
    <month-report-by-money-table ref="table" />
  </div>
</template>

<script name="MonthReportByMoney" setup>
import MonthReportByMoneySearch from './monthReport/MonthReportByMoneySearch.vue';
import MonthReportByMoneySearchBtnGroups from './monthReport/MonthReportByMoneySearchBtnGroups.vue';
import MonthReportByMoneyTable from './monthReport/MonthReportByMoneyTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const search = ref(null);
const search_btn = ref(null);

const state = reactive({
  params: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});


const searchMonthReportByMoneyList = (params) => {
  state.params = params;
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findMonthReportByMoney(params);
  table.value.getList(params);
};

const searchMonthReportByMoneyFromGroups = (queryParams) => {
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findMonthReportByMoney(queryParams);
  table.value.getList(queryParams);
};

</script>
