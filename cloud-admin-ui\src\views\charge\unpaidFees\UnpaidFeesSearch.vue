<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" placeholder="请选择车场" :readonly="true" @click="authCharge(true)" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.order_no" placeholder="订单号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.order_states" style="width: 100%" placeholder="订单状态" multiple>
        <el-option v-for="item in orderStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.park_region_id" style="width: 100%" placeholder="子场名称">
        <el-option v-for="item in childList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.inDateRange" type="datetimerange" style="width: 100%" range-separator="至"
        start-placeholder="入场开始日期" :shortcuts="shortcuts" end-placeholder="入场结束日期" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime" />
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.outDateRange" type="datetimerange" style="width: 100%" range-separator="至"
        start-placeholder="出场开始日期" :shortcuts="shortcuts" end-placeholder="出场结束日期" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime" />
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.payTime" type="datetimerange" style="width: 100%" range-separator="至"
        start-placeholder="支付开始日期" :shortcuts="shortcuts" end-placeholder="支付接收日期" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss" :default-time="defaultTime" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.stop_car_type" style="width: 100%" placeholder="停车类型">
        <el-option v-for="item in stopCarTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.pay_methods" style="width: 100%" placeholder="支付方式" multiple>
        <el-option v-for="item in payTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.pay_channels" style="width: 100%" placeholder="支付渠道" multiple>
        <el-option v-for="item in payChannelList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.refund_states" style="width: 100%" placeholder="退款状态" multiple>
        <el-option v-for="item in refundStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.in_gateway_id" style="width: 100%" placeholder="入场通道"
        @click="listParkGateway">
        <el-option v-for="item in inGatewayList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.out_gateway_id" style="width: 100%" placeholder="出场通道"
        @click="listParkGateway">
        <el-option v-for="item in outGatewayList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.car_types" style="width: 100%" placeholder="车辆类型" multiple>
        <el-option v-for="item in carTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.event_no" placeholder="事件编号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.event_type_id" style="width: 100%" placeholder="事件类型">
        <el-option v-for="item in eventTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible"
    :before-close="handleClose">
    <park-fee-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag"
      @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="ParkFeeSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import parkFeeService from '@/service/charge/ParkFeeService';
import commonService from '@/service/common/CommonService';
import parkSpaceService from '@/service/park/ParkSpaceService';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import ParkFeeFindBack from './UnpaidFeesFindBack.vue';

const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const childList = ref([]);
const carTypeList = ref([]);
const inGatewayList = ref([]);
const outGatewayList = ref([]);
const orderStateList = ref([]);
const payTypeList = ref([]);
const payChannelList = ref([]);
const stopCarTypeList = ref([]);
const refundStateList = ref([]);
const eventTypeList = ref([
  { key: '跟车事件', value: 85 },
  { key: '折返事件', value: 84 },
  { key: '断网补录离场', value: 6 },
]);
const eventNumberList = ref([]);
const form = reactive({
  queryParams: {
    order_no: undefined,
    plate_no: undefined,
    order_states: [],
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    in_gateway_id: undefined,
    out_gateway_id: undefined,
    car_types: [],
    pay_type: [],
    refund_states: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: undefined,
    out_end_time: undefined,
    stop_car_type: undefined,
    event_type_id: undefined,
    event_no: undefined,
    page: 1,
    limit: 30
  },
  inDateRange: [],
  outDateRange: [],
  payTime: []
});

onMounted(() => {
  initSelects();
  form.queryParams.order_states = [4];
  form.outDateRange = [dayjs().format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59'];
  form.queryParams.out_start_time = form.outDateRange[0];
  form.queryParams.out_end_time = form.outDateRange[1];

  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    parkSpaceService.listParkRegion(form.queryParams.park_id).then((response) => {
      childList.value = response;
      form.queryParams.park_region_id = response[0].id;
      this.listParkGateway();
      const query = Object.assign(form.queryParams, {});
      emits('form-search', query);
    });
  }
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType'
    },
    {
      enum_key: 'orderStateList',
      enum_value: 'EnumOrderState'
    },
    {
      enum_key: 'inGatewayList',
      enum_value: 'EnumInGateway'
    },
    {
      enum_key: 'outGatewayList',
      enum_value: 'EnumoutGateway'
    },
    {
      enum_key: 'payTypeList',
      enum_value: 'EnumPayMethod'
    },
    {
      enum_key: 'payChannelList',
      enum_value: 'EnumPayChannel'
    },
    {
      enum_key: 'refundStateList',
      enum_value: 'EnumRefundState'
    },
    {
      enum_key: 'stopCarTypeList',
      enum_value: 'EnumStopCarType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    carTypeList.value = response.data.carTypeList;
    stopCarTypeList.value = response.data.stopCarTypeList;
  });
  commonService.findEnums('order', param).then((response) => {
    orderStateList.value = response.data.orderStateList;
    payTypeList.value = response.data.payTypeList;
    payChannelList.value = response.data.payChannelList;
    refundStateList.value = response.data.refundStateList;
  });
};
const shortcuts = [
  {
    text: '最近三天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
      return [start, end];
    }
  },
  {
    text: '最近一年',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
      return [start, end];
    }
  }
];
const defaultTime = [
  new Date(2000, 1, 1, 0, 0, 0),  // 开始时间默认00:00:00
  new Date(2000, 1, 1, 23, 59, 59) // 结束时间默认23:59:59
];
const handleDataSearch = () => {
  if (form.inDateRange?.length > 0) {
    form.queryParams.in_start_time = form.inDateRange[0];
    form.queryParams.in_end_time = form.inDateRange[1];
  } else {
    form.queryParams.in_start_time = undefined;
    form.queryParams.in_end_time = undefined;
  }
  if (form.outDateRange?.length > 0) {
    form.queryParams.out_start_time = form.outDateRange[0];
    form.queryParams.out_end_time = form.outDateRange[1];
  } else {
    form.queryParams.out_start_time = undefined;
    form.queryParams.out_end_time = undefined;
  }
  if (form.payTime?.length > 0) {
    form.queryParams.pay_start_time = form.payTime[0];
    form.queryParams.pay_end_time = form.payTime[1];
  } else {
    form.queryParams.pay_start_time = undefined;
    form.queryParams.pay_end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  childList.value = [];
  inGatewayList.value = [];
  outGatewayList.value = [];
  form.inDateRange = [];
  form.outDateRange = [dayjs().format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59'];
  form.payTime = [];
  form.queryParams = {
    order_no: undefined,
    plate_no: undefined,
    order_states: [4],
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    in_gateway_id: undefined,
    out_gateway_id: undefined,
    car_types: [],
    pay_type: [],
    refund_states: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: form.outDateRange[0],
    out_end_time: form.outDateRange[1],
    stop_car_type: undefined,
    event_type_id: undefined,
    event_no: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  if (val.length === 0) {
    form.queryParams.park_id = undefined;
    form.queryParams.park_name = undefined;
    childList.value = [];
  } else {
    form.queryParams.park_id = val[0].park_id;
    form.queryParams.park_name = val[0].park_name;
    parkSpaceService.listParkRegion(form.queryParams.park_id).then((response) => {
      childList.value = response;
      form.queryParams.park_region_id = response[0].id;
    });
  }
};

const listParkGateway = () => {
  if (form.queryParams.park_region_id !== undefined && form.queryParams.park_region_id !== '') {
    parkFeeService.listParkGateway(form.queryParams.park_region_id).then((response) => {
      inGatewayList.value = response;
      outGatewayList.value = response;
    });
  }
};
</script>
<style lang="scss" scoped></style>
