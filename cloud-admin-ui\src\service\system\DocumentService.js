import * as document from '@/api/system/DocumentApi';

/**
 * 文档服务层
 */
export default {
  /**
   * 查询文档分类树形
   */
  docTypeTree() {
    return new Promise((resolve, reject) => {
      try {
        document.docTypeTree().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 添加文档分类
   */
  createDocType(data) {
    return new Promise((resolve, reject) => {
      try {
        document.createDocType(data).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除文档分类
   */
  deleteDocType(data) {
    return new Promise((resolve, reject) => {
      try {
        document.deleteDocType(data).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询文档(按照文档分类)
   */
  pagingDocument(data) {
    return new Promise((resolve, reject) => {
      try {
        document.pagingDocument(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 添加文档
   */
  createDocument(data) {
    return new Promise((resolve, reject) => {
      try {
        document.createDocument(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改文档
   */
  updateDocument(data) {
    return new Promise((resolve, reject) => {
      try {
        document.updateDocument(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除文档
   */
  deleteDocument(data) {
    return new Promise((resolve, reject) => {
      try {
        document.deleteDocument(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 通过文档ID获取文档信息
   */
  getDocumentById(data) {
    return new Promise((resolve, reject) => {
      try {
        document.getDocumentById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取文档分类名称
   */
  getDocTypeName(data) {
    return new Promise((resolve, reject) => {
      try {
        document.getDocTypeName(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
