<template>
  <el-input :readonly="true" @click="clickAction" :placeholder="placeholder">
    <template #suffix>
      <el-icon @click="clear" class="closeBtn"><CircleClose /></el-icon>
    </template>
  </el-input>
</template>

<script setup>
import { ref } from 'vue';
const props = defineProps({
  value: String,
  placeholder: String
});
const emit = defineEmits(['charge', 'clear']);
const clickAction = () => {
  emit('charge');
};

const placeholder = ref(props.placeholder);

const clear = () => {
  emit('clear');
};
</script>

<style lang="scss" scoped>
.closeBtn {
  visibility: hidden;
  cursor: pointer;
}
.el-input--suffix {
  &:hover {
    .closeBtn {
      visibility: visible;
    }
  }
}
</style>
