<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-28 18:16:00
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\statisticalReport\ParkingCarFlow.vue
 * @Description: {}
-->
<template>
  <div class="container">
    <parking-car-flow-search
      :radioType="radio"
      @form-search="(param) => (radio === '1' ? searchParkingCarFlowList(param) : searchParkingCarFlowChart(param))"
      @reset="resetParamsAndData"
    />
    <parking-car-flow-tab v-model:radio="radio" />
    <parking-car-flow-table ref="table" v-show="radio === '1'" />
    <parking-car-flow-chart ref="chart" v-if="radio !== '1'" :radioType="radio" />
  </div>
</template>

<script name="ParkingCarFlow" setup>
import ParkingCarFlowSearch from './parkingCarFlow/ParkingCarFlowSearch.vue';
import ParkingCarFlowTable from './parkingCarFlow/ParkingCarFlowTable.vue';
import ParkingCarFlowTab from './parkingCarFlow/ParkingCarFlowTab.vue';
import ParkingCarFlowChart from './parkingCarFlow/ParkingCarFlowChart.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const chart = ref(null);
const params = reactive({});
const radio = ref('1');

const searchParkingCarFlowList = (queryParams) => {
  table.value.getList(queryParams);
};
const searchParkingCarFlowChart = (queryParams) => {
  console.log('searchParkingCarFlowChart');
  setTimeout(() => {
    chart.value.getData(queryParams);
  }, 300);
};
const resetParamsAndData = (backDate) => {
  table.value.getList(backDate);
};
</script>
