<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.nick_name" placeholder="会员昵称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.mobile" placeholder="手机号" /></form-search-item>
    <form-search-item>
      <el-row :gutter="5">
        <el-col :span="6">
          <el-select v-model="form.queryParams.dateType" placeholder="时间类型" style="width: 100%">
            <el-option label="注册时间" value="1"></el-option>
            <el-option label="最近缴费时间" value="2"></el-option>
          </el-select>
        </el-col>
        <el-col :span="18">
          <el-date-picker
            v-model="form.dateRange"
            type="datetimerange"
            style="width: 100%"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
          />
        </el-col>
      </el-row>
    </form-search-item>
  </FormSearch>
</template>

<script name="MemberInfoSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { ElMessage, dayjs } from 'element-plus';
import { reactive } from 'vue';
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    nick_name: undefined,
    mobile: undefined,
    dateType: undefined,
    register_start_time: undefined,
    register_end_time: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

const handleDataSearch = () => {
  if (form.dateType !== null && form.dateRange.length !== 0 && form.queryParams.dateType === undefined) {
    ElMessage({
      message: '请选择时间类型',
      type: 'warning'
    });
    return;
  }
  if (form.queryParams.dateType === '1') {
    if (undefined != form.dateRange && form.dateRange.length > 0) {
      form.queryParams.register_start_time = form.dateRange[0];
      form.queryParams.register_end_time = form.dateRange[1];
      form.queryParams.start_time = undefined;
      form.queryParams.end_time = undefined;
    }
    if (null === form.dateRange) {
      form.queryParams.register_start_time = undefined;
      form.queryParams.register_end_time = undefined;
      form.queryParams.start_time = undefined;
      form.queryParams.end_time = undefined;
    }
  } else if (form.queryParams.dateType === '2') {
    if (undefined != form.dateRange && form.dateRange.length > 0) {
      form.queryParams.start_time = form.dateRange[0];
      form.queryParams.end_time = form.dateRange[1];
      form.queryParams.register_start_time = undefined;
      form.queryParams.register_end_time = undefined;
    }
    if (null === form.dateRange) {
      form.queryParams.register_start_time = undefined;
      form.queryParams.register_end_time = undefined;
      form.queryParams.start_time = undefined;
      form.queryParams.end_time = undefined;
    }
  }

  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  (form.dateRange = []),
    (form.queryParams = {
      nick_name: undefined,
      mobile: undefined,
      dateType: undefined,
      register_start_time: undefined,
      register_end_time: undefined,
      start_time: undefined,
      end_time: undefined,
      page: 1,
      limit: 30
    });
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
