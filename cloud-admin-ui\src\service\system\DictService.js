import * as dict from '@/api/system/DictApi';

/**
 * 字典
 */
export default {
  /**
   * 分页查询
   */
  pagingDictType(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.pagingDictType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建字典分类
   */
  createDictType(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.createDictType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改字典分类
   */
  updateDictType(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.updateDictType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除字典分类
   */
  deleteDictType(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.deleteDictType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询
   */
  pagingDict(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.pagingDict(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建字典
   */
  createDict(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.createDict(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改字典
   */
  updateDict(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.updateDict(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除字典
   */
  deleteDict(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.deleteDict(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询字典分类
   */
  getDictType(data) {
    return new Promise((resolve, reject) => {
      try {
        dict.getDictType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询字典分类
   */
  getExpirationTime() {
    return new Promise((resolve, reject) => {
      try {
        dict.getExpirationTime().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通过code查询字典列表
   */
  getDictsList(code) {
    return new Promise((resolve, reject) => {
      try {
        dict.getDictsList(code).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
