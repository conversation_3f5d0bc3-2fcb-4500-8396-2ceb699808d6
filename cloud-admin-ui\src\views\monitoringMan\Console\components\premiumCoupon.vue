<template>
  <el-dialog v-model="dialogVisible" title="优免券信息" width="700px" :before-close="handleSpecialPassClose" :close-on-click-modal="false">
    <el-table :data="tableData" v-loading="loading" border style="max-height: 500px; overflow-y: auto">
      <el-table-column type="index" label="序号" align="center" width="60" />
      <el-table-column prop="coupon_no" label="卡券编号" align="center" />
      <el-table-column prop="merchant_name" label="发放商家" align="center" />
      <el-table-column prop="type_desc" label="券类型" align="center" />
      <el-table-column prop="expire_time" label="过期时间" align="center" />
    </el-table>
    <div>
      <!-- <el-space>
        <el-button type="primary" @click="calcSpecialPassCarFee(specialPassFormRef)">车辆计费</el-button>
        <el-button @click="resetSpecialPassCarFee(specialPassFormRef)">重 置</el-button>
      </el-space> -->
    </div>
  </el-dialog>
</template>
<script name="onTocars" setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { onMounted, ref } from 'vue';
const duty = useDuty();
const tableData = ref([]);
const dialogVisible = ref(false);
const getData = () => {
  UnattendedApi.queryCouponsByPlateNo(duty.callInfo.park_id, duty.callInfo.plate_no).then((res) => {
    let ary = [
      { name: res.data.rows[0].contact_name1, phone: res.data.rows[0].contact_mobile1, no: '' },
      { name: res.data.rows[1].contact_name1, phone: res.data.rows[1].contact_mobile1, no: '' },
      { name: res.data.rows[2].contact_name1, phone: res.data.rows[2].contact_mobile1, no: '' }
    ];
    tableData.value = ary;
  });
};
onMounted(() => {
  // getList(data.queryParams);
  // getData();
});

defineExpose({
  dialogVisible,
  getData
});
</script>
<style lang="scss" scoped></style>
