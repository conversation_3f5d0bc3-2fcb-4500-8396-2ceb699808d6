<template>
  <div class="container">
    <fee-evasion-orders-search @form-search="searchFeeEvasionOrdersList" />
    <fee-evasion-orders-table ref="table" />
  </div>
</template>

<script name="FeeEvasionOrders" setup>
import FeeEvasionOrdersSearch from './feeEvasionOrders/FeeEvasionOrdersSearch.vue';
import FeeEvasionOrdersTable from './feeEvasionOrders/FeeEvasionOrdersTable.vue';
import { ref } from 'vue';

const table = ref(null);

const searchFeeEvasionOrdersList = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
