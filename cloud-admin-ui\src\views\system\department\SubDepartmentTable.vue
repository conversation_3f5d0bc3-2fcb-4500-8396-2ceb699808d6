<template>
  <div>
    <!--表格内容栏-->
    <el-table ref="multipleTable" v-loading="loading" :data="tableData" border tooltip-effect="dark">
      <el-table-column prop="action" label="操作" align="center" width="140">
        <template v-slot="scope">
          <el-link type="primary" :underline="false" @click="handleEdit('edit', scope.row)"> 修改&ensp; </el-link>
          <el-link type="danger" :underline="false" @click="handleDel(scope.row)"> 删除&ensp; </el-link>
        </template>
      </el-table-column>
      <el-table-column prop="name" label="部门名称" align="center" />
    </el-table>

    <!-- 部门新增/修改 -->
    <el-dialog :title="data.dialogTitle == 'add' ? '新增部门' : '修改部门'" v-model="data.dialogVisible" @close="closeAddDialog(addForm)" width="30%">
      <el-form ref="addForm" :model="data.form" :rules="data.rules" label-width="100px">
        <template v-if="data.dialogTitle === 'add'">
          <el-form-item label="上级部门">
            <el-input v-model="data.parentDepartmentName" readonly />
          </el-form-item>
        </template>
        <el-form-item label="部门名称" prop="name">
          <el-input v-model="data.form.name" />
        </el-form-item>
        <el-form-item label="路径ID" prop="pathId">
          <el-input v-model="data.form.path_id" />
        </el-form-item>
        <el-form-item label="权限" prop="root">
          <el-input-number v-model="data.form.root" />
        </el-form-item>
      </el-form>
      <div style="text-align: center">
        <el-button @click="createCancel(addForm)"> 取 消 </el-button>
        <el-button type="primary" @click="handleSave(addForm)"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script name="SubDepartmentTable" setup>
import { reactive, onMounted, ref, getCurrentInstance } from 'vue';
import departmentService from '@/service/system/DepartmentService';
import { ElMessageBox, ElMessage } from 'element-plus';
const addForm = ref();
const { proxy } = getCurrentInstance();
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {},
  dialogTitle: 'add',
  dialogVisible: false,
  parentDepartmentName: undefined,
  deptId: 0,
  form: {
    name: undefined,
    path_id: '1',
    root: '1',
    parent_department_id: 0
  },
  rules: {
    name: [
      { required: true, message: '部门名称不能为空', trigger: 'blur' },
      { min: 1, max: 50, message: '请输入1-50个字符', trigger: 'blur' }
    ],
    employees: []
  }
});
onMounted(() => {});
const getData = (params) => {
  loading.value = true;
  data.parentDepartmentName = params.parentDepartmentName;
  delete params.parentDepartmentName;
  data.queryParams = params;
  departmentService.pagingDepartment(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//查询条件
const getSearchData = (params) => {
  loading.value = true;
  data.parentDepartmentName = params.departmentName;
  data.queryParams = params;
  departmentService.pagingDepartment(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleDel = (row) => {
  ElMessageBox.confirm('删除部门将同步删除其子部门，确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    departmentService.deleteDepartment(row.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getData(data.queryParams);
        // 调用刷新树
        proxy.$emit('flush');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
const handleEdit = (oper, dept) => {
  data.deptId = 0;
  if (oper === 'add') {
    //新增
    if (dept.name === null || dept.name === undefined) {
      ElMessage({
        message: '请先选择一个上级部门',
        type: 'warning'
      });
      return;
    }
    data.parentDepartmentName = dept.name;
    data.form = {
      name: undefined,
      parent_department_id: dept.parent_department_id
    };
  } else {
    //修改，先去查询部门信息
    data.deptId = dept.id;
    data.form = {
      path_id: dept.path_id,
      root: dept.root,
      name: dept.name,
      parent_department_id: dept.parent_id
    };
  }
  data.dialogTitle = oper;
  data.dialogVisible = true;
};
//保存
const handleSave = (addForm) => {
  addForm.validate().then(() => {
    console.log('data.deptId', data.deptId);
    if (data.deptId === 0) {
      departmentService.addDepartment(data.form).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getData(data.queryParams);
          //调用刷新树
          proxy.$emit('flush');
          addForm.resetFields();
          data.dialogVisible = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    } else {
      data.form.id = data.deptId;
      departmentService.updateDepartment(data.form, data.deptId).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getData(data.queryParams);
          //调用刷新树
          proxy.$emit('flush');
          addForm.resetFields();
          data.dialogVisible = false;
        } else {
          ElMessage({
            message: response.message,
            type: 'warning'
          });
        }
      });
    }
  });
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  data.dialogVisible = false;
};
defineExpose({ handleEdit, getData, getSearchData });
</script>
<style lang="scss"></style>
