export const rentProductRanges = [
  { label: 1, text: '1个月' },
  { label: 2, text: '3个月' },
  { label: 3, text: '6个月' },
  { label: 4, text: '12个月' }
];

export const dailyRentProductRanges = [
  { label: 2, text: '2天' },
  { label: 3, text: '3天' },
  { label: 5, text: '5天' },
  { label: 7, text: '7天' }
];

export const weeklyRentProductRanges = [
  { label: 1, text: '1周' },
  { label: 2, text: '2周' },
  { label: 3, text: '3周' },
  { label: 4, text: '4周' }
];

// 根据label获取text
export const getRentProductRangeText = (arrs, label) => {
  const range = arrs.find((item) => item.label === label);
  return range ? range.text : '--';
};
