import * as device from '@/api/device/DeviceApi';

/**
 * 设备
 */
export default {
  /**
   * 分页查询
   */
  pagingDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        device.pagingDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询设备列表数据
   */
  getDeviceFactoryList(data) {
    return new Promise((resolve, reject) => {
      try {
        device.getDeviceFactoryList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建设备
   */
  createDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        device.createDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改设备
   */
  updateDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        device.updateDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除设备
   */
  deleteDevice(data) {
    return new Promise((resolve, reject) => {
      try {
        device.deleteDevice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 厂商列表
   */
  listDeviceFactory(data) {
    return new Promise((resolve, reject) => {
      try {
        device.listDeviceFactory(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
