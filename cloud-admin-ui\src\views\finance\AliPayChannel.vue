<template>
  <div style="margin: 10px 0px">
    <ali-pay-channel-search @form-search="searchAilPayChannelList" @reset="resetParamsAndData" />
    <ali-pay-channel-table ref="table" />
  </div>
</template>

<script name="AliPayChannel" setup>
import AliPayChannelSearch from './aliPayChannel/AliPayChannelSearch.vue';
import AliPayChannelTable from './aliPayChannel/AliPayChannelTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchAilPayChannelList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
defineExpose({
  searchAilPayChannelList
});
</script>
