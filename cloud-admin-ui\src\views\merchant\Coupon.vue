<template>
  <div class="container">
    <coupon-search @form-search="searchCoupons" @reset="resetParamsAndData" />
    <coupon-table ref="table" />  
  </div>
</template>

<script setup name="Coupon">
import CouponSearch from './coupon/CouponSearch.vue';
import CouponTable from './coupon/CouponTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchCoupons = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
