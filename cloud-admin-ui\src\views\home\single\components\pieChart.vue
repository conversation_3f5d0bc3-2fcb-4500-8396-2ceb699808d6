<template>
  <div ref="chartRef" class="chart-warp"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { markRaw, onBeforeUnmount, onMounted, ref, nextTick } from 'vue';

const props = defineProps({
  // 颜色数组
  color: {
    type: Array,
    default: () => ['#3B8FFF', '#F94E4E', '#FFA900']
  },
  // 是否玫瑰图
  rose: {
    type: Boolean,
    default: false
  },
  // 是否环形图
  circle: {
    type: Boolean,
    default: false
  },
  // 图例布局方向
  legendOrient: {
    type: String,
    default: 'horizontal'
  },
  // 环形半径
  radius: {
    type: [String, Array],
    default: () => ['45%', '65%']
  },
  // 中心
  center: {
    type: Array,
    default: () => ['50%', '50%']
  },
  border: {
    type: Number,
    default: 0
  },
  showLengend: {
    type: Boolean,
    default: true
  },
  labelFormatter: {
    type: String,
    default: '{d}%'
  },
  size: {
    type: String,
    default: 'default'
  },
  labelColor: {
    type: String,
    default: '#333'
  },
  labelAlgin: {
    type: String,
    default: 'none'
  },
  labelPosition: {
    type: String,
    default: 'outside'
  },
  legendPosition: {
    type: Object,
    default: () => {}
  },
  showLabel: {
    type: Boolean,
    default: true
  }
});
const chartRef = ref(null);
let chartInstance = null;
onMounted(async () => {
  await nextTick();
  chartInstance = markRaw(echarts.init(chartRef.value));
  window.addEventListener('resize', resizeHandler);
});

const resizeHandler = () => {
  if (!chartInstance) return;
  chartInstance.resize();
};

const setData = (data) => {
  chartInstance.clear();
  const options = {
    tooltip: {
      trigger: 'item',
      formatter: '{b} : {d}% <br/> {c}'
    },
    legend: {
      show: props.showLengend,
      orient: props.legendOrient,
      icon: 'roundRect',
      bottom: 0,
      left: 'center',
      textStyle: {
        color: '#333'
      },
      itemWidth: 10,
      itemHeight: 6
    },
    color: props.color,
    series: [
      {
        type: 'pie',
        radius: props.radius,
        center: props.center,
        roseType: props.rose ? 'radius' : '',
        itemStyle: {
          borderColor: 'rgb(11,36,92)',
          borderWidth: props.border
        },
        data: data,
        label: {
          show: props.showLabel,
          position: props.labelPosition,
          alignTo: props.labelAlgin,
          formatter: props.labelFormatter,
          color: props.labelColor
        },
        labelLine: {
          length: 0, // 第一段引导线的长度
          length2: 20, // 第二段引导线的长度
          show: true
        }
      }
    ]
  };
  if (props.legendPosition) {
    options.legend = {
      ...props.legendPosition,
      ...options.legend
    };
  }
  chartInstance.setOption(options, true);
};

const destoryChart = () => {
  if (!chartInstance) return;
  chartInstance.dispose();
  chartInstance = null;
  window.removeEventListener('resize', resizeHandler);
};

onBeforeUnmount(() => {
  destoryChart();
});

defineExpose({
  setData
});
</script>

<style lang="scss" scoped>
.chart-warp {
  width: 100%;
  height: 100%;
}
</style>
