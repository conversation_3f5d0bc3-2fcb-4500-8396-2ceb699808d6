<template>
  <el-row :gutter="10" class="h-full">
    <el-col :span="5" class="h-full">
      <menu-tree ref="menuTree" @checkMenuNode="checkMenuNode" />
    </el-col>
    <el-col :span="19" class="my-table-container">
      <menu-config :menu="data.menu" @getMenuTree="getMenuTree" />
      <permission-config :menu="data.menu" />
    </el-col>
  </el-row>
</template>

<script name="Menu" setup>
import MenuTree from './menu/MenuTree.vue';
import MenuConfig from './menu/MenuConfig.vue';
import PermissionConfig from './menu/PermissionConfig.vue';
import { reactive, ref } from 'vue';

const menuTree = ref();
const data = reactive({
  menu: {
    id: undefined,
    name: undefined
  }
});
// 检查菜单节点
const checkMenuNode = (menus) => {
  data.menu = menus;
};
// 获取菜单树
const getMenuTree = () => {
  menuTree.value.getMenuTree();
  data.menu = {
    id: undefined,
    name: undefined
  };
};
</script>
<style lang="scss" scoped></style>
