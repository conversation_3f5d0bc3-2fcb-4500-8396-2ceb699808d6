<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-30 16:32:49
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\system\messagePush\MessagePushCard.vue
 * @Description: {}
-->
<template>
  <div ref="cardRef">
    <el-card class="table" shadow="never">
      <el-form :model="formData" :label-width="200">
        <el-form-item v-if="tamplateMap[radioType].typeListLabel" :label="tamplateMap[radioType].typeListLabel">
          <el-checkbox-group v-model="formData.push_second_type" class="ml-4">
            <el-checkbox v-for="(item, i) in eventTypeList" :key="item.id" :label="item.name" :value="item.id" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="tamplateMap[radioType].remindTypeLabel">
          <el-checkbox-group v-model="formData.remindType" class="ml-4">
            <el-checkbox label="短信" value="0" />
            <el-checkbox label="万信" v-if="radioHide" value="1" />
          </el-checkbox-group>
        </el-form-item>
        <el-form-item :label="tamplateMap[radioType].onOffLabel">
          <el-switch v-model="formData.onOff" size="large" active-text="开启" inactive-text="关闭" />
        </el-form-item>
        <el-form-item :label="tamplateMap[radioType].durationLable || tamplateMap[radioType].durationLable1">
          <template v-if="radioType != '3' && radioType != '5' && radioType != '6' && radioType != '7'">
            <el-col :span="2" v-if="radioType != '7'"><el-input type="number" v-model="formData.duration1" /></el-col>
            <el-col :span="2" v-if="radioType != '7'"> {{ tamplateMap[radioType].unit }}</el-col>
          </template>
          <el-col :span="5">
            <el-tag type="primary" round v-if="radioType === '1'">会员</el-tag>
            <el-tag type="primary" round v-else-if="radioType === '2'">临停会员</el-tag>
            <template v-else>
              <el-tag v-for="tag in formData.pushUser1List" :key="tag.id" closable :disable-transitions="false"
                @close="handleClose(tag, 'pushUser1List')">
                {{ tag.name }}
              </el-tag>
              <el-select v-if="controller.selectPushUser1Visiable" ref="select1Ref" v-model="select1" class="w-20"
                size="small" @change="selectRole('1')" @blur="selectRole('1')">
                <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
              </el-select>
              <!-- @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm" -->
              <el-button v-else class="button-new-tag" size="small" @click="showSelection('1')"> 添加角色 </el-button>
            </template>
          </el-col>
        </el-form-item>
        <el-form-item v-show="['4', '7'].includes(radioType)" :label="tamplateMap[radioType].durationLable2">
          <el-col :span="2" v-if="radioType != '3' && radioType != '7'"><el-input type="number"
              v-model="formData.duration2" /></el-col>
          <el-col :span="2" v-if="radioType != '3' && radioType != '7'"> {{ tamplateMap[radioType].unit }}</el-col>
          <el-col :span="5">
            <el-tag v-for="tag in formData.pushUser2List" :key="tag.id" closable :disable-transitions="false"
              @close="handleClose(tag, 'pushUser2List')">
              {{ tag.name }}
            </el-tag>
            <el-select v-if="controller.selectPushUser2Visiable" ref="select2Ref" v-model="select2" class="w-20"
              size="small" @change="selectRole('2')" @blur="selectRole('2')">
              <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
            <!-- @keyup.enter="handleInputConfirm"
            @blur="handleInputConfirm" -->
            <el-button v-else class="button-new-tag" size="small" @click="showSelection('2')"> 添加角色 </el-button>
          </el-col>
        </el-form-item>
        <el-form-item :label="tamplateMap[radioType].pushContextLabel">
          <el-col :span="12">
            <el-input type="textarea" autosize :value="contentText" disabled />
          </el-col>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSubmit">保存设置</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
  <!-- <el-empty description="description"/> -->
</template>

<script name="MessagePushCard" setup>
import MessagePushService from '@/service/system/MessagePushService';
import { ElLoading, ElMessage } from 'element-plus';
import { computed, nextTick, reactive, ref, watch } from 'vue';

const eventTypeList = ref([
  { id: 84, name: '折返' },
  { id: 85, name: '跟车' },
  { id: 80, name: '人员滞留' },
  { id: 81, name: '车辆滞留' },
  { id: 96, name: '人员聚集' },
  { id: 129, name: '道闸未归位' },
  { id: 144, name: '车辆拥堵' },
  { id: 112, name: '非机动车滞留' },
])
const props = defineProps({
  radioType: {
    type: String
  }
});
// import { dayjs, ElMessage } from 'element-plus';
// import parkingCarFlowService from '@/service/statisticalReport/ParkingCarFlowService';
const tamplateMap = {
  1: {
    pushContext:
      '【惠达云停车】尊敬的用户，您在【XX停车场】的【长租服规则】即将于【到期日期】结束，为确保您的使用不受影响，可关注并登录"惠达停车小程序->月卡->我要续费"完成自助续费，如您已完成续费，可忽略此消息。祝您一路平安',
    remindTypeLabel: '长租车到期提醒方式：',
    onOffLabel: '长租车到期提醒：',
    durationLable: '提前提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '天'
  },
  2: {
    pushContext: '【惠达云停车】您的【XX停车场】的【车牌号】车辆已临停超过【duration1】天，为避免影响您的正常使用，敬请尽早续费。',
    remindTypeLabel: '车辆长期临停提醒方式：',
    onOffLabel: '车辆长期临停提醒功能：',
    durationLable: '临停天数提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '天'
  },
  3: {
    pushContext: '【XX停车场】的【事件时间】发生【事件类型】事件，车牌号为【显示车牌号码】，【发生事件的通道名称】，请及时现场处置，若为跟车/折返事件而产生欠逃费账单，请登录停车业务系统进行核实并追缴。',
    typeListLabel: '事件类型',
    remindTypeLabel: '提醒方式：',
    onOffLabel: '提醒功能：',
    durationLable: '提醒人员设置：',
    pushContextLabel: '通知内容模板：',
    unit: '分钟'
  },
  4: {
    pushContext:
      '【XX停车场】位于【所属通道】的【离线设备名称】从【开始离线时间】至【触发离线报警时间】已经离线超过【duration1/duration2】分钟，请尽快处理。',
    remindTypeLabel: '设备离线提醒方式：',
    onOffLabel: '设备离线提醒功能：',
    durationLable: '持续时间提醒：',
    pushContextLabel: '通知内容模板：',
    unit: '分钟'
  },
  5: {
    pushContext:
      '【XX停车场】贵宾车辆入场提醒，车牌号为【车牌号】的要客车辆已由车主【车主姓名】于【具体入场时间】驶入【具体车道名称】。请留意并提供相应服务。',
    remindTypeLabel: '入场功能提醒方式：',
    onOffLabel: '入场功能提醒功能：',
    durationLable: '提醒人员设置：',
    pushContextLabel: '内容模板：',
    unit: '分钟'
  },
  6: {
    pushContext:
      '【XX停车场】访客车入场提醒，车牌号为【车牌号】的访客【访客姓名】于【具体入场时间】驶入【具体车道名称】；访客类型:【访客类型】,访问事由:【来访事由】。',
    remindTypeLabel: '入场功能提醒方式：',
    onOffLabel: '入场功能提醒功能：',
    durationLable: '提醒人员设置：',
    pushContextLabel: '内容模板：',
    unit: '分钟'
  },
  7: {
    pushContext: '[XX停车场】您有一笔长租车审批业务提醒，请您尽快登录系统完成审批，车牌号为【车牌号】的车主。',
    remindTypeLabel: '月卡审批提醒方式：',
    onOffLabel: '月卡审批提醒功能：',
    durationLable1: '一级审批人员设置(物业)：',
    durationLable2: '二级审批人员设置(财务)：',
    pushContextLabel: '内容模板：'
  }
};

const cardRef = ref();
const cardRefLoading = ref();
const select1Ref = ref();
const select2Ref = ref();
const select1 = ref('');
const select2 = ref('');
const controller = reactive({
  selectPushUser1Visiable: false,
  selectPushUser2Visiable: false,
  select1Ref: select1Ref,
  select2Ref: select2Ref
});
const roleList = ref([]);
const formData = reactive({
  remindType: [],
  onOff: false,
  duration1: '5',
  duration2: '15',
  pushUser1: '',
  pushUser1List: [],
  pushUser2: '',
  pushUser2List: [],
  pushContext: '',
  push_second_type: []
});

const contentText = computed(() => {
  let retTemp = tamplateMap[props.radioType].pushContext || '';
  retTemp = retTemp.replace('duration1', formData.duration1);
  retTemp = retTemp.replace('duration2', formData.duration2);
  return retTemp;
});
const radioHide = computed(() => {
  let status = true;
  if (props.radioType == '1' || props.radioType == '2') {
    status = false;
  }
  return status;
});

const getConfig = () => {
  cardRefLoading.value = ElLoading.service({
    target: cardRef.value
  });
  MessagePushService.getMessagePushInfo({ id: props.radioType })
    .then((res) => {
      console.log('getMessagePushInfo', res);
      if (res.success) {
        // Object.assign(formData, res.data);
        let obj = res.data;
        formData.remindType = (obj && obj.remind_type && obj.remind_type.split(',')) || [];
        formData.onOff = obj && obj.on_off == 1 ? true : false;
        formData.duration1 = (obj && obj.duration1) || '';
        formData.duration2 = (obj && obj.duration2) || '';
        formData.pushUser1List = obj && obj.push_user1 ? obj.push_user1.split(',').map((i) => roleList.value.find((j) => j.id === i)) : [];
        formData.pushUser2List = obj && obj.push_user2 ? obj.push_user2.split(',').map((i) => roleList.value.find((j) => j.id === i)) : [];
        formData.push_second_type = obj.push_second_type ? obj.push_second_type.split(',').map(item => Number(item)) : []
        console.log(formData.push_second_type, obj.push_second_type)
      }
    })
    .finally(() => {
      cardRefLoading.value.close();
    });
};

const getMessagePushRoleList = () => {
  MessagePushService.getMessagePushRoleList({ page: 1, limit: 30 }).then((res) => {
    roleList.value = res.data.rows.map((i) => ({ label: i.name, value: i.id, id: i.id, name: i.name }));
    getConfig();
  });
};

const onSubmit = () => {
  if (!formData.remindType.length) {
    ElMessage({
      message: '至少选中一个提醒方式',
      type: 'warning'
    });
    return;
  }
  const params = {
    pushType: props.radioType,
    remindType: formData.remindType.join(','),
    onOff: formData.onOff ? '1' : '0',
    duration1: String(formData.duration1),
    duration2: String(formData.duration2),
    pushUser1: formData.pushUser1List.length ? formData.pushUser1List.map((i) => i.id).join(',') : '',
    pushUserName1: formData.pushUser1List.length ? formData.pushUser1List.map((i) => i.name).join(',') : '',
    pushUser2: formData.pushUser2List.length ? formData.pushUser2List.map((i) => i.id).join(',') : '',
    pushUserName2: formData.pushUser2List.length ? formData.pushUser2List.map((i) => i.name).join(',') : '',
    pushContext: contentText.value,

  };
  if (props.radioType == 3) {

    if (!formData.push_second_type || formData.push_second_type.length == 0) {
      ElMessage({
        message: '至少选中一个事件类型',
        type: 'warning'
      });
      return
    }
    params.pushSecondType = formData.push_second_type.length ? formData.push_second_type.join(',') : ''
  }
  MessagePushService.messagePushSet(params).then((res) => {
    if (res.success) {
      ElMessage({
        message: res.message ?? '推送配置成功',
        type: 'success'
      });
      getConfig();
    }
  });
};

const handleClose = (tag, listKey) => {
  formData[listKey].splice(formData[listKey].indexOf(tag), 1);
};

const selectRole = (key) => {
  // console.log('key', key, arg);
  if (key === '1') {
    controller.selectPushUser1Visiable = false;
    if (roleList.value.find((i) => i.id === select1.value)) formData.pushUser1List.push(roleList.value.find((i) => i.id === select1.value));
    select1.value = '';
  } else {
    controller.selectPushUser2Visiable = false;
    if (roleList.value.find((i) => i.id === select2.value)) formData.pushUser2List.push(roleList.value.find((i) => i.id === select2.value));
    select2.value = '';
  }
};

const showSelection = (key) => {
  if (key === '1') {
    controller.selectPushUser1Visiable = true;
    nextTick(() => {
      controller.select1Ref.focus();
    });
  } else {
    controller.selectPushUser2Visiable = true;
    nextTick(() => {
      controller.select2Ref.focus();
    });
  }
  // nextTick(() => {
  //   InputRef.value!.input!.focus()
  // })
};

watch(
  () => props.radioType,
  (newValue) => {
    formData.pushContext = tamplateMap[props.radioType].pushContext;
    if (newValue == 1 || newValue == 2) {
      formData.remindType = ['0'];
    }
    getMessagePushRoleList();
  },
  { immediate: true }
);
</script>
<style lang="scss" scoped></style>
