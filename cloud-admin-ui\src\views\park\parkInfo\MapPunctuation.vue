<template>
  <div style="height: 600px">
    <div style="margin-top: -17px">
      <el-select v-model="province_id" placeholder="请选择省份" @change="changeProvince">
        <el-option v-for="item in provinces" :key="item.code" :label="item.name" :value="item.code" />
      </el-select>
    </div>
    <div style="margin: 10px 0">
      当前位置: <span style="color: #1482f0">{{ data.address }}</span>
    </div>
    <div id="map-container"></div>
    <div id="myPageTop">
      <el-input id="tipinput" v-model="input1" placeholder="请输入关键字" />
    </div>
    <div style="float: right; margin-top: 20px">
      <el-button size="medium" @click="cancel"> 取消 </el-button>
      <el-button type="primary" size="medium" @click="submit"> 保存 </el-button>
    </div>
  </div>
</template>

<script name="MapPunctuation" setup>
import regionService from '@/service/region/RegionService';
import { reactive, ref, onMounted, getCurrentInstance } from 'vue';

const props = defineProps({
  provinceId: {
    type: String,
    required: true
  },
  longitude: {
    type: String,
    required: true
  },
  latitude: {
    type: String,
    required: true
  }
});
const { proxy } = getCurrentInstance();
const map = ref('');
const input1 = ref('');
const province_id = ref('110000');
const provinces = ref([]);
const data = reactive({
  lng: '',
  lat: '',
  address: ''
});

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  regionService.listProvince().then((response) => {
    provinces.value = response;
    initMap();
  });
};
const initMap = () => {
  map.value = new AMap.Map('map-container', {
    resizeEnable: true,
    zoom: 12
  });

  //输入提示
  const autoOptions = {
    input: 'tipinput'
  };

  // 同时引入工具条插件，比例尺插件和鹰眼插件
  const markers = [];
  AMap.plugin(
    ['AMap.PlaceSearch', 'AMap.Autocomplete', 'AMap.ToolBar', 'AMap.Scale', 'AMap.OverView', 'AMap.MapType', 'AMap.Geolocation'],
    function () {
      var auto = new AMap.Autocomplete(autoOptions);
      var placeSearch = new AMap.PlaceSearch({
        map: map.value
      }); //构造地点查询类
      auto.on('select', select); //注册监听，当选中某条记录时会触发
      function select(e) {
        console.log(e);
        placeSearch.setCity(e.poi.adcode);
        placeSearch.search(e.poi.name); //关键字查询查询
      }
      // 在图面添加工具条控件，工具条控件集成了缩放、平移、定位等功能按钮在内的组合控件
      map.value.addControl(new AMap.ToolBar());

      // 在图面添加比例尺控件，展示地图在当前层级和纬度下的比例尺
      map.value.addControl(new AMap.Scale());

      // 在图面添加类别切换控件，实现默认图层与卫星图、实施交通图层之间切换的控制
      map.value.addControl(new AMap.MapType());

      // 在图面添加定位控件，用来获取和展示用户主机所在的经纬度位置
      map.value.addControl(new AMap.Geolocation());
    }
  );
  // 修改时候回显
  if (props.provinceId !== '' && props.provinceId !== undefined) {
    province_id.value = props.provinceId;
    loadMapData(props.provinceId);
  } else {
    loadMapData(province_id.value);
  }
  if (props.longitude !== undefined && props.latitude !== undefined) {
    // 创建标记点和地理位置详细地址
    AMap.plugin('AMap.Geocoder', function () {
      const geocoder = new AMap.Geocoder({
        radius: 1000, // 以给定坐标为中心点，单位：米。取值范围：0-3000。默认值：1000
        extensions: 'base' // 返回信息的详略。默认值：base，返回基本地址信息；取值为：all，返回地址信息及附近poi、道路、道路交叉口等信息
      });
      const lnglat = [props.longitude, props.latitude];
      geocoder.getAddress(lnglat, function (status, result) {
        // 传入经纬度，根据给定坐标进行解析，把经纬度转为地址
        if (status === 'complete' && result.info === 'OK') {
          data.address = result.regeocode.formattedAddress; // result为对应的地理位置详细信息
        }
      });
    });

    const marker = new AMap.Marker({
      // 创建 Marker 点标记
      position: new AMap.LngLat(props.longitude, props.latitude), // 点标记在地图上显示的位置，默认为地图中心点
      title: '当前位置' // 鼠标滑过点标记时的文字提示，不设置则鼠标滑过点标无文字提示
    });
    map.value.add(marker); // 将创建的点标记添加到已有的地图
  }
  map.value.on('click', function (e) {
    // 点击地图进行的触发操作
    data.lng = e.lnglat.getLng();
    data.lat = e.lnglat.getLat();
    AMap.plugin('AMap.Geocoder', function () {
      // 引入Geocoder 逆向地理编码插件，把经纬度转为地址
      const geocoder = new AMap.Geocoder({
        radius: 1000, // 以给定坐标为中心点，单位：米。取值范围：0-3000。默认值：1000
        extensions: 'base' // 返回信息的详略。默认值：base，返回基本地址信息；取值为：all，返回地址信息及附近poi、道路、道路交叉口等信息
      });
      const lnglat = [e.lnglat.getLng(), e.lnglat.getLat()];
      geocoder.getAddress(lnglat, function (status, result) {
        // 传入经纬度，根据给定坐标进行解析，把经纬度转为地址
        if (status === 'complete' && result.info === 'OK') {
          data.address = result.regeocode.formattedAddress; // result为对应的地理位置详细信息
        }
      });
    });
    map.value.remove(markers); // 移除地图上已创建的点标记
    const marker = new AMap.Marker({
      // 创建 Marker 点标记
      position: new AMap.LngLat(e.lnglat.getLng(), e.lnglat.getLat()), // 点标记在地图上显示的位置，默认为地图中心点
      title: '当前位置' // 鼠标滑过点标记时的文字提示，不设置则鼠标滑过点标无文字提示
    });
    markers.push(marker); // 将marker标注用markers数组存起来，便于移除上次点击地图产生的点标记
    map.value.add(marker); // 将创建的点标记添加到已有的地图
  });
};
const changeProvince = (val) => {
  loadMapData(val);
  map.value.clearMap();
};
const loadMapData = (provinceId) => {
  let data = provinces.value.find((item) => {
    if (provinceId == item.code) {
      return item;
    }
  });
  const centerPoints = data.center.split(',');
  map.value.setCenter(centerPoints);
  proxy.$forceUpdate();
};
const cancel = () => {
  proxy.$emit('closeMapDialogVisible');
};
const submit = () => {
  proxy.$emit('checkedPunctuation', data);
  proxy.$emit('closeMapDialogVisible');
};
</script>

<style lang="scss">
#map-container {
  width: 100%;
  height: 500px;
  margin-top: 10px;
  border: 1px solid #dedede;
}

#map-container .amap-marker-label {
  position: absolute;
  z-index: 2;
  border: none;
  background-color: rgba(0, 0, 0, 0.55);
  white-space: nowrap;
  cursor: default;
  padding: 5px;
  font-size: 12px;
  line-height: 14px;
  color: #fff;
}

.amap-info-close {
  top: 10px;
  right: -10px;
  color: #c3c3c3;
  width: 14px;
  height: 14px;
}

.amap-info-content {
  padding: 0px;
}

.content-window-card {
  position: relative;
  box-shadow: none;
  bottom: 0;
  left: 0;
  width: auto;
  padding: 0;
}

.content-window-card p {
  height: 2rem;
}

.custom-info {
  border: solid 1px silver;
  width: 300px;
}

.input-card {
  display: flex;
  flex-direction: column;
  min-width: 0;
  word-wrap: break-word;
  background-color: #fff;
  background-clip: border-box;
  border-radius: 0.25rem;
  width: 22rem;
  border-width: 0;
  border-radius: 0.4rem;
  position: fixed;
  bottom: 1rem;
  right: 1rem;
  -ms-flex: 1 1 auto;
  flex: 1 1 auto;
  padding: 0.75rem 1.25rem;
}

div.info-top {
  position: relative;
  background: none repeat scroll 0 0 #f9f9f9;
  border-bottom: 1px solid #ccc;
  border-radius: 5px 5px 0 0;
  padding-left: 5px;
  margin-top: -19px;
  height: 32px;
  line-height: 32px;
  font-weight: bold;
}

//搜索框
.button-group {
  position: absolute;
  bottom: 20px;
  right: 20px;
  font-size: 12px;
  padding: 10px;
}

.button-group .button {
  height: 28px;
  line-height: 28px;
  background-color: #0d9bf2;
  color: #fff;
  border: 0;
  outline: none;
  padding-left: 5px;
  padding-right: 5px;
  border-radius: 3px;
  margin-bottom: 4px;
  cursor: pointer;
}
.button-group .inputtext {
  height: 26px;
  line-height: 26px;
  border: 1px;
  outline: none;
  padding-left: 5px;
  padding-right: 5px;
  border-radius: 3px;
  margin-bottom: 4px;
  cursor: pointer;
}

#tip {
  background-color: #fff;
  padding-left: 10px;
  padding-right: 10px;
  position: absolute;
  font-size: 12px;
  right: 10px;
  top: 20px;
  border-radius: 3px;
  border: 1px solid #ccc;
  line-height: 30px;
}

#myPageTop {
  position: absolute;
  top: 51px;
  left: 240px;
  // background: #fff none repeat scroll 0 0;
  // border: 1px solid #ccc;
  margin: 10px auto;
  padding: 6px;
  font-family: 'Microsoft Yahei', '微软雅黑', 'Pinghei';
  font-size: 14px;
}
#myPageTop label {
  margin: 0 20px 0 0;
  color: #666666;
  font-weight: normal;
}
#myPageTop input {
  width: 170px;
}
#myPageTop .column2 {
  padding-left: 25px;
}
.amap-sug-result {
  position: absolute;
  z-index: 2048;
  background-color: #fefefe;
  border: 1px solid #d1d1d1;
  bottom: auto;
}
</style>
