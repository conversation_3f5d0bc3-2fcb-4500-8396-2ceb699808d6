<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="abnormalRecordService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="日期" align="center" width="120" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="200" />
        <el-table-column prop="park_id" label="车场ID" align="center" />
        <el-table-column prop="organizational_structure" label="组织架构" align="center" width="120" />
        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count_car_in" label="入场车次" align="center" width="120" />
        <el-table-column prop="count_car_out" label="出场车次" align="center" width="120" />
        <el-table-column prop="count_sentry_operate" label="岗亭操作总计" align="center" width="120" />
        <el-table-column prop="count_special_release" label="特殊放行" align="center" width="120" />
        <el-table-column prop="count_cancel_release" label="取消放行" align="center" width="120" />
        <el-table-column prop="count_plate_no_correct" label="车牌矫正" align="center" width="120" />
        <el-table-column prop="count_entrance_range_pole" label="入口抬杆" align="center" width="120" />
        <el-table-column prop="count_exit_range_pole" label="出口抬杆" align="center" width="120" />
        <el-table-column prop="count_manual_matching_exit" label="手动匹配出场" align="center" width="120" />
        <el-table-column prop="count_repeat_entrance" label="重复入场" align="center" width="120" />
        <el-table-column prop="count_rushed_car" label="被冲车辆" align="center" width="120" />
        <el-table-column prop="count_change_rate" label="切换费率" align="center" width="120" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="AbnormalRecordTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import abnormalRecordService from '@/service/statisticalReport/AbnormalRecordService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  abnormalRecordService.pagingAbnormalRecord(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
