<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column prop="statistics_date" label="日期" align="center" width="120" />
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" width="200" />
        <el-table-column prop="park_id" label="车场ID" align="center" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <!-- <el-table-column prop="organizational_structure" label="组织架构" align="center" width="120" /> -->

        <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="count_car_in" label="入场车次" align="center" width="120" />
        <el-table-column prop="count_car_out" label="出场车次" align="center" width="120" />
        <el-table-column prop="count_sentry_operate" label="岗亭操作总计" align="center" width="120" />
        <el-table-column prop="count_special_release" label="特殊放行" align="center" width="120" />
        <el-table-column prop="count_cancel_release" label="取消放行" align="center" width="120" />
        <el-table-column prop="count_plate_no_correct" label="车牌矫正" align="center" width="120" />
        <el-table-column prop="count_entrance_range_pole" label="入口抬杆" align="center" width="120" />
        <el-table-column prop="count_exit_range_pole" label="出口抬杆" align="center" width="120" />
        <el-table-column prop="count_manual_matching_exit" label="手动匹配出场" align="center" width="120" />
        <el-table-column prop="count_repeat_entrance" label="重复入场" align="center" width="120" />
        <el-table-column prop="count_rushed_car" label="被冲车辆" align="center" width="120" />
        <el-table-column prop="count_change_rate" label="切换费率" align="center" width="120" />
      </el-table>
    </div>
         <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30,100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
     </div>
  </el-card>
</template>

<script name="AbnormalRecordTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import abnormalRecordService from '@/service/statisticalReport/AbnormalRecordService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total=ref(0)
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    limit: 30,
    page:1
  }
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
   getList({})
}
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({})
}
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(5);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const getList = (params) => {
  loading.value = true;
  data.queryParams =  {...data.queryParams,...params};
  abnormalRecordService.pagingAbnormalRecord(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value= Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end
}
</style>
