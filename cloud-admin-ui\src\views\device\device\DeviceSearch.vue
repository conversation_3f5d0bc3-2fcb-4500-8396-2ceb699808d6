<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="设备名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.dev_factory_id" style="width: 100%" placeholder="设备厂家">
        <el-option v-for="item in devFactoryList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.types" multiple style="width: 100%" placeholder="设备类型">
        <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" /> </el-select
    ></form-search-item>
  </FormSearch>
</template>

<script name="DeviceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref, onMounted } from 'vue';
import commonService from '@/service/common/CommonService';
import deviceService from '@/service/device/DeviceService';
const emits = defineEmits(['form-search']);
const deviceTypeList = ref([]);
const devFactoryList = ref([]);
const form = reactive({
  queryParams: {
    name: '',
    dev_factory_id: '',
    types: [],
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  // 设备类型
  const param = [
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    }
  ];
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
  });
  deviceService.listDeviceFactory().then((response) => {
    devFactoryList.value = response.data;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    dev_factory_id: '',
    types: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
