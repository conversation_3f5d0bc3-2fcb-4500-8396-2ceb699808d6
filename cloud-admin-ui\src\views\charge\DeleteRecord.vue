<template>
  <div class="container">
    <delete-record-search @form-search="searchDeleteRecordList" @reset="resetParamsAndData" />
    <delete-record-table ref="table" />
  </div>
</template>

<script setup name="DeleteRecord">
import DeleteRecordSearch from './deleteRecord/DeleteRecordSearch.vue';
import DeleteRecordTable from './deleteRecord/DeleteRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageDeleteRecord = (queryParams) => {
  table.value.getList(queryParams);
};

const searchDeleteRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageDeleteRecord
});
</script>
