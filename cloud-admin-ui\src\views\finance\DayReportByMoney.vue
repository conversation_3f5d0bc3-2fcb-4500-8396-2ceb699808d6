<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-28 17:21:15
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\finance\DayReportByMoney.vue
-->
<template>
  <div class="container">
    <day-report-by-money-search ref="searchRef" @form-search="searchDayReportByMoneyList" />
    <day-report-by-money-search-btn-groups @search="searchDayReportByMoneyFromGroups" ref="search_btn" />
    <day-report-by-money-table ref="table" />
  </div>
</template>

<script name="DayReportByMoney" setup>
import { reactive, ref, defineExpose } from 'vue';
import DayReportByMoneySearch from './dayReport/DayReportByMoneySearch.vue';
import DayReportByMoneySearchBtnGroups from './dayReport/DayReportByMoneySearchBtnGroups.vue';
import DayReportByMoneyTable from './dayReport/DayReportByMoneyTable.vue';

const table = ref(null);
const searchRef = ref(null);
const search = ref(null);
const search_btn = ref(null);

const state = reactive({
  params: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});


const runSearchRef = () => {
  searchRef.value.handleDataSearch();
};
defineExpose({
  runSearchRef
});
const searchDayReportByMoneyList = (params) => {
  state.params = params;

  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findDayReportByMoney(params);
  table.value.getList(params);
};

const searchDayReportByMoneyFromGroups = (queryParams) => {
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findDayReportByMoney(queryParams);
  table.value.getList(queryParams);
};
</script>
