import * as messagePush from '@/api/system/ApiMessagePush';

/**
 * 页面服务层
 */
export default {
  /**
   * 添加页面
   */
  messagePushSet(param) {
    return new Promise((resolve, reject) => {
      try {
        messagePush.messageInsertApis(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  getMessagePushInfo(param) {
    return new Promise((resolve, reject) => {
      try {
        messagePush.messageInsertInfoApis(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  getMessagePushRoleList(param) {
    return new Promise((resolve, reject) => {
      try {
        messagePush.roleListApis(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
};
