<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="黑名单" name="balck">
      <black-list ref="balckList" />
    </el-tab-pane>
    <el-tab-pane label="区域防控" name="prevent">
      <prevent-list ref="preventList" />
    </el-tab-pane>
    <el-tab-pane label="拦截记录" name="carInterceptRecord">
      <car-intercept-record ref="carInterceptRecords" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="BlackAdmin" setup>
import BlackList from './BlackList.vue';
import PreventList from './PreventList.vue';
import CarInterceptRecord from './CarInterceptRecord.vue';
import { ref, reactive, onMounted } from 'vue';

const activeName = ref('balck');
const balckList = ref(null);
const preventList = ref(null);
const carInterceptRecords = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

onMounted(() => {
  balckList.value.searchBlackList(params);
});

const handleClick = (tab) => {
  if (tab.props.name === 'balck') {
    balckList.value.searchBlackList(params);
  }
  if (tab.props.name === 'prevent') {
    preventList.value.searchPreventList(params);
  }
  if (tab.props.name === 'carInterceptRecord') {
    carInterceptRecords.value.searchCarInterceptRecord(params);
  }
};
</script>
<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
