import * as standardAgreementApi from '@/api/system/StandardAgreementApi';

/**
 * 标准协议
 */
export default {
  /**
   * 获取标准协议
   */
  getStandardLicense(data) {
    return new Promise((resolve, reject) => {
      try {
        standardAgreementApi.getStandardLicense(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 保存标准协议
   */
  saveStandardLicense(data) {
    return new Promise((resolve, reject) => {
      try {
        standardAgreementApi.saveStandardLicense(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
