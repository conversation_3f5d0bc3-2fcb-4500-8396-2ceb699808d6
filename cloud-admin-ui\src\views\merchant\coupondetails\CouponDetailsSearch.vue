<template>
  <!-- <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.merchant_name" placeholder="商户名称" /></form-search-item>
  </FormSearch> -->
  <export-button :export-func="exportCouponGrant" :params="form.queryParams"></export-button>
</template>

<script name="CouponMetaSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import exportButton from '@/components/exportButton.vue';
import { exportCouponGrant } from '@/api/merchant/CouponStatsApi';

const route = useRoute();
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    merchant_coupon_id: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onActivated(() => {
  // 数据初始化
  form.queryParams.merchant_coupon_id = route.query?.merchantCouponId;
  handleDataSearch();
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
