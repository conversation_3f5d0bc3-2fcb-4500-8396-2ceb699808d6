<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.phone" placeholder="手机号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.nick_name" placeholder="会员名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.use_state" placeholder="使用状态" style="width: 100%" clearable>
        <el-option label="未使用" :value="0" />
        <el-option label="已使用" :value="1" />
        <el-option label="已冻结" :value="2" />
        <el-option label="已过期" :value="3" />
      </el-select>
    </form-search-item>
    <template #button>
      <export-button :export-func="exportCouponGrant" :params="form.queryParams"></export-button>
    </template>
  </FormSearch>
</template>

<script name="CouponMetaSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive, ref, onActivated } from 'vue';
import { useRoute } from 'vue-router';
import exportButton from '@/components/exportButton.vue';
import { exportCouponGrant } from '@/api/merchant/CouponStatsApi';

const route = useRoute();
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    merchant_coupon_id: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onActivated(() => {
  // 数据初始化
  form.queryParams.merchant_coupon_id = route.query?.merchantCouponId;
  handleDataSearch();
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
