<template>
  <div class="container">
    <terminal-search @form-search="searchTerminal" @reset="resetParamsAndData" />
    <terminal-table ref="table" />  
  </div>
</template>

<script setup name="Terminal">
import TerminalSearch from './terminal/TerminalSearch.vue';
import TerminalTable from './terminal/TerminalTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);

const searchTerminal = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
