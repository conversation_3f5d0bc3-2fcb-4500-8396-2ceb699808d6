<template>
  <el-dialog
    v-model="dialogVisible"
    title="应急通讯录"
    width="400px"
    :before-close="handleSpecialPassClose"
    :close-on-click-modal="false"
    @open="getData"
  >
    <el-form ref="specialPassFormRef" label-position="top">
      <el-form-item prop="money" :label="'值班室电话：' + detailData.duty_phone">
        <!-- <el-input v-model="detailData.duty_phone" readonly placeholder="请输入" style="width: 100%" :min="0" :precision="2" /> -->
      </el-form-item>
    </el-form>
    <el-table :data="tableData" v-loading="loading" border style="max-height: 500px; overflow-y: auto">
      <el-table-column prop="name" label="姓名" align="center" />
      <!-- <el-table-column prop="no" label="万信号" align="center" /> -->
      <el-table-column prop="phone" label="手机号" align="center" />
    </el-table>
    <div style="margin-top: 30px; text-align: center">
      <!-- <el-space>
        <el-button type="primary" @click="calcSpecialPassCarFee(specialPassFormRef)">车辆计费</el-button>
        <el-button @click="resetSpecialPassCarFee(specialPassFormRef)">重 置</el-button>
      </el-space> -->
    </div>
  </el-dialog>
</template>
<script name="onTocars" setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { reactive, ref } from 'vue';
const duty = useDuty();
const tableData = ref([]);
const dialogVisible = ref(false);
const detailData = reactive({
  park_name: '',
  duty_phone: '',
  contact_name1: '',
  contact_mobile1: '',
  contact_name2: '',
  contact_mobile2: '',
  contact_name3: '',
  contact_mobile3: ''
});
const getData = () => {
  UnattendedApi.emergencylist({
    park_name: duty.callInfo.park_name,
    park_id: duty.callInfo.park_id,
    space_code: '',
    plate_no: '',
    mbr_member_name: '',
    mbr_member_mobile: '',
    audit_states: [],
    states: [],
    page: 1,
    limit: 30
  }).then((res) => {
    detailData.park_name = res.data.rows[0].park_name;
    detailData.duty_phone = res.data.rows[0].duty_phone;
    detailData.contact_name1 = res.data.rows[0].contact_name1;
    detailData.contact_mobile1 = res.data.rows[0].contact_mobile1;
    detailData.contact_name2 = res.data.rows[0].contact_name2;
    detailData.contact_mobile2 = res.data.rows[0].contact_mobile2;
    detailData.contact_name3 = res.data.rows[0].contact_name3;
    detailData.contact_mobile3 = res.data.rows[0].contact_mobile3;
    let ary = [];
    if (res.data.rows[0].contact_name1 && res.data.rows[0].contact_name1 != '') {
      ary.push({ name: res.data.rows[0].contact_name1, phone: res.data.rows[0].contact_mobile1 });
    }
    if (res.data.rows[0].contact_name2 && res.data.rows[0].contact_name2 != '') {
      ary.push({ name: res.data.rows[0].contact_name2, phone: res.data.rows[0].contact_mobile2 });
    }
    if (res.data.rows[0].contact_name3 && res.data.rows[0].contact_name3 != '') {
      ary.push({ name: res.data.rows[0].contact_name3, phone: res.data.rows[0].contact_mobile3 });
    }
    tableData.value = ary;
  });
};
// onMounted(() => {
//   // getList(data.queryParams);
//   getData();
// });

defineExpose({
  dialogVisible
});
</script>
<style lang="scss" scoped></style>
