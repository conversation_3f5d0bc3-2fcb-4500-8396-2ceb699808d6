/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找
export const pagingApis = (data) => {
  return $({
    url: '/console/api/pagingApis',
    method: 'post',
    data
  });
};

// api接口保存
export const createApi = (data) => {
  return $({
    url: '/console/api/createApi',
    method: 'post',
    data
  });
};

// api接口修改
export const updateApi = (data) => {
  return $({
    url: '/console/api/updateApi',
    method: 'post',
    data
  });
};

// api接口删除
export const deleteApis = (data) => {
  return $({
    url: '/console/api/deleteApis',
    method: 'post',
    data
  });
};

// 查询单条api接口
export const getApiById = (id) => {
  return $({
    url: '/console/api/getApiById/' + id,
    method: 'post'
  });
};

//api接口列表
export const apiList = () => {
  return $({
    url: '/console/api/apiList',
    method: 'get'
  });
};

//权限组列表
export const permissionGroupList = () => {
  return $({
    url: '/console/permissionGroup/permissionGroupList',
    method: 'get'
  });
};

//查询权限分组下的api树
export const getApiPermissionByGroupId = (groupId) => {
  return $({
    url: '/console/api/getApiPermissionByGroupId/' + groupId,
    method: 'post'
  });
};

// 查询角色权限分组下的api树
export const getRoleApiPermissionTree = (roleId) => {
  return $({
    url: '/console/api/getRoleApiPermissionTree/' + roleId,
    method: 'post'
  });
};
