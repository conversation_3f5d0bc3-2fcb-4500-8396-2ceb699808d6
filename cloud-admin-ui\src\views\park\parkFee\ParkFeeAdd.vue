<template>
  <div class="container">
    <el-card shadow="hover">
      <template #header>
        <div style="display: inline-block; line-height: 32px">基础设置</div>
      </template>
      <div>
        <el-form ref="addForm" :model="form" label-width="150px" :rules="rules">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="规则名称" prop="name">
                <el-input v-model="form.name" maxlength="30" show-word-limit placeholder="请输入规则名称" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆类型" prop="car_type">
                <el-select v-model="form.car_type" placeholder="车辆类型" style="width: 100%" clearable>
                  <el-option v-for="item in carTypes" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="关联子场" prop="park_region_id">
                <el-select v-model="form.park_region_id" placeholder="请选择关联子场" style="width: 100%" clearable>
                  <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="同车重新起收限制" prop="same_car_time">
                <el-input-number v-model="form.same_car_time" min="0" max="24" style="width: calc(100% - 34px)" placeholder="同车重新起收限制" />
                <div class="el-input-number-append-time">时</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计费精度" prop="fee_precision">
                <el-select v-model="form.fee_precision" placeholder="计费精度" style="width: 100%" clearable>
                  <el-option v-for="item in feePrecisions" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用精度" prop="cost_precision">
                <el-select v-model="form.cost_precision" placeholder="费用精度" style="width: 100%" clearable>
                  <el-option v-for="item in costPrecisions" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="进位方式" prop="trunc_mode">
                <el-select v-model="form.trunc_mode" placeholder="进位方式" style="width: 100%" clearable>
                  <el-option v-for="item in truncModes" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跨分段计费方式" prop="cross_time_fee_type">
                <el-select v-model="form.cross_time_fee_type" placeholder="跨分段计费方式" style="width: 100%" clearable>
                  <el-option v-for="item in crossTimeFeeTypes" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跨分段起收计费方式" prop="cross_time_start_fee_type">
                <el-select v-model="form.cross_time_start_fee_type" placeholder="跨分段起收计费方式" style="width: 100%" clearable>
                  <el-option v-for="item in crossTimeStartFeeTypes" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="单笔封顶费用" prop="bill_top_mode">
                <el-input-number
                  v-if="form.bill_top_mode == 0"
                  v-model="form.bill_top_money"
                  style="width: calc(100% - 60px)"
                  :min="1"
                  :max="10000"
                  placeholder="无"
                  :disabled="true"
                />
                <el-input-number
                  v-else
                  v-model="form.bill_top_money"
                  :min="1"
                  :max="10000"
                  show-word-limit
                  placeholder="单笔封顶费用"
                  style="width: calc(100% - 60px)"
                />
                <div class="el-input-number-append">
                  <el-checkbox v-model="form.bill_top_mode" true-label="0" false-label="1" @change="topChange" style="display: inline">
                    无
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="首日限额" prop="first_day_limit_mode">
                <el-input-number
                  v-if="form.first_day_limit_mode == 0"
                  v-model="form.first_day_limit_money"
                  style="width: calc(100% - 60px)"
                  placeholder="无"
                  :min="1"
                  :max="10000"
                  :disabled="true"
                />
                <el-input-number
                  v-else
                  v-model="form.first_day_limit_money"
                  :min="1"
                  :max="10000"
                  :precision="0"
                  placeholder="首日限额"
                  style="width: calc(100% - 60px)"
                />
                <div class="el-input-number-append">
                  <el-checkbox v-model="form.first_day_limit_mode" true-label="0" false-label="1" @change="firstChange" style="display: inline">
                    无
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="每日限额" prop="each_day_limit_mode">
                <el-input-number
                  v-if="form.each_day_limit_mode == 0"
                  v-model="form.each_day_limit_money"
                  style="width: calc(100% - 60px)"
                  placeholder="无"
                  :min="1"
                  :max="10000"
                  :disabled="true"
                />
                <el-input-number
                  v-else
                  v-model="form.each_day_limit_money"
                  :min="1"
                  :max="10000"
                  placeholder="每日限额"
                  style="width: calc(100% - 60px)"
                />
                <div class="el-input-number-append">
                  <el-checkbox v-model="form.each_day_limit_mode" true-label="0" false-label="1" @change="eachChange" style="display: inline">
                    无
                  </el-checkbox>
                </div>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="提前缴费出场时限" prop="pre_payment_time_limit">
                <el-input-number
                  v-model="form.pre_payment_time_limit"
                  min="1"
                  max="60"
                  style="width: calc(100% - 34px)"
                  placeholder="提前缴费出场时限"
                />
                <div class="el-input-number-append-time">分</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="二次免费时长" prop="pre_payment_time_limit_two">
                <el-input-number
                  v-model="form.pre_payment_time_limit_two"
                  min="1"
                  max="99999"
                  style="width: calc(100% - 34px)"
                  placeholder="二次免费时长"
                />
                <div class="el-input-number-append-time">分</div>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生效日期" class="required">
                <el-date-picker
                  v-model="form.valid_start_time"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  type="date"
                  placeholder="生效日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="超过24小时计算免费时长" class="required">
                <el-switch v-model="form.is_free_first_time" active-value="0" inactive-value="1" />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-card shadow="hover" style="margin-top: 10px">
      <template #header>
        <div style="display: inline-block; line-height: 32px">计费规则</div>
        <div class="tab-right">
          <el-button type="primary" @click="feeSettingAdd">添加规则</el-button>
        </div>
      </template>
      <div class="feeSetting">
        <div v-if="form.fee_rules === undefined || form.fee_rules.length > 0">
          <el-card class="box-card" v-for="(item, index) in form.fee_rules" :key="item.id" style="width: 20%">
            <template #header>
              <span>{{ item.name }}</span>
              <el-button class="moreEdit" type="text">···</el-button>
              <div class="hiddenButton">
                <div class="btnChild">
                  <el-link type="primary" :underline="false" @click="handleEdit(index)">编辑</el-link>
                </div>
                <div class="btnChild">
                  <el-link type="danger" :underline="false" @click="handleDelete(index)">删除</el-link>
                </div>
              </div>
            </template>
            <div class="textItem">
              <div class="tag">时间段</div>
              <div class="right">
                {{ item.start_time + '-' + item.end_time }}
              </div>
            </div>
            <div class="textItem">
              <div class="tag">计费方式</div>
              <div class="right">{{ item.type_desc }}</div>
            </div>
            <!-- 类型为1 -->
            <div v-if="item.type == 1">
              <div class="textItem">
                <div class="tag">收费金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.times_fee.money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.times_fee.free_time }}</span> 分钟
                </div>
              </div>
            </div>
            <!-- 类型为2 -->
            <div v-if="item.type == 3">
              <div class="textItem">
                <div class="tag">起收时长(分)</div>
                <div class="right">{{ item.period_fee.start_fee_time }} 分钟</div>
              </div>
              <div class="textItem">
                <div class="tag">起收单位时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">起收单位金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续费单位时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续费单位金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.period_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.period_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
            <!-- 类型为3 -->
            <div v-if="item.type == 2">
              <div class="textItem">
                <div class="tag">起收时长(分)</div>
                <div class="right">{{ item.duration_fee.start_fee_time }}分钟</div>
              </div>
              <div class="textItem">
                <div class="tag">起收金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.start_fee_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续收时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续收金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.duration_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.duration_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
            <!-- 类型4 -->
            <div v-if="item.type == 4">
              <div class="textItem">
                <div class="tag">阶段时长(分)</div>
                <div class="right">{{ item.stair_fee.stair_time }}分钟</div>
              </div>
              <div class="textItem" v-for="(money, index) in item.stair_fee.stair_moneys" :key="index">
                <div class="tag">第{{ index + 1 }}阶段金额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ money.value }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长(分)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">满24小时是否从新计算</div>
                <div class="right">
                  <span v-if="item.stair_fee.beyond_day_calculate_again_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.stair_fee.beyond_day_calculate_again_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.stair_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.stair_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额(元)</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无数据" style="height: 300px"></el-empty>
      </div>
    </el-card>
    <div class="fixed-bottom">
      <el-button @click="closeTab(addForm)">取 消</el-button>
      <el-button type="primary" @click="saveParkFee(addForm)">保 存</el-button>
    </div>
    <!-- 添加规则 -->
    <el-dialog title="添加规则" v-model="createDialogVisible" :close-on-click-modal="false" width="600px">
      <el-form ref="addFeeForm" label-width="170px" :rules="fee_rules" :model="data.feeForm">
        <el-form-item prop="name" label="分段名称">
          <el-input v-model="data.feeForm.name" />
        </el-form-item>
        <el-form-item label="时间段" class="required">
          <el-time-picker v-model="data.feeForm.start_time" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="开始时间" style="width: 45%" />
          &ensp;-&ensp;
          <el-time-picker v-model="data.feeForm.end_time" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="结束时间" style="width: 45%" />
        </el-form-item>
        <el-form-item prop="type" label="计费方式">
          <el-select v-model="data.feeForm.type" style="width: 100%" @change="findTypeDesc" ref="ruleType">
            <el-option v-for="item in feeRuleTypes" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <div v-if="data.feeForm.type == 1">
          <el-form-item label="收费金额(元)" class="required">
            <el-input-number v-model="data.times_fee.money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.times_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 2">
          <el-form-item label="起收时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.start_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收金额(元)" class="required">
            <el-input-number v-model="data.duration_fee.start_fee_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.renew_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收金额(元)" class="required">
            <el-input-number v-model="data.duration_fee.renew_fee_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.duration_fee.not_enough_time_fee_type" style="width: 100%">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input-number v-model="data.duration_fee.time_limit_money" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 3">
          <el-form-item label="起收时长(分)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收单位时长(分)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_unit_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收单位金额(元)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_unit_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.period_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收单位时长(分)" class="required">
            <el-input-number v-model="data.period_fee.renew_fee_unit_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收单位金额(元)" class="required">
            <el-input v-model="data.period_fee.renew_fee_unit_money" />
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.period_fee.not_enough_time_fee_type" style="width: 100%">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input v-model="data.period_fee.time_limit_money" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 4">
          <el-form-item label="阶段时长(分)" class="required">
            <el-select v-model="data.stair_fee.stair_time" style="width: 100%">
              <el-option v-for="item in sairTimes" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-for="(money, index) in data.stair_fee.stair_moneys"
            :label="'第' + parseInt(index + 1) + '阶段金额(元)'"
            :key="money.key"
            class="required"
          >
            <el-input-number v-model="money.value" :min="0" style="width: 75%" />&ensp;
            <div style="display: inline-block; height: 30px; margin-left: 5px; vertical-align: middle">
              <el-icon :size="30" v-if="data.stair_fee.stair_moneys.length < 24" color="#409eff" @click="addDomain"><CirclePlus /></el-icon>&ensp;
              <el-icon
                :size="30"
                v-if="data.stair_fee.stair_moneys.length > 1 || data.stair_fee.stair_moneys.length == 24"
                color="#f56c6c"
                @click="removeDomain(money)"
                ><CircleClose
              /></el-icon>
            </div>
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.stair_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="满24小时是否从新计算" class="required">
            <el-radio-group v-model="data.stair_fee.beyond_day_calculate_again_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.stair_fee.not_enough_time_fee_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input-number v-model="data.stair_fee.time_limit_money" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveFeeSetting(addFeeForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改规则 -->
    <el-dialog title="修改规则" v-model="updateDialogVisible" :close-on-click-modal="false" width="600px">
      <el-form ref="editFeeForm" label-width="170px" :rules="fee_rules" :model="data.feeForm">
        <el-form-item prop="name" label="分段名称">
          <el-input v-model="data.feeForm.name" />
        </el-form-item>
        <el-form-item label="时间段" class="required">
          <el-time-picker v-model="data.feeForm.start_time" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="开始时间" style="width: 45%" />
          &ensp;-&ensp;
          <el-time-picker v-model="data.feeForm.end_time" format="HH:mm:ss" value-format="HH:mm:ss" placeholder="结束时间" style="width: 45%" />
        </el-form-item>
        <el-form-item prop="type" label="计费方式">
          <el-select v-model="data.feeForm.type" style="width: 100%">
            <el-option v-for="item in feeRuleTypes" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <div v-if="data.feeForm.type == 1">
          <el-form-item label="收费金额(元)" class="required">
            <el-input-number v-model="data.times_fee.money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.times_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 2">
          <el-form-item label="起收时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.start_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收金额(元)" class="required">
            <el-input-number v-model="data.duration_fee.start_fee_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收时长(分)" class="required">
            <el-input-number v-model="data.duration_fee.renew_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收金额(元)" class="required">
            <el-input-number v-model="data.duration_fee.renew_fee_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.duration_fee.not_enough_time_fee_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input v-model="data.duration_fee.time_limit_money" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 3">
          <el-form-item label="起收时长(分)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收单位时长(分)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_unit_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="起收单位金额(元)" class="required">
            <el-input-number v-model="data.period_fee.start_fee_unit_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.period_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收单位时长(分)" class="required">
            <el-input-number v-model="data.period_fee.renew_fee_unit_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="续收单位金额(元)" class="required">
            <el-input-number v-model="data.period_fee.renew_fee_unit_money" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.period_fee.not_enough_time_fee_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input-number v-model="data.period_fee.time_limit_money" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
        <div v-if="data.feeForm.type == 4">
          <el-form-item label="阶段时长(分)" class="required">
            <el-select v-model="data.stair_fee.stair_time" style="width: 100%">
              <el-option v-for="item in sairTimes" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item
            v-for="(money, index) in data.stair_fee.stair_moneys"
            :label="'第' + parseInt(index + 1) + '阶段金额(元)'"
            :key="money.key"
            class="required"
          >
            <el-input-number v-model="money.value" :min="0" style="width: 75%" />&ensp;
            <div style="display: inline-block; height: 30px; margin-left: 5px; vertical-align: middle">
              <el-icon :size="30" v-if="data.stair_fee.stair_moneys.length < 24" color="#409eff" @click="addDomain"><CirclePlus /></el-icon>&ensp;
              <el-icon
                :size="30"
                v-if="data.stair_fee.stair_moneys.length > 1 || data.stair_fee.stair_moneys.length == 24"
                color="#f56c6c"
                @click="removeDomain(money)"
                ><CircleClose
              /></el-icon>
            </div>
          </el-form-item>
          <el-form-item label="免费时长(分)" class="required">
            <el-input-number v-model="data.stair_fee.free_time" :min="0" style="width: 100%" />
          </el-form-item>
          <el-form-item label="满24小时是否从新计算" class="required">
            <el-radio-group v-model="data.stair_fee.beyond_day_calculate_again_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="不足一个时段是否计费" class="required">
            <el-radio-group v-model="data.stair_fee.not_enough_time_fee_type">
              <el-radio label="0" size="large">无</el-radio>
              <el-radio label="1" size="large">是</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="时限额(元)" class="required">
            <el-input-number v-model="data.stair_fee.time_limit_money" :min="0" style="width: 100%" />
          </el-form-item>
        </div>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="updateFeeSetting(editFeeForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script name="ParkFeeAdd" setup>
import parkRegionService from '@/service/park/ParkRegionService';
import parkFeeService from '@/service/park/ParkFeeService';
import commonService from '@/service/common/CommonService';
import { CirclePlus, CircleClose } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { reactive, ref, nextTick, getCurrentInstance, onActivated } from 'vue';
import { closeCurrentTab } from '@/utils/tabKit';
import { useRoute } from 'vue-router';

const route = useRoute();
const addForm = ref();
const addFeeForm = ref();
const editFeeForm = ref();
const parkRegions = ref([]);
const carTypes = ref([]);
const feePrecisions = ref([]);
const costPrecisions = ref([]);
const truncModes = ref([]);
const crossTimeFeeTypes = ref([]);
const crossTimeStartFeeTypes = ref([]);
const modelValidStates = ref([]);
const modelAuditStates = ref([]);
const feeRuleTypes = ref([]);
const notEnoughTimeFeeTypes = ref([]);
const billTopModes = ref([]);
const firstDayLimitModes = ref([]);
const eachDayLimitModes = ref([]);
const sairTimes = ref([]);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const ruleType = ref([]);
const { proxy } = getCurrentInstance();
const form = reactive({
  name: undefined, //规则名称
  car_type: undefined, //车辆类型
  park_id: undefined, //车场ID
  park_region_id: undefined, //子场ID
  same_car_time: undefined, //同车重新起收限制(时)
  fee_precision: undefined, //计费精度
  cost_precision: undefined, //费用精度
  trunc_mode: undefined, //进位方式
  cross_time_fee_type: undefined, //跨分段计费方式
  cross_time_start_fee_type: undefined, //跨分段起收计费方式
  bill_top_mode: '1', //单笔封顶费用(1-有;0-无;)
  bill_top_money: undefined, //单笔费用封顶
  first_day_limit_mode: '1', //首日限额(1-有;0-无;)
  first_day_limit_money: undefined, //首日限额
  each_day_limit_mode: '1', //每日限额(1-有;0-无;)
  each_day_limit_money: undefined, //每日限额
  valid_start_time: '', //生效日期
  is_free_first_time: '1', //超过24小时计算免费时长(0-是;1-否;)
  pre_payment_time_limit: undefined, //提前缴费时间限制
  pre_payment_time_limit_two: undefined, //二次免费时长
  fee_rules: []
});
const data = reactive({
  feeForm: {
    name: '', //分段名称
    type: '1', //计费方式(1-按次计费;2-时长计费;3-时段计费;)
    type_desc: '按次计费',
    start_time: '', //开始时间
    end_time: '', //结束时间
    index: ''
  },
  times_fee: {
    //type=1,按次计费时,此字段不为空
    money: 0, //收费金额
    free_time: 0 //免费时长
  },
  period_fee: {
    //type=3,时长计费时,此字段不为空
    start_fee_time: 0, //起收时长
    start_fee_unit_time: 0, //起收单位时长
    start_fee_unit_money: 0, //起收单位金额
    free_time: 0, //免费时长
    renew_fee_unit_time: 0, //续费单位时长
    renew_fee_unit_money: 0, //续费单位金额
    not_enough_time_fee_type: '1', //不足一个时段是否计费(1-是;0-否;)
    time_limit_money: 0 //时限额
  },
  duration_fee: {
    //type=2,时段计费时,此字段不为空
    start_fee_time: 0, //起收时长
    start_fee_money: 0, //起收金额
    free_time: 0, //免费时长
    renew_fee_time: 0, //续费时长
    renew_fee_money: 0, //续费金额
    not_enough_time_fee_type: '1', //不足一个时段是否计费(1-是;0-否;)
    time_limit_money: 0 //时限额
  },
  stair_fee: {
    //type=4,阶梯计费时,此字段不为空
    stair_time: '30', //阶段时长
    stair_moneys: [{ value: '' }], //阶段金额
    free_time: 0, //免费时长
    beyond_day_calculate_again_type: '1', //满24小时是否从新计算beyondDayCalculateAgainType
    not_enough_time_fee_type: '1', //不足一个时段是否计费
    time_limit_money: 0 //时限额
  },
  //测试计费规则
  testForm: {
    start_time: '', //开始时间
    end_time: '' //结束时间
  }
});

const rules = reactive({
  name: [
    {
      required: true,
      message: '请输入规则名称',
      trigger: 'blur'
    }
  ],
  car_type: [
    {
      required: true,
      message: '请选择车辆类型',
      trigger: 'change'
    }
  ],
  park_region_id: [
    {
      required: true,
      message: '请选择关联子场',
      trigger: 'change'
    }
  ],
  same_car_time: [
    {
      required: true,
      message: '请选择同车重新起收限制(时)',
      trigger: 'change'
    }
  ],
  pre_payment_time_limit: [
    {
      required: true,
      message: '请输入提前缴费出场时限',
      trigger: 'change'
    }
  ],
  pre_payment_time_limit_two: [
    {
      required: true,
      message: '请输入二次免费时长',
      trigger: 'change'
    }
  ],
  fee_precision: [
    {
      required: true,
      message: '请选择计费精度',
      trigger: 'change'
    }
  ],
  cost_precision: [
    {
      required: true,
      message: '请选择费用精度',
      trigger: 'change'
    }
  ],
  trunc_mode: [
    {
      required: true,
      message: '请选择进位方式',
      trigger: 'change'
    }
  ],
  cross_time_fee_type: [
    {
      required: true,
      message: '请选择跨分段计费方式',
      trigger: 'change'
    }
  ],
  cross_time_start_fee_type: [
    {
      required: true,
      message: '请选择跨分段起收计费方式',
      trigger: 'change'
    }
  ],
  bill_top_mode: [
    {
      required: true,
      message: '请选择单笔封顶费用',
      trigger: 'change'
    }
  ],
  first_day_limit_mode: [
    {
      required: true,
      message: '请选择首日限额',
      trigger: 'change'
    }
  ],
  each_day_limit_mode: [
    {
      required: true,
      message: '请选择每日限额',
      trigger: 'change'
    }
  ]
});
const fee_rules = reactive({
  name: [
    {
      required: true,
      message: '请输入分段名称',
      trigger: 'blur'
    }
  ],
  type: [
    {
      required: true,
      message: '请选择计费方式',
      trigger: 'change'
    }
  ]
});

onActivated(() => {
  addForm.value.resetFields();
  form.park_id = route.query.park_id;
  initSelects();
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'carTypes',
      enum_value: 'EnumCarType'
    },
    {
      enum_key: 'feePrecisions',
      enum_value: 'EnumFeePrecision'
    },
    {
      enum_key: 'costPrecisions',
      enum_value: 'EnumCostPrecision'
    },
    {
      enum_key: 'truncModes',
      enum_value: 'EnumTruncMode'
    },
    {
      enum_key: 'crossTimeFeeTypes',
      enum_value: 'EnumCrossTimeFeeType'
    },
    {
      enum_key: 'crossTimeStartFeeTypes',
      enum_value: 'EnumCrossTimeStartFeeType'
    },
    {
      enum_key: 'modelValidStates',
      enum_value: 'EnumFeeModelValidState'
    },
    {
      enum_key: 'modelAuditStates',
      enum_value: 'EnumFeeModelAuditState'
    },
    {
      enum_key: 'feeRuleTypes',
      enum_value: 'EnumFeeRuleType'
    },
    {
      enum_key: 'notEnoughTimeFeeTypes',
      enum_value: 'EnumNotEnoughTimeFeeType'
    },
    {
      enum_key: 'billTopModes',
      enum_value: 'EnumBillTopMode'
    },
    {
      enum_key: 'firstDayLimitModes',
      enum_value: 'EnumFirstDayLimitMode'
    },
    {
      enum_key: 'eachDayLimitModes',
      enum_value: 'EnumEachDayLimitMode'
    },
    { enum_key: 'sairTimes', enum_value: 'EnumStairTime' }
  ];
  commonService.findEnums('park', param).then((response) => {
    carTypes.value = response.data.carTypes;
    feePrecisions.value = response.data.feePrecisions;
    costPrecisions.value = response.data.costPrecisions;
    truncModes.value = response.data.truncModes;
    crossTimeFeeTypes.value = response.data.crossTimeFeeTypes;
    crossTimeStartFeeTypes.value = response.data.crossTimeStartFeeTypes;
    modelValidStates.value = response.data.modelValidStates;
    modelAuditStates.value = response.data.modelAuditStates;
    feeRuleTypes.value = response.data.feeRuleTypes;
    notEnoughTimeFeeTypes.value = response.data.notEnoughTimeFeeTypes;
    billTopModes.value = response.data.billTopModes;
    firstDayLimitModes.value = response.data.firstDayLimitModes;
    eachDayLimitModes.value = response.data.eachDayLimitModes;
    sairTimes.value = response.data.sairTimes;
  });
  parkRegionService.listParkRegion(form.park_id).then((response) => {
    parkRegions.value = response;
  });
};

const topChange = () => {
  if (form.bill_top_mode == 0) {
    form.bill_top_money == '';
  }
};
const firstChange = () => {
  if (form.first_day_limit_mode == 0) {
    form.first_day_limit_money == '';
  }
};
const eachChange = () => {
  if (form.each_day_limit_mode == 0) {
    form.each_day_limit_money == '';
  }
};
const closeTab = (addForm) => {
  addForm.resetFields();
  closeCurrentTab({
    path: '/park/parkAdmin',
    query: {
      parkId: form.park_id,
      redirect_tab: 'parkFee'
    }
  });
};
const saveParkFee = (addForm) => {
  addForm.validate().then(() => {
    if (form.valid_start_time === '' || form.valid_start_time === null) {
      ElMessage({
        message: '请选择生效日期',
        type: 'warning'
      });
      return false;
    }
    if (form.fee_rules.length == 0) {
      ElMessage({
        message: '请添加计费规则',
        type: 'warning'
      });
      return false;
    }
    const rules = form.fee_rules;
    rules.forEach(function (item) {
      if (item.type == 4) {
        const list = [];
        const arr = item.stair_fee.stair_moneys;
        arr.forEach(function (data) {
          list.push(data.value);
        });
        item.stair_fee.stair_moneys = list;
      }
    });
    parkFeeService
      .createParkFee(form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          closeTab(addForm);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        closeTab(addForm);
      });
  });
};

const feeSettingAdd = () => {
  data.feeForm = {
    name: '', //分段名称
    type: 1, //计费方式(1-按次计费;2-时长计费;3-时段计费;)
    type_desc: '按次计费',
    start_time: '', //开始时间
    end_time: '', //结束时间
    index: ''
  };
  data.times_fee = {
    money: 0, //收费金额
    free_time: 0 //免费时长
  };
  data.period_fee = {
    start_fee_time: 0, //起收时长
    start_fee_unit_time: 0, //起收单位时长
    start_fee_unit_money: 0, //起收单位金额
    free_time: 0, //免费时长
    renew_fee_unit_time: 0, //续费单位时长
    renew_fee_unit_money: 0, //续费单位金额
    not_enough_time_fee_type: '1', //不足一个时段是否计费(1-是;0-否;)
    time_limit_money: 0 //时限额
  };
  data.duration_fee = {
    start_fee_time: 0, //起收时长
    start_fee_money: 0, //起收金额
    free_time: 0, //免费时长
    renew_fee_time: 0, //续费时长
    renew_fee_money: 0, //续费金额
    not_enough_time_fee_type: '1', //不足一个时段是否计费(1-是;0-否;)
    time_limit_money: 0 //时限额
  };
  data.stair_fee = {
    stair_time: '30', //阶段时长
    stair_moneys: [{ value: '' }], //阶段金额
    free_time: 0, //免费时长
    beyond_day_calculate_again_type: '1', //满24小时是否从新计算
    not_enough_time_fee_type: '1', //不足一个时段是否计费
    time_limit_money: 0 //时限额
  };
  createDialogVisible.value = true;
};

const findTypeDesc = (value) => {
  data.feeForm.type_desc = feeRuleTypes.value.find((item) => item.value === value).key;
  // data.feeForm.type_desc = proxy.$refs['ruleType'].selected.currentLabel;
};

// 动态增减表单项
const removeDomain = (item) => {
  var index = data.stair_fee.stair_moneys.indexOf(item);
  if (index !== -1) {
    data.stair_fee.stair_moneys.splice(index, 1);
  }
};
const addDomain = () => {
  data.stair_fee.stair_moneys.push({
    value: ''
  });
};
// 修改
const handleEdit = (index) => {
  updateDialogVisible.value = true;
  data.feeForm = { ...form.fee_rules[index] };
  data.feeForm.type = data.feeForm.type;
  if (data.feeForm.times_fee != null) {
    data.times_fee = data.feeForm.times_fee;
  }
  if (data.feeForm.period_fee != null) {
    data.period_fee = data.feeForm.period_fee;
  }
  if (data.feeForm.duration_fee != null) {
    data.duration_fee = data.feeForm.duration_fee;
  }
  if (data.feeForm.stair_fee != null) {
    data.stair_fee = data.feeForm.stair_fee;
  }
  data.feeForm.index = index;
};
// 删除
const handleDelete = (index) => {
  ElMessageBox.confirm('确定要删除该规则配置吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      form.fee_rules.splice(index, 1);
      ElMessage({
        message: '规则配置删除成功',
        type: 'success'
      });
    })
    .catch(() => {});
};
// 提交添加规则
const saveFeeSetting = (addFeeForm) => {
  addFeeForm.validate().then(() => {
    if (data.feeForm.start_time === '' || data.feeForm.start_time === null) {
      ElMessage({
        message: '请选择规则开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.feeForm.end_time === '' || data.feeForm.end_time === null) {
      ElMessage({
        message: '请选择规则结束时间',
        type: 'warning'
      });
      return false;
    }
    if (data.feeForm.type == 1) {
      if (data.times_fee.money === '' || data.times_fee.money === undefined) {
        ElMessage({
          message: '收费金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.times_fee.free_time === '' || data.times_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.times_fee = data.times_fee;
    }
    if (data.feeForm.type == 3) {
      if (data.period_fee.start_fee_time === '' || data.period_fee.start_fee_time === undefined) {
        ElMessage({
          message: '起收时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.start_fee_unit_time === '' || data.period_fee.start_fee_unit_time === undefined) {
        ElMessage({
          message: '起收单位时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.start_fee_unit_money === '' || data.period_fee.start_fee_unit_money === undefined) {
        ElMessage({
          message: '起收单位金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.free_time === '' || data.period_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.renew_fee_unit_time === '' || data.period_fee.renew_fee_unit_time === undefined) {
        ElMessage({
          message: '续费单位时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.renew_fee_unit_money === '' || data.period_fee.renew_fee_unit_money === undefined) {
        ElMessage({
          message: '续费单位金额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.period_fee = data.period_fee;
    }
    if (data.feeForm.type == 2) {
      if (data.duration_fee.start_fee_time === '' || data.duration_fee.start_fee_time === undefined) {
        ElMessage({
          message: '起收时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.start_fee_money === '' || data.duration_fee.start_fee_money === undefined) {
        ElMessage({
          message: '起收金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.free_time === '' || data.duration_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.renew_fee_time === '' || data.duration_fee.renew_fee_time === undefined) {
        ElMessage({
          message: '续费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.renew_fee_money === '' || data.duration_fee.renew_fee_money === undefined) {
        ElMessage({
          message: '续费金额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.duration_fee = data.duration_fee;
    }
    if (data.feeForm.type == 4) {
      if (data.stair_fee.stair_time === '' || data.stair_fee.stair_time === undefined) {
        ElMessage({
          message: '阶段时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.stair_moneys.length <= 0) {
        ElMessage({
          message: '阶段金额不能为空',
          type: 'warning'
        });
        return false;
      } else {
        let flag = false;
        const moneys = data.stair_fee.stair_moneys;
        moneys.forEach(function (item) {
          if (item.value === '' || item.value === undefined) {
            flag = true;
          }
        });
        if (flag) {
          ElMessage({
            message: '阶段金额不能为空',
            type: 'warning'
          });
          return false;
        }
      }
      if (data.stair_fee.free_time === '' || data.stair_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.beyond_day_calculate_again_type === '' || data.stair_fee.beyond_day_calculate_again_type === undefined) {
        ElMessage({
          message: '满24小时是否从新计算不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.not_enough_time_fee_type === '' || data.stair_fee.not_enough_time_fee_type === undefined) {
        ElMessage({
          message: '不足一个时段是否计费不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.time_limit_money === '' || data.stair_fee.time_limit_money === undefined) {
        ElMessage({
          message: '时限额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.stair_fee = data.stair_fee;
    }
    // 将拼接好的数据放在form中
    if (form.fee_rules == undefined) {
      form.fee_rules = [];
    }
    form.fee_rules.push(data.feeForm);

    ElMessage({
      message: '规则配置成功',
      type: 'success'
    });
    createDialogVisible.value = false;
  });
};
const updateFeeSetting = (editFeeForm) => {
  editFeeForm.validate().then(() => {
    if (data.feeForm.start_time === '' || data.feeForm.start_time === undefined) {
      ElMessage({
        message: '请选择规则开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.feeForm.end_time === '' || data.feeForm.end_time === undefined) {
      ElMessage({
        message: '请选择规则结束时间',
        type: 'warning'
      });
      return false;
    }
    if (data.feeForm.type == 1) {
      if (data.times_fee.money === '' || data.times_fee.money === undefined) {
        ElMessage({
          message: '收费金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.times_fee.free_time === '' || data.times_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.times_fee = data.times_fee;
    }
    if (data.feeForm.type == 3) {
      if (data.period_fee.start_fee_time === '' || data.period_fee.start_fee_time === undefined) {
        ElMessage({
          message: '起收时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.start_fee_unit_time === '' || data.period_fee.start_fee_unit_time === undefined) {
        ElMessage({
          message: '起收单位时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.start_fee_unit_money === '' || data.period_fee.start_fee_unit_money === undefined) {
        ElMessage({
          message: '起收单位金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.free_time === '' || data.period_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.renew_fee_unit_time === '' || data.period_fee.renew_fee_unit_time === undefined) {
        ElMessage({
          message: '续费单位时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.period_fee.renew_fee_unit_money === '' || data.period_fee.renew_fee_unit_money === undefined) {
        ElMessage({
          message: '续费单位金额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.period_fee = data.period_fee;
    }
    if (data.feeForm.type == 2) {
      if (data.duration_fee.start_fee_time === '' || data.duration_fee.start_fee_time === undefined) {
        ElMessage({
          message: '起收时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.start_fee_money === '' || data.duration_fee.start_fee_money === undefined) {
        ElMessage({
          message: '起收金额不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.free_time === '' || data.duration_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.renew_fee_time === '' || data.duration_fee.renew_fee_time === undefined) {
        ElMessage({
          message: '续费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.duration_fee.renew_fee_money === '' || data.duration_fee.renew_fee_money === undefined) {
        ElMessage({
          message: '续费金额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.duration_fee = data.duration_fee;
    }
    if (data.feeForm.type == 4) {
      if (data.stair_fee.stair_time === '' || data.stair_fee.stair_time === undefined) {
        ElMessage({
          message: '阶段时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.stair_moneys.length <= 0) {
        ElMessage({
          message: '阶段金额不能为空',
          type: 'warning'
        });
        return false;
      } else {
        let flag = false;
        const moneys = data.stair_fee.stair_moneys;
        moneys.forEach(function (item) {
          if (item.value === '' || item.value === undefined) {
            flag = true;
          }
        });
        if (flag) {
          ElMessage({
            message: '阶段金额不能为空',
            type: 'warning'
          });
          return false;
        }
      }
      if (data.stair_fee.free_time === '' || data.stair_fee.free_time === undefined) {
        ElMessage({
          message: '免费时长不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.beyond_day_calculate_again_type === '' || data.stair_fee.beyond_day_calculate_again_type === undefined) {
        ElMessage({
          message: '满24小时是否从新计算不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.not_enough_time_fee_type === '' || data.stair_fee.not_enough_time_fee_type === undefined) {
        ElMessage({
          message: '不足一个时段是否计费不能为空',
          type: 'warning'
        });
        return false;
      }
      if (data.stair_fee.time_limit_money === '' || data.stair_fee.time_limit_money === undefined) {
        ElMessage({
          message: '时限额不能为空',
          type: 'warning'
        });
        return false;
      }
      data.feeForm.stair_fee = data.stair_fee;
    }
    // 将拼接好的数据放在form中
    if (form.fee_rules == undefined) {
      form.fee_rules = [];
    }
    form.fee_rules[data.feeForm.index] = {
      ...data.feeForm
    };
    ElMessage({
      message: '规则修改成功',
      type: 'success'
    });
    updateDialogVisible.value = false;
  });
};
</script>

<style lang="scss" scoped>
.feeSetting {
  width: 100%;
  height: 100%;
}

.box-card {
  position: relative;
  color: rgb(90, 90, 90);
  margin-right: 10px;
  margin-bottom: 10px;
  float: left;
}

.hiddenButton {
  position: absolute;
  right: 0;
  top: 20px;
  z-index: 999;
  width: 70px;
  height: 100%;
  display: none;
}

.hiddenButton:hover {
  display: block;
}

.moreEdit:hover + .hiddenButton {
  display: block;
}

.btnChild {
  height: 25px;
  border: 1px solid rgb(250, 250, 250);
  text-align: center;
  background: #fff;
}

.moreEdit {
  display: inline-block;
  height: 20px;
  text-align: right;
  float: right;
  padding: 3px 0;
  font-weight: bolder;
}

.right {
  width: 200px;
  height: 30px;
  float: right;
  margin-top: -28px;
  text-align: right;
  line-height: 35px;
}
.tab-right {
  width: 200px;
  height: 30px;
  float: right;
  margin-top: -4px;
  text-align: right;
  line-height: 35px;
}
.textItem {
  margin-top: 10px;
}

:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}

.el-input-number-append {
  display: inline;
  background-color: #f5f7fa;
  padding: 0px 10px;
  width: 60px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-radius: 0px 2px 2px 0px;
}

.el-input-number-append-time {
  display: inline;
  background-color: #f5f7fa;
  padding: 0px 10px;
  width: 34px;
  height: 32px;
  border: 1px solid #dcdfe6;
  border-left: none;
  border-radius: 0px 2px 2px 0px;
}

:deep(.el-checkbox__input) {
  top: 2%;
}

:deep(.el-checkbox__label) {
  padding-left: 6px;
  position: relative;
  top: -4%;
}
</style>
