<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.invoice_title" placeholder="开票公司" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.audit_states" style="width: 100%" placeholder="审核状态" multiple>
        <el-option v-for="item in auditStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.draw_states" style="width: 100%" placeholder="领取状态" multiple>
        <el-option v-for="item in drawStatesList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="领取开始日期"
        end-placeholder="领取结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
    <park-find-back :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="QuotaInvoiceApplicationSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from './ParkFindBack.vue';
import { reactive, onMounted, ref } from 'vue';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';

const emits = defineEmits(['form-search']);
const auditStateList = ref([]);
const drawStatesList = ref([]);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    invoice_title: undefined,
    order_no: undefined,
    name_or_mobile: undefined,
    audit_states: undefined,
    draw_states: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onMounted(() => {
  initSelects();
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'auditStateList',
      enum_value: 'EnumAuditState'
    },
    {
      enum_key: 'drawStatesList',
      enum_value: 'EnumQuotaInvoiceDrawState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    drawStatesList.value = response.data.drawStatesList;
  });
  commonService.findEnums('audit', param).then((response) => {
    auditStateList.value = response.data.auditStateList;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  (form.dateRange = []),
    (form.queryParams = {
      park_id: undefined,
      park_name: undefined,
      invoice_title: undefined,
      order_no: undefined,
      name_or_mobile: undefined,
      audit_states: undefined,
      draw_states: undefined,
      start_time: undefined,
      end_time: undefined,
      page: 1,
      limit: 30
    });
  emits('reset', form.queryParams);
  // handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
