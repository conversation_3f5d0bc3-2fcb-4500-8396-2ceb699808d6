<template>
  <div>
    <el-row :gutter="10">
      <el-col :xs="24" :sm="4">
        <department-tree ref="dept_tree" @initData="initDepartmentTree" />
      </el-col>
      <el-col :xs="24" :sm="20">
        <el-tabs v-model="activeDate.activeName" type="border-card" @tab-click="tabsHandleClick" class="my-table-container">
          <el-tab-pane label="子部门信息" name="subDepartment">
            <el-card class="table" shadow="never">
              <div class="opers">
                <el-button type="primary" @click="handleAdd">新建子部门</el-button>
                <div>
                  <el-input
                    v-model="data.queryParams.department_name"
                    class="w-50 m-2"
                    placeholder="部门名称"
                    @keyup.enter="searchDepartments"
                    clearable
                  >
                    <template #suffix
                      ><el-icon @click="searchDepartments"><Search /></el-icon>
                    </template>
                  </el-input>
                </div>
              </div>
              <sub-department-table ref="sub_table" @flush="flushTree" />
            </el-card>
          </el-tab-pane>
          <el-tab-pane label="员工通讯录" name="departmentEmployee" class="table-warp h-full">
            <department-employee-table ref="emp_table" />
          </el-tab-pane>
        </el-tabs>
      </el-col>
    </el-row>
  </div>
</template>

<script name="Department" setup>
import DepartmentTree from './department/DepartmentTree.vue';
import SubDepartmentTable from './department/SubDepartmentTable.vue';
import DepartmentEmployeeTable from './department/DepartmentEmployeeTable.vue';
import { reactive, ref, onActivated, getCurrentInstance } from 'vue';
import { ElMessage } from 'element-plus';
import { Search } from '@element-plus/icons-vue';

const sub_table = ref(SubDepartmentTable);
const emp_table = ref(DepartmentEmployeeTable);
const activeDate = reactive({
  activeName: 'subDepartment'
});
const data = reactive({
  queryParams: {
    department_name: undefined,
    parent_department_id: undefined,
    page: 1,
    limit: 30
  }
});
let queryParams = reactive({
  parent_department_id: 0
});
let queryParamsEmp = reactive({
  department_id: 0
});

let dept = reactive({
  name: '',
  parent_department_id: 0
});
const { proxy } = getCurrentInstance();
onActivated(() => {
  initDeptData(); // 初始化查询
});
// 树节点点击调用
const initDepartmentTree = (queryDept) => {
  dept = queryDept;
  initDeptData();
};
// 构建查询
const initDeptData = () => {
  if (activeDate.activeName === 'subDepartment') {
    queryParams = {
      parent_department_id: dept.parent_department_id,
      parentDepartmentName: dept.name
    };

    sub_table.value.getData(queryParams);
  }
  if (activeDate.activeName === 'departmentEmployee') {
    queryParamsEmp.department_id = dept.parent_department_id;
    searchEmployees(queryParamsEmp);
  }
};
//标签页切换
const tabsHandleClick = (tab, event) => {
  initDeptData();
};
const dept_tree = ref(null);
const flushTree = () => {
  dept_tree.value.initDepartmentTree();
};
// 查询子部门
const searchDepartments = () => {
  data.queryParams = {
    parent_department_id: dept.parent_department_id,
    department_name: data.queryParams.department_name
  };
  const query = Object.assign(data.queryParams, { page: 1, limit: 30 });
  sub_table.value.getSearchData(data.queryParams);
};
// 新增部门

const handleAdd = () => {
  if (dept.parent_department_id === undefined || dept.parent_department_id === 0) {
    ElMessage({
      message: '请选择部门',
      type: 'warning'
    });
  } else {
    sub_table.value.handleEdit('add', dept);
  }
};

//员工通讯录查询
const searchEmployees = (queryParamsEmp) => {
  queryParamsEmp.department_id = dept.parent_department_id;
  emp_table.value.getDataList(queryParamsEmp);
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  flex: 1;
}
</style>
