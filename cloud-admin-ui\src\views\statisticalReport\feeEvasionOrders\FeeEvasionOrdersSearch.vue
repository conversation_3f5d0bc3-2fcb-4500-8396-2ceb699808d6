<template>
  <div>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
      <form-search-item>
        <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
      </form-search-item>
      <form-search-item>
        <ClearableChargeInput
          v-model="form.queryParams.organizational_structure"
          @charge="orgCharge(true)"
          @clear="clearDepartment"
          placeholder="组织架构"
        />
      </form-search-item>
      <form-search-item>
        <el-select v-model="form.queryParams.type_of_events" placeholder="事件类型" multiple>
          <el-option v-for="item in eventTypes" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </form-search-item>
      <form-search-item>
        <el-date-picker
          v-model="form.dateRange"
          type="daterange"
          style="width: 100%"
          range-separator="至"
          start-placeholder="统计日期开始日期"
          end-placeholder="统计日期结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :shortcuts="shortcuts"
          :disabled-date="disabledDateFn"
        />
      </form-search-item>
       <template #button>
        <export-button :export-func="exportFeeEvasion" :params="exportParams"></export-button>
      </template>
    </FormSearch>
    <!-- 关联车场 -->
    <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
      <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
    </el-dialog>
    <!-- 关联组织架构 -->
    <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
      <org-find-back
        :organization_id="organization_ids"
        :department_name="organizational_structure"
        @orgCharge="orgCharge(false)"
        :mode="flag"
        @renderOrgTableInput="renderOrgTableInput"
      />
    </el-dialog>
  </div>
</template>

<script name="FeeEvasionOrdersSearch" setup>
import { exportFeeEvasion } from '@/api/statisticalReport/FeeEvasionOrdersApi';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import exportButton from '@/components/exportButton.vue';
import { useUser } from '@/stores/user';
import { onMounted, reactive, ref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { dayjs } from 'element-plus';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';

const exportParams = computed(() => {
  const { start_date, end_date } = form.queryParams;
  return {
    ...form.queryParams,
    start_time: start_date ? start_date : dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    end_time: end_date ? end_date : dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  };
});

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    organizational_structure: undefined,
    start_date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    end_date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    page: 1,
    limit: 30
  },
  dateRange: [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')]
});
const park_id = ref('');
const park_name = ref('');
const organization_ids = ref('');
const organizational_structure = ref('');
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const router = useRouter();
const eventTypes = [
  {
    label: '跟车',
    value: 15
  },
  {
    label: '折返',
    value: 16
  },
  {
    label: '断网',
    value: 17
  }
  // {
  //   label: '断电',
  //   value: 18
  // }
];
const shortcuts = [
  {
    text: '近三天',
    value: () => {
      const end = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 4);
      return [start, end];
    }
  },
  {
    text: '近一周',
    value: () => {
      const end = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 8);
      return [start, end];
    }
  },
  {
    text: '近一个月',
    value: () => {
      const end = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 31);
      return [start, end];
    }
  },
  {
    text: '近三个月',
    value: () => {
      const end = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 91);
      return [start, end];
    }
  },
  {
    text: '近一年',
    value: () => {
      const end = new Date(Date.now() - 24 * 60 * 60 * 1000);
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 366);
      return [start, end];
    }
  }
];

onMounted(() => {
  const user = useUser();
  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  // if (user.role_id == 1) {
  //   return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const disabledDateFn = (time) => {
  return time.getTime() > dayjs().subtract(1, 'day');
};

const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.organizational_structure = undefined;
};
const handleDataSearch = () => {
  if (undefined !== form.dateRange && null !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_date = form.dateRange[0];
    form.queryParams.end_date = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_date = undefined;
    form.queryParams.end_date = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    if (
      typeof form.queryParams.organization_ids !== 'undefined' &&
      form.queryParams.organization_ids != null &&
      form.queryParams.organization_ids !== ''
    ) {
      const query = Object.assign(form.queryParams, {});
      emits('form-search', query);
    } else {
      const query = Object.assign(form.queryParams, {});
      emits('form-search', query);
      return false;
    }
  }
};
const handleAllReset = () => {
  form.dateRange = [dayjs().subtract(1, 'day').format('YYYY-MM-DD'), dayjs().subtract(1, 'day').format('YYYY-MM-DD')];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    organizational_structure: undefined,
    start_date: dayjs().subtract(1, 'day').format('YYYY-MM-DD'),
    end_date: dayjs().subtract(1, 'day').format('YYYY-MM-DD')
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    organizational_structure.value = form.queryParams.organizational_structure;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.queryParams.organizational_structure = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
