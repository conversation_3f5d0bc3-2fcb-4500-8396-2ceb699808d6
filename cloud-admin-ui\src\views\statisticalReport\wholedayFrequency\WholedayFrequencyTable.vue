<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="wholedayFrequencyService.exportParkingHighPayOrderRecords"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="park_id" label="车场id" align="center" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" />
        <el-table-column prop="openid" label="OpenId" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" />
        <el-table-column prop="pay_time" label="支付时间" align="center" />
        <el-table-column prop="pay_money" label="支付金额（元）" align="center" />
        <el-table-column prop="in_gateway_name" label="进场口" align="center" />
        <el-table-column prop="out_gateway_name" label="出场口" align="center" />
        <el-table-column prop="in_time" label="进场时间" align="center" />
        <el-table-column prop="to_time" label="出场时间" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="WholedayFrequencyTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import wholedayFrequencyService from '@/service/statisticalReport/WholedayFrequencyService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  wholedayFrequencyService.pagingAbnormalRecord(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
