<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button v-if="scope.row.used_time" link type="primary" @click="checkDetail(scope.row)">停车记录</el-button>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column prop="prk_park_name" label="车场名称" align="center" min-width="120" />
        <el-table-column prop="mbr_member_name" label="会员昵称" align="center" min-width="120">
          <template v-slot="scope">
            <span>{{ scope.row.mbr_member_name ? scope.row.mbr_member_name : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="mbr_member_phone" label="手机号" align="center" min-width="120">
          <template v-slot="scope">
            <span>{{ scope.row.mbr_member_phone ? scope.row.mbr_member_phone : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" label="车牌号" align="center" min-width="120" />
        <el-table-column prop="coupon_meta_name" label="优免券名称" align="center" min-width="120" />
        <el-table-column prop="total_count" label="优免数（张）" align="center" min-width="120" />
        <el-table-column prop="type_desc" label="优免券类型" align="center" min-width="120" />
        <el-table-column prop="draw_type_desc" label="获取方式" align="center" min-width="120" />
        <el-table-column prop="created_at" label="获取时间" align="center" min-width="120" />
        <el-table-column prop="used_time" label="核销时间" align="center" min-width="120">
          <template v-slot="scope">
            <span>{{ scope.row.used_time ? scope.row.used_time : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="use_state_desc" label="使用状态" align="center" min-width="120" />
        <el-table-column prop="face_value" label="抵扣金额（元）" align="center" min-width="160" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CouponMetaTable" setup>
import { reactive, ref } from 'vue';
import couponStatsService from '@/service/merchant/CouponStatsService';
import { ElMessage } from 'element-plus';
import { activeRouteTab } from '@/utils/tabKit';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  couponStatsService.pagingCouponGrant(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const checkDetail = (row) => {
  console.log('checkDetail', row);
  activeRouteTab({
    path: '/charge/parkFee',
    query: {
      parkId: row.prk_park_id,
      parkName: row.prk_park_name,
      plateNo: row.plate_no,
      usedTime: row.used_time.split(' ')[0]
    }
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
