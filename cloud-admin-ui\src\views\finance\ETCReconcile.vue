<template>
  <div class="container">
    <ETCReconcileSearch @form-search="searchETCReconcileSearch" @reset="resetParamsAndData" />
    <div class="table-content">
      <div class="summary-table">
        <ETCReconcileSummarizeTable ref="summarizeTable" @update-records-loading="handleUpdateRecordsLoading" @update-records="handleUpdateRecords" />
      </div>
      <div class="details-table">
        <ETCReconcileTransactionTable ref="transactionTable" />
        <ETCReconcileRefundTable ref="refundTable" />
        <div class="description">
          <div class="description-title">
            <el-icon><BellFilled /></el-icon>
            操作提示
          </div>
          <p class="subtitle">1.选择车场和月份，点击查询，停车场临停汇总显示选择月份的信息。</p>
          <p class="subtitle">2.若选择的是当前月，停车场临停汇总显示当前月当日之前的数据，如当日是11号，则显示1至10号的10条数据和1条汇总数据；</p>
          <p class="subtitle indent">若选择的是非前月，停车场临停汇总显示选择月整月的数据，即月初至月末的整月逐日数据和1条汇总数据。</p>
          <p class="subtitle">3.当鼠标在停车场临停汇总浏览未点击某日数据时，ETC交易、退款、拒付表单首行显示汇总合计信息 ；</p>
          <p class="subtitle indent">
            当鼠标在停车场临停汇总浏览点击某日数据时，ETC交易、退款、拒付表单在首行汇总合计信息基础上，增加1行对应日的信息。
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script name="ETCReconcile" setup>
import { ref } from 'vue';
import ETCReconcileSearch from './etcReconcile/ETCReconcileSearch.vue';
import ETCReconcileSummarizeTable from './etcReconcile/ETCReconcileSummarizeTable.vue';
import ETCReconcileTransactionTable from './etcReconcile/ETCReconcileTransactionTable.vue';
import ETCReconcileRefundTable from './etcReconcile/ETCReconcileRefundTable.vue';

const summarizeTable = ref(null);

const transactionTable = ref(null);

const refundTable = ref(null);

const searchETCReconcileSearch = (queryParams) => {
  summarizeTable.value.getList(queryParams);
};

const handleUpdateRecordsLoading = (loading) => {
  transactionTable.value.setLoading(loading);
  refundTable.value.setLoading(loading);
};

const handleUpdateRecords = ({ transactionTableData, refundTableData }) => {
  transactionTable.value.setTableData(transactionTableData);
  refundTable.value.setTableData(refundTableData);
};

const resetParamsAndData = () => {
  summarizeTable.value.setTableData([]);
  transactionTable.value.setTableData([]);
  refundTable.value.setTableData([]);
};
</script>

<style scoped lang="scss">
.table-content {
  display: flex;
  flex-direction: row;
  gap: 10px;
  height: calc(100vh - 180px);
  margin-top: 10px;
  overflow: hidden;
  .summary-table,
  .details-table {
    display: flex;
    flex-direction: column;
    gap: 10px;
    height: 100%;
  }
  .summary-table {
    width: 55%;
  }
  .details-table {
    width: 45%;
    background-color: #ffffff;
  }

  .description {
    line-height: 20px;
    padding: 10px;
    color: rgba(0, 0, 0, 0.7);
    .description-title {
      display: flex;
      align-items: center;
      gap: 6px;
    }
    .subtitle {
      text-indent: 1em;
      &.indent {
        text-indent: 2em;
        margin-left: -3px;
      }
    }
  }
}
</style>
