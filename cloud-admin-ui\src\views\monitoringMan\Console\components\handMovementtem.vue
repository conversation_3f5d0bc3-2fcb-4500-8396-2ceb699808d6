<template>
  <el-dialog v-model="dialogVisible" title="手动补录" width="400px" :before-close="handleManualRecordClose" :close-on-click-modal="false">
    <el-form ref="manualRecordFormRef" :model="manualRecord.form" label-position="top">
      <el-form-item label="车牌号" class="required">
        <el-input v-model="duty.callInfo.plate_no" readonly="true" style="width: 100%" :min="0" :precision="2" />
      </el-form-item>
      <el-form-item prop="gateway_id" label="入口通道">
        <el-select v-model="manualRecord.form.park_in_gateway_id" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in manualRecord.in_gateways" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="入场时间" class="required">
        <el-date-picker
          v-model="manualRecord.form.in_time"
          type="datetime"
          style="width: 100%"
          placeholder="入场时间"
          format="YYYY-MM-DD HH:mm:ss"
          value-format="YYYY-MM-DD HH:mm:ss"
        />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="submitManualRecord(manualRecordFormRef)">确 定</el-button>
        <el-button @click="handleManualRecordClose()">取 消</el-button>
      </el-space>
    </div>
  </el-dialog>
</template>
<script name="onTocars" setup>
import commonService from '@/service/common/CommonService';
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { dayjs, ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
const duty = useDuty();
const out_reason_options = ref([]);
const manualRecord = reactive({
  dialogVisible: false,
  form: {
    park_in_gateway_id: undefined,
    park_out_gateway_id: duty.callInfo.gateway_id,
    in_time: undefined,
    plate_no: duty.callInfo.plate_no
  },
  in_gateways: []
});
const param = [
  {
    enum_key: 'out_reason_options',
    enum_value: 'EnumOutReason'
  }
];
commonService.findEnums('park', param).then((response) => {
  out_reason_options.value = response.data.out_reason_options;
});
const dialogVisible = ref(false);
// 查询岗亭值守的通道信息
const querySentryGateway = () => {
  const param = {
    park_id: duty.callInfo.park_id,
    type: 2
  };
  UnattendedApi.querySentryGateway(param).then((res) => {
    if (res.success) {
      manualRecord.in_gateways = res.data.filter((item) => item.type === 2);
    } else {
      ElMessage({
        message: res.detail_message ? res.detail_message : res.message,
        type: 'error'
      });
    }
  });
};
const emits = defineEmits(['sdblInit']);
const manualRecordFormRef = ref(null);
//手动补录提交
const submitManualRecord = debounce((manualRecordFormRef) => {
  if (manualRecord.form.in_time === null || manualRecord.form.in_time === undefined) {
    ElMessage({
      message: '请选择入场时间，无入场时间无法补录！',
      type: 'warning'
    });
    return;
  }
  if (dayjs(manualRecord.form.in_time).isAfter(dayjs(duty.callInfo.event_time))) {
    ElMessage({
      message: '入场时间不能大于进场时间！',
      type: 'warning'
    });
    return;
  }
  if (manualRecord.form.park_in_gateway_id === null || manualRecord.form.park_in_gateway_id === undefined) {
    ElMessage({
      message: '请选择入场通道，无入场时间无法补录！',
      type: 'warning'
    });
    return;
  }
  manualRecordFormRef.validate().then(() => {
    let paramStr = `?parkId=${duty.callInfo.park_id}&inTime=${manualRecord.form.in_time}&plateNo=${duty.callInfo.plate_no}&parkInGatewayId=${manualRecord.form.park_in_gateway_id}&parkOutGatewayId=${duty.callInfo.gateway_out_id}`;
    UnattendedApi.cloudManualRecordCarInRecord(paramStr).then((res) => {
      if (res.success) {
        ElMessage({
          message: res.message,
          type: 'success'
        });
        // emits('sdblInit', true);
        emits('handMovementCallback');
        // duty.callInfo.carInRecord = {
        //   display: false,
        //   tip: undefined
        // };
        // duty.callInfo.has_car_in_record = true;
        // duty.callInfo.parkOrder = res.data;

        // if (res.data.park_in_record_id != null) {
        //   duty.callInfo.memo = '临停车，正常计费, 等待支付';
        // }
        manualRecordFormRef.resetFields();
        dialogVisible.value = false;
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
}, 800);
// 防抖工具函数
function debounce(fn, delay = 800) {
  let timer = null;
  return function (...args) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => {
      fn.apply(this, args);
    }, delay);
  };
}
//关闭手动补录
const handleManualRecordClose = () => {
  manualRecord.form = {
    in_time: undefined,
    plate_no: undefined,
    park_in_gateway_id: undefined
  };
  dialogVisible.value = false;
};

onMounted(() => {
  // getList(data.queryParams);
  // querySentryGateway
});

defineExpose({
  dialogVisible,
  querySentryGateway
});
</script>
<style lang="scss" scoped></style>
