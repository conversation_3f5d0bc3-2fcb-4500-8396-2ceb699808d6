<template>
  <div class="tab-container" :class="'tab-' + props.type">
    <div class="item" v-for="item in props.options" :key="item.value" :class="{ active: props.modelValue === item.value }" @click="handleClick(item)">
      {{ item.label }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  // tab数据源
  options: {
    type: Array,
    default: () => []
  },
  // 默认选中项
  modelValue: {
    type: [String, Number],
    default: ''
  },
  type: {
    type: String,
    default: 'card'
  }
});
const emits = defineEmits(['change', 'update:modelValue']);
const handleClick = (val) => {
  emits('update:modelValue', val.value);
  emits('change', val.value);
};
// 平分宽度
const width = ref(100 / props.options.length + '%');
</script>

<style lang="scss" scoped>
.tab-container {
  height: 30px;
  box-sizing: border-box;
  border-right: 0px;
  width: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
  color: #333;
  .item {
    height: 30px;
    line-height: 30px;
    font-size: 16px;
    width: v-bind(width);
    text-align: center;
  }
  .active {
    color: #005bac;
  }
}
.tab-simple {
  margin: 5px auto !important;
  font-size: 14px !important;
  .active {
    color: #005bac;
  }
  .active::after {
    content: '';
    display: block;
    width: 60%;
    border-radius: 5px;
    margin: 0 auto;
    height: 3px;
    background-color: #005bac;
  }
}
.tab-card {
  border: 1px solid #e2e2e2;
  .item {
    border-right: 1px solid #e2e2e2;
  }
  .active {
    background-color: #035ba5;
    color: #fff;
  }
}
</style>
