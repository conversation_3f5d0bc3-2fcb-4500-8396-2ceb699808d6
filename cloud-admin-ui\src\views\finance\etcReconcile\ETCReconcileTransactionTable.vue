<template>
  <el-card class="transaction-table" shadow="never">
    <span class="title">ETC交易汇总（惠达平台数据）</span>
    <div ref="table">
      <el-table :data="tableData" v-loading="tableLoading" border>
        <el-table-column prop="stl_dat" align="center" width="120">
          <template v-slot:header>
            <div class="cell-with-diagonal">
              <span class="category"> 类别 </span>
              <span class="diagonal-line"></span>
              <span class="dateColumn"> 日期 </span>
            </div>
            <!-- 在这里插入自定义内容 -->
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="etc_need_total_money" label="应收总金额(元)" align="center" />
        <el-table-column prop="etc_real_total_money" label="实收总金额(元)" align="center" />
        <el-table-column prop="etc_total_cnt" label="交易成功笔数" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ETCReconcileSummarizeTable" setup>
import { ref } from 'vue';
const tableData = ref([]);
const tableLoading = ref(false);

const setLoading = (loading) => {
  tableLoading.value = loading;
};

const setTableData = (list) => {
  tableData.value = list;
};

defineExpose({
  setLoading,
  setTableData
});
</script>
<style lang="scss" scoped>
.transaction-table {
  .title {
    display: inline-block;
    width: 100%;
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }
  .el-card {
    height: auto;
  }
  .cell-with-diagonal {
    display: flex;
    flex-direction: column;
    .category,
    .dateColumn {
      position: relative;
      font-weight: 500;
    }
    .category {
      top: 0px;
      right: 0px;
      align-self: flex-end;
    }
    .diagonal-line {
      width: 100%;
      height: 100%;
      border-top: 1px solid rgba(195, 195, 195, 0.5); /* 调整颜色和样式 */
      transform: rotate(24deg);
    }
    .dateColumn {
      display: inline-block;
      width: 32px;
      left: 0px;
      bottom: 0px;
    }
  }
}
</style>
