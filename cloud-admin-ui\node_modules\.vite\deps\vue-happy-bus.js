import {
  getCurrentInstance,
  onUnmounted
} from "./chunk-56RVKWQJ.js";
import "./chunk-HUBM7RA2.js";

// node_modules/vue-happy-bus/dist/index.esm.js
var n = /* @__PURE__ */ new WeakMap();
var o = new function() {
  var e = this, t = this;
  this.on = function(t2, n2) {
    return e.eventMap.has(t2) || e.eventMap.set(t2, []), e.eventMap.get(t2).push(n2), function() {
      return e.off(t2, n2);
    };
  }, this.once = function(n2, o2) {
    var f2 = function e2() {
      t.off(n2, e2), o2.apply(void 0, [].slice.call(arguments));
    };
    return e.on(n2, f2), function() {
      return e.off(n2, f2);
    };
  }, this.emit = function(e2) {
    var n2 = arguments, o2 = t.eventMap.get(e2);
    Array.isArray(o2) && o2.forEach(function(e3) {
      "function" == typeof e3 && e3.apply(void 0, [].slice.call(n2, 1));
    });
  }, this.off = function(t2, n2) {
    if (t2)
      if ("function" == typeof n2) {
        if (e.eventMap.has(t2)) {
          var o2 = e.eventMap.get(t2).filter(function(e2) {
            return e2 !== n2;
          });
          e.eventMap.set(t2, o2);
        }
      } else
        e.eventMap.delete(t2);
    else
      e.eventMap.clear();
  }, this.eventMap = /* @__PURE__ */ new Map();
}();
var f = /* @__PURE__ */ new WeakSet();
var i = (o2) => {
  let i2 = getCurrentInstance();
  if (null === i2)
    return;
  const a2 = n.get(i2) || n.set(i2, []).get(i2);
  null == a2 || a2.push(o2), f.has(i2) || (f.add(i2), onUnmounted(() => {
    const e = i2 && n.get(i2);
    null == e || e.forEach((e2) => e2()), i2 = null;
  }, i2));
};
var a = (e, t) => {
  const n2 = o.on(e, t);
  return i(n2), n2;
};
var r = (e, t) => {
  const n2 = o.once(e, t);
  return i(n2), n2;
};
var u = o.off;
var c = o.emit;
export {
  c as $emit,
  u as $off,
  a as $on,
  r as $once
};
//# sourceMappingURL=vue-happy-bus.js.map
