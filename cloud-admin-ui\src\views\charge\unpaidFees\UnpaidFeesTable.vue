<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton btnType="default" :exportFunc="unpaidFeesService.exportParkPayRecords"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
        </DownloadButton>
      </el-space>
    </div>

    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 430px)">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button link type="primary"
              v-if="scope.row.order_state === 2 && (scope.row.refund_state === 3 || scope.row.refund_state === 4) && scope.row.payed_money !== 0"
              @click="refundMoney(scope.row)">
              申请退款
            </el-button>
            <el-button v-if="scope.row.order_state === 4" link type="primary" @click="writeOff(scope.row)">
              核销
            </el-button>
            <!-- <el-button link type="primary" @click="paymentReminder(scope.row)">
              催缴提醒
            </el-button> -->
            <el-button link type="primary" @click="writeOffListFn(scope.row)">
              核销记录
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="order_no" label="订单号" align="center" width="300" />
        <el-table-column prop="event_type" label="事件类型" align="center" width="150">
          <template #default="scope">
            <span>{{ scope.row.event_type ? scope.row.event_type : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="event_no" label="事件编号" align="center" width="150">
          <template #default="scope">
            <span>{{ scope.row.event_no ? scope.row.event_no : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" width="150" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="150" />
        <el-table-column prop="in_time" label="入场时间" align="center" width="175" />
        <el-table-column prop="in_gateway_name" label="入场通道" align="center" width="120" />
        <el-table-column prop="in_car_photo" label="入场图片" align="center" width="110">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="out_car_photo" label="出场图片" align="center" width="110">
          <template #default="scope">
            <el-button link type="primary" @click="checkOutPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_time" label="事件时间" align="center" width="175">
          <template #default="scope">
            <span>{{ scope.row.event_time ? scope.row.event_time : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="event_data" label="事件抓拍图" align="center" width="175">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture2(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_video" label="事件短视频" align="center" width="175" />
        <!-- <el-table-column prop="out_type_desc" label="跟车类型" align="center" width="175">
          <template #default="scope">
            <span>{{ scope.row.out_type_desc ? scope.row.out_type_desc : '--' }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="to_time" label="出场时间" align="center" width="175" />
        <el-table-column prop="out_gateway_name" label="出场通道" align="center" width="120" />
        <el-table-column prop="time" label="停车时长" align="center" width="100" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" width="100" />
        <el-table-column prop="stop_car_type_desc" label="停车类型" align="center" width="100">
          <template #default="scope">
            <span
              v-if="scope.row.stop_car_type === undefined || scope.row.stop_car_type === null || scope.row.stop_car_type === ''">
              临停车辆</span>
            <span v-else> {{ scope.row.stop_car_type_desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="plate_no" label="车牌号" align="center" width="100" />
        <el-table-column prop="order_state_desc" label="订单状态" align="center" width="100" />
        <el-table-column prop="pay_time" label="支付时间" align="center" width="100" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" width="100" />
        <el-table-column prop="pay_channel_desc" label="支付渠道" align="center" width="100" />
        <el-table-column prop="order_money" label="应交金额" align="center" width="100" />
        <el-table-column prop="debate_money" label="优惠金额" align="center" width="100" />
        <el-table-column prop="total_charge_money" label="核销减免" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.total_charge_money ? scope.row.total_charge_money : '0' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="should_pay_money" label="欠缴金额" align="center" width="100" />
        <el-table-column prop="payed_money" label="实缴金额" align="center" width="100" />
        <el-table-column prop="transfer_amount" label="找零金额" align="center" width="100">
          <template #default="scope">
            <span>{{ scope.row.transfer_amount ? scope.row.transfer_amount : '0' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="state" label="找零状态" align="center" width="100" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="100" />
        <el-table-column prop="charge_name" label="收费员" align="center" width="100" />
        <el-table-column prop="invoice_state_desc" label="发票状态" align="center" width="100" />
        <el-table-column prop="is_hong_kong_macau_desc" label="是否港澳车" align="center" width="100" />
        <el-table-column prop="pay_channel_order_no" label="交易单号" align="center" width="100" />
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <!-- 申请退款 -->
    <el-dialog v-if="refundDialogVisible" width="500px" title="申请退款" v-model="refundDialogVisible"
      @close="closeDialog(refund)">
      <el-form ref="refund" label-width="90px" :rules="data.rules" :model="data.refundForm">
        <el-form-item label=" 车牌号">
          {{ data.refundForm.plate_no }}
        </el-form-item>
        <el-form-item prop="refund_user" label="姓名">
          <el-input v-model="data.refundForm.refund_user" placeholder="姓名" />
        </el-form-item>
        <el-form-item prop="mobile" label="手机号">
          <el-input v-model="data.refundForm.mobile" placeholder="手机号" />
        </el-form-item>
        <el-form-item prop="refund_money" label="退款金额">
          <el-input v-model="data.refundForm.refund_money" placeholder="退款金额" />
        </el-form-item>
        <el-form-item prop="refund_channel" label="退款渠道">
          <el-select v-model="data.refundForm.refund_channel" style="width: 100%" placeholder="退款渠道">
            <el-option v-for="item in refundWayList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="refund_account" label="退款账号">
          <el-input v-model="data.refundForm.refund_account" placeholder="退款账号" />
        </el-form-item>
        <el-form-item prop="refund_reason" label="退款原因">
          <el-input v-model="data.refundForm.refund_reason" type="textarea" placeholder="退款原因" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="applyRefund(refund)">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 核销操作 -->
    <el-dialog v-if="writeOffDialogVisible" width="500px" title="核销操作" v-model="writeOffDialogVisible"
      @close="closeDialog2()">
      <el-form ref="refund2" label-width="90px" :rules="rules2" :model="formData2">
        <el-form-item label="欠缴金额">
          <el-input readonly v-model="formData2.should_pay_money" placeholder="减免金额"
            style="flex:0.9;margin-right: 10px;" />元

        </el-form-item>
        <el-form-item prop="type" label="核销类型">
          <el-select v-model="formData2.type" style="width: 100%" placeholder="核销类型" @change="typeChange">
            <el-option v-for="item in typeList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="chargeoff_money" label="减免金额">
          <el-input :readonly="formData2.type == 0" v-model="formData2.chargeoff_money" placeholder="减免金额"
            style="flex:0.9;margin-right: 10px;" />元
        </el-form-item>
        <el-form-item prop="reason_group_id" label="原因分类">
          <el-select v-model="formData2.reason_group_id" style="width: 100%" placeholder="原因分类">
            <el-option v-for="item in reasonGroupIdList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="reason" label="原因说明">
          <el-input maxlength="100" resize='none' v-model="formData2.reason" type="textarea" placeholder="原因说明"
            :rows="3" />
          <div style="text-align: right;color: blue;cursor: pointer;width: 100%;" @click="speedWrite">快捷用语></div>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="writeOffDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="writeOffDialogVisibleSave(refund2)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 常用快捷用语 -->
    <el-dialog v-if="speedWriteVisible" width="500px" title="核销操作" v-model="speedWriteVisible" @close="closeDialog3()">
      <el-radio-group v-model="speedradio" style="display: flex; flex-direction: column;align-items: baseline;">
        <el-radio v-for="(item, i) in radioList" :key="i" :value="item.value">{{ item.label }}</el-radio>
      </el-radio-group>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="speedWriteVisible = false">取 消</el-button>
          <el-button type="primary" @click="speedradioConfirm()">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 核销记录 -->
    <el-dialog v-if="verificationRecordVisible" width="800px" title="核销记录" v-model="verificationRecordVisible"
      @close="closeDialog3()">
      <div ref="table2">
        <el-table :data="writeOffListTable" v-loading="loading" border
          :style="{ 'max-height': writeOffListTable.length > 0 ? 'calc(100vh - 430px)' : '100px' }"
          style="overflow-y: auto;">
          <el-table-column prop="in_charge_off_type_desc" label="核销类型" align="center" width="" />
          <el-table-column prop="chargeoff_money" label="本次核免金额" align="center" width="" />
          <el-table-column prop="in_reason_id_type_desc" label="原因分类" align="center" width="" />
          <el-table-column prop="reason" label="原因说明" align="center" width="" />
          <el-table-column prop="user_name" label="核销操作人" align="center" width="" />
          <el-table-column prop="created_at" label="核销操作时间" align="center" width="" />
        </el-table>
      </div>
      <div>累计核免 {{ writeOffListTableAllNum }}元</div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="verificationRecordVisible = false">关 闭</el-button>
          <!-- <el-button type="primary" @click="applyRefund(refund)">确 定</el-button> -->
        </span>
      </template>
    </el-dialog>

  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="ParkFeeTable" setup>
import DownloadButton from '@/components/DownloadButton.vue';
import unpaidFeesService from '@/service/charge/UnpaidFeesService';
import commonService from '@/service/common/CommonService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onActivated, onMounted, reactive, ref } from 'vue';
const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const refund = ref();
const refund2 = ref();
const refundWayList = ref([]);
const refundDialogVisible = ref(false);
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const title = ref('');
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  refundForm: {
    id: undefined,
    refund_user: undefined,
    mobile: undefined,
    refund_money: undefined,
    should_pay_money: undefined,
    refund_channel: undefined,
    refund_account: undefined,
    refund_reason: undefined,
    plate_no: undefined
  },
  rules: {
    refund_user: [
      {
        required: true,
        message: '请输入退款人姓名',
        trigger: 'blur'
      }
    ],
    mobile: [
      {
        required: true,
        message: '请输入退款人手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    refund_money: [
      {
        required: true,
        message: '请输入退款金额',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: (rule, value, callback) => {
          if (value > data.refundForm.should_pay_money) {
            callback(new Error('退款金额不能大于实缴金额!'));
          } else {
            callback();
          }
        }
      }
    ],
    refund_channel: [
      {
        required: true,
        message: '请选择退款渠道',
        trigger: 'change'
      }
    ],
    refund_account: [
      {
        required: true,
        message: '请输入退款账号',
        trigger: 'blur'
      }
    ]
  }
});
const rules2 = {
  chargeoff_money: [
    {
      required: true,
      message: '请输入减免金额',
      trigger: 'blur'
    },
    {
      trigger: 'blur',
      validator: (rule, value, callback) => {
        if (value > formData2.should_pay_money) {
          callback(new Error('减免金额不能大于欠缴金额!'));
        } else {
          callback();
        }
      }
    }
  ],
  type: [
    {
      required: true,
      message: '请选择核销类型',
      trigger: 'change'
    }
  ],
  reason_group_id: [
    {
      required: true,
      message: '请选择原因分类',
      trigger: 'change'
    }
  ],
  reason: [
    {
      required: true,
      message: '请输入原因说明',
      trigger: 'blur'
    }
  ]
}
const formData2 = reactive({
  debt_money: null,
  type: null,
  chargeoff_money: null,
  reason_group_id: null,
  reason: null,
  should_pay_money: null
})
const typeList = ref([
  { value: 0, key: '全额核销减免' },
  { value: 1, key: '部分核销减免' },
])
const reasonGroupIdList = ref([
  { value: 1, key: '坏账核销' },
  { value: 2, key: '客户协商核减' },
  { value: 3, key: '平台软件故障' },
  { value: 4, key: '平台硬件故障/缺陷' },
  { value: 5, key: '车场断网/断电' },
  { value: 6, key: '特殊车辆核销' },
])
onActivated(() => {
  // getList(data.queryParams);
  initSelects();
});

onMounted(() => {
  data.queryParams.order_states = [4];
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = (event) => {
  console.log('接收', event.data, event);
  getList(data.queryParams);
};
const initSelects = () => {
  const param = [
    {
      enum_key: 'refundWayList',
      enum_value: 'EnumRefundChannelType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    refundWayList.value = response.data.refundWayList;
  });
};

const getList = (params) => {
  if (!params?.park_id || params?.park_id == '') return;
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;

  const { park_name, ...newParams } = params;
  unpaidFeesService.recordsPage(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 查看入场图片
const checkInPicture = (row) => {
  if (row.in_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '入场图片';
    dialogImageUrl.value = row.in_car_photo_url;
  }
};
// 事件抓拍图
const checkInPicture2 = (row) => {
  if (row.event_data === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '事件抓拍图';
    dialogImageUrl.value = row.event_data;
  }
};
const typeChange = () => {
  if (formData2.type == 0) {
    formData2.chargeoff_money = formData2.should_pay_money
  } else {
    formData2.chargeoff_money = null
  }
}
// 查看出场图片
const checkOutPicture = (row) => {
  if (row.out_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '出场图片';
    dialogImageUrl.value = row.out_car_photo_url;
  }
};

// 申请退款
const refundMoney = (row) => {
  data.refundForm = {
    id: row.id,
    park_id: row.park_id,
    order_no: row.order_no,
    refund_user: row.refund_user,
    mobile: row.mobile,
    refund_money: row.refund_money,
    should_pay_money: row.should_pay_money,
    refund_channel: row.refund_channel,
    refund_account: row.refund_account,
    refund_reason: row.refund_reason,
    plate_no: row.plate_no
  };
  data.refundForm.plate_no = row.plate_no;
  refundDialogVisible.value = true;
};
const applyRefund = (refund) => {
  refund.validate().then(() => {
    unpaidFeesService
      .applyRefund(data.refundForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          refundDialogVisible.value = false;

          // 表单流程  停车缴费-临停退款申请单
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const closeDialog = (refund) => {
  refund.resetFields();
};
const closeDialog2 = () => {
  writeOffDialogVisible.value = false;
};
const closeDialog3 = () => {
  verificationRecordVisible.value = false;
};
const changeRowData = ref({})
// 核销操作
const writeOffDialogVisible = ref(false);
const writeOff = (row) => {
  formData2.type = null
  formData2.chargeoff_money = null
  formData2.reason_group_id = null
  formData2.reason = null
  changeRowData.value = row;
  writeOffDialogVisible.value = true;
  formData2.debt_money = row.order_money - row.debate_money - row.should_pay_money - row.total_charge_money
  formData2.order_id = row.id
  formData2.should_pay_money = row.should_pay_money
};
const writeOffDialogVisibleSave = (refund2) => {
  refund2.validate().then(() => {
    console.log("通过")
    unpaidFeesService.chargeoffsave(formData2).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: res.data.data
        });
        writeOffDialogVisible.value = false;
        getList(data.queryParams);
      } else {
        ElMessage({
          message: res.data.message,
          type: 'error'
        });
      }

    })
  })
}
// 催缴提醒
const paymentReminder = (row) => {
  ElMessageBox.confirm('确定要对该笔欠逃费订单推送催缴提醒吗？', '催缴提醒', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    unpaidFeesService.pushRemind(row.id).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: res.data.data
        });
      } else {
        ElMessage({
          message: res.data.message,
          type: 'error'
        });
      }
    })
  });
};

// 核销记录
const verificationRecordVisible = ref(false);
const writeOffListTable = ref([])
const writeOffListTableAllNum = ref(0)
const writeOffListFn = (item) => {
  changeRowData.value = item;
  writeOffListTable.value = [];
  writeOffListTableAllNum.value = 0
  unpaidFeesService.chargeofflistid(item.id).then((res) => {
    if (res.data && res.data.length > 0) {
      writeOffListTable.value = res.data
      res.data.forEach((item) => {
        writeOffListTableAllNum.value += Number(item.chargeoff_money)
      })
      writeOffListTableAllNum.value = writeOffListTableAllNum.value.toFixed(2)
    }

  })
  verificationRecordVisible.value = true
};
//快捷用语
const speedradio = ref(0);
const speedWriteVisible = ref(false);
const speedWrite = () => {
  speedWriteVisible.value = true;
}
const radioList = ref([
  { value: 0, label: '订单有争议，与车主友好协商，对本笔欠费订单做全额/部分减免核销处理。' },
  { value: 1, label: '车主拒不缴纳，追缴多次无果，内部作坏账核销处理。' },
  { value: 2, label: '无法联系到车主，内部作坏账核销处理。' },
  { value: 3, label: '欠费订单有争议，核查监控影像，无法判明其行为欠逃费。' },
  { value: 4, label: '车道监控重报、误报事件，导致系统生成欠费订单。' },
  { value: 5, label: '出入场时间有误/系统故障，导致多计费，进行相应金额核减纠正处置。' },
])
const speedradioConfirm = () => {
  formData2.reason = radioList.value[speedradio.value].label
  speedWriteVisible.value = false
}

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
