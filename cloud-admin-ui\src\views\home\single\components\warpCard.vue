<template>
  <div class="card" :class="props.size">
    <div class="title" v-if="props.title">{{ props.title }}</div>
    <div class="main-container">
      <slot></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';

const props = defineProps({
  height: {
    type: String, // 高度
    default: '240px'
  },
  title: {
    type: String,
    default: ''
  }
});

const height = ref(props.height);

const mainHeight = computed(() => {
  if (props.title) {
    return 'calc(100% - 60px)';
  } else {
    return '100%';
  }
});
</script>

<style scoped lang="scss">
.card {
  background-size: 100% 100%;
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  height: v-bind(height);
  width: 100%;
  overflow: hidden;
  .title {
    height: 60px;
    line-height: 60px;
    font-weight: 550;
    font-size: 18px;
    color: #2d2d2d;
    text-align: left;
    position: relative;
    padding: 0 20px;
    padding-left: 30px;
    &::before {
      content: '';
      position: absolute;
      left: 20px;
      top: 22px;
      width: 4px;
      height: 16px;
      background: #005bac;
      margin-right: 8px;
    }
  }
  .main-container {
    width: 100%;
    height: v-bind(mainHeight);
    padding: 0px 20px 20px 20px;
  }
}
</style>
