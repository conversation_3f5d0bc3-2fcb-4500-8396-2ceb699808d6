<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-button type="primary" @click="handleCreate()">添加终端</el-button>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="terminal_code" label="终端编码" align="center" min-width="100" />
        <el-table-column prop="name" label="终端名称" align="center" min-width="40" />
        <el-table-column prop="park_region_name" label="所属子场" align="center" />
        <el-table-column prop="terminal_auth_code" label="终端授权码" align="center" min-width="120" />
        <el-table-column prop="updated_at" label="更新时间" align="center" min-width="50" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog title="添加终端" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
      <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
        <el-form-item prop="name" label="终端名称">
          <el-input v-model="data.form.name" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.form.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createCancel(addForm)">取 消</el-button>
          <el-button type="primary" @click="createSentry(addForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="编辑终端" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
      <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
        <el-form-item prop="name" label="终端名称">
          <el-input v-model="data.updateForm.name" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.updateForm.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateCancel(editForm)">取 消</el-button>
          <el-button type="primary" @click="updateSentry(editForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script name="TerminalTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import terminalService from '@/service/park/ParkSentryTerminalService';
import parkRegionService from '@/service/park/ParkRegionService';
import { useRoute } from 'vue-router';

const route = useRoute();
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const parkRegions = ref([]);
const loading = ref(false);
const park_id = ref('');
const total = ref(0);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    park_id: undefined,
    name: undefined,
    park_region_id: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入终端名称',
        trigger: 'blur'
      }
    ],
    park_region_id: [
      {
        required: true,
        message: '请选择所属子场',
        trigger: 'change'
      }
    ]
  }
});
onActivated(() => {
  park_id.value = route.query.park_id;
  data.queryParams.park_id = route.query.park_id;
  getList(data.queryParams);
  getRegions();
});

const getRegions = () => {
  parkRegionService.listParkRegion(park_id.value).then((response) => {
    parkRegions.value = response;
  });
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  terminalService.pagingSentryTerminals(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: park_id.value,
    name: undefined,
    park_region_id: undefined
  };
  createDialogVisible.value = true;
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createSentry = (addForm) => {
  addForm.validate().then(() => {
    terminalService
      .createSentryTerminal(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: park_id.value,
    name: row.name,
    park_region_id: row.park_region_id
  };
  updateDialogVisible.value = true;
};
const updateSentry = (editForm) => {
  editForm.validate().then(() => {
    terminalService
      .updateSentryTerminal(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    terminalService
      .deleteSentryTerminal(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
