<template>
  <div class="container">
    <vehicle-model-search ref="searchRef" @form-search="searchVehicleModelList" @reset="resetParamsAndData" />
    <vehicle-model-table ref="table" />
  </div>
</template>

<script setup name="VehicleModel">
import VehicleModelSearch from './vehicleModel/VehicleModelSearch.vue';
import VehicleModelTable from './vehicleModel/VehicleModelTable.vue';
import { ref, reactive, defineExpose } from 'vue';

const table = ref(null);
const searchRef = ref(null);
const params = reactive({});

const pageHasVehicleModel = (queryParams) => {
  table.value.getList(queryParams);
};

const searchVehicleModelList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
const runSearchRef = () => {
  searchRef.value.handleDataSearch();
};
defineExpose({
  pageHasVehicleModel,
  runSearchRef
});
</script>
