<template>
  <div>
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>菜单配置</span>
          <div>
            <el-button type="primary" @click="createRootMenu"> 添加根菜单 </el-button>
            <el-button type="primary" @click="createChildMenu"> 添加子菜单 </el-button>
            <el-button type="primary" @click="createPage"> 添加页面 </el-button>
          </div>
        </div>
      </template>
      <div>
        <el-row :gutter="5">
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu' || data.menuConfig.type === null || data.menuConfig.type === undefined">
              <el-col :span="7" class="grid-content-right"> 菜单ID： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.id" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 页面ID： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.id" readonly />
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu' || data.menuConfig.type === null || data.menuConfig.type === undefined">
              <el-col :span="7" class="grid-content-right"> 菜单名称： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.name" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 页面名称： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.name" readonly />
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu' || data.menuConfig.type === null || data.menuConfig.type === undefined">
              <el-col :span="7" class="grid-content-right"> 菜单编码： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.code" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 页面编码： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.code" readonly />
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu' || data.menuConfig.type === null || data.menuConfig.type === undefined">
              <el-col :span="7" class="grid-content-right"> 菜单类型： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.type_display" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 页面类型： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.type" readonly />
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row :gutter="5" style="margin-top: 16px">
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu' || data.menuConfig.type === null || data.menuConfig.type === undefined">
              <el-col :span="7" class="grid-content-right"> 菜单权重： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.weight" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 页面权重： </el-col>
              <el-col :span="17">
                <div style="line-height: 32px">
                  <el-input :value="data.menuConfig.weight" readonly />
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5">
              <el-col :span="7" class="grid-content-right"> URL： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.url" readonly />
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5">
              <el-col :span="7" class="grid-content-right"> 菜单图标： </el-col>
              <el-col :span="17">
                <div style="line-height: 32px">
                  <el-icon size="14" v-if="data.menuConfig.icon !== undefined && data.menuConfig.icon !== null">
                    <component :is="data.menuConfig.icon"></component>
                  </el-icon>
                  <span v-else>--</span>
                </div>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="6">
            <el-row :gutter="5" v-if="data.menuConfig.type === 'menu'">
              <el-col :span="7" class="grid-content-right"> 是否启用： </el-col>
              <el-col :span="17">
                <el-input :value="data.menuConfig.enabled_display" readonly />
              </el-col>
            </el-row>
            <el-row :gutter="5" v-if="data.menuConfig.type === 'page'">
              <el-col :span="7" class="grid-content-right"> 是否启用： </el-col>
              <el-col :span="17">
                <span>--</span>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
        <el-row v-if="data.menuConfig.id" :gutter="5" style="margin-top: 16px">
          <el-col :span="24" style="text-align: right; line-height: 30px">
            <el-button v-if="data.menuConfig.type === 'menu'" type="success" @click="updateMenu"> 修改菜单 </el-button>
            <el-button v-if="data.menuConfig.type === 'page'" type="success" @click="updatePage"> 修改页面 </el-button>
            <el-button v-if="data.menuConfig.type === 'menu'" type="danger" @click="deleteMenu"> 删除菜单 </el-button>
            <el-button v-if="data.menuConfig.type === 'page'" type="danger" @click="deletePage"> 删除页面 </el-button>
          </el-col>
        </el-row>
      </div>
    </el-card>
    <!-- 添加根菜单 -->
    <el-dialog
      title="添加根菜单"
      v-model="createRootMenuDialogVisible"
      :close-on-click-modal="false"
      @close="closeAddRootDialog(rootFormRef)"
      width="500px"
    >
      <el-form ref="rootFormRef" :model="data.rootCreateForm" label-width="110px" :rules="data.rootRules">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="data.rootCreateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单编码" prop="code">
          <el-input v-model="data.rootCreateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单分类" prop="menu_category_id">
          <el-select v-model="data.rootCreateForm.menu_category_id" style="width: 100%">
            <el-option v-for="item in categories" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="菜单类型" prop="type">
          <el-select v-model="data.rootCreateForm.type" style="width: 100%">
            <el-option v-for="item in menuTypes" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转地址" prop="icon">
          <el-input v-model="data.rootCreateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="data.rootCreateForm.icon" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序" prop="weight">
          <el-input-number v-model="data.rootCreateForm.weight" :controls="false" :min="0" :precision="0" style="width: 100%" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="data.rootCreateForm.enabled" :active-value="1" active-text="启用" :inactive-value="0" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelCreateRootMenu(rootFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitCreateRootMenu(rootFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 添加子菜单 -->
    <el-dialog
      title="添加子菜单"
      v-model="createChildMenuDialogVisible"
      :close-on-click-modal="false"
      @close="closeAddChildDialog(createChildFormRef)"
      width="500px"
    >
      <el-form ref="createChildFormRef" :model="data.childCreateForm" label-width="110px" :rules="data.ChildAddRules">
        <el-form-item label="父级菜单" prop="parent_menu_name">
          <el-input :value="data.childCreateForm.parent_menu_name" readonly />
        </el-form-item>
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="data.childCreateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单编码" prop="code">
          <el-input v-model="data.childCreateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="data.childCreateForm.icon" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单类型" prop="type">
          <el-select v-model="data.childCreateForm.type" style="width: 100%">
            <el-option v-for="item in menuTypes" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="跳转地址" prop="url">
          <el-input v-model="data.childCreateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序" prop="weight">
          <el-input-number v-model="data.childCreateForm.weight" :controls="false" :min="0" :precision="0" style="width: 100%" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="data.childCreateForm.enabled" :active-value="1" active-text="启用" :inactive-value="0" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelCreateChildMenu(createChildFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitCreateChildMenu(createChildFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 添加页面 -->
    <el-dialog
      title="添加页面"
      v-model="createPageDialogVisible"
      :close-on-click-modal="false"
      @close="closeAddPageDialog(createPageFormRef)"
      width="500px"
    >
      <el-form ref="createPageFormRef" :model="data.pageCreateForm" label-width="100px" :rules="data.pageRules">
        <el-form-item label="父级菜单" prop="parent_menu_name">
          <el-input v-model="data.pageCreateForm.parent_menu_name" readonly />
        </el-form-item>
        <el-form-item label="页面名称" prop="name">
          <el-input v-model="data.pageCreateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面编码" prop="code">
          <el-input v-model="data.pageCreateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面权重" prop="weight">
          <el-input v-model="data.pageCreateForm.weight" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面URL" prop="url">
          <el-input v-model="data.pageCreateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="配置信息" prop="config_info">
          <el-input v-model="data.pageCreateForm.config_info" type="textarea" :rows="6" style="width: 100%" placeholder="请输入" />
          <div style="color: rgba(0, 0, 0, 0.6); line-height: 22px">
            配置信息为JSON格式，可配置项包括：
            <div>&emsp;visible: 页面可见(1-显示; 0-隐藏;)</div>
            <div>&emsp;cached: 页面缓存(1-缓存; 0-不缓存;)</div>
            <div>&emsp;component: 页面组件名称</div>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="data.pageCreateForm.memo" type="textarea" maxlength="100" :rows="3" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelCreatePage(createPageFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitCreatePage(createPageFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 修改页面 -->
    <el-dialog
      title="修改页面"
      v-model="updatePageDialogVisible"
      :close-on-click-modal="false"
      @close="closeEditPageDialog(updatePageFormRef)"
      width="500px"
    >
      <el-form ref="updatePageFormRef" :model="data.pageUpdateForm" label-width="100px" :rules="data.pageRules">
        <el-form-item label="页面名称" prop="name">
          <el-input v-model="data.pageUpdateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面编码" prop="code">
          <el-input v-model="data.pageUpdateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面权重" prop="weight">
          <el-input v-model="data.pageUpdateForm.weight" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="页面URL" prop="url">
          <el-input v-model="data.pageUpdateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="配置信息" prop="config_info">
          <el-input v-model="data.pageUpdateForm.config_info" type="textarea" :rows="6" style="width: 100%" placeholder="请输入" />
          <div style="color: rgba(0, 0, 0, 0.6); line-height: 22px">
            配置信息为JSON格式，可配置项包括：
            <div>&emsp;visible: 页面可见(1-显示; 0-隐藏;)</div>
            <div>&emsp;cached: 页面缓存(1-缓存; 0-不缓存;)</div>
            <div>&emsp;component: 页面组件名称</div>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="memo">
          <el-input v-model="data.pageUpdateForm.memo" type="textarea" maxlength="100" :rows="3" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelUpdatePage(updatePageFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitUpdatePage(updatePageFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 修改根菜单 -->
    <el-dialog
      title="修改菜单"
      v-model="updateRootMenuDialogVisible"
      :close-on-click-modal="false"
      @close="closeEditRootDialog(rootUpFormRef)"
      width="500px"
    >
      <el-form ref="rootUpFormRef" :model="data.rootUpdateForm" label-width="110px" :rules="data.rootRules">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="data.rootUpdateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单编码" prop="code">
          <el-input v-model="data.rootUpdateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="跳转地址" prop="url">
          <el-input v-model="data.rootUpdateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="data.rootUpdateForm.icon" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序" prop="weight">
          <el-input-number v-model="data.rootUpdateForm.weight" :controls="false" :min="0" :precision="0" style="width: 100%" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="data.rootUpdateForm.enabled" :active-value="1" active-text="启用" :inactive-value="0" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelUpdateRootMenu(rootUpFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitUpdateRootMenu(rootUpFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
    <!-- 修改子菜单 -->
    <el-dialog
      title="修改菜单"
      v-model="updateChildMenuDialogVisible"
      :close-on-click-modal="false"
      @close="closeEditChildDialog(updateChildFormRef)"
      width="500px"
    >
      <el-form ref="updateChildFormRef" :model="data.childUpdateForm" label-width="110px" :rules="data.childUpdateRules">
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="data.childUpdateForm.name" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="菜单编码" prop="code">
          <el-input v-model="data.childUpdateForm.code" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="图标" prop="icon">
          <el-input v-model="data.childUpdateForm.icon" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="跳转地址" prop="url">
          <el-input v-model="data.childUpdateForm.url" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序" prop="weight">
          <el-input-number v-model="data.childUpdateForm.weight" :controls="false" :min="0" :precision="0" style="width: 100%" placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否启用" prop="enabled">
          <el-switch v-model="data.childUpdateForm.enabled" :active-value="1" active-text="启用" :inactive-value="0" inactive-text="停用" />
        </el-form-item>
      </el-form>
      <div style="text-align: right">
        <el-button @click="cancelUpdateChildMenu(updateChildFormRef)"> 取 消 </el-button>
        <el-button type="primary" @click="submitUpdateChildMenu(updateChildFormRef)"> 确 定 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script name="MenuConfig" setup>
import menuService from '@/service/system/MenuService';
import pageService from '@/service/system/PageService';
import commonService from '@/service/common/CommonService';
import { reactive, ref, watch, onActivated } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';

const emits = defineEmits(['getMenuTree']);
const props = defineProps({
  menu: {
    type: Object,
    required: true,
    default: () => ({
      id: undefined,
      name: undefined
    })
  }
});
const rootFormRef = ref();
const rootUpFormRef = ref();
const createChildFormRef = ref();
const updateChildFormRef = ref();
const createPageFormRef = ref();
const updatePageFormRef = ref();
const categories = ref([]);
const menuTypes = ref([]);
const createRootMenuDialogVisible = ref(false);
const updateRootMenuDialogVisible = ref(false);
const createChildMenuDialogVisible = ref(false);
const updateChildMenuDialogVisible = ref(false);
const createPageDialogVisible = ref(false);
const updatePageDialogVisible = ref(false);
const data = reactive({
  menuConfig: {
    id: undefined,
    name: undefined,
    code: undefined,
    type: undefined,
    type_display: undefined,
    weight: undefined,
    enabled: undefined,
    url: undefined,
    icon: undefined,
    menu_category_id: undefined,
    parent_menu_id: undefined
  },
  rootCreateForm: {
    name: undefined,
    code: undefined,
    menu_category_id: undefined,
    parent_menu_id: 0,
    icon: undefined,
    type: undefined,
    url: undefined,
    weight: 10,
    enabled: 1
  },
  rootUpdateForm: {
    id: undefined,
    name: undefined,
    code: undefined,
    menu_category_id: undefined,
    parent_menu_id: 0,
    icon: undefined,
    type: undefined,
    url: undefined,
    weight: 10,
    enabled: 1
  },
  childCreateForm: {
    name: undefined,
    code: undefined,
    menu_category_id: undefined,
    parent_menu_id: undefined,
    parent_menu_name: undefined,
    type: undefined,
    url: undefined,
    icon: undefined,
    weight: 10,
    enabled: 1
  },
  childUpdateForm: {
    id: undefined,
    name: undefined,
    code: undefined,
    menu_category_id: undefined,
    parent_menu_id: undefined,
    parent_menu_name: undefined,
    icon: undefined,
    weight: 10,
    enabled: 1
  },
  pageCreateForm: {
    menu_id: undefined,
    parent_menu_name: undefined,
    name: undefined,
    code: undefined,
    url: undefined,
    weight: undefined,
    config_info: undefined,
    memo: undefined
  },
  pageUpdateForm: {
    menu_id: undefined,
    parent_menu_name: undefined,
    id: undefined,
    name: undefined,
    code: undefined,
    url: undefined,
    weight: undefined,
    config_info: undefined,
    memo: undefined
  },
  rootRules: {
    name: [
      {
        required: true,
        message: '请输入菜单名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入菜单编码',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择菜单类型',
        trigger: 'blur'
      }
    ],
    menu_category_id: [
      {
        required: true,
        message: '请选择菜单分类',
        trigger: 'change'
      }
    ],
    weight: [
      {
        required: true,
        message: '请输入排序',
        trigger: 'blur',
        type: 'number'
      }
    ]
  },
  ChildAddRules: {
    name: [
      {
        required: true,
        message: '请输入菜单名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入菜单编码',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择菜单类型',
        trigger: 'blur'
      }
    ],
    weight: [
      {
        required: true,
        message: '请输入排序',
        trigger: 'blur',
        type: 'number'
      }
    ]
  },
  childUpdateRules: {
    name: [
      {
        required: true,
        message: '请输入菜单名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入菜单编码',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择菜单类型',
        trigger: 'blur'
      }
    ],
    weight: [
      {
        required: true,
        message: '请输入排序',
        trigger: 'blur',
        type: 'number'
      }
    ]
  },
  pageRules: {
    name: [
      {
        required: true,
        message: '请输入页面名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入页面编码',
        trigger: 'blur'
      }
    ],
    weight: [
      {
        required: true,
        message: '请输入页面权重',
        trigger: 'blur'
      }
    ],
    url: [
      {
        required: true,
        message: '请输入页面URL',
        trigger: 'blur'
      }
    ]
  }
});

onActivated(() => {
  initSelects();
});

watch(
  () => props.menu,
  (newVal) => {
    if (newVal.type == 'menu') {
      // 是菜单
      if (newVal.id !== undefined) {
        // 去掉后台拼接的多余的部分,拿到真正的id
        let menuVal = '';
        if (newVal.id.substring(0, 3) == '100' || newVal.id.substring(0, 3) == '200') {
          menuVal = newVal.id.slice(3);
        }
        let param = {
          id: menuVal,
          menu_category_id: newVal.menu_category_id
        };
        menuService.getMenuById(param).then((response) => {
          if (response.success === true) {
            data.menuConfig = response.data;
            let i = 0;
            for (i = 0; i < menuTypes.value.length; i++) {
              if (menuTypes.value[i].value == data.menuConfig.type_display + '') {
                data.menuConfig.type_display = menuTypes.value[i].key;
              }
            }
            data.menuConfig.id = data.menuConfig.id.slice(3);
          } else {
            ElMessage({
              message: response.message,
              type: 'error'
            });
          }
        });
      } else {
        data.menuConfig = {
          id: undefined,
          name: undefined,
          code: undefined,
          type: undefined,
          type_display: undefined,
          weight: undefined,
          enabled: undefined,
          url: undefined,
          icon: undefined,
          menu_category_id: undefined,
          parent_menu_id: undefined
        };
      }
    } else if (newVal.type == 'page') {
      // 是页面
      pageService.getPageById(newVal.id).then((response) => {
        if (response.success === true) {
          data.menuConfig = response.data;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    }
  }
);
// 页面初始化查询菜单分类和枚举
const initSelects = () => {
  menuService.listMenuCategories().then((response) => {
    if (response.success === true) {
      categories.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
  const param = [
    {
      enum_key: 'menuTypes',
      enum_value: 'EnumMenuType'
    }
  ];
  commonService.findEnums('system', param).then((response) => {
    menuTypes.value = response.data.menuTypes;
  });
};
// 新建根菜单
const createRootMenu = () => {
  data.rootCreateForm.menu_category_id =
    props.menu.id !== undefined && props.menu.type === 'menu' && props.menu.menu_category_id !== undefined
      ? '' + props.menu.menu_category_id
      : categories.value[0].id;
  createRootMenuDialogVisible.value = true;
};
// 取消新建根菜单
const cancelCreateRootMenu = (rootFormRef) => {
  rootFormRef.resetFields();
  createRootMenuDialogVisible.value = false;
};
// 提交并保存新建根菜单
const submitCreateRootMenu = (rootFormRef) => {
  rootFormRef.validate().then(() => {
    menuService.createMenu(data.rootCreateForm).then((response) => {
      if (response.success) {
        createRootMenuDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        rootFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 新建子菜单
const createChildMenu = () => {
  if (props.menu.type !== 'menu') {
    ElMessage({
      message: '请选择一个菜单，且类型不能为页面',
      type: 'warning'
    });
    return;
  }
  data.childCreateForm.menu_category_id = props.menu.menu_category_id !== undefined ? '' + props.menu.menu_category_id : categories.value[0].id;
  data.childCreateForm.parent_menu_id = props.menu.id;
  data.childCreateForm.parent_menu_name = props.menu.name;
  createChildMenuDialogVisible.value = true;
};
// 取消新建子菜单
const cancelCreateChildMenu = (createChildFormRef) => {
  createChildFormRef.resetFields();
  createChildMenuDialogVisible.value = false;
};
// 提交并保存新建子菜单
const submitCreateChildMenu = (createChildFormRef) => {
  createChildFormRef.validate().then(() => {
    menuService.createMenu(data.childCreateForm).then((response) => {
      if (response.success) {
        createChildMenuDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        createChildFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 修改菜单
const updateMenu = () => {
  const _menu = data.menuConfig;
  if (props.menu.parent_menu_id === '0') {
    data.rootUpdateForm = {
      id: _menu.id,
      name: _menu.name,
      code: _menu.code,
      type: _menu.type,
      url: _menu.url,
      menu_category_id: _menu.menu_category_id,
      parent_menu_id: _menu.parent_menu_id,
      icon: _menu.icon,
      weight: _menu.weight,
      enabled: _menu.enabled
    };
    updateRootMenuDialogVisible.value = true;
  } else {
    data.childUpdateForm = {
      id: _menu.id,
      name: _menu.name,
      code: _menu.code,
      type: _menu.type,
      url: _menu.url,
      menu_category_id: _menu.menu_category_id,
      parent_menu_id: _menu.parent_menu_id,
      icon: _menu.icon,
      weight: _menu.weight,
      enabled: _menu.enabled
    };
    updateChildMenuDialogVisible.value = true;
  }
};
// 取消修改菜单
const cancelUpdateRootMenu = (rootUpFormRef) => {
  rootUpFormRef.resetFields();
  updateRootMenuDialogVisible.value = false;
};
// 提交并保存修改菜单
const submitUpdateRootMenu = (rootUpFormRef) => {
  rootUpFormRef.validate().then(() => {
    menuService.updateMenu(data.rootUpdateForm).then((response) => {
      if (response.success) {
        updateRootMenuDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        rootUpFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 取消修改子菜单
const cancelUpdateChildMenu = (updateChildFormRef) => {
  updateChildFormRef.resetFields();
  updateChildMenuDialogVisible.value = false;
};
// 提交并保存修改子菜单
const submitUpdateChildMenu = (updateChildFormRef) => {
  updateChildFormRef.validate().then(() => {
    menuService.updateMenu(data.childUpdateForm).then((response) => {
      if (response.success) {
        updateChildMenuDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        updateChildFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 新建页面
const createPage = () => {
  if (props.menu.type !== 'menu') {
    ElMessage({
      message: '请选择一个菜单，且类型不能为页面',
      type: 'warning'
    });
    return;
  }
  data.pageCreateForm.menu_id = props.menu.id;
  data.pageCreateForm.parent_menu_name = props.menu.name;
  createPageDialogVisible.value = true;
};
// 取消新建页面
const cancelCreatePage = (createPageFormRef) => {
  createPageFormRef.resetFields();
  createPageDialogVisible.value = false;
};
// 提交并保存新建页面
const submitCreatePage = (createPageFormRef) => {
  // 去掉后台拼接的多余的部分,拿到真正的id
  if (data.pageCreateForm.menu_id.substring(0, 3) == '100' || data.pageCreateForm.menu_id.substring(0, 3) == '200') {
    data.pageCreateForm.menu_id = data.pageCreateForm.menu_id.slice(3);
  }
  createPageFormRef.validate().then(() => {
    pageService.createPage(data.pageCreateForm).then((response) => {
      if (response.success) {
        createPageDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        createPageFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 修改页面
const updatePage = () => {
  const _page = data.menuConfig;
  data.pageUpdateForm = {
    id: _page.id,
    name: _page.name,
    code: _page.code,
    url: _page.url,
    weight: _page.weight,
    enabled: _page.enabled,
    config_info: _page.config_info,
    memo: _page.memo
  };
  updatePageDialogVisible.value = true;
};
// 取消修改页面
const cancelUpdatePage = (updatePageFormRef) => {
  updatePageFormRef.resetFields();
  updatePageDialogVisible.value = false;
};
// 提交并保存修改页面
const submitUpdatePage = (updatePageFormRef) => {
  updatePageFormRef.validate().then(() => {
    pageService.updatePage(data.pageUpdateForm).then((response) => {
      if (response.success) {
        updatePageDialogVisible.value = false;
        ElMessage({
          message: response.message,
          type: 'success'
        });
        updatePageFormRef.resetFields();
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 删除菜单
const deleteMenu = () => {
  if (props.menu.id === undefined) {
    ElMessage({
      message: '请选择一个菜单',
      type: 'warning'
    });
    return;
  }
  ElMessageBox.confirm('确定删除此菜单吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    menuService.deleteMenu(props.menu.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 删除页面
const deletePage = () => {
  if (props.menu.id === undefined) {
    ElMessage({
      message: '请选择一个页面',
      type: 'warning'
    });
    return;
  }
  ElMessageBox.confirm('确定删除此页面吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    pageService.deletePage(props.menu.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        emits('getMenuTree');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
const closeAddRootDialog = (rootFormRef) => {
  rootFormRef.resetFields();
};
const closeAddChildDialog = (createChildFormRef) => {
  createChildFormRef.resetFields();
};
const closeAddPageDialog = (createPageFormRef) => {
  createPageFormRef.resetFields();
};
const closeEditPageDialog = (updatePageFormRef) => {
  updatePageFormRef.resetFields();
};
const closeEditRootDialog = (rootUpFormRef) => {
  rootUpFormRef.resetFields();
};
const closeEditChildDialog = (updateChildFormRef) => {
  updateChildFormRef.resetFields();
};
</script>
<style lang="scss" scoped>
:deep(.el-card__header) {
  padding: 6px 10px;
}
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.grid-content-right {
  min-height: 32px;
  line-height: 32px;
  text-align: right;
  font-size: 10pt;
}
</style>
