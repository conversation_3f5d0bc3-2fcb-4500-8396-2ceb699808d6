<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加防控</el-button>
      </el-space>
      <div>
        <DownloadButton
          btnType="default"
          :exportFunc="preventListService.exportPreventLists"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 343px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="plate_no" label="车牌省市" align="center" />
        <el-table-column prop="effective_start_time" label="开始时间" align="center" />
        <el-table-column prop="effective_end_time" label="结束时间" align="center" />
        <el-table-column prop="state_desc" label="状态" align="center" />
        <el-table-column prop="updator_name" label="最后更新人" align="center" />
        <el-table-column prop="updated_at" label="更新时间" align="center" min-width="120" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加防控" v-model="preventCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择车场" prop="park_id">
            <el-input v-model="data.form.park_name" placeholder="请选择车场" readonly @click="authCharge(true, 'add')" />
          </el-form-item>
          <el-form-item label="车牌省市" class="required">
            <el-row :gutter="10">
              <el-col :span="6">
                <el-select v-model="value_before" clearable style="width: 100%">
                  <el-option v-for="item in plateNos" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
              </el-col>
              <el-col :span="18">
                <el-select v-model="value_after" multiple clearable style="width: 100%">
                  <el-option v-for="item in letterList" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="开始时间" class="required">
            <el-date-picker
              v-model="data.form.effective_start_time"
              type="date"
              format="YYYY-MM-DD"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <el-form-item label="结束时间" class="required">
            <el-date-picker
              v-model="data.form.effective_end_time"
              type="date"
              format="YYYY-MM-DD"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createPreventList(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改防控" v-model="preventUpdateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="选择车场" prop="park_id">
            <el-input v-model="data.updateForm.park_name" placeholder="请选择车场" readonly @click="authCharge(true, 'edit')" />
          </el-form-item>
          <el-form-item label="车牌省市" class="required">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-select v-model="value_before" clearable style="width: 100%">
                  <el-option v-for="item in plateNos" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
              </el-col>
              <el-col :span="16">
                <el-select v-model="value_after" multiple clearable style="width: 100%">
                  <el-option v-for="item in letterList" :key="item.label" :label="item.label" :value="item.label" />
                </el-select>
              </el-col>
            </el-row>
          </el-form-item>
          <el-form-item label="开始时间" class="required">
            <el-date-picker
              v-model="data.updateForm.effective_start_time"
              type="date"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <el-form-item label="结束时间" class="required">
            <el-date-picker
              v-model="data.updateForm.effective_end_time"
              type="date"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updatePreventList(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="PreventListTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import preventListService from '@/service/car/PreventListService';
import ParkFindBack from './ParkFindBack.vue';
import DownloadButton from '@/components/DownloadButton.vue';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const total = ref(0);
const loading = ref(false);
const preventCreateDialogVisible = ref(false);
const preventUpdateDialogVisible = ref(false);
const parkInfoDialogVisible = ref(false);
const value_before = ref('');
const value_after = ref([]);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const plateNos = ref([
  { label: '蒙' },
  { label: '京' },
  { label: '冀' },
  { label: '晋' },
  { label: '辽' },
  { label: '吉' },
  { label: '黑' },
  { label: '沪' },
  { label: '苏' },
  { label: '浙' },
  { label: '皖' },
  { label: '闽' },
  { label: '赣' },
  { label: '鲁' },
  { label: '豫' },
  { label: '鄂' },
  { label: '湘' },
  { label: '粤' },
  { label: '桂' },
  { label: '琼' },
  { label: '渝' },
  { label: '川' },
  { label: '贵' },
  { label: '云' },
  { label: '藏' },
  { label: '陕' },
  { label: '甘' },
  { label: '青' },
  { label: '宁' },
  { label: '新' }
]);
const letterList = ref([
  { label: 'A' },
  { label: 'B' },
  { label: 'C' },
  { label: 'D' },
  { label: 'E' },
  { label: 'F' },
  { label: 'G' },
  { label: 'H' },
  { label: 'I' },
  { label: 'J' },
  { label: 'K' },
  { label: 'L' },
  { label: 'M' },
  { label: 'N' },
  { label: 'O' },
  { label: 'P' },
  { label: 'Q' },
  { label: 'R' },
  { label: 'S' },
  { label: 'T' },
  { label: 'U' },
  { label: 'V' },
  { label: 'W' },
  { label: 'X' },
  { label: 'Y' },
  { label: 'Z' }
]);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: '',
    park_name: '',
    plate_no: undefined,
    effective_start_time: undefined,
    effective_end_time: undefined
  },
  updateForm: {},
  rules: {
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ]
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  preventListService.pagingPreventLists(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: '',
    park_name: '',
    plate_no: undefined,
    effective_start_time: undefined,
    effective_end_time: undefined
  };
  value_before.value = '';
  value_after.value = '';
  preventCreateDialogVisible.value = true;
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createPreventList = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.effective_start_time === '' || data.form.effective_start_time === undefined) {
      ElMessage({
        message: '请选择开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.form.effective_end_time === '' || data.form.effective_end_time === undefined) {
      ElMessage({
        message: '请选择结束时间',
        type: 'warning'
      });
      return false;
    }
    if (value_before.value == '' || value_before.value == undefined) {
      ElMessage({
        message: '请选择车牌省市',
        type: 'warning'
      });
      return false;
    }
    if (value_after.value == '' || value_after.value == undefined) {
      ElMessage({
        message: '请选择车牌省市',
        type: 'warning'
      });
      return false;
    }

    var plateNos = [];
    var plateNoArr = value_after.value;
    plateNoArr.forEach(function (element) {
      plateNos.push(value_before.value + element);
    });
    data.form.plate_no = plateNos.toString();

    delete data.form.park_name;
    preventListService
      .createPreventList(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          preventCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    preventListService
      .deletePreventList(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (val) => {
  data.updateForm = val;
  var plate_nos = [];
  value_after.value = [];
  plate_nos = val.plate_no.split(',');
  plate_nos.forEach(function (element) {
    value_after.value.push(element.substring(element.length - 1));
  });
  value_before.value = plate_nos[0].substring(0, plate_nos[0].length - 1);
  preventUpdateDialogVisible.value = true;
};
const updatePreventList = (editForm) => {
  editForm.validate().then(() => {
    if (data.updateForm.effective_start_time === '' || data.updateForm.effective_start_time === undefined) {
      ElMessage({
        message: '请选择开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.updateForm.effective_end_time === '' || data.updateForm.effective_end_time === undefined) {
      ElMessage({
        message: '请选择结束时间',
        type: 'warning'
      });
      return false;
    }
    if (value_after.value == '' || value_after.value == undefined) {
      ElMessage({
        message: '请选择车牌省市',
        type: 'warning'
      });
      return false;
    }

    var plateNos = [];
    var plateNoArr = value_after.value;
    plateNoArr.forEach(function (element) {
      plateNos.push(value_before.value + element);
    });
    data.updateForm.plate_no = plateNos.toString();
    delete data.updateForm.park_name;
    preventListService
      .updatePreventList(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          preventUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  preventCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  preventUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
