<template>
  <div>
    <div class="search-btn-group" v-loading="loading">
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.total_small_change }}元/{{ data.countData.total_small_sum }}笔</p>
        <span class="search-btn-group-total-label">找零总额</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.success_small_change }}元/{{ data.countData.success_small_sum }}笔</p>
        <span class="search-btn-group-total-label">找零成功</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.failure_small_change }}元/{{ data.countData.failure_small_sum }}笔</p>
        <span class="search-btn-group-total-label">找零失败</span>
      </div>
    </div>
  </div>
</template>

<script name="CashTransferTopGroups" setup>
import cashTransferService from '@/service/finance/CashTransferService';
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import '@/styles/searchBtnGroup.scss';

const loading = ref(false);
const data = reactive({
  queryParams: {},
  countData: {
    total_small_change: 0,
    total_small_sum: 0,
    success_small_change: 0,
    success_small_sum: 0,
    failure_small_change: 0,
    failure_small_sum: 0,
    process_small_change: 0,
    process_small_sum: 0
  }
});

onMounted(() => {
  data.queryParams.order_states = [];
});

const countTransferByState = (queryParams) => {
  loading.value = true;
  data.queryParams = queryParams;
  const { park_name, ...newQueryParams } = queryParams;
  cashTransferService.countTransferByState(newQueryParams).then((response) => {
    if (response.success === true) {
      data.countData = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  countTransferByState
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
</style>
