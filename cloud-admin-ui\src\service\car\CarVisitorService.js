import * as spaceRentApply from '@/api/car/CarVisitorServiceApi';

/**
 * 长租车
 */
export default {
  /**
   * 分页查询
   */
  pagingVisitorApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.pagingVisitorApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增访客车申请
   */
  createVisitorApply(data) {
    return spaceRentApply.createVisitorApply(data);
  },

  /**
   * 查看访客车详情
   */
  visitorApplyDetail(data) {
    return spaceRentApply.visitorApplyDetail(data);
  },

  /**
   * 修改访客车申请
   */
  updateVisitorApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.updateVisitorApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除访客车申请
   */
  deleteVisitorApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.deleteVisitorApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 提交审核访客车申请
   */
  submitAuditVisitorApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.submitAuditVisitorApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出访客车申请
   */
  exportVisitorApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.exportVisitorApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
