<template>
  <div class="container">
    <el-card shadow="never">
      <span class="label">选择停车场：</span>
      <el-input v-model="data.queryParams.park_name" v-if="timer" readonly placeholder="请选择停车场名称" style="width: 300px" />
      <el-input
        v-model="data.queryParams.park_name"
        v-else
        readonly
        @click="authCharge(true, '')"
        placeholder="请选择停车场名称"
        style="width: 300px"
      />
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <span class="label">选择通道：</span>
      <el-select v-if="timer" disabled v-model="data.queryParams.gateway_ids" placeholder="选择通道" clearable multiple style="width: 300px">
        <el-option v-for="item in gatewayList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      <el-select v-else v-model="data.queryParams.gateway_ids" placeholder="选择通道" clearable multiple style="width: 300px">
        <el-option v-for="item in gatewayList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <el-button type="primary" v-if="!timer" @click="handleCloudWatch()">值守</el-button>
      <el-button type="primary" v-else disabled @click="handleCloudWatch()">值守</el-button>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <el-button type="warning" @click="handleFinishCloudWatch()">结束值守</el-button>
      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
      <el-tag>{{ flag }}</el-tag>
    </el-card>
    <el-card>
      <el-row :gutter="10">
        <el-col :span="12" v-for="item in dataList" :key="item.gateway_id">
          <div class="content">
            <el-row :gutter="10">
              <el-col :span="12">
                <span v-if="item.park_region_name == null">当前子场：</span>
                <span v-else>当前子场：{{ item.park_region_name }}</span>
              </el-col>
              <el-col :span="12">
                <span>当前通道：{{ item.gateway_name }}</span>
              </el-col>
            </el-row>
            <el-row :gutter="10" style="margin: 5px 0">
              <el-col :span="24">
                <div style="display: flex; flex-direction: row; justify-content: space-around">
                  <img src="@/assets/cloud_watch.png" v-if="item.base64_image == ''" style="width: 622px; height: 262px" />
                  <img v-else :src="'data:image/png;base64,' + item.base64_image" style="width: 622px; height: 262px" />
                  <div style="display: flex; flex-direction: column; justify-content: space-around">
                    <el-button type="primary" @click="handleRefresh(item.gateway_id)" style="margin: 0 0 0 12px">刷新</el-button>
                    <el-button type="primary" @click="handleSnap(item.gateway_id)">抓拍</el-button>
                    <el-button type="primary" @click="handleEditPlateNo(item.gateway_id, item.car_biz_no)">修改车牌号</el-button>
                    <el-button type="primary" @click="handlePass(item.gateway_id, item.car_biz_no)">抬杆放行</el-button>
                    <el-button type="primary" @click="handleReleaseGate(item.gateway_id)">远程放闸</el-button>
                    <el-button type="primary" v-if="item.gateway_direction == 2" @click="handleRecord(item.gateway_id, item.plate_no)"
                      >查询缴费</el-button
                    >
                  </div>
                </div>
              </el-col>
            </el-row>
            <el-row :gutter="10">
              <el-col :span="8">
                <span>车牌号：{{ item.plate_no }}</span>
              </el-col>
              <el-col :span="8">
                <span>入：{{ item.in_time }}</span>
              </el-col>
              <el-col :span="8">
                <span>出：{{ item.out_time }}</span>
              </el-col>
            </el-row>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 车场查找带回 -->
    <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleParkClose">
      <park-find-back @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
    </el-dialog>

    <!-- 修改车牌号 -->
    <el-dialog title="修改车牌号" v-model="dialogVisible" :close-on-click-modal="false" width="500px">
      <el-form label-width="100px" :model="data.editPlateNoForm">
        <el-form-item label="车牌号" class="required">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-select v-model="data.editPlateNoForm.plateNoFirst" filterable style="width: 100%">
                <el-option v-for="item in plateNoFirsts" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
            </el-col>
            <el-col :span="8">
              <el-input type="text" v-model="data.editPlateNoForm.plateNoSecond" show-word-limit maxlength="10" style="width: 100%" />
            </el-col>
            <el-col :span="8">
              <el-select v-model="data.editPlateNoForm.plateNoThirdly" filterable style="width: 100%">
                <el-option v-for="item in plateNoThirdlies" :key="item.label" :label="item.label" :value="item.label" />
              </el-select>
            </el-col>
          </el-row>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateCancel()">取 消</el-button>
          <el-button type="primary" @click="updatePlateNo()">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 缴费记录 -->
    <el-dialog title="缴费记录" v-model="recordDialogVisible" :close-on-click-modal="false" width="800px">
      <el-table :data="recordList" border v-loading="loading">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="order_no" label="订单号" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="in_gateway_name" label="入场通道" align="center" />
        <el-table-column prop="to_time" label="出场时间" align="center" />
        <el-table-column prop="out_gateway_name" label="出场通道" align="center" />
        <el-table-column prop="time" label="停车时长" align="center" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
        <el-table-column prop="stop_car_type_desc" label="停车类型" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="order_state_desc" label="订单状态" align="center" />
        <el-table-column prop="pay_method_desc" label="支付方式" align="center" />
        <el-table-column prop="order_money" label="应交金额" align="center" />
        <el-table-column prop="debate_money" label="优惠金额" align="center" />
        <el-table-column prop="should_pay_money" label="实缴金额" align="center" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="recordDialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script name="CloudWatch" setup>
import parkGatewayService from '@/service/park/ParkGatewayService';
import watchService from '@/service/watch/WatchService';
import { onMounted, ref, reactive } from 'vue';
import { v4 as uuidv4 } from 'uuid';
import ParkFindBack from './ParkFindBack.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { getToken } from '@/utils/common';
const parkInfoDialogVisible = ref(false);
const dialogVisible = ref(false);
const recordDialogVisible = ref(false);
const recordList = ref([]);
const dataList = ref([]);
const flag = ref('未值守');
const data = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    gateway_ids: []
  },
  editPlateNoForm: {
    car_biz_no: undefined,
    gateway_id: undefined,
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  },
  rules: {
    plate_no: [
      {
        required: true,
        message: '请输入车牌号',
        trigger: 'blur'
      }
    ]
  }
});
const plateNoFirsts = ref([
  { label: '京' },
  { label: '冀' },
  { label: '晋' },
  { label: '蒙' },
  { label: '辽' },
  { label: '吉' },
  { label: '黑' },
  { label: '沪' },
  { label: '苏' },
  { label: '浙' },
  { label: '皖' },
  { label: '闽' },
  { label: '赣' },
  { label: '鲁' },
  { label: '豫' },
  { label: '鄂' },
  { label: '湘' },
  { label: '粤' },
  { label: '桂' },
  { label: '琼' },
  { label: '渝' },
  { label: '川' },
  { label: '贵' },
  { label: '云' },
  { label: '藏' },
  { label: '陕' },
  { label: '甘' },
  { label: '青' },
  { label: '宁' },
  { label: '新' },
  { label: '民航' },
  { label: '使' },
  { label: '无' }
]);
const plateNoThirdlies = ref([
  { label: '警' },
  { label: '学' },
  { label: '使' },
  { label: '领' },
  { label: '挂' },
  { label: '应急' },
  { label: '无' }
]);
const state = reactive({
  path: 'wss://' + import.meta.env.VITE_WEB_SOCKET_URL + '/ws',
  //path: 'ws://' + import.meta.env.VITE_WEB_SOCKET_URL + '/ws',
  socket: undefined,
  intervalPush: undefined
});
const gatewayList = ref([]);
const timer = ref();

onMounted(() => {
  if (timer.value) {
    clearInterval(timer.value); //清除定时器
  }
});

// 处理WebSocket消息
const handleWebSocketMessage = (msg) => {
  if (msg.detail.command == 'c2s.watch-heartbeat') {
    console.log('监控中');
    flag.value = '监控中';
  } else if (msg.detail.command == 's2c.watch-update') {
    dataList.value.forEach(function (item) {
      if (item.gateway_id == msg.detail.gateway_id) {
        item.plate_no = msg.detail.plate_no;
        item.gateway_id = msg.detail.gateway_id;
        item.base64_image = msg.detail.base64_image;
        item.park_id = msg.detail.park_id;
        item.in_time = msg.detail.in_time;
        item.out_time = msg.detail.out_time;
      }
    });
    const param = {
      park_id: msg.detail.park_id,
      gateway_id: msg.detail.gateway_id
    };
    queryCarInGateway(param);
  }
};

//回去车场下的通道列表
const listGateway = (val) => {
  const params = {
    park_id: val
  };
  parkGatewayService.listGateway(params).then((response) => {
    if (response.success === true) {
      gatewayList.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

// 初始化WebSocket
const createWebSocket = () => {
  if (typeof WebSocket === 'undefined') {
    ElMessage({
      message: '浏览器不支持WebSocket',
      type: 'warning'
    });
  } else {
    if (!state.socket) {
      // 实例化socket
      state.socket = new WebSocket(state.path);
      // 监听socket消息
      state.socket.onmessage = handleMessage;

      // 监听socket关闭
      state.socket.onclose = handleClose;

      // 监听socket连接
      state.socket.onopen = handleOpen;
    } else {
      handleOpen();
    }
  }
};

// WebSocket打开处理
const handleOpen = () => {
  // 心跳
  heartBeat();

  if (state.socket.readyState == WebSocket.OPEN) {
    console.log('WebSocket 连接成功');
    console.log('正常');
  } else {
    console.log('异常');
  }
};

// WebSocket关闭处理
const handleClose = () => {
  clearInterval(state.intervalPush);
  console.log('WebSocket 连接已关闭');
};

// 接收消息
const handleMessage = (msgEvent) => {
  const msg = JSON.parse(msgEvent.data);

  console.log('收到消息');
  window.dispatchEvent(
    new CustomEvent('onmessage', {
      detail: msg
    })
  );
};

// 发送消息
const send = (msg) => {
  if (state.socket.readyState == WebSocket.OPEN) {
    //如果WebSocket是打开状态
    state.socket.send(msg);
  } else if (state.socket.readyState == WebSocket.CLOSED) {
    createWebSocket();
  }
};

// 心跳
const heartBeat = () => {
  clearInterval(state.intervalPush);
  state.intervalPush = setInterval(() => {
    const param = {
      command: 'c2s.watch-heartbeat',
      type: 'REQ',
      token: getToken(),
      park_id: data.queryParams.park_id,
      msg_id: uuidv4(),
      gateway_ids: []
    };
    if (data.queryParams.gateway_ids.length == 0) {
      return false;
    } else {
      param.gateway_ids = data.queryParams.gateway_ids;
    }
    console.log(param);
    send(JSON.stringify(param));

    if (state.socket.readyState === WebSocket.OPEN) {
      console.log('正常');
    } else {
      console.log('异常');
    }
  }, 1000 * 10);
};

//车场查找带回;
const handleParkClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  data.queryParams.park_id = val[0].park_id;
  data.queryParams.park_name = val[0].park_name;
  listGateway(val[0].park_id);
};

const handleEditPlateNo = (gateway_id, car_biz_no) => {
  data.editPlateNoForm.car_biz_no = car_biz_no;
  data.editPlateNoForm.gateway_id = gateway_id;
  //普通
  var c_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //特种
  var ts_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{4}[学挂领试超练警]{1}$/u;
  //武警
  var wj_reg = /^WJ[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]?[0-9A-Z]{5}$/iu;
  //军牌
  var j_reg = /^[QVKHBSLJNGCEZ]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9A-Z]{5}$/u;
  //新能源
  // 小型车
  var xs_reg =
    /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[DF]{1}[1-9ABCDEFGHJKLMNPQRSTUVWXYZ]{1}[0-9]{4}$/u;

  // 大型车
  var xb_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNPQRSTUVWXY]{1}[0-9]{5}[DF]{1}$/u;
  //民航
  var mh_reg = /^民航[0-9A-Z]{5}$/u;
  //使馆
  var s_reg = /^[1-3]{1}[0-9]{2}[0-9A-Z]{3}使$/u;
  var s1_reg = /^使[0-9]{6}$/u;
  //领馆
  var l_reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]{1}[1-3]{1}[0-9]{2}[0-9A-Z]{2}领$/u;
  //应急
  var yj_reg = /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[0-9A-Z]{5}应急$/u;
  //判断并进行拆分
  if (c_reg.test(val.plate_no) || xs_reg.test(val.plate_no) || xb_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1);
    data.editPlateNoForm.plateNoThirdly = '无';
  }
  if (ts_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 1);
    data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
  }
  if (wj_reg.test(val.plate_no) || j_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = '无';
    data.editPlateNoForm.plateNoSecond = val.plate_no;
    data.editPlateNoForm.plateNoThirdly = '无';
  }
  if (mh_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 2);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(2);
    data.editPlateNoForm.plateNoThirdly = '无';
  }
  if (s_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = '无';
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(0, val.plate_no.length - 1);
    data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
  }
  if (s1_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1);
    data.editPlateNoForm.plateNoThirdly = '无';
  }
  if (l_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 1);
    data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 1);
  }
  if (yj_reg.test(val.plate_no)) {
    data.editPlateNoForm.plateNoFirst = val.plate_no.substring(0, 1);
    data.editPlateNoForm.plateNoSecond = val.plate_no.substring(1, val.plate_no.length - 2);
    data.editPlateNoForm.plateNoThirdly = val.plate_no.substring(val.plate_no.length - 2);
  }
  dialogVisible.value = true;
};

const handleRecord = (gateway_id, plate_no) => {
  recordDialogVisible.value = true;
  const param = {
    park_id: data.queryParams.park_id,
    gateway_id: gateway_id,
    plate_no: plate_no
  };
  const { park_name, ...newParams } = param;
  watchService.pagingParkFee(newParams).then((res) => {
    if (res.success) {
      recordList.value = res.data.rows;
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });
};

const updatePlateNo = () => {
  if (data.editPlateNoForm.plateNoFirst == undefined) {
    ElMessage({
      message: '请输入车牌号！',
      type: 'error'
    });
    return;
  }

  if (data.editPlateNoForm.plateNoSecond == undefined) {
    ElMessage({
      message: '请输入车牌号！',
      type: 'error'
    });
    return;
  }

  if (data.editPlateNoForm.plateNoThirdly == undefined) {
    ElMessage({
      message: '请输入车牌号！',
      type: 'error'
    });
    return;
  }
  var regex = /^[0-9A-Z]+$/;
  if (!regex.test(data.editPlateNoForm.plateNoSecond)) {
    ElMessage({
      message: '请输入正确的车牌号！',
      type: 'error'
    });
    return;
  }

  const param = {
    first_no: data.editPlateNoForm.plateNoFirst,
    mid_no: data.editPlateNoForm.plateNoSecond,
    last_no: data.editPlateNoForm.plateNoThirdly,
    car_biz_no: data.editPlateNoForm.car_biz_no,
    no_plate_car: 0,
    gateway_id: data.editPlateNoForm.gateway_id
  };
  watchService.updateCarInPlateNo(param).then((res) => {
    if (res.success) {
      ElMessage({
        type: 'success',
        message: '修改车牌号成功'
      });
      dialogVisible.value = false;
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });
};

const handlePass = (gateway_id, car_in_biz_no) => {
  ElMessageBox.confirm('请确认是否抬杆放行？', '抬杆放行', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const param = {
        park_id: data.queryParams.park_id,
        gateway_id: gateway_id,
        car_in_biz_no: car_in_biz_no
      };
      watchService.pushOpenStrobe(param).then((res) => {
        if (res.success) {
          ElMessage({
            type: 'success',
            message: '抬杆放行成功'
          });
        } else {
          ElMessage({
            message: res.message,
            type: 'error'
          });
        }
      });
    })
    .catch(() => {});
};

//远程放闸
const handleReleaseGate = (gateway_id) => {
  ElMessageBox.confirm('请确认是否远程放闸？', '远程放闸', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const param = {
      park_id: data.queryParams.park_id,
      gateway_id: gateway_id
    };
    watchService.pushCloseStrobe(param).then((res) => {
      if (res.success) {
        ElMessage({
          type: 'success',
          message: '远程放闸成功'
        });
      } else {
        ElMessage({
          message: res.message,
          type: 'error'
        });
      }
    });
  });
};

// 取消
const updateCancel = () => {
  data.editPlateNoForm = {
    car_biz_no: undefined,
    gateway_id: undefined,
    plateNoFirst: undefined,
    plateNoSecond: undefined,
    plateNoThirdly: undefined
  };
  dialogVisible.value = false;
};

const handleCloudWatch = () => {
  const param = {
    park_id: data.queryParams.park_id,
    gateway_ids: data.queryParams.gateway_ids
  };
  watchService.beginWatch(param).then((response) => {
    if (response.success === true) {
      createWebSocket();
      onTimer();
      //将通道id绑定道一个数组中

      //循环所选着的通道id
      const gatewayIds = data.queryParams.gateway_ids;
      gatewayIds.forEach(function (item) {
        //定义一个对象
        const tableData = {};
        gatewayList.value.forEach(function (data) {
          if (item == data.id) {
            tableData.gateway_id = item;
            tableData.gateway_name = data.name;
            tableData.park_region_name = data.park_region_name;
            tableData.base64_image = '';
          }
        });
        dataList.value.push(tableData);
      });
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const onTimer = () => {
  timer.value = setInterval(() => {
    //设置定时器
    console.log('定时器启动');
    window.addEventListener('onmessage', handleWebSocketMessage);
  }, 1000);
};
const handleFinishCloudWatch = () => {
  const param = {
    park_id: data.queryParams.park_id
  };
  watchService.endWatch(param).then((response) => {
    if (response.success === true) {
      dataList.value = [];
      data.queryParams.gateway_ids = [];
      handleClose(); //关闭socket
      clearInterval(timer.value); //清除定时器
      timer.value = '';
      flag.value = '未值守';
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const handleRefresh = (val) => {
  //获取抓拍的快照
  const param = {
    park_id: data.queryParams.park_id,
    gateway_id: val
  };
  watchService.getCarSnapshot(param).then((response) => {
    if (response.success === true) {
      console.log(response.data);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
  queryCarInGateway(param);
};
//查询通道中的车辆
const queryCarInGateway = (param) => {
  watchService.queryCarInGateway(param).then((response) => {
    if (response.success === true) {
      const gateway_id = response.data.gateway_id;
      //循环通道列表
      dataList.value.forEach(function (item) {
        if (item.gateway_id == gateway_id) {
          item.park_id = response.data.park_id;
          item.region_id = response.data.region_id;
          item.car_biz_no = response.data.car_biz_no;
          item.gateway_direction = response.data.gateway_direction;
        }
      });
    }
  });
};
const handleSnap = (val) => {
  const param = {
    park_id: data.queryParams.park_id,
    gateway_id: val,
    recognition: false
  };
  watchService.triggerCamera(param).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: '抓拍成功',
        type: 'success'
      });
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
</script>

<style lang="scss" scoped>
.content {
  padding: 15px;
  width: 765px;
}
</style>
