<template>
  <el-card class="table" shadow="never">
    <div class="uodataClass">
      <!-- <el-tooltip>
        <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
        <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
      </el-tooltip> -->
      <!-- <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div> -->
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 320px)">
        <el-table-column prop="statistics_date" label="日期" align="center" min-width="180" />
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <el-table-column prop="" label="省/市/区" align="center" min-width="180">
          <template #="{ row }"> {{ row.province_name }}/ {{ row.city_name }}/ {{ row.district_name }} </template>
        </el-table-column>
        <!-- <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" /> -->
        <el-table-column prop="exit_lane_count" label="出口通道数" align="center" min-width="180" />
        <el-table-column prop="congestion_lane_count" label="拥堵通道数" align="center" min-width="180" />
        <el-table-column prop="total_congestion_duration" label="累计拥堵时长(秒)" align="center" min-width="180" />
        <el-table-column prop="single_congestion_times" label="单次拥堵频次" align="center" min-width="180" />
        <el-table-column prop="continuous_congestion_times" label="连续拥堵频次" align="center" min-width="180" />
      </el-table>
      <div style="height: 80px; display: flex; align-items: center; justify-content: flex-end">
        <el-pagination
          background
          :current-page="data.queryParams.page"
          :page-sizes="[30, 50, 100]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          class="table-pagination"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
        <el-pagination />
      </div>
    </div>
  </el-card>
</template>

<script name="ParkSpaceAvailabilityTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
// import parkSpaceAvailabilityService from '@/service/statisticalReport/ParkSpaceAvailabilityService';
// import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { getCongestionData } from '@/api/statisticalReport/congestionWarning.js';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 获取最新更新时间
// const getNewUpdateTmieData = async () => {
//   try {
//     const rudata = await getNewUpdateTmie(2);
//     if (rudata.code == 200) {
//       newdata.value = rudata.data.last_job_time;
//       console.log(rudata, 'rudata');
//     }
//   } catch (error) {
//     console.log('获取最新更新时间失败', error);
//   }
// };
// onMounted(() => {
//   getList(data.queryParams);
// });
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case 1:
      break;
    case 6:
    case 2:
      return row.statistics_date.split('-')[1] + '月';
    case 3:
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case 4:
      break;
    case 5:
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  getCongestionData(params).then((response) => {
    console.log('ximo', response);
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap;
}
</style>
