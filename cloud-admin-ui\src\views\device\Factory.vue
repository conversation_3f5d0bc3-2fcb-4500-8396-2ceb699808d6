<template>
  <div class="container">
    <factory-search @form-search="searchFactoryList" @reset="resetParamsAndData" />
    <factory-table ref="table" />
  </div>
</template>

<script name="Factory" setup>
import FactorySearch from './factory/FactorySearch.vue';
import FactoryTable from './factory/FactoryTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchFactoryList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
