{"version": 3, "sources": ["../../vue-happy-bus/node_modules/happy-event-bus/dist/index.esm.js"], "sourcesContent": ["export default function(){var e=this,t=this;this.on=function(t,n){return e.eventMap.has(t)||e.eventMap.set(t,[]),e.eventMap.get(t).push(n),function(){return e.off(t,n)}},this.once=function(n,i){var f=function e(){t.off(n,e),i.apply(void 0,[].slice.call(arguments))};return e.on(n,f),function(){return e.off(n,f)}},this.emit=function(e){var n=arguments,i=t.eventMap.get(e);Array.isArray(i)&&i.forEach(function(e){\"function\"==typeof e&&e.apply(void 0,[].slice.call(n,1))})},this.off=function(t,n){if(t)if(\"function\"==typeof n){if(e.eventMap.has(t)){var i=e.eventMap.get(t).filter(function(e){return e!==n});e.eventMap.set(t,i)}}else e.eventMap.delete(t);else e.eventMap.clear()},this.eventMap=new Map}\n//# sourceMappingURL=index.esm.js.map\n"], "mappings": ";;;;;;;;YAAe,WAAA;AAAW,MAAIA,IAAEC,MAAKC,IAAED;AAAKA,OAAKE,KAAG,SAASD,IAAEE,IAAAA;AAAG,WAAOJ,EAAEK,SAASC,IAAIJ,EAAAA,KAAIF,EAAEK,SAASE,IAAIL,IAAE,CAAA,CAAA,GAAIF,EAAEK,SAASG,IAAIN,EAAAA,EAAGO,KAAKL,EAAAA,GAAG,WAAA;AAAW,aAAOJ,EAAEU,IAAIR,IAAEE,EAAAA;IAAAA;EAAAA,GAAKH,KAAKU,OAAK,SAASP,IAAEQ,IAAAA;AAAG,QAAIC,KAAE,SAASb,KAAAA;AAAIE,QAAEQ,IAAIN,IAAEJ,EAAAA,GAAGY,GAAEE,MAAAA,QAAa,CAAA,EAAGC,MAAMC,KAAKC,SAAAA,CAAAA;IAAAA;AAAa,WAAOjB,EAAEG,GAAGC,IAAES,EAAAA,GAAG,WAAA;AAAW,aAAOb,EAAEU,IAAIN,IAAES,EAAAA;IAAAA;EAAAA,GAAKZ,KAAKiB,OAAK,SAASlB,IAAAA;AAAG,QAAII,KAAEa,WAAUL,KAAEV,EAAEG,SAASG,IAAIR,EAAAA;AAAGmB,UAAMC,QAAQR,EAAAA,KAAIA,GAAES,QAAQ,SAASrB,IAAAA;AAAG,oBAAA,OAAmBA,MAAGA,GAAEc,MAAAA,QAAa,CAAA,EAAGC,MAAMC,KAAKZ,IAAE,CAAA,CAAA;IAAA,CAAA;EAAA,GAAOH,KAAKS,MAAI,SAASR,IAAEE,IAAAA;AAAG,QAAGF;AAAE,UAAG,cAAA,OAAmBE,IAAAA;AAAG,YAAGJ,EAAEK,SAASC,IAAIJ,EAAAA,GAAG;AAAC,cAAIU,KAAEZ,EAAEK,SAASG,IAAIN,EAAAA,EAAGoB,OAAO,SAAStB,IAAAA;AAAG,mBAAOA,OAAII;UAAAA,CAAAA;AAAIJ,YAAEK,SAASE,IAAIL,IAAEU,EAAAA;QAAAA;MAAAA;AAASZ,UAAEK,SAASkB,OAAOrB,EAAAA;;AAAQF,QAAEK,SAASmB,MAAAA;EAAAA,GAASvB,KAAKI,WAAS,oBAAIoB;AAAAA;IAAAA,IAAAA,oBAAAA;IAAAA,IAAAA,CAAAA,OAAAA;AAAAA,MAAAA,KAAAA,mBAAAA;AAAAA,MAAAA,SAAAA;AAAAA;AAAAA,QAAAA,KAAAA,EAAAA,IAAAA,EAAAA,KAAAA,EAAAA,IAAAA,IAAAA,CAAAA,CAAAA,EAAAA,IAAAA,EAAAA;AAAAA,UAAAA,MAAAA,GAAAA,KAAAA,EAAAA,GAAAA,EAAAA,IAAAA,EAAAA,MAAAA,EAAAA,IAAAA,EAAAA,GAAAA,YAAAA,MAAAA;AAAAA,UAAAA,IAAAA,MAAAA,EAAAA,IAAAA,EAAAA;AAAAA,YAAAA,KAAAA,EAAAA,QAAAA,CAAAA,OAAAA,GAAAA,CAAAA,GAAAA,KAAAA;EAAAA,GAAAA,EAAAA;AAAAA;IAAAA,IAAAA,CAAAA,GAAAA,MAAAA;AAAAA,QAAAA,KAAAA,EAAAA,GAAAA,GAAAA,CAAAA;AAAAA,SAAAA,EAAAA,EAAAA,GAAAA;AAAAA;IAAAA,IAAAA,CAAAA,GAAAA,MAAAA;AAAAA,QAAAA,KAAAA,EAAAA,KAAAA,GAAAA,CAAAA;AAAAA,SAAAA,EAAAA,EAAAA,GAAAA;AAAAA;IAAAA,IAAAA,EAAAA;IAAAA,IAAAA,EAAAA;", "names": ["e", "this", "t", "on", "n", "eventMap", "has", "set", "get", "push", "off", "once", "i", "f", "apply", "slice", "call", "arguments", "emit", "Array", "isArray", "for<PERSON>ach", "filter", "delete", "clear", "Map"]}