<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button v-if="scope.row.state === 0 || scope.row.state === 2" link type="primary" @click="handle(scope.row, addForm)">
              处 理
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="name" label="会员昵称" align="center" />
        <el-table-column prop="contact_name" label="投诉人姓名" align="center" />
        <el-table-column prop="contact_mobile" label="手机号" align="center" />
        <el-table-column prop="question_type_desc" label="投诉类型" align="center" />
        <el-table-column prop="content" label="投诉建议" align="center" />
        <el-table-column prop="state_desc" label="处理状态" align="center" />
        <el-table-column prop="result" label="处理结果" align="center" />
        <el-table-column prop="operator_name" label="处理人" align="center" />
        <el-table-column prop="updated_at" label="处理时间" align="center" />
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="投诉建议处理" v-model="handleDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="600px">
        <el-form ref="addForm" label-width="90px" :rules="data.rules" :model="data.form">
          <el-form-item prop="contact_name" label="处理结果">
            <el-input v-model="data.form.contact_name" style="width: 100%" maxlength="50" type="textarea" placeholder="请填写处理结果" :rows="6" />
          </el-form-item>
          <el-form-item prop="time" label="处理时间">
            <el-date-picker
              style="width: 100%"
              v-model="data.form.time"
              type="datetime"
              placeholder="请选择处理时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
          <el-form-item prop="state" label="处理状态">
            <el-select v-model="data.form.state" style="width: 100%" placeholder="请选择处理状态">
              <el-option v-for="item in statesList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="submitAdvice(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>
<script name="AdviceTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import commonService from '@/service/common/CommonService';
import adviceService from '@/service/member/AdviceService';

const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const addForm = ref();
const total = ref(0);
const handleDialogVisible = ref(false);
const statesList = ref([]);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    id: undefined,
    contact_name: undefined,
    time: undefined,
    state: undefined
  },
  rules: {
    contact_name: [
      {
        required: true,
        message: '请输入处理结果',
        trigger: 'blur'
      }
    ],
    time: [
      {
        required: true,
        message: '请选择处理时间',
        trigger: 'blur'
      }
    ],
    state: [
      {
        required: true,
        message: '请选择处理状态',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
  getList(data.queryParams);
  status.value = true;
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'statesList',
      enum_value: 'EnumQuestionState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    const list = response.data.statesList.filter((item) => item.value !== 0);
    statesList.value = list;
  });
};

// 分页查询设备列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  adviceService.pagingMemberComplaint(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 提交建议处理
const submitAdvice = (addForm) => {
  addForm.validate().then(() => {
    adviceService.handleComplaint(data.form).then((response) => {
      if (response.success == true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
        addForm.resetFields();
        handleDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 处理
const handle = (row, addForm) => {
  data.form = {
    id: undefined,
    contact_name: undefined,
    time: undefined,
    state: undefined
  };
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form.id = row.id;
  handleDialogVisible.value = true;
  status.value = false;
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  handleDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
