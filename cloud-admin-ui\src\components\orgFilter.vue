<template>
  <el-cascader
    :options="options"
    :props="cascaderConfig"
    :show-all-levels="false"
    v-model="selected"
    @change="handleChange"
    :placeholder="placeholder"
    style="width: 100%"
    clearable
    filterable
  />
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { departmentTree } from '@/api/system/DepartmentApi';

const props = defineProps({
  modelValue: String,
  placeholder: {
    type: String,
    default: '组织架构'
  }
});

const selected = ref(props.modelValue);
watch(
  () => props.modelValue,
  (val) => {
    selected.value = val;
  }
);

const emits = defineEmits(['update:modelValue', 'change']);

const options = ref([]);
const cascaderConfig = {
  expandTrigger: 'hover',
  checkStrictly: true,
  value: 'id',
  emitPath: false
};

const fetchOrganizations = async () => {
  const { data } = await departmentTree();
  options.value = data;
};

onMounted(() => {
  fetchOrganizations();
});

const handleChange = (value) => {
  emits('update:modelValue', value);
  emits('change', value);
};
</script>
<style scoped lang="sass"></style>
