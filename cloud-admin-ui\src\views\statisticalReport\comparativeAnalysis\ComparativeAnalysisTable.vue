<template>
  <el-card class="table" shadow="never">
    <el-radio-group v-model="tabPosition" @change="changeTabPosition">
      <el-radio-button value="列表">列表</el-radio-button>
      <el-radio-button value="图表">图表</el-radio-button>
    </el-radio-group>
    <div v-if="tabPosition == '列表'" ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 317px)">
        <el-table-column label="月份" align="center" prop="statistics_date" min-width="100" />
        <el-table-column label="车场名称" align="center" prop="park_name" min-width="150" />
        <el-table-column label="车场ID" align="center" prop="park_id" min-width="90" />
        <el-table-column label="大区" align="center" prop="region_name" min-width="150" />
        <el-table-column label="城市公司" align="center" prop="organizational_structure" min-width="150" />
        <el-table-column label="付费临停车次" align="center">
          <el-table-column label="同比(%)" align="center" prop="car_total_yoy_growth_rate" min-width="100" />
          <el-table-column label="环比(%)" align="center" prop="car_total_mom_growth_rate" min-width="100" />
        </el-table-column>
        <el-table-column label="付费临停收入" align="center">
          <el-table-column label="同比(%)" align="center" prop="income_total_yoy_growth_rate" min-width="100" />
          <el-table-column label="环比(%)" align="center" prop="income_total_mom_growth_rate" min-width="100" />
        </el-table-column>
        <el-table-column label="付费临停总时长" align="center">
          <el-table-column label="同比(%)" align="center" prop="hours_total_yoy_growth_rate" min-width="100" />
          <el-table-column label="环比(%)" align="center" prop="hours_total_mom_growth_rate" min-width="100" />
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div v-if="tabPosition == '图表' && parkId">
      <echartbar ref="eChartBarRef"></echartbar>
    </div>
    <div v-if="tabPosition == '图表' && !parkId">
      <div style="width: 100%; height: 600px; background: #fff; line-height: 600px; text-align: center">请选择停车场进行统计</div>
    </div>
  </el-card>
</template>

<script name="TimedAccessTable" setup>
import { reactive, ref, nextTick } from 'vue';
import { pick } from 'lodash';
import { ElMessage } from 'element-plus';
import { getCompareAnalysisList } from '@/api/statisticalReport/ComparativeAnalysisApi';
import echartbar from './barChart.vue';

const originalData = ref([]);
const tableData = ref([]);
const loading = ref(false);
const tabPosition = ref('列表');
const eChartBarRef = ref();
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const parkId = ref(null);

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};

const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  data.queryParams = {
    ...data.queryParams,
    ...pick(params, ['park_id', 'organization_ids', 'start_time', 'end_time']),
    page: data.queryParams.page || 1,
    limit: data.queryParams.limit || 30
  };
  getCompareAnalysisList(data.queryParams).then((response) => {
      if (response.success === true) {
        parkId.value = data.queryParams.park_id;
        originalData.value = response.data.rows;
        tableData.value = response.data.rows;
        total.value = parseInt(response.data.total);
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

const changeTabPosition = (val) => {
  if (val === '图表') {
    if (!parkId.value) return;
    getChartData(data.queryParams);
  } else {
    const params = {
      ...data.queryParams,
      park_id: parkId.value || ''
    }
    getList(params);
  }
};

const getChartData = (queryParams) => {
  parkId.value = queryParams.park_id;
  if (!queryParams.park_id) return;
  data.queryParams = {
    ...data.queryParam,
    ...queryParams
  };
  nextTick(() => {
    eChartBarRef.value.getCAChart(queryParams);
  });
};

defineExpose({
  tabPosition,
  getList,
  getChartData
});
</script>

<style lang="scss" scoped>
.el-table th.el-table__cell>.cell {
  white-space: pre-wrap;
}
.el-radio-group {
  margin-bottom: 10px;
}
</style>
