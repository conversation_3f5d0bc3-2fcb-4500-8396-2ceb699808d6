<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="规则名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="启用状态" clearable multiple>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="GroupLongRentRuleSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: '',
    states: [],
    page: 1,
    limit: 30
  }
});
const states = ref([]);

onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumRuleState' }];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    states: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
