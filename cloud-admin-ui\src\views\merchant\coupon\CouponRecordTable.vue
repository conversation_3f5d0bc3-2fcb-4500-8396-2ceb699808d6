<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="couponStatsService.exportReports"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="130">
          <template v-slot="scope">
            <el-button link type="primary" @click="checkDetail(scope.row)">查看明细</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="160" />
        <el-table-column prop="merchant_name" label="商家名称" align="center" />
        <el-table-column prop="coupon_meta_name" label="优免券名称" align="center" />
        <el-table-column prop="type_desc" label="优免券类型" align="center" />
        <el-table-column prop="total_count" label="优免卷数量" align="center" />
        <el-table-column label="有效期" align="center" min-width="180">
          <template v-slot="scope">
            <span>{{ scope.row.valid_start_time + '-' + scope.row.valid_end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="manual_draw_count" label="人工发放数量" align="center" />
        <el-table-column prop="qrcode_draw_count" label="扫码领取数量" align="center" />
        <el-table-column prop="third_draw_count" label="第三方发券数量" align="center" />
        <el-table-column prop="remainder_count" label="剩余数量" align="center" />
        <el-table-column prop="verified_count" label="实际核销数量" align="center" />
        <el-table-column prop="deducted_value" label="实际抵扣金额" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CouponMetaTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import couponStatsService from '@/service/merchant/CouponStatsService';
import DownloadButton from '@/components/DownloadButton.vue';
import { useRouter } from 'vue-router';
import { activeRouteTab } from '@/utils/tabKit';

const router = useRouter();
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  couponStatsService.pagingCouponStats(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const checkDetail = (row) => {
  console.log('checkDetail', row);
  activeRouteTab({
    path: '/merchant/couponDetails',
    query: {
      merchantCouponId: row.id
    }
  });
  // router.push({
  //   name: 'CouponDetails',
  //   query: {
  //     merchantCouponId: row.id
  //   }
  // });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
