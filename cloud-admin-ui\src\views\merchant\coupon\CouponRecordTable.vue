<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <!-- 导出exel表格按钮 组件实际就是el-button  "btnType"参数为el-button按钮类型  "exportFunc"为点击进行生成exel的api   "rules"为是"params"规则  -->
        <DownloadButton
          btnType="default"
          :exportFunc="couponStatsService.exportReports"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 320px)">
        <el-table-column label="车场基本信息" align="center">
          <el-table-column prop="action" label="操作" align="center" width="130">
            <template v-slot="scope">
              <el-button link type="primary" @click="checkDetail(scope.row)">操作明细</el-button>
            </template>
          </el-table-column>
          <el-table-column prop="park_name" label="停车场名称" align="center" min-width="160" />
          <el-table-column prop="park_id" label="车场ID" align="center" />
          <el-table-column prop="region_name" label="大区" align="center" />
          <el-table-column prop="organizational_structure" label="城市分公司" align="center" />
          <el-table-column prop="province_name" label="省份名称" align="center" />
          <el-table-column label="城市名称" align="center" min-width="180" prop="city_name"> </el-table-column>
          <el-table-column prop="merchant_name" label="商家名称" align="center" />
          <el-table-column prop="first_category_name" label="商家一级分类" align="center" />
          <el-table-column prop="second_category_name" label="商家二级分类" align="center">
            <template #="{ row }">
              <div class="second_category_desc">
                <div v-if="row.second_category_name">{{ row.second_category_name }}</div>
                <div v-else>/</div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="type_desc" label="优免种类" align="center">
            <template #="{ row }">
              <div style="height: 100%; width: 100%; display: flex; justify-content: center; align-items: center">
                {{ typesec(row.type) }}
              </div>
            </template>
          </el-table-column>
          <!--  -->
        </el-table-column>
        <el-table-column label="业务指标" align="center">
          <el-table-column prop="money_category" label="金额类别" align="center">
            <template #header>
              <div class="money_categorycalss">
                金额类别
                <div>
                  <el-tooltip placement="top-end" effect="light">
                    <template #content
                      ><b>商家从广场物业处购买优惠劵的折扣幅度。计算公<br />式:</b>折扣率=实际销售金额/总原价x100%
                    </template>
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
            <template #="{ row }">
              <div>{{ preciseMultiply(row.money_category, 10) }}折</div>
              <!-- <div>{{ (row.money_category * 10).toFixed(0) }}折</div> -->
            </template>
          </el-table-column>
          <el-table-column prop="contract_terms_desc" label="合同约定" align="center">
            <template #="{ row }">
              {{ row.contract_terms ? '是' : '否' }}
            </template>
          </el-table-column>
          <el-table-column prop="valid_day" label="有效期(天)" align="center" />
        </el-table-column>
        <!--  -->
        <el-table-column label="售卖指标(已领取)" align="center">
          <el-table-column prop="deducted_value" label="有效时间" align="center" width="110">
            <template #="{ row }">
              <div>{{ row.valid_start_time }} - {{ row.valid_end_time }}</div>
            </template>
          </el-table-column>
          <el-table-column prop="total_count" label="总数量" align="center" />
          <el-table-column prop="total_price" label="总金额" align="center" />
          <el-table-column prop="remainder_count" label="待领取" align="center" />
        </el-table-column>
        <el-table-column label="内部系统核销指标(已领取)" align="center">
          <el-table-column prop="in_verified_count" label="已核销数量" align="center" />
          <el-table-column prop="in_wait_verified_count" label="待核销数量" align="center" />
          <el-table-column prop="in_over_time_count" label="作废数量(未核销过期失效)" align="center">
            <template #header>
              <div class="money_categorycalss">
                作废数量(未核销过期失效)
                <div>
                  <el-tooltip placement="top-end" effect="light">
                    <template #content><b>过期优免卷返还到待领取数量中</b> </template>
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="in_verified_deduction_money" label="核销抵扣金额(元)" align="center" />
          <el-table-column prop="in_verified_short_parking_money" label="核销临停金额(元)" align="center" />
          <el-table-column prop="in_verified_other_money_rate" label="核销金额占比" align="center">
            <template #="{ row }">{{ preciseMultiply(row.in_verified_other_money_rate, 100) }}% </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="第三方系统核销指标" align="center">
          <el-table-column prop="tri_verified_count" label="已核销数量" align="center" />
          <el-table-column prop="tri_wait_verified_count" label="待核销数量" align="center" />
          <el-table-column prop="tri_over_time_count" label="作废数量(未核销过期失效)" align="center">
            <template #header>
              <div class="money_categorycalss">
                作废数量(未核销过期失效)
                <div>
                  <el-tooltip placement="top-end" effect="light">
                    <template #content><b>过期优免卷返还到待领取数量中</b> </template>
                    <el-icon><QuestionFilled /></el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="tri_verified_deduction_money" label="核销抵扣金额(元)" align="center" />
          <el-table-column prop="tri_verified_short_parking_money" label="核销临停金额(元)" align="center" />
          <el-table-column prop="tri_verified_other_money_rate" label="核销金额占比" align="center">
            <template #="{ row }">{{ preciseMultiply(row.tri_verified_other_money_rate, 100) }}% </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CouponMetaTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { ElMessage } from 'element-plus';
import decimal from 'decimal.js';
import couponStatsService from '@/service/merchant/CouponStatsService';
import DownloadButton from '@/components/DownloadButton.vue';
import { activeRouteTab } from '@/utils/tabKit';
import { getWriteOffData } from '@/api/merchant/CouponStatsApi';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);

const data = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    organization_ids: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  }
});
onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
});
const typesec = (type) => {
  switch (type) {
    case 1:
      return '小时卷';
    case 2:
      return '现金卷';
    case 3:
      return '折扣卷';
    case 4:
      return '全面卷';
  }
};
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  const { park_name, ...otherdata } = params; //解构赋值发送的参数没有"park_name" 解构出来
  data.queryParams = otherdata;
  console.log('table', otherdata);
  // return;
  getWriteOffData(otherdata).then((response) => {
    console.log('getWriteOffData', response);
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
function preciseMultiply(num, multiplier) {
  return decimal(num).mul(multiplier);
}
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const checkDetail = (row) => {
  activeRouteTab({
    path: '/merchant/couponDetails',
    query: {
      merchantCouponId: row.id
    }
  });
  // router.push({
  //   name: 'CouponDetails',
  //   query: {
  //     merchantCouponId: row.id
  //   }
  // });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.money_categorycalss {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  > div {
    cursor: pointer;
  }
}
.second_category_desc {
  height: 100%;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
