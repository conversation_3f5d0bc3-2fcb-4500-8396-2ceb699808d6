<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" placeholder="请选择车场" :readonly="true" @click="authCharge(true)" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.park_region_id" style="width: 100%" placeholder="子场名称">
        <el-option v-for="item in childList" :key="item.id" :label="item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.order_no" placeholder="业务单号" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>

    <form-search-item>
      <el-select v-model="form.queryParams.state" style="width: 100%" placeholder="找零状态" multiple>
        <el-option v-for="item in transferStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>

    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="申请开始时间"
        end-placeholder="申请结束时间"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
    <cash-transfer-find-back
      :park_id="park_id"
      :park_name="park_name"
      @authCharge="authCharge(false)"
      :mode="flag"
      @renderTableInput="renderTableInput"
    />
  </el-dialog>
</template>

<script name="CashTransferSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import parkSpaceService from '@/service/park/ParkSpaceService';
import CashTransferFindBack from './CashTransferFindBack.vue';
import { reactive, onMounted, ref } from 'vue';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';
import { useRouter } from 'vue-router';
const router = useRouter();

const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const childList = ref([]);
const form = reactive({
  queryParams: {
    order_no: undefined,
    plate_no: undefined,
    state: [],
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    start_time: undefined,
    end_time: undefined
  },
  dateRange: [],
  payTime: []
});

const transferStateList = [
  { key: '已受理', value: 'ACCEPTED' },
  { key: '处理中', value: 'PROCESSING' },
  { key: '待收款用户确认', value: 'WAIT_USER_CONFIRM' },
  { key: '转账结果未明确', value: 'TRANSFERING' },
  { key: '转账成功', value: 'SUCCESS' },
  { key: '转账失败', value: 'FAIL' },
  { key: '撤销中', value: 'CANCELING' },
  { key: '已撤销', value: 'CANCELLED' }
];

onMounted(() => {
  form.queryParams.state = [];
  form.dateRange = [dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  form.queryParams.start_time = form.dateRange[0];
  form.queryParams.end_time = form.dateRange[1];

  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    parkSpaceService.listParkRegion(form.queryParams.park_id).then((response) => {
      childList.value = response;
      form.queryParams.park_region_id = response[0].id;
      const query = Object.assign(form.queryParams, {});
      emits('form-search', query);
    });
  }
});

const handleDataSearch = () => {
  if (form.dateRange?.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  } else {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  if (form.payTime?.length > 0) {
    form.queryParams.pay_start_time = form.payTime[0];
    form.queryParams.pay_end_time = form.payTime[1];
  } else {
    form.queryParams.pay_start_time = undefined;
    form.queryParams.pay_end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams);
    // const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  childList.value = [];
  form.dateRange = [];
  form.payTime = [];
  form.queryParams = {
    order_no: undefined,
    plate_no: undefined,
    state: [],
    park_id: undefined,
    park_name: undefined,
    park_region_id: undefined,
    start_time: undefined,
    end_time: undefined
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  if (val.length === 0) {
    form.queryParams.park_id = undefined;
    form.queryParams.park_name = undefined;
    childList.value = [];
  } else {
    form.queryParams.park_id = val[0].park_id;
    form.queryParams.park_name = val[0].park_name;
    parkSpaceService.listParkRegion(form.queryParams.park_id).then((response) => {
      childList.value = response;
      form.queryParams.park_region_id = response[0].id;
    });
  }
};
</script>
<style lang="scss" scoped></style>
