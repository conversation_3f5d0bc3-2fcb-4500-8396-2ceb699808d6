<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
    </form-search-item>
    <form-search-item>
      <time-range
        v-model:date="form.dateRange"
        v-model:unit="form.dateType"
        style="width: 100%"
        :disabled-date="disabledDateFn"
        :show-type="['date', 'week', 'month', 'fiscalMonth']"
        type-value="type"
      />
      <!-- <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="统计日期开始日期"
        end-placeholder="统计日期结束日期"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
        :disabled-date="disabledDateFn"
      /> -->
    </form-search-item>
    <template #button>
      <el-button type="success" @click="exportDataByDay()" :loading="dayDownLoading">导出</el-button>
      <el-button type="warning" @click="exportDataByGather()" :loading="summaryDownLoading" v-show="form.dateType != 'fiscalMonth'"
        >汇总导出</el-button
      >
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>

  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_ids="organization_ids"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="ExitPassageEfficiencySearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import timeRange from '@/components/timeRange.vue';
import commonService from '@/service/common/CommonService';
import exitPassageEfficiencyService from '@/service/statisticalReport/ExitPassageEfficiencyService';
import { saveToFile } from '@/utils/utils.js';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import { useUser } from '@/stores/user';
import { useRouter } from 'vue-router';
const router = useRouter();

const emits = defineEmits(['form-search']);
const defaultDateRange = ref([dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const organization_ids = ref('');
const department_name = ref('');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: undefined,
    end_time: undefined,
    limit: 30,
    page: 1
  },
  dateRange: defaultDateRange.value,
  dateType: 'date'
});

const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.department_name = undefined;
};
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};

onMounted(() => {
  form.dateRange = defaultDateRange.value;
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    handleDataSearch();
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const handleDataSearch = () => {
  if (undefined !== form.dateRange && null !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
    // 选择的时间最长只能是31天
    // var d = new Date(Date.parse(form.queryParams.start_time.replace(/-/g, '/')));
    // var d2 = new Date(Date.parse(form.queryParams.end_time.replace(/-/g, '/')));
    // var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    // if (days + 1 > 31) {
    //   ElMessage({
    //     message: '查询日期最长只能选择31天！',
    //     type: 'warning'
    //   });
    //   return false;
    // }
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  let type;
  switch (form.dateType) {
    case 'date':
      type = '3';
      break;
    case 'week':
      type = '5';
      break;
    case 'month':
      type = '2';
      break;
    case 'fiscalMonth':
      type = '6';
      break;
    case 'year':
      type = '1';
      break;
    default: {
      type = '4';
    }
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { time_type: type });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    if (
      typeof form.queryParams.organization_ids !== 'undefined' &&
      form.queryParams.organization_ids != null &&
      form.queryParams.organization_ids !== ''
    ) {
      const query = Object.assign(form.queryParams, { time_type: type });
      emits('form-search', query);
    } else {
      const query = Object.assign(form.queryParams, { time_type: type });
      emits('form-search', query);
      // ElMessage({
      //   message: '请选择停车场或组织进行统计',
      //   type: 'warning'
      // });
      // return false;
    }
  }
};
const handleAllReset = () => {
  form.dateRange = defaultDateRange.value;
  form.dateType = 'date';
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: undefined,
    end_time: undefined
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
const dayDownLoading = ref(false);
const summaryDownLoading = ref(false);
// 按日导出
const exportDataByDay = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  // if (form.queryParams.start_time !== undefined && form.queryParams.start_time !== '' && form.queryParams.start_time !== null) {
  //   // 选择的时间最长只能是31天
  //   var d = new Date(Date.parse(form.queryParams.start_time.replace(/-/g, '/')));
  //   var d2 = new Date(Date.parse(form.queryParams.end_time.replace(/-/g, '/')));
  //   var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
  //   if (days + 1 > 31) {
  //     ElMessage({
  //       message: '查询日期最长只能选择31天！',
  //       type: 'warning'
  //     });
  //     return false;
  //   }
  // }
  exitPassageEfficiencyService
    .exportDataByDay(form.queryParams)
    .then((response) => {
      if (response.success == true) {
        dayDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            dayDownLoading.value = false;
          })
          .catch(() => {
            dayDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        dayDownLoading.value = false;
      }
    })
    .catch(() => {
      dayDownLoading.value = false;
    });
};

// 汇总导出
const exportDataByGather = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  if (
    form.queryParams.start_time === undefined ||
    form.queryParams.start_time === '' ||
    form.queryParams.end_time === undefined ||
    form.queryParams.end_time === ''
  ) {
    ElMessage({
      message: '请选择统计日期！',
      type: 'warning'
    });
    return false;
  }
  // if (form.queryParams.start_time !== undefined && form.queryParams.start_time !== '' && form.queryParams.start_time !== null) {
  //   // 选择的时间最长只能是31天
  //   var d = new Date(Date.parse(form.queryParams.start_time.replace(/-/g, '/')));
  //   var d2 = new Date(Date.parse(form.queryParams.end_time.replace(/-/g, '/')));
  //   var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
  //   if (days + 1 > 31) {
  //     ElMessage({
  //       message: '查询日期最长只能选择31天！',
  //       type: 'warning'
  //     });
  //     return false;
  //   }
  // }
  exitPassageEfficiencyService
    .exportDataByGather(form.queryParams)
    .then((response) => {
      if (response.success == true) {
        summaryDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            summaryDownLoading.value = false;
          })
          .catch(() => {
            summaryDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        summaryDownLoading.value = false;
      }
    })
    .catch(() => {
      summaryDownLoading.value = false;
    });
};
</script>
<style lang="scss" scoped></style>
