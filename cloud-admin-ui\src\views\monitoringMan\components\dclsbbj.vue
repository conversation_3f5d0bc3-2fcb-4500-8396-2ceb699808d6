<template>
  <div ref="table" style="width: 100%; height: 100%; overflow: hidden; padding: 10px; box-sizing: border-box">
    <el-table :data="tableData" v-loading="loading" border style="height: calc(100% - 50px); overflow-y: auto">
      <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
      <el-table-column prop="action" label="操作" align="center" width="100">
        <template v-slot="scope">
          <el-button link type="primary" @click="handlePayRecord(scope.row)"> 联系现场 </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="park_name" label="停车场名称" align="center" />
      <el-table-column prop="region_name" label="所属子场" align="center" />
      <el-table-column prop="gateway_name" label="所属通道" align="center" />
      <el-table-column prop="device_id" label="设备id" align="center" />
      <el-table-column prop="event_name" label="在线状态" align="center" />
      <el-table-column prop="done" label="是否处理" align="center">
        <template v-slot="scope">
          <div>{{ scope.row.done == 0 ? '未处理' : '已处理' }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="created_at" label="创建时间" align="center" />
    </el-table>
    <el-pagination
      background
      :current-page="data.queryParams.page"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="data.queryParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      style="margin-right: 10px"
    />
    <el-dialog :title="dtitle" v-model="dialogVisible" :close-on-click-modal="false" destroy-on-close @close="dialogVisible = false" width="500px">
      <el-form ref="editForm" label-width="140px" :rules="data.rules" :model="detailData">
        <el-form-item prop="park_name" label="所属车场">
          <el-input v-model="detailData.park_name" :readonly="true" @click="authCharge(true, 'edit')" placeholder="所属车场" />
        </el-form-item>
        <el-form-item prop="duty_phone" label="值班室电话">
          <el-input v-model="detailData.duty_phone" :readonly="true" @click="authCharge(true, 'edit')" placeholder="所属车场" />
        </el-form-item>
        <el-form-item prop="contact_name1" label="应急联系人1">
          <el-input v-model="detailData.contact_name1" :readonly="true" placeholder="车主姓名" />
        </el-form-item>
        <el-form-item prop="contact_mobile1" label="手机号">
          <el-input v-model="detailData.contact_mobile1" :readonly="true" placeholder="车牌号" />
        </el-form-item>
        <el-form-item prop="contact_name2" label="应急联系人2">
          <el-input v-model="detailData.contact_name2" :readonly="true" placeholder="车主姓名" />
        </el-form-item>
        <el-form-item prop="contact_mobile2" label="手机号">
          <el-input v-model="detailData.contact_mobile2" :readonly="true" placeholder="车牌号" />
        </el-form-item>
        <el-form-item prop="contact_name3" label="应急联系人3">
          <el-input v-model="detailData.contact_name3" :readonly="true" placeholder="车主姓名" />
        </el-form-item>
        <el-form-item prop="contact_mobile3" label="手机号">
          <el-input v-model="detailData.contact_mobile3" :readonly="true" placeholder="车牌号" />
        </el-form-item>
      </el-form>
      <template #footer>
        <!-- <span class="dialog-footer">
          <el-button @click="updateDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="updateFreeCar(editForm)">确 定</el-button>
        </span> -->
      </template>
    </el-dialog>
  </div>
</template>
<script name="MemberInfoTable" setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { ElMessage } from 'element-plus';
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
const duty = useDuty();
const dialogVisible = ref(false);
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const detailData = reactive({
  park_name: '',
  duty_phone: '',
  contact_name1: '',
  contact_mobile1: '',
  contact_name2: '',
  contact_mobile2: '',
  contact_name3: '',
  contact_mobile3: ''
});
const timer = ref(null);
watch(
  () => duty.cloud_park_ids,
  (newId) => {
    if (newId) {
      getList(data.queryParams);
    }
  },
  { immediate: false } // 立即执行一次
);
onMounted(() => {
  timer.value = setInterval(() => {
    getList(data.queryParams);
  }, 15000); // 每30秒自动刷新数据
});
// 组件卸载前
onBeforeUnmount(() => {
  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
const emits = defineEmits(['updateData']);
// 分页查询设备列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  params.park_ids = duty.cloud_park_ids;
  UnattendedApi.pageWatchDeviceEventByUserId(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      emits('total', total.value); // 触发父组件监听的事件
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const dtitle = ref('');
const handlePayRecord = (row) => {
  dtitle.value = row.park_name + '-应急通讯录';
  dialogVisible.value = true;
  UnattendedApi.emergencylist({
    park_name: row.park_name,
    park_id: row.park_id,
    space_code: '',
    plate_no: '',
    mbr_member_name: '',
    mbr_member_mobile: '',
    audit_states: [],
    states: [],
    page: 1,
    limit: 30
  }).then((res) => {
    dialogVisible.value = true;
    detailData.park_name = res.data.rows[0].park_name;
    detailData.duty_phone = res.data.rows[0].duty_phone;
    detailData.contact_name1 = res.data.rows[0].contact_name1;
    detailData.contact_mobile1 = res.data.rows[0].contact_mobile1;
    detailData.contact_name2 = res.data.rows[0].contact_name2;
    detailData.contact_mobile2 = res.data.rows[0].contact_mobile2;
    detailData.contact_name3 = res.data.rows[0].contact_name3;
    detailData.contact_mobile3 = res.data.rows[0].contact_mobile3;
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
