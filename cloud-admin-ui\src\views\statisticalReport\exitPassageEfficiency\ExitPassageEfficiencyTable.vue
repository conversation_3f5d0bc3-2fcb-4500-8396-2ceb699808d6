<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div class="search-btn-group" v-loading="countLoading">
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.midday_evening_payed_number_count }}</p>
            <span class="search-btn-group-total-label">午晚高峰付费车次</span>
          </div>
          <div class="search-btn-group-total">
            <p class="search-btn-group-total-num">{{ countData.midday_evening_payed_average_pass_time_count }}</p>
            <span class="search-btn-group-total-label">午晚高峰付费车次平均通行时间（秒/车次）</span>
          </div>
        </div>
      </el-space>
      <el-space>
        <el-button type="default" @click="exportDataByDay()" :loading="dayDownLoading">按日导出</el-button>
        <el-button type="default" @click="exportDataByGather()" :loading="summaryDownLoading">汇总导出</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 280px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column label="组织架构" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.organizational_structure }}</span>
            </template>
          </el-table-column>
          <el-table-column label="省市区" align="center" min-width="80">
            <template #default="scope">
              <span
                v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined">{{
                  scope.row.province_name }}/</span>
              <span
                v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined">{{
                  scope.row.city_name }}/</span>
              <span
                v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
                  scope.row.district_name
                }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="全量车次周转率" align="center">
          <el-table-column label="周转率（含长租）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.turnover_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="全量临停数据" align="center">
          <el-table-column label="临停出场车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.parking_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停车次平均通行时间(秒/车次）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="长租临停数据" align="center">
          <el-table-column label="长租出场车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.rent_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="长租车次平均通行时间(秒/车次）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.rent_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="临停收费车次" align="center">
          <el-table-column label="临停付费车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停付费车次通行总时长(秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.parking_payed_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="临停付费车次平均通行时间(秒/车次）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.payed_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column :render-header="renderHeader" label="午晚高峰期临停收费车次通行效率|12:00-15:00 午高峰|18:00-21:00 晚高峰"
          align="center">
          <el-table-column label="午晚高峰付费车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="午晚高峰付费车次通行总时长（秒）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_total_hours }}</span>
            </template>
          </el-table-column>
          <el-table-column label="午晚高峰付费车次平均通行时间（秒/车次）" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.midday_evening_payed_average_pass_time }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="支付方式(加总约等于收费车次)" align="center">
          <el-table-column label="现金支付车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.cash_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="场内支付车次（场内c扫b）" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.inside_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="出口主动支付车次(出口c扫b，出口b扫c)" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.exit_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="无感支付车次" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.passive_payment_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="etc支付车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etc_payment_number }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="ExitPassageEfficiencyTable" setup>
import commonService from '@/service/common/CommonService';
import exitPassageEfficiencyService from '@/service/statisticalReport/ExitPassageEfficiencyService';
import { saveToFile } from '@/utils/utils.js';
import { ElMessage } from 'element-plus';
import { h, reactive, ref } from 'vue';

const tableData = ref([]);
const loading = ref(false);
const countLoading = ref(false);
const countData = ref({
  midday_evening_payed_number_count: 0,
  midday_evening_payed_average_pass_time_count: 0
});
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const renderHeader = ({ column, $index }) => {
  return h('span', {}, [
    h('span', {}, column.label.split('|')[0]),
    h('br'),
    h('span', {}, column.label.split('|')[1]),
    h('br'),
    h('span', {}, column.label.split('|')[2])
  ]);
};

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  exitPassageEfficiencyService.pagingExitPassageEfficiency(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  exitPassageEfficiencyService.parkExitTrafficEfficienciesCount(params).then((response) => {
    if (response.success === true) {
      if (response.data) {
        countData.value = response.data;
      }
      countLoading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      countLoading.value = false;
    }
  });
};

const dayDownLoading = ref(false);
const summaryDownLoading = ref(false);
// 按日导出
const exportDataByDay = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
    // 选择的时间最长只能是31天
    var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
    var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
    var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    if (days + 1 > 31) {
      ElMessage({
        message: '查询日期最长只能选择31天！',
        type: 'warning'
      });
      return false;
    }
  }
  exitPassageEfficiencyService
    .exportDataByDay(data.queryParams)
    .then((response) => {
      if (response.success == true) {
        dayDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            dayDownLoading.value = false;
          })
          .catch(() => {
            dayDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        dayDownLoading.value = false;
      }
    })
    .catch(() => {
      dayDownLoading.value = false;
    });
};

// 汇总导出
const exportDataByGather = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  if (
    data.queryParams.start_time === undefined ||
    data.queryParams.start_time === '' ||
    data.queryParams.end_time === undefined ||
    data.queryParams.end_time === ''
  ) {
    ElMessage({
      message: '请选择统计日期！',
      type: 'warning'
    });
    return false;
  }
  if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
    // 选择的时间最长只能是31天
    var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
    var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
    var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    if (days + 1 > 31) {
      ElMessage({
        message: '查询日期最长只能选择31天！',
        type: 'warning'
      });
      return false;
    }
  }
  exitPassageEfficiencyService
    .exportDataByGather(data.queryParams)
    .then((response) => {
      if (response.success == true) {
        summaryDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            summaryDownLoading.value = false;
          })
          .catch(() => {
            summaryDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        summaryDownLoading.value = false;
      }
    })
    .catch(() => {
      summaryDownLoading.value = false;
    });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell>.cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}

.search-btn-group {
  gap: 4px;
}

.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
</style>
