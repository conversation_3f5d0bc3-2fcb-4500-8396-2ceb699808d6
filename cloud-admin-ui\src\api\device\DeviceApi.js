/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找设备
export const pagingDevice = (data) => {
  return $({
    url: '/console/park/device/pagingDevices',
    method: 'post',
    data
  });
};

// 查询设备列表数据
export const getDeviceFactoryList = (data) => {
  return $({
    url: '/console/park/device/factory/listDeviceFactory',
    method: 'get',
    data
  });
};

// 新建设备
export const createDevice = (data) => {
  return $({
    url: '/console/park/device/createDevice',
    method: 'post',
    data
  });
};

// 修改设备
export const updateDevice = (data) => {
  return $({
    url: '/console/park/device/updateDevice',
    method: 'post',
    data
  });
};

// 删除设备
export const deleteDevice = (id) => {
  return $({
    url: '/console/park/device/deleteDevice/' + id,
    method: 'post'
  });
};

// 厂商列表
export const listDeviceFactory = (data) => {
  return $({
    url: '/console/park/device/factory/listDeviceFactory',
    method: 'get',
    data
  });
};
