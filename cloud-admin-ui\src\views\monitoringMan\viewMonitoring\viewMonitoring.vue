<template>
  <div class="box-con">
    <div class="box-con-t">
      <el-form>
        <el-form-item>
          <el-input v-model="params.park_name" placeholder="请输入车场名称查询" clearable @input="changeFilterText" />
        </el-form-item>
        <el-form-item>
          <ClearableChargeInput v-model="department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">查询</el-button>
        </el-form-item>
        <el-form-item>
          <el-button @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="box-con-b">
      <div class="box-con-b-title">
        <div class="fill"></div>
        车场名单{{ leftAllCount1 > 0 ? '(' + leftAllCount1 + ')' : '' }}
      </div>
      <div class="box-con-b-con">
        <div class="navbar">
          <div v-for="(i, index) in nvbarData" :key="index" :class="{ navAc: activeName == index }" @click="changeNav(index)">
            {{ i.name }}{{ i.num > 0 ? '(' + i.num + ')' : '' }}
          </div>
        </div>
        <div class="box-b-con">
          <div class="scrollbox">
            <InfoCard ref="infoCardRef" />
            <!-- <div class="paigin"><el-pagination background layout="prev, pager, next" :total="1000" size="small" /></div> -->
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="department_id"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script setup>
import ClearableChargeInput from '@/components/ClearableChargeInput.vue'; //引入组件
import OrgFindBack from '@/components/OrgFindBack.vue'; //引入"组织架构"组件
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { onActivated, onMounted, reactive, ref } from 'vue'; //引入vue
import { useRoute } from 'vue-router';
import InfoCard from './component/InfoCard.vue'; //引入信息卡片组件
import ParkFindBack from './component/ParkFindBack.vue'; //引入"关联车场"组件
const duty = useDuty();
const relatedParkDialogVisible = ref(false); //控制“关联车场”显示
const relatedOrgDialogVisible = ref(false); //控制“组织架构”显示
const activeName = ref(0); //控制“tab”显示
const nvbarData = ref([
  { name: '云客服管理', num: 0 },
  { name: '车场本地管理', num: 0 }
]); //navBar信息
// 发送给“后端”的数据
const senddata = reactive({
  park_id: '', //车场id
  department_ids: '' //组织架构id
});
const park_name = ref(''); //选择的车场名称 //用于回显input
const department_name = ref(''); //选择的组织架构名称 //用于回显input
const params = reactive({
  park_name: undefined,
  org_id: undefined
});
// 控制navbar
const changeNav = (e) => {
  console.log(e, 'xxxxxxxxxxxxxxxxxxxxx');
  activeName.value = e;
  infoCardRef.value.tabIndex = e;
  getList();
};
// 显示“组织架构”
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    relatedOrgDialogVisible.value = true;
  }
};
// 显示“车场”
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    relatedParkDialogVisible.value = true;
  }
};
//选择“车场”
const renderTableInput = (val) => {
  senddata.park_id = val[0].park_id;
  park_name.value = val[0].park_name;
};
//选择“组织架构”
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  // senddata.department_ids = arrId.toString();
  department_name.value = arrName.toString();
  params.org_id = arrId.toString();
};
//初始化数据
const initData = () => {
  park_name.value = '';
  department_name.value = '';
  params.park_name = '';
  params.org_id = '';
};
// 查询
const onSearch = () => {
  console.log('submit!');
  getList();
};
//点击“重置”按钮
const onReset = () => {
  console.log('reset!');
  initData();
  getList();
};
// 点击组织架构“清楚”按钮
const clearDepartment = () => {
  senddata.department_ids = '';
  department_name.value = '';
};

const cloudData = ref([]);
const infoCardRef = ref(null);
const getList = () => {
  getStatics();
  let api = 'monitorconsole';
  if (activeName.value == 1) {
    api = 'monitoragent';
  }
  UnattendedApi[api](params).then((res) => {
    if (res.success) {
      // 云客服i
      if (activeName.value == 0) {
        cloudData.value = res.data.cloud_list;

        let ary = [];
        res.data.cloud_list.forEach((item) => {
          if (item.entrance_list && item.entrance_list.length > 0) {
            item.entrance_list.forEach((m) => {
              ary.push(m.id);
            });
          }
          if (item.export_list && item.export_list.length > 0) {
            item.export_list.forEach((m) => {
              m.showtype = 'export';
              ary.push(m.id);
            });
          }
        });
        getAbnormalChannel(ary);
      }
      // 本地
      if (activeName.value == 1) {
        res.data.agent_list.forEach((item) => {
          if (item.entrance_list && item.entrance_list.length > 0) {
            item.entrance_list.forEach((m) => {
              m.tabName = '本地';
            });
          }
          if (item.export_list && item.export_list.length > 0) {
            item.export_list.forEach((m) => {
              m.tabName = '本地';
            });
          }
        });
        infoCardRef.value.tableData = res.data.agent_list;
      }
    }
  });
};
const msgList = ref([]);
const timer = ref(null);
// watch(
//   () => activeName.value,
//   (newId) => {
//     if (timer.value) {
//       clearInterval(timer.value);
//     }
//     if (newId == 0) {
//       timer.value = setInterval(() => {
//         changeNav(newId);
//       }, 10000);
//     }
//   },
//   { immediate: true } // 立即执行一次
// );
// 获取异常通道
const getAbnormalChannel = (ary) => {
  duty.callInfo.msgList = [];
  UnattendedApi.abnormalGatewayList({ gateway_ids: ary }).then((res) => {
    if (res.success) {
      // 处理异常通道数据
      console.log(res.data);
      if (res.data && res.data.length > 0) {
        res.data.forEach((item) => {
          cloudData.value.forEach((m) => {
            if (m.entrance_list && m.entrance_list.length > 0) {
              m.entrance_list.forEach((n, i) => {
                if (n.id == item.id) {
                  m.entrance_list[i] = {
                    ...n,
                    ...item
                  };
                  if (item.event_type_desc && item.event_type_desc.length > 0) {
                    msgList.value.push({
                      ...n,
                      ...item
                    });
                  }
                }
              });
            }
            if (m.export_list && m.export_list.length > 0) {
              m.export_list.forEach((n, i) => {
                if (n.id == item.id) {
                  m.export_list[i] = {
                    ...n,
                    ...item
                  };
                  if (item.event_type_desc && item.event_type_desc.length > 0) {
                    msgList.value.push({
                      ...n,
                      ...item
                    });
                  }
                }
              });
            }
          });
        });
        duty.callInfo.msgList = msgList.value;
      }
      console.log(cloudData.value, 'xxxxxxxxxxxxxxxxxxxxxx');
      infoCardRef.value.tableData = cloudData.value;
    }
  });
};
// 车场服务管理权
const leftCount1 = ref(0);
const leftAllCount1 = ref(0);
const leftCount2 = ref(0);
const leftAllCount2 = ref(0);
const getStatics = () => {
  UnattendedApi.statisticsparkId().then((res) => {
    console.log('zhixingle11111111111111111');
    if (res.success) {
      leftCount1.value = res.data.cloud_count;
      leftAllCount1.value = res.data.total_count;
      leftCount2.value = res.data.agent_count;
      leftAllCount2.value = res.data.total_count;
      nvbarData.value[0].num = res.data.cloud_count;
      nvbarData.value[1].num = res.data.agent_count;
    }
  });
};
const route = useRoute();
onMounted(() => {
  // getList();
});
onActivated(() => {
  changeNav(route.query.type);
});
</script>

<style scoped lang="scss">
* {
  user-select: none;
}
.box-con {
  height: 100%;
  width: 100%;
  padding: 5px;
  display: flex;
  flex-direction: column;
  gap: 10px;

  .box-con-t {
    height: 50px;
    background-color: #fff;
    border-radius: 10px;
    display: flex;
    align-items: center;
    padding-left: 20px;
    padding-top: 20px;
    .el-form {
      display: flex;
      align-items: center;
    }
  }
  .box-con-b {
    flex: 1;
    min-height: 0;
    background-color: #fff;
    border-radius: 10px;
    padding: 10px 20px;
    .box-con-b-title {
      height: 20px;
      font-size: 14px;
      display: flex;
      align-items: center;
      .fill {
        height: 80%;
        width: 3px;
        border-radius: 3px;
        background-color: #009dff;
        margin-right: 5px;
      }
    }
    .box-con-b-con {
      height: calc(100% - 20px);
      display: flex;
      flex-direction: column;
      flex-wrap: wrap;
      .navbar {
        height: 40px;
        display: flex;
        align-items: center;
        gap: 20px;
        font-size: 14px;
        padding-left: 10px;

        > div {
          cursor: pointer;
        }
        .navAc {
          color: #009dff;
          position: relative;
          &::after {
            content: '';
            position: absolute;
            bottom: -3px;
            left: 50%;
            transform: translateX(-50%);
            height: 2px;
            width: 60%;
            background-color: #009dff;
            border-radius: 2px;
          }
        }
      }
      .box-b-con {
        height: calc(100% - 50px);
        overflow-y: auto;
        .scrollbox {
          height: 100%;
          padding: 5px;
          overflow: auto;
        }
      }
    }
  }
}
.el-form-item {
  margin-right: 20px;
}
.el-input,
.el-button {
  font-size: 14px;
}
.paigin {
  width: 100%;
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
<style>
.el-dialog__body {
  padding: 0px 20px 20px 20px !important;
}
</style>
