import * as propertyOwner from '@/api/system/PropertyOwnerApi';

/**
 * 产权方
 */
export default {
  /**
   * 分页查询
   */
  pagingPropertyOwner(data) {
    return new Promise((resolve, reject) => {
      try {
        propertyOwner.pagingPropertyOwner(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建产权方
   */
  createPropertyOwner(data) {
    return new Promise((resolve, reject) => {
      try {
        propertyOwner.createPropertyOwner(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改产权方
   */
  updatePropertyOwner(data) {
    return new Promise((resolve, reject) => {
      try {
        propertyOwner.updatePropertyOwner(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除产权方
   */
  deletePropertyOwner(data) {
    return new Promise((resolve, reject) => {
      try {
        propertyOwner.deletePropertyOwner(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 产权放列表
   * @param {*} data
   * @returns
   */
  listPropertyOwner() {
    return new Promise((resolve, reject) => {
      try {
        propertyOwner.listPropertyOwner().then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
