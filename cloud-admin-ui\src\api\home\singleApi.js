import $ from '@/utils/axios';
const basePrefix = '/console/homepage';
const generateUrl = (url) => {
  return basePrefix + url;
};
/**
 * @description 获取首页统计信息
 * @param {*} params
 * @returns
 */
export const fetchStaticInfo = (params = {}) => {
  return $({
    url: generateUrl('/countParkInfo'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取临停支付统计
 * @param {*} params
 * @returns
 */
export const fetchParkingPay = (params = {}) => {
  return $({
    url: generateUrl('/statParkPayments'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取长租支付统计
 * @param {*} params
 * @returns
 */
export const fetchRentPay = (params = {}) => {
  return $({
    url: generateUrl('/rentPayInfo'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取待办事项
 * @param {*} params
 * @returns
 */
export const fetchTodo = (params = {}) => {
  return $({
    url: generateUrl('/rentTodo'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取交接班记录
 * @param {*} params
 * @returns
 */
export const fetchShiftHandoverRecords = (params = {}) => {
  return $({
    url: generateUrl('/shiftHandoverRecords'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取周转率
 * @param {*} params
 * @returns
 */
export const fetchTurnoverAndSpaceUsage = (params = {}) => {
  return $({
    url: generateUrl('/parkTurnoverAndSpaceUsage'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取停车时长
 * @param {*} params
 * @returns
 */
export const fetchParkingRentDuration = (params = {}) => {
  return $({
    url: generateUrl('/parkingRentDuration'),
    method: 'post',
    data: params
  });
};
// 新版停车平均时长数据
export const statParkTrafficFlowsByInterval = (params) => {
  return $({
    url: generateUrl('/statParkTrafficFlowsByInterval'),
    method: 'post',
    data: params
  });
};

/**
 * @description 通行效率
 * @param {*} params
 * @returns
 */
export const fetchTrafficEfficiency = (params = {}) => {
  return $({
    url: generateUrl('/parkTrafficEfficiency'),
    method: 'post',
    data: params
  });
};

// 新版通行效率
export const homepageParkTrafficEfficiency = (params) => {
  return $({
    url: generateUrl('/parkTrafficEfficiency'),
    method: 'post',
    data: params
  });
};
