/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 会员表格数据查询
export const pagingMembers = (data) => {
  return $({
    url: '/console/member/info/pagingMembers',
    method: 'post',
    data
  });
};

//会员详情数据查询
export const getMemberById = (memberId) => {
  return $({
    url: '/console/member/info/getParkById/' + memberId,
    method: 'get'
  });
};

// 会员信息数据查询
export const pagingMemberMessage = (data) => {
  return $({
    url: '/console/member/memberMessage/pagingMemberMessage',
    method: 'post',
    data
  });
};

// 缴费记录
export const pagingMemberPayRecords = (data) => {
  return $({
    url: '/console/park/memberDetail/pagingMemberPayRecords',
    method: 'post',
    data
  });
};

// 免费车辆
export const pagingMemberWhiteCar = (data) => {
  return $({
    url: '/console/park/memberDetail/pagingMemberWhiteCar',
    method: 'post',
    data
  });
};

// 长租车辆
export const pagingMemberRentCar = (data) => {
  return $({
    url: '/console/park/memberDetail/pagingMemberRentCar',
    method: 'post',
    data
  });
};

// 临停车辆
export const pagingMemberStopCar = (data) => {
  return $({
    url: '/console/park/memberDetail/pagingMemberStopCar',
    method: 'post',
    data
  });
};

// 已绑车辆
export const pagingMemberBindCar = (data) => {
  return $({
    url: '/console/member/memberMessage/pagingMemberBindCar',
    method: 'post',
    data
  });
};
