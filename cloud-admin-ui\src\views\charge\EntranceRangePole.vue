<template>
  <div class="container">
    <entrance-range-pole-search @form-search="searchEntranceRangePoleList" @reset="resetParamsAndData" />
    <entrance-range-pole-table ref="table" />
  </div>
</template>

<script setup name="EntranceRangePole">
import EntranceRangePoleSearch from './entranceRangePole/EntranceRangePoleSearch.vue';
import EntranceRangePoleTable from './entranceRangePole/EntranceRangePoleTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageEntranceRangePole = (queryParams) => {
  table.value.getList(queryParams);
};

const searchEntranceRangePoleList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageEntranceRangePole
});
</script>
