import * as merchant from '@/api/merchant/MerchantApi';

/**
 * 商户管理
 */
export default {
  /**
   *商户表格数据查询
   */
  pagingMerchants(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.pagingMerchants(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查找带回
   * @param {*} data
   * @returns
   */
  findBackMerchants(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.findBackMerchants(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增商户
   */
  createMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.createMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改商户
   */
  updateMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.updateMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用
   */
  enableMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.enableMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 停用
   */
  disableMerchant(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.disableMerchant(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 重置商户密码
   */
  resetPassword(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.resetPassword(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 根据车场id关联应用
   */
  parkByAppList(data) {
    return new Promise((resolve, reject) => {
      try {
        merchant.parkByAppList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
