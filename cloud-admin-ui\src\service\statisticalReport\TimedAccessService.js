import * as timedAccessApi from '@/api/statisticalReport/TimedAccessApi';

/**
 * 分时段进出
 */
export default {
  /**
   * 分页查询分时段进出
   */
  pagingTimedAccess(data) {
    return new Promise((resolve, reject) => {
      try {
        timedAccessApi.pagingTimedAccess(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        timedAccessApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
