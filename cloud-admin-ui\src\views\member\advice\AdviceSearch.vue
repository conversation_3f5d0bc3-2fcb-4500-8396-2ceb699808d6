<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.question_types" style="width: 100%" placeholder="投诉类型" multiple>
        <el-option v-for="item in adviceTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.contact_name" placeholder="投诉人姓名" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.contact_mobile" placeholder="手机号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" style="width: 100%" placeholder="处理状态" multiple>
        <el-option v-for="item in statesList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-row :gutter="5">
        <el-col :span="6">
          <el-select v-model="form.queryParams.dateType" placeholder="时间类型" style="width: 100%">
            <el-option label="创建时间" value="1"></el-option>
            <el-option label="处理时间" value="2"></el-option>
          </el-select>
        </el-col>
        <el-col :span="18">
          <el-date-picker
            v-model="form.dateRange"
            type="datetimerange"
            style="width: 100%"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
          />
        </el-col>
      </el-row>
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
    <park-find-back :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="AdviceSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from './ParkFindBack.vue';
import { ElMessage, dayjs } from 'element-plus';
import { reactive, onMounted, ref } from 'vue';
const emits = defineEmits(['form-search']);
const adviceTypeList = ref([]);
const statesList = ref([]);
const relatedParkDialogVisible = ref(false);
const park_name = ref('');
const form = reactive({
  queryParams: {
    park_name: undefined,
    question_types: [],
    contact_name: undefined,
    contact_mobile: undefined,
    states: [],
    dateType: undefined,
    start_created_at: undefined,
    end_created_at: undefined,
    start_updated_at: undefined,
    end_updated_at: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

onMounted(() => {
  initSelects();
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'adviceTypeList',
      enum_value: 'EnumQuestionType'
    },
    {
      enum_key: 'statesList',
      enum_value: 'EnumQuestionState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    adviceTypeList.value = response.data.adviceTypeList;
    statesList.value = response.data.statesList;
  });
};

const handleDataSearch = () => {
  if (form.dateRange !== null && form.dateRange.length !== 0 && form.queryParams.dateType === undefined) {
    ElMessage({
      message: '请选择时间类型',
      type: 'warning'
    });
    return;
  }
  if (form.queryParams.dateType === '1') {
    if (undefined != form.dateRange && form.dateRange.length > 0) {
      form.queryParams.start_created_at = form.dateRange[0];
      form.queryParams.end_created_at = form.dateRange[1];
      form.queryParams.start_updated_at = undefined;
      form.queryParams.end_updated_at = undefined;
    }
    if (null === form.dateRange) {
      form.queryParams.start_created_at = undefined;
      form.queryParams.end_created_at = undefined;
      form.queryParams.start_updated_at = undefined;
      form.queryParams.end_updated_at = undefined;
    }
  } else if (form.queryParams.dateType === '2') {
    if (undefined != form.dateRange && form.dateRange.length > 0) {
      form.queryParams.start_updated_at = form.dateRange[0];
      form.queryParams.end_updated_at = form.dateRange[1];
      form.queryParams.start_created_at = undefined;
      form.queryParams.end_created_at = undefined;
    }
    if (null === form.dateRange) {
      form.queryParams.start_created_at = undefined;
      form.queryParams.end_created_at = undefined;
      form.queryParams.start_updated_at = undefined;
      form.queryParams.end_updated_at = undefined;
    }
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  (form.dateRange = []),
    (form.queryParams = {
      park_name: undefined,
      question_types: [],
      contact_name: undefined,
      contact_mobile: undefined,
      states: [],
      dateType: undefined,
      start_created_at: undefined,
      end_created_at: undefined,
      start_updated_at: undefined,
      end_updated_at: undefined,
      page: 1,
      limit: 30
    });
  emits('reset', form.queryParams);
  handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
