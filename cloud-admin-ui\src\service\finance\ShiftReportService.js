import * as shiftReportApi from '@/api/finance/ShiftReportApi';

/**
 * 交接班报表
 */
export default {
  /**
   * 分页查询交接班报表
   */
  pagingShiftReport(data) {
    return new Promise((resolve, reject) => {
      try {
        shiftReportApi.pagingShiftReport(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出报表
   */
  exportReports(data) {
    return new Promise((resolve, reject) => {
      try {
        shiftReportApi.exportReports(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
