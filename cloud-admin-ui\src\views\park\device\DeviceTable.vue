<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加设备</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" v-if="scope.row.state == 0" @click="batchDelete(scope.row.id)"> 删除 </el-button>
            <el-button link v-if="scope.row.state == 0" type="primary" @click="enable(scope.row)"> 启用 </el-button>
            <el-button link v-if="scope.row.state == 1" type="danger" @click="disable(scope.row)"> 禁用 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="sn" label="设备序列号" align="center" min-width="180" />
        <el-table-column prop="dev_device_name" label="设备名称" align="center" min-width="150" />
        <el-table-column prop="model" label="设备型号" align="center" min-width="120" />
        <el-table-column prop="type_desc" label="设备类型" align="center" min-width="150" />
        <el-table-column prop="in_out_desc" label="出入类型" align="center" min-width="150" />
        <el-table-column prop="dev_factory_name" label="设备厂家" align="center" min-width="180" />
        <el-table-column prop="park_region_name" label="所属子场" align="center" min-width="180" />
        <el-table-column prop="gateway_name" label="所属通道" align="center" min-width="180" />
        <el-table-column prop="ip" label="设备地址" align="center" min-width="120" />
        <el-table-column prop="port" label="控制端口" align="center" min-width="100" />
        <el-table-column prop="account" label="设备账号" align="center" min-width="120" />
        <el-table-column prop="password" label="设备密码" align="center" min-width="120" />
        <el-table-column prop="state_desc" label="状态" align="center" min-width="100" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加设备" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="1000px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="device_sn" label="设备序列号">
                <el-input
                  v-model="data.form.device_sn"
                  placeholder="设备序列号"
                  maxlength="30"
                  onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                /> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="name" label="设备名称">
                <el-input v-model="data.form.name" readonly="true" @click="relatedDevice" placeholder="设备名称" /></el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="type_desc" label="设备类型">
                {{ data.form.type_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="model" label="设备型号">
                {{ data.form.model }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="factory_name" label="设备厂家">
                {{ data.form.factory_name }}
              </el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item prop="park_region_id" label="所属子场">
                <el-select
                  v-model="data.form.park_region_id"
                  style="width: 100%"
                  placeholder="所属子场"
                  @change="changeRegion(data.form.park_region_id)"
                >
                  <el-option v-for="item in childList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="gateway_id" label="所属通道">
                <el-select v-model="data.form.gateway_id" style="width: 100%" placeholder="所属通道" @click="findGateway">
                  <el-option v-for="item in gatewayList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="ip" label="设备地址"> <el-input v-model="data.form.ip" placeholder="设备地址" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="port" label="控制端口">
                <el-input v-model="data.form.port" maxlength="10" placeholder="控制端口" type="number" /> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="account" label="设备账号">
                <el-input v-model="data.form.account" maxlength="20" placeholder="设备账号" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="password" label="设备密码"
                ><el-input v-model="data.form.password" maxlength="20" placeholder="设备密码" /> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="video_stream_address" label="监控视频地址">
                <el-input v-model="data.form.video_stream_address" maxlength="100" placeholder="监控视频地址" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="in_out" label="出入类型">
                <el-select v-model="data.form.in_out" style="width: 100%" placeholder="出入类型">
                  <el-option v-for="item in inOutList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createDevice(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改设备" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="1000px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="device_sn" label="设备序列号">
                <el-input
                  v-model="data.updateForm.device_sn"
                  placeholder="设备序列号"
                  maxlength="30"
                  onkeyup="value=value.replace(/[^\w\.\/]/ig,'')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="name" label="设备名称">
                <el-input v-model="data.updateForm.name" readonly="true" @click="relatedDevice" placeholder="设备名称" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="type_desc" label="设备类型">
                {{ data.updateForm.type_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="model" label="设备型号">
                {{ data.updateForm.model }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="factory_name" label="设备厂家">
                {{ data.updateForm.factory_name }}
              </el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item prop="park_region_id" label="所属子场">
                <el-select
                  v-model="data.updateForm.park_region_id"
                  style="width: 100%"
                  placeholder="所属子场"
                  @change="updateChangeRegion(data.updateForm.park_region_id)"
                >
                  <el-option v-for="item in childList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="gateway_id" label="所属通道">
                <el-select v-model="data.updateForm.gateway_id" style="width: 100%" placeholder="所属通道" @click="findGateway">
                  <el-option v-for="item in gatewayList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="ip" label="设备地址"> <el-input v-model="data.updateForm.ip" placeholder="设备地址" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="port" label="控制端口">
                <el-input v-model="data.updateForm.port" type="number" placeholder="控制端口"
              /></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="account" label="设备账号"> <el-input v-model="data.updateForm.account" placeholder="设备账号" /></el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="password" label="设备密码"><el-input v-model="data.updateForm.password" placeholder="设备密码" /></el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item prop="video_stream_address" label="监控视频地址">
                <el-input v-model="data.updateForm.video_stream_address" maxlength="100" placeholder="监控视频地址" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="in_out" label="出入类型">
                <el-select v-model="data.updateForm.in_out" style="width: 100%" placeholder="出入类型">
                  <el-option v-for="item in inOutList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateDevice(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 关联设备 -->
      <el-dialog v-if="deviceDialogVisible" width="900px" title="关联设备" v-model="deviceDialogVisible" :before-close="handleClose">
        <device-find-back
          :device_name="deviceName"
          :device_id="deviceId"
          @relatedDevice="relatedDevice(false)"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="DeviceTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import deviceService from '@/service/park/DeviceService';
import commonService from '@/service/common/CommonService';
import DeviceFindBack from './DeviceFindBack.vue';

const validateIP = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))(\.((2((5[0-5])|([0-4]\d)))|([0-1]?\d{1,2}))){3}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的IP地址'));
    }
  }
  callback();
};

const validateNumAndEng = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^[A-Za-z0-9]+$/;
    if (!reg.test(value)) {
      callback(new Error('只能输入英文和数字'));
    }
  }
  callback();
};

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const deviceTypeList = ref([]);
const inOutList = ref([]);
const childList = ref([]);
const gatewayList = ref([]);
const total = ref(0);
const park_id = ref('');
const deviceId = ref('');
const deviceName = ref('');
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const deviceDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    park_id: undefined,
    device_sn: undefined,
    device_id: undefined,
    name: undefined,
    type: undefined,
    type_desc: undefined,
    model: undefined,
    factory_id: undefined,
    park_region_id: undefined,
    gateway_id: undefined,
    ip: undefined,
    port: undefined,
    account: undefined,
    password: undefined,
    in_out: undefined
  },
  updateForm: {},
  rules: {
    device_sn: [
      {
        required: true,
        message: '请输入设备序列号',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请选择设备名称',
        trigger: 'blur'
      }
    ],
    park_region_id: [
      {
        required: true,
        message: '请选择所属子场',
        trigger: 'blur'
      }
    ],
    gateway_id: [
      {
        required: true,
        message: '请选择所属通道',
        trigger: 'blur'
      }
    ],
    ip: [
      {
        required: true,
        message: '请输入设备地址',
        trigger: 'blur'
      }
    ],
    port: [
      {
        required: true,
        message: '请输入控制端口',
        trigger: 'blur'
      }
    ],
    account: [
      {
        required: true,
        message: '请输入设备账号',
        trigger: 'blur'
      }
      // {
      //   trigger: 'blur',
      //   validator: validateNumAndEng
      // }
    ],
    password: [
      {
        required: true,
        message: '请输入设备密码',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateNumAndEng
      }
    ],
    in_out: [
      {
        required: true,
        message: '请选择出入类型',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
  status.value = true;
});

// 初始化数据
const initSelects = () => {
  const param = [
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    },
    {
      enum_key: 'inOutList',
      enum_value: 'EnumGatewayDeviceDirection'
    }
  ];
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
    inOutList.value = response.data.inOutList;
  });
};

const getList = (params) => {
  loading.value = true;
  park_id.value = params.park_id;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  deviceService.pagingDevice(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  gatewayList.value = [];
  data.form = {
    park_id: park_id,
    device_sn: undefined,
    device_id: undefined,
    name: undefined,
    type: undefined,
    model: undefined,
    factory_id: undefined,
    park_region_id: undefined,
    gateway_id: undefined,
    ip: undefined,
    port: undefined,
    account: undefined,
    password: undefined,
    in_out: undefined
  };
  createDialogVisible.value = true;
  // 所属子场
  deviceService.listParkRegion(data.form.park_id).then((response) => {
    childList.value = response.data;
  });
  status.value = false;
};

// 所属通道
const findGateway = () => {
  if (data.form.park_region_id !== undefined) {
    deviceService.listParkGateway(data.form.park_region_id).then((response) => {
      gatewayList.value = response.data;
    });
  }
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const renderTableInput = (val) => {
  if (createDialogVisible.value) {
    data.form.device_id = val[0].device_id;
    data.form.name = val[0].device_name;
    data.form.type = val[0].type;
    data.form.type_desc = val[0].type_desc;
    data.form.model = val[0].model;
    data.form.factory_id = val[0].dev_factory_id;
    data.form.factory_name = val[0].dev_factory_name;
  } else {
    data.updateForm.device_id = val[0].device_id;
    data.updateForm.name = val[0].device_name;
    data.updateForm.type = val[0].type;
    data.updateForm.type_desc = val[0].type_desc;
    data.updateForm.model = val[0].model;
    data.updateForm.factory_id = val[0].dev_factory_id;
    data.updateForm.factory_name = val[0].dev_factory_name;
  }
};

// 关联设备
const relatedDevice = (visible) => {
  if (visible === false) {
    deviceDialogVisible.value = false;
  } else {
    ``;
    deviceDialogVisible.value = true;
  }
  if (createDialogVisible.value) {
    deviceId.value = data.form.device_id;
    deviceName.value = data.form.name;
  } else {
    deviceId.value = data.updateForm.device_id;
    deviceName.value = data.updateForm.name;
  }
};

const createDevice = (addForm) => {
  addForm.validate().then(() => {
    deviceService
      .createDevice(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          addForm.resetFields();
          getList(data.queryParams);
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceService
      .deleteDevice(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: park_id,
    device_sn: row.sn,
    device_id: row.dev_device_id,
    name: row.dev_device_name,
    type: row.type,
    type_desc: row.type_desc,
    model: row.model,
    factory_id: row.dev_factory_id,
    factory_name: row.dev_factory_name,
    park_region_id: row.park_region_id,
    park_region_name: row.park_region_name,
    gateway_id: row.gateway_id,
    gateway_name: row.gateway_name,
    ip: row.ip,
    port: row.port,
    account: row.account,
    password: row.password,
    in_out: row.in_out
  };
  updateDialogVisible.value = true;
  // 所属子场
  deviceService.listParkRegion(data.updateForm.park_id).then((response) => {
    childList.value = response.data;
  });
  // 所属通道
  deviceService.listParkGateway(data.updateForm.park_region_id).then((response) => {
    gatewayList.value = response.data;
  });
};
const updateDevice = (editForm) => {
  editForm.validate().then(() => {
    deviceService
      .updateDevice(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 启用
const enable = (row) => {
  ElMessageBox.confirm('是否要启用该设备？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceService.enableParkDevice(row.id).then(() => {
      ElMessage({
        message: '设备启用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};
// 禁用
const disable = (row) => {
  ElMessageBox.confirm('是否要禁用该设备？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceService.disableParkDevice(row.id).then(() => {
      ElMessage({
        message: '设备停用成功',
        type: 'success'
      });
      getList(data.queryParams);
    });
  });
};

// 当子场发生变化时通道对应变化
const changeRegion = (row) => {
  deviceService.listParkGateway(row).then((response) => {
    gatewayList.value = response.data;
  });
  data.form.gateway_id = undefined;
};
const updateChangeRegion = (row) => {
  deviceService.listParkGateway(row).then((response) => {
    gatewayList.value = response.data;
  });
  data.updateForm.gateway_id = undefined;
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
