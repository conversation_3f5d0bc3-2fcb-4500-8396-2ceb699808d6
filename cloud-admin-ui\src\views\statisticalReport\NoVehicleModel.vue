<template>
  <div class="container">
    <no-vehicle-model-search ref="searchRef" @form-search="searchNoVehicleModelList" @reset="resetParamsAndData" />
    <no-vehicle-model-table ref="table" />
  </div>
</template>

<script setup name="NoVehicleModel">
import NoVehicleModelSearch from './noVehicleModel/NoVehicleModelSearch.vue';
import NoVehicleModelTable from './noVehicleModel/NoVehicleModelTable.vue';
import { ref, reactive,defineExpose } from 'vue';

const table = ref(null);
const searchRef = ref(null);
const params = reactive({});

const pageNoVehicleModel = (queryParams) => {
  table.value.getList(queryParams);
};

const searchNoVehicleModelList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

const runSearchRef = () => {
  searchRef.value.handleDataSearch();
};
defineExpose({
  pageNoVehicleModel,
  runSearchRef
});
</script>
