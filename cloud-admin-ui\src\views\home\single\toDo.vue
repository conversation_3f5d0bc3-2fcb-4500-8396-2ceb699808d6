<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-26 09:37:37
 * @LastEditors: 达万安 段世煜
 * @Description: 待办事项
 * @FilePath: \cloud-admin-ui\src\views\home\single\toDo.vue
-->
<template>
  <warp-card height="24%" title="待办事项">
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column label="滞留车辆数量" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.stranded_num === 'button'" type="primary" link
            @click="viewDetail('/charge/chargeAdmin')">查看详情</el-button>
          <span v-else> {{ scope.row.stranded_num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="优免审批" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.other === 'button'" type="primary" link
            @click="viewDetail('/bizAudit/BizAudit')">查看详情</el-button>
          <span v-else> {{ scope.row.other }}</span>
        </template>
      </el-table-column>
      <el-table-column label="长租车位即将到期" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.date === 'button'" type="primary" link
            @click="viewDetail('/car/spaceRentApply')">查看详情</el-button>
          <span v-else> {{ scope.row.date }}</span>
        </template>
      </el-table-column>
      <el-table-column label="待处理退款数量" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.back === 'button'" type="primary" link
            @click="viewDetail('/finance/refund')">查看详情</el-button>
          <span v-else> {{ scope.row.back }}</span>
        </template>
      </el-table-column>
    </el-table>
    <el-table :data="tableData" border style="width: 100%">
      <el-table-column label="长租车申请" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.rent_apply_num === 'button'" type="primary" link
            @click="viewDetail('/car/spaceRentApply')">查看详情</el-button>
          <span v-else> {{ scope.row.rent_apply_num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="长租车续费" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.rent_renew_num === 'button'" type="primary" link
            @click="viewDetail('/charge/longRentPay')">查看详情</el-button>
          <span v-else> {{ scope.row.rent_renew_num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="访客车申请" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.visit_apply_num === 'button'" type="primary" link
            @click="viewDetail('/car/CarVisitor')">查看详情</el-button>
          <span v-else> {{ scope.row.visit_apply_num }}</span>
        </template>
      </el-table-column>
      <el-table-column label="其他待审核" align="center">
        <template #default="scope">
          <el-button v-if="scope.row.other_apply_num === 'button'" type="primary" link
            @click="viewDetail('/car/carFree')">查看详情</el-button>
          <span v-else> {{ scope.row.other_apply_num }}</span>
        </template>
      </el-table-column>
    </el-table>
  </warp-card>
</template>

<script setup>
import { fetchTodo } from '@/api/home/<USER>';
import { activeRouteTab } from '@/utils/tabKit';
import { reactive } from 'vue';
import warpCard from './components/warpCard.vue';

const viewDetail = (path) => {
  activeRouteTab({
    path: path
  });
};

const tableData = reactive([
  {
    stranded_num: 0,
    date: 0,
    back: 0,
    other: 0,
    rent_apply_num: 0,
    rent_renew_num: 0,
    visit_apply_num: 0,
    other_apply_num: 0,
  },
  {
    stranded_num: 'button',
    date: 'button',
    back: 'button',
    other: 'button',
    rent_apply_num: 'button',
    rent_renew_num: 'button',
    visit_apply_num: 'button',
    other_apply_num: 'button',
  }
]);

const fetchData = async (params) => {
  const { data } = await fetchTodo(params);
  tableData[0].date = data.overdue_rent_num || 0;
  tableData[0].back = data.refund_rent_num || 0;
  tableData[0].other = data.other_rent_num || 0;
  tableData[0].stranded_num = data.stranded_num || 0;
  tableData[0].rent_apply_num = data.rent_apply_num || 0;
  tableData[0].rent_renew_num = data.rent_renew_num || 0;
  tableData[0].visit_apply_num = data.visit_apply_num || 0;
  tableData[0].other_apply_num = data.other_apply_num || 0;
};

defineExpose({
  fetchData
});
</script>

<style scoped lang="scss">
::v-deep .el-table .el-table__cell {
  padding: 5px 0;
}
</style>
