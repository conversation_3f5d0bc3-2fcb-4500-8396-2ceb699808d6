<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-25 19:10:28
 * @LastEditors: 达万安 段世煜
 * @Description: 周转率及车位利用率趋势图
 * @FilePath: \cloud-admin-ui\src\views\home\single\turnoverRate.vue
-->
<template>
  <normal-chart
    ref="lineChartRef"
    showLabel
    labelFormatter="{c}%"
    :color="color"
    :grid="{ top: 50, bottom: 20, left: 40, right: 10 }"
    :legendPosition="{ top: 10 }"
    type="line"
    :yLeftUnit="'%'"
  />
</template>

<script setup>
import { ref, nextTick } from 'vue';
import { dayjs } from 'element-plus';
import normalChart from './components/normalChart.vue';
import { fetchTurnoverAndSpaceUsage } from '@/api/home/<USER>';

const color = ['#FFA900', '#3B8FFF'];
const lineChartRef = ref(null);
let data = [
  {
    name: '周转率',
    value: [0]
  },
  {
    name: '车位利用率',
    value: [0]
  }
];
let xData = [''];
const timeFormatter = {
  3: 'MM月DD日',
  2: 'MM月',
  5: 'ww周'
};
const fetchData = async (params) => {
  try {
    const { data: resData } = await fetchTurnoverAndSpaceUsage(params);
    let useData = [];
    if (resData && resData.length) useData = resData.reverse();
    data = [
      { name: '周转率', value: useData.map((item) => item.average_turnround_rate || 0) },
      { name: '车位利用率', value: useData.map((item) => item.average_use_ratio || 0) }
    ];
    xData = useData.map((item) => dayjs(item.statistics_date).format(timeFormatter[params.time_unit]));
  } finally {
    nextTick(() => {
      lineChartRef.value.setData(data, xData);
    });
  }
};
defineExpose({
  fetchData
});
</script>

<style scoped lang="scss"></style>
