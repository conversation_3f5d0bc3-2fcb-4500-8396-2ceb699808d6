import $ from '@/utils/axios';

const basePrefix = '/console/groupHomepage';
const basePrefix1 = '/console/dashboard';
const generateUrl = (url) => {
  return basePrefix + url;
};
const generateUrl1 = (url) => {
  return basePrefix1 + url;
};
/**
 * @description 车场排名统计
 * @param {*} type 类型
 * @param {*} params 参数
 * @returns
 */
export const fetchRankingData = (type, params) => {
  const urlObj = {
    '交易总额(临停)': '/pagingParkPayedMoneySort',
    订单笔数: '/pagingParkPayedNumSort',
    单车位价值: '/parkSpaceValueSort',
    指标达成率: '/pagingParkPayedRatioSort'
  };
  return $({
    url: generateUrl(urlObj[type]),
    method: 'post',
    data: params
  });
};

/**
 * @description 出入车次统计
 * @param {*} type 类型
 * @param {*} params 参数
 * @returns
 */
export const fetchEntryAndExitsData = (type, params) => {
  console.log(type,"sssss")
  const urlObj = {
    // 0: '/parkingRentDuration',
    // 1: '/parkingRentOutStat'
    0: '/parkingRentInChart',
    1: '/parkingRentDuration'
  };
  return $({
    url: type == 0 ? generateUrl1(urlObj[type]) : generateUrl(urlObj[type]),
    method: 'post',
    data: params
  });
};

// 新版临停/长租出入车次统计
export const carInOutFlowByInterval = (params) => {
  return $({
    url: generateUrl('/carInOutFlowByInterval'),
    method: 'post',
    data: params
  });
};

/**
 * @description 进出场总次数
 * @param {*} params
 * @returns
 */
export const fetchEntryAndExitsCount = (params) => {
  return $({
    url: generateUrl('/parkInOutChart'),
    method: 'post',
    data: params
  });
};

// 新版进出车场总次数和停车平均时长数据
export const statParkTrafficFlowsByInterval = (params) => {
  return $({
    url: generateUrl('/statParkTrafficFlowsByInterval'),
    method: 'post',
    data: params
  });
};

/**
 * @description 车场信息概览
 * @param {*} params
 * @returns
 */
export const fetchParkingInfo = (params) => {
  return $({
    url: generateUrl('/countParkInfo'),
    method: 'post',
    data: params
  });
};

/**
 * @description 其他信息统计
 * @param {*} params
 * @returns
 */
export const fetchParkingOther = (params) => {
  return $({
    url: generateUrl('/countParkState'),
    method: 'post',
    data: params
  });
};

/**
 * @description 现金/电子收入占比
 * @param {*} params
 * @returns
 */
export const fetchIncomeRatio = (params) => {
  return $({
    url: generateUrl('/statParkPayments'),
    method: 'post',
    data: params
  });
};
/**
 * @description 获取交易金额/笔数数据
 * @param {*} params
 * @returns
 */
export const fetchTransaction = (params) => {
  return $({
    url: generateUrl('/parkPayMoneyChart'),
    method: 'post',
    data: params
  });
};

/**
 * @description 获取地图数据
 * @param {*} params
 * @returns
 */
export const fetchMapData = (params) => {
  return $({
    url: generateUrl('/subDepartmentList'),
    method: 'post',
    data: params
  });
};

/**
 * @description 通行效率
 * @param {*} params
 * @returns
 */
export const parkTrafficEfficiency = (params) => {
  return $({
    url: generateUrl('/parkTrafficEfficiency'),
    method: 'post',
    data: params
  });
};

// 新版通行效率
export const groupHomepageParkTrafficEfficiency = (params) => {
  return $({
    url: generateUrl('/parkTrafficEfficiency'),
    method: 'post',
    data: params
  });
};
