import menuService from '@/service/system/MenuService';
import { addDynamicRoutes } from '@/utils/router';
import { useMenu } from '@/stores/menu';
import { activeRouteTab } from './tabKit';

/**
 * 加载菜单
 */
export const loadMenusAndRoutes = (obj) => {
  const menu = useMenu();
  if (!menu.state.menuLoaded) {
    menuService.findNavMenus().then((res) => {
      if (res.success) {
        // 添加动态路由
        addDynamicRoutes(res.data);

        // 设置菜单
        menu.state.menuList = res.data;
        // 设置页面
        setPages(res.data);
        // 设置权限
        setPermissions(res.data);

        // 设置菜单加载状态
        menu.state.menuLoaded = true;

        // 默认激活
        activeRouteTab({
          path: '/'
        });

        // 激活路由页签
        activeRouteTab({
          path: obj.path,
          query: obj.query
        });
      }
    });
  }
};

/**
 * 设置页面
 * @param {*} menus
 */
export const setPages = (menus) => {
  for (const idx in menus) {
    if (menus[idx].type == 'page' || (menus[idx].type == 'menu' && menus[idx].children.length === 0)) {
      const menu = useMenu();
      menu.state.pageList.push(menus[idx]);
      if (menus[idx].cached && menus[idx].cached === 1) {
        menu.state.includeKeepAlives.push(menus[idx].name);
      } else {
        menu.state.excludeKeepAlives.push(menus[idx].name);
      }
    }

    if (menus[idx].children && menus[idx].children.length > 0) {
      setPages(menus[idx].children);
    }
  }
};

/**
 * 设置权限
 * @param {*} menus
 */
export const setPermissions = (menus) => {
  for (const idx in menus) {
    if (menus[idx].permissions) {
      const menu = useMenu();
      menu.state.permissionList.push(...menus[idx].permissions);
    }

    if (menus[idx].children && menus[idx].children.length > 0) {
      setPermissions(menus[idx].children);
    }
  }
};
