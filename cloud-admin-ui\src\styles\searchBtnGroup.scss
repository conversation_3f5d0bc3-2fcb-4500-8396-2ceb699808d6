.search-btn-group {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-flex-wrap: wrap;
  -ms-flex-wrap: wrap;
  flex-wrap: wrap;
  text-align: center;
  margin: 10px 0 10px;
  width: 100%;
}

.search-btn-group-container {
  transition: all 0.8s ease;
  opacity: 0;
  width: 0;
}

.group-active {
  transition: all 1s ease;
  opacity: 1;
  width: initial;
}

.search-btn-group-total {
  padding: 6px 16px;
  background-color: #fff;
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  -webkit-flex-direction: column;
  -moz-box-orient: vertical;
  -moz-box-direction: normal;
  -ms-flex-direction: column;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  -moz-box-pack: center;
  -ms-flex-pack: center;
  justify-content: center;
  cursor: pointer;

  .search-btn-group-total-num {
    color: #409eff;
    margin: 1px;
    font-weight: 700;
    font-size: 12px;
  }

  .search-btn-group-total-label {
    color: #161616;
    font-size: 12px;
  }
}

.search-btn-group-con {
  display: -webkit-box;
  display: -webkit-flex;
  display: -moz-box;
  display: -ms-flexbox;
  display: flex;
  margin-left: 16px;
  -webkit-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.15);
  -moz-box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.15);
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.15);
  max-width: calc(100% - 144px);

  .search-btn-group-con-title {
    padding: 9px 16px;
    color: #ffffff;
    background-color: #409eff;
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    align-items: middle;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    cursor: pointer;
    max-width: 100px;
    position: relative;
    font-size: 9pt;
  }

  .search-btn-group-type {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-flex-wrap: nowrap;
    -ms-flex-wrap: nowrap;
    flex-wrap: nowrap;
    max-width: calc(100% - 100px);
    height: 100%;
    // overflow: hidden;

    .search-btn-group-type-block {
      -moz-box-orient: vertical;
      -moz-box-direction: normal;
      -ms-flex-direction: column;
      flex-direction: column;
      -webkit-box-pack: center;
      -webkit-justify-content: center;
      -moz-box-pack: center;
      -ms-flex-pack: center;
      justify-content: center;
      background-color: #ffffff;
      border-left: 1px dashed #d1dbe5;
      border-bottom: 3px;
      cursor: pointer;
      -webkit-box-flex: 0;
      -webkit-flex: 0 0 auto;
      -moz-box-flex: 0;
      -ms-flex: 0 0 auto;
      flex: 0 0 auto;
      display: table;
      height: 100%;

      .search-btn-group-type-content {
        display: table-cell;
        vertical-align: middle;
        padding: 6px 16px;
      }

      .search-btn-group-type-num {
        color: #409eff;
        font-weight: 700;
        font-size: 12px;
      }

      .search-btn-group-type-label {
        font-size: 12px;
        color: #161616;
      }
    }

    .search-btn-group-type-block:first-child {
      border-left: none;
    }
  }
}
