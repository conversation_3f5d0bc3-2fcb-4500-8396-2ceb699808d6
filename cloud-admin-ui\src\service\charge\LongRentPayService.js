import * as longRentPayApi from '@/api/charge/LongRentPayApi';

/**
 * 长租缴费
 */
export default {
  /**
   * 分页查询长租缴费
   */
  pagingLongRentPay(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentPayApi.pagingLongRentPay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 线上申请退款
   */
  applyRefund(data) {
    return longRentPayApi.applyRefund(data);
  },
  /**
   * 线下申请退款
   */
  offlineRefundApply(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentPayApi.offlineRefundApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出长租缴费
   */
  exportLongRentPay(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentPayApi.exportLongRentPay(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 计算退款天数和金额
   */
  calculateRefundAmount(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentPayApi.calculateRefundAmount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
