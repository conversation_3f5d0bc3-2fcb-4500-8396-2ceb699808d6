<template>
  <div class="container">
    <el-card class="card">
      <div class="content">
        <div style="margin-top: 60px; margin-bottom: 100px; text-align: center">
          <el-form ref="appForm" label-width="110px" :rules="data.rules" :model="data.appForm">
            <template #header>
              <div style="display: inline-block; line-height: 32px">编辑版本</div>
            </template>
            <el-form-item label="应用名称" class="required" prop="name">
              <el-input v-model="data.appForm.name" maxlength="30" />
            </el-form-item>
            <el-form-item label="应用地址" class="required" prop="url">
              <el-input v-model="data.appForm.url" readonly maxlength="200" />
            </el-form-item>
            <el-form-item label="版本更新说明" prop="versionMemo">
              <el-input type="textarea" :rows="4" v-model="data.appForm.versionMemo" maxlength="500" show-word-limit />
            </el-form-item>
            <el-form-item label="应用介绍" prop="appMemo">
              <el-input type="textarea" :rows="4" v-model="data.appForm.appMemo" maxlength="500" show-word-limit />
            </el-form-item>
            <el-form-item>
              <el-button style="margin-top: 12px" @click="cancel(addForm)">取消</el-button>
              <el-button type="primary" style="margin-top: 12px" @click="save(addForm)">保存</el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="AppEdit">
import { reactive, ref, onMounted } from 'vue';
import { closeCurrentTab } from '@/utils/tabKit';
import { useRoute } from 'vue-router';

const addForm = ref();
const route = useRoute();
const data = reactive({
  appForm: {
    name: undefined,
    url: undefined,
    versionMemo: undefined,
    appMemo: undefined
  },
  rules: {
    name: [
      {
        required: true,
        message: '请输入应用名称',
        trigger: 'blur'
      }
    ],
    url: [
      {
        required: true,
        message: '请输入应用地址',
        trigger: 'blur'
      }
    ],
    versionMemo: [
      {
        required: true,
        message: '请输入版本更新说明',
        trigger: 'blur'
      }
    ],
    appMemo: [
      {
        required: true,
        message: '请输入应用介绍',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  if ({} !== route.query && undefined !== route.query.params) {
    const param = route.query.params;
    data.appForm = {
      id: param.id,
      name: param.name,
      url: param.url,
      versionMemo: param.versionMemo,
      appMemo: param.appMemo
    };
  }
});

const cancel = (addForm) => {
  addForm.resetFields();
  closeCurrentTab({
    path: '/system/appAdmin'
  });
};
const save = (addForm) => {
  addForm.resetFields();
  closeCurrentTab({
    path: '/system/appAdmin'
  });
};
</script>

<style lang="scss" scoped>
.card {
  vertical-align: middle;
  height: 100%;
}

.content {
  width: 1000px;
  margin: 50px auto;
}

.form {
  width: 600px;
  margin: 50px auto;
}

.desc {
  width: 100%;
  padding: 0 0px;
  color: rgba(0, 0, 0, 0.45);
}

.desc h3 {
  margin: 0 0 12px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;
  line-height: 32px;
  font-weight: 500;
}

.desc h4 {
  margin: 0 0 4px;
  color: rgba(0, 0, 0, 0.45);
  font-size: 14px;
  line-height: 22px;
  font-weight: 500;
}

.desc p {
  margin-top: 0;
  margin-bottom: 12px;
  line-height: 22px;
}

.required > .el-form-item__label::before {
  padding-top: 5px;
  content: '* ';
  color: red;
}

.el-upload-dragger {
  width: 500px;
}
</style>
