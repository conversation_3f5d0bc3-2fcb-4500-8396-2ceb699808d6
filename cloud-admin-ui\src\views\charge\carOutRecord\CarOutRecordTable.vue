<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton btnType="default" :exportFunc="carOutRecordService.exportCarOutRecord"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
        </DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 324px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_code" label="停车场编号" align="center" />
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" width="160" />
        <el-table-column prop="out_time" label="出场时间" align="center" width="160" />
        <el-table-column prop="duration" label="停车时长" align="center" />
        <el-table-column prop="gateway_name" label="出场通道" align="center" />
        <el-table-column prop="out_type_desc" label="出场状态" align="center" />
        <el-table-column prop="out_reason_desc" label="放行原因" align="center" />
        <el-table-column prop="main_brand" label="车辆品牌" align="center" />
        <el-table-column prop="sub_brand" label="车辆型号" align="center" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
        <el-table-column prop="in_car_photo" label="入场图片" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="out_car_photo" label="出场图片" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="checkOutPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
  </el-card>
  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="CarOutRecordTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import carOutRecordService from '@/service/charge/CarOutRecordService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const title = ref('');
const loading = ref(false);
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

// onMounted(() => {
//     getList(data.queryParams);
// });

// 查看入场图片
const checkInPicture = (row) => {
  if (row.in_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '入场图片';
    dialogImageUrl.value = row.in_car_photo_url;
  }
};

// 查看出场图片
const checkOutPicture = (row) => {
  if (row.out_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '出场图片';
    dialogImageUrl.value = row.out_car_photo_url;
  }
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  const { park_name, ...newParams } = params;
  carOutRecordService.pagingCarOutRecord(newParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
