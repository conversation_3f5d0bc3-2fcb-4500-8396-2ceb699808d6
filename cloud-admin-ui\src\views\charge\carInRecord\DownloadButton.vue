<template>
  <el-button :type="props.btnType" @click="templateDownload" :loading="downLoading">
    <slot>模板下载</slot>
  </el-button>
  <el-upload :action="uploadExcelUrl" accept=".xlsx" :headers="headers" :show-file-list="false"
    :before-upload="beforeUploadExcel" :on-success="onSuccessUploadExcel">
    <el-button type="plain">批量上传</el-button>
  </el-upload>
  <el-button :type="props.btnType" @click="handleExport" :loading="downLoading">
    <slot>导出</slot>
  </el-button>
</template>
<script setup>
import commonService from '@/service/common/CommonService';
import { getToken } from '@/utils/common';
import { saveToFile } from '@/utils/utils.js';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
const props = defineProps({
  btnType: {
    type: String,
    default: 'success'
  },
  exportFunc: {
    type: Function,
    default: () => { }
  },
  params: {
    type: Object,
    default: () => { }
  },
  // 导出必要条件
  // rules: [{
  //   name: 'park_id',
  //   required: true,
  //   message: '请选择停车场进行统计'
  //  }]
  rules: {
    type: Array,
    default: () => []
  }
});
const emits = defineEmits(['reloadList']);
const uploadExcelUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/fee/inRecords/uploadImportExcel');
const headers = reactive({
  Authorization: getToken()
});
const beforeUploadExcel = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};

const onSuccessUploadExcel = (response) => {
  if (response.data?.detailMessage) {
    ElMessage.error(response.data.detailMessage);
  } else {
    ElMessage({
      message: response.message,
      type: 'success'
    });
    emits("reloadList", true)
  }
};
// 下载
const templateDownload = () => {
  commonService.fileDownload('template/1_parkIn.xlsx').then((res) => {
    let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
    saveToFile(res.data, decodeURIComponent(fileName));
  });
};
const downLoading = ref(false);
const handleExport = async () => {
  if (props.rules) {
    const requiredItem = props.rules.filter((item) => item.required);
    for (let i = 0; i < requiredItem.length; i++) {
      if (!props.params[requiredItem[i]?.name]) {
        ElMessage.warning(requiredItem[i].message);
        return false;
      }
    }
  }
  if (!props.exportFunc) return;
  const { data, success, detail_message, message } = await props.exportFunc(props.params);
  if (success == true) {
    downLoading.value = true;
    commonService
      .fileDownload(data)
      .then((res) => {
        let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
        saveToFile(res.data, decodeURIComponent(fileName));
        downLoading.value = false;
      })
      .catch(() => {
        downLoading.value = false;
      });
  } else {
    ElMessage({
      message: detail_message != '' ? detail_message : message,
      type: 'error'
    });
    downLoading.value = false;
  }
};
</script>
