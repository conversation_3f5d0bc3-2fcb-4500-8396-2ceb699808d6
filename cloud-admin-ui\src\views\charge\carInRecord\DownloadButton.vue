<template>
  <div>
    <el-button :type="props.btnType" @click="templateDownload" :loading="downLoading">
      <slot>模板下载</slot>
    </el-button>
    <el-button :type="props.btnType" @click="uploadFn" :loading="downLoading">
      <slot>批量上传</slot>
    </el-button>
    <el-button :type="props.btnType" @click="handleExport" :loading="downLoading">
      <slot>导出</slot>
    </el-button>

    <!-- 批量上传对话框 -->
    <el-dialog v-model="dialogVisible" width="500px" :title="'批量上传'" @close="resetForm" :close-on-click-modal="false">
      <el-form ref="upForm" label-width="120px" :rules="rules2" :model="form" :validate-on-rule-change="false">
        <el-form-item label="导入方式" prop="importType">
          <el-select v-model="form.importType" style="width: 100%" placeholder="请选择导入方式" @change="handleTypeChange">
            <el-option v-for="item in importTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item v-if="form.importType === 'stayCar'" label="导入时间点" prop="importDate">
          <el-date-picker v-model="form.importDate" type="datetime" placeholder="请选择导入时间点"
            value-format="YYYY-MM-DD HH:mm:ss" />
        </el-form-item>

        <el-form-item label="上传文件" prop="file">
          <el-upload ref="fileUpload" :auto-upload="false" :limit="1" accept=".xlsx,.xls"
            :headers="{ 'Content-Type': 'multipart/form-data' }" :show-file-list="true" :before-upload="beforeUpload"
            :on-change="handleFileChange" :on-remove="handleFileRemove" :file-list="fileList">
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
            <template #tip>
              <div class="el-upload__tip">
                请上传Excel文件
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm" :loading="uploading">
            确 定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import commonService from '@/service/common/CommonService';
import uploadCustom from '@/utils/axios2';
import { saveToFile } from '@/utils/utils.js';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
const props = defineProps({
  btnType: {
    type: String,
    default: 'primary'
  },
  exportFunc: {
    type: Function,
    default: () => { }
  },
  params: {
    type: Object,
    default: () => ({})
  },
  rules: {
    type: Array,
    default: () => []
  },
  parkId: {
    type: [String, Number],
    required: true
  }
});

const emits = defineEmits(['reloadList', 'uploadSuccess']);

// 上传状态
const dialogVisible = ref(false);
const uploading = ref(false);
const downLoading = ref(false);
const upForm = ref();
const fileUpload = ref();
const fileList = ref([]);

// 导入类型选项
const importTypes = [
  { label: '在场车辆校验导入', value: 'stayCar' },
  { label: '基础数据导入', value: 'basicData' }
];

// 表单数据
const form = reactive({
  importType: '',
  importDate: '',
  file: null
});

// 验证规则
const rules2 = reactive({
  importType: [
    { required: true, message: '请选择导入方式', trigger: 'change' }
  ],
  importDate: [
    { required: true, message: '请选择导入时间点', trigger: 'change' }
  ],
  file: [
    { required: true, message: '请选择上传文件', trigger: 'change' }
  ]
});


// API地址
const apiUrls = {
  stayCar: '/console/fee/stayCarDelApply/importDataOperateStayCar',
  basicData: '/console/park/fee/inRecords/uploadImportExcel'
};

// 打开上传对话框
const uploadFn = () => {
  dialogVisible.value = true;
};

// 处理导入类型变化
const handleTypeChange = (val) => {
  // 清空相关字段
  form.importDate = '';
  form.file = null;
  fileList.value = [];

  // 动态调整验证规则
  if (val === 'basicData') {
    rules2.importDate[0].required = false;
  } else {
    rules2.importDate[0].required = true;
  }
};

// 文件变化处理
const handleFileChange = (file, files) => {
  form.file = file.raw;
  fileList.value = files;
};

// 文件移除处理
const handleFileRemove = () => {
  form.file = null;
};

// 上传前校验
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
    file.type === 'application/vnd.ms-excel';
  const isLt25M = file.size / 1024 / 1024 < 25;

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!');
    return false;
  }
  if (!isLt25M) {
    ElMessage.error('上传文件大小不能超过25MB!');
    return false;
  }

  return true;
};

// 提交表单
const submitForm = async () => {
  try {
    // 表单验证
    await upForm.value.validate();

    if (!form.file) {
      ElMessage.error('请选择上传文件');
      return;
    }

    uploading.value = true;

    // 构建FormData
    const formData = new FormData();
    formData.append('file', form.file);

    // 根据不同类型添加不同参数
    if (form.importType === 'stayCar') {
      formData.append('importDate', form.importDate);
      if (props.params.park_id) {
        formData.append('parkId', props.params.park_id);
      } else {
        ElMessage.warning('请选择车场');
        return
      }
    }

    // 获取对应的API地址
    const apiUrl = apiUrls[form.importType];


    // 执行上传
    const response = await uploadCustom(apiUrl, formData)
    // 处理响应
    if (response.data?.detailMessage) {
      ElMessage.error(response.data.detailMessage);
    } else {
      ElMessage.success(response.message || '上传成功');
      emits('uploadSuccess', response.data);
      emits('reloadList', true);
      dialogVisible.value = false;
    }
  } catch (error) {
    console.error('上传失败:', error);
    // ElMessage.error(error.message || '上传失败');
  } finally {
    uploading.value = false;
  }
};

// 重置表单
const resetForm = () => {
  upForm.value?.resetFields();
  fileList.value = [];
  form.file = null;
};

// 模板下载
const templateDownload = () => {
  downLoading.value = true;
  commonService.fileDownload('template/1_parkIn.xlsx')
    .then((res) => {
      const contentDisposition = res.headers['content-disposition'];
      const fileName = contentDisposition?.split('filename=')[1]?.replace(/"/g, '') || 'template.xlsx';
      saveToFile(res.data, decodeURIComponent(fileName));
    })
    .catch(error => {
      ElMessage.error('下载失败: ' + error.message);
    })
    .finally(() => {
      downLoading.value = false;
    });
};

// 导出功能
const handleExport = async () => {
  if (props.rules) {
    const requiredItem = props.rules.filter(item => item.required);
    for (let i = 0; i < requiredItem.length; i++) {
      if (!props.params[requiredItem[i]?.name]) {
        ElMessage.warning(requiredItem[i].message);
        return false;
      }
    }
  }

  if (!props.exportFunc) return;

  try {
    downLoading.value = true;
    const { data, success, detail_message, message } = await props.exportFunc(props.params);

    if (success) {
      const res = await commonService.fileDownload(data);
      const contentDisposition = res.headers['content-disposition'];
      const fileName = contentDisposition?.split('filename=')[1]?.replace(/"/g, '') || 'export.xlsx';
      saveToFile(res.data, decodeURIComponent(fileName));
    } else {
      ElMessage.error(detail_message || message || '导出失败');
    }
  } catch (error) {
    ElMessage.error(error.message || '导出失败');
  } finally {
    downLoading.value = false;
  }
};
</script>

<style scoped>
.el-upload__tip {
  font-size: 12px;
  color: #909399;
  margin-top: 7px;
}
</style>