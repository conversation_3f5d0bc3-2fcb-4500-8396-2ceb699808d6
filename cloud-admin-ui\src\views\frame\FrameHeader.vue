<template>
  <div class="frame-header">
    <div class="logo-area">
      <img src="@/assets/logo.png" class="logo" />
      <div class="icon-warp" @click="toggleMenu" :class="{ active: menuVisable }">
        <img src="@/assets/menu.png" class="icon" />
      </div>
    </div>
    <div class="title-warp">
      <span class="title">&ensp;惠达云停车管理平台</span>
    </div>
    <div class="info-area">
      <el-space :size="0">
        <el-popover :width="120" trigger="hover">
          <template #reference>
            <div class="user" style="margin-right: 30px">
              <img src="@/assets/notice.png" style="width: 16px; margin-right: 10px" />
              <el-badge :value="data.waitAuditCount" :hidden="!data.waitAuditCount">消息</el-badge>
            </div>
          </template>
          <template #default>
            <!-- <div class="notice-item">
              <el-badge :value="data.off_line" :hidden="!data.off_line" @click="handleClickAlarm">设备报警</el-badge>
            </div> -->
            <!-- <el-divider></el-divider> -->
            <div class="notice-item">
              <el-badge :value="data.waitAuditCount" :hidden="!data.waitAuditCount" @click="handleClickAudit">待审批</el-badge>
            </div>
          </template>
        </el-popover>
        <el-popover :width="220" trigger="hover">
          <template #reference>
            <div class="user" style="margin-right: 10px; padding: 16px 12px">
              <img src="@/assets/avatar.png" style="width: 24px; margin-right: 10px" />
              <span class="username">&ensp;{{ user.name }}</span>
              <el-icon style="color: #fff; font-size: 16px; margin-left: 5px">
                <CaretBottom />
              </el-icon>
            </div>
          </template>
          <template #default>
            <div style="display: flex; justify-content: space-between">
              <el-icon style="color: #1e9fff; font-size: 38px">
                <Avatar />
              </el-icon>
              <div style="text-align: right">
                <div>{{ user.name }}</div>
                <div>{{ user.username }}</div>
              </div>
            </div>
            <el-divider></el-divider>
            <div style="display: flex">
              <el-dropdown style="margin-top: 2px; width: 100px">
                <el-link type="primary" :underline="false"
                  >{{ selectedRole }}<el-icon class="el-icon--right"><arrow-down /></el-icon
                ></el-link>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-for="role in data.roles" :key="role.id" @click="handleCommand(role.id)">{{ role.name }}</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
              <el-divider direction="vertical" style="height: 26px"></el-divider>
              <div style="width: 85px; text-align: right">
                <el-link type="primary" @click="changePasswd" :underline="false">修改密码</el-link>
              </div>
            </div>
            <el-row :gutter="8">
              <el-col :span="24">
                <el-button type="danger" plain @click="logout" style="width: 100%; margin-top: 10px; height: 32px">退出登录</el-button>
              </el-col>
            </el-row>
          </template>
        </el-popover>
      </el-space>
    </div>
  </div>
  <el-dialog title="修改密码" v-model="changePasswdDialogVisible" :close-on-click-modal="false" width="30%">
    <el-form ref="form" :model="data.form" label-width="120px" :rules="data.rules">
      <el-form-item label="原密码" prop="old_passwd">
        <el-input v-model="data.form.old_passwd" type="password" />
      </el-form-item>
      <el-form-item label="新密码" prop="new_passwd">
        <el-input v-model="data.form.new_passwd" type="password" maxlength="20" />
      </el-form-item>
      <el-form-item label="重复新密码" prop="repeat_passwd">
        <el-input v-model="data.form.repeat_passwd" type="password" maxlength="20" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="changePasswdDialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitChangePasswd(form)">确 定</el-button>
    </template>
  </el-dialog>
</template>

<script name="FrameHeader" setup>
import { useUser } from '@/stores/user';
import { useMenu } from '@/stores/menu';
import { useTabs } from '@/stores/tabs';
import loginService from '@/service/login/LoginService';
import employeeService from '@/service/system/EmployeeService';
import roleService from '@/service/system/RoleService';
import router from '@/router';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref, computed } from 'vue';
import bizAuditService from '@/service/bizAudit/BizAuditService';
import deviceStatusService from '@/service/device/DeviceStatusService';
import { activeRouteTab } from '@/utils/tabKit';

const validatePassword = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^(?=.*?[a-z])(?=.*?[A-Z])(?=.*?\d)(?=.*?[!#@*&.])[a-zA-Z\d!#@*&.]{6,20}$/;
    if (!reg.test(value)) {
      callback(new Error('密码必须包含大小写字母，数字，符号组成，最少6位，最多20位'));
    }
  }
  callback();
};

const user = useUser();
const menu = useMenu();
const tab = useTabs();

const form = ref();
const selectedRole = ref();

const data = reactive({
  roles: [],
  waitAuditCount: 0,
  off_line: 0,
  form: {
    old_passwd: undefined,
    new_passwd: undefined,
    repeat_passwd: undefined
  },
  rules: {
    old_passwd: [{ required: true, message: '请输入原密码', trigger: 'blur' }],
    new_passwd: [
      { required: true, message: '请输入新密码', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validatePassword
      }
    ],
    repeat_passwd: [
      { required: true, message: '请重复输入新密码', trigger: 'blur' },
      {
        trigger: 'blur',
        validator: validatePassword
      }
    ]
  }
});
const changePasswdDialogVisible = ref(false);

const menuVisable = computed(() => menu.state.menuVisable);

const toggleMenu = () => {
  menu.state.menuVisable = !menuVisable.value;
};

onMounted(() => {
  const user = useUser();
  // 角色列表
  roleService.listRoles().then((res) => {
    if (res.success) {
      data.roles = res.data;
      selectedRole.value = res.data.filter((item) => item.id === '' + user.role_id)[0].name;
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });

  // 设备报警数量
  deviceStatusService.getWarningCount(user.park_ids ? user.park_ids.join(',') : '').then((res) => {
    if (res.success) {
      data.off_line = parseInt(res.data || 0);
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });

  // 待审批业务
  bizAuditService.getWaitAuditCount().then((res) => {
    if (res.success) {
      data.waitAuditCount = parseInt(res.data || 0);
    } else {
      ElMessage({
        message: res.message,
        type: 'error'
      });
    }
  });
});

// 退出登录
const logout = () => {
  const VITE_GLOB_MIDDLE_LOGIN_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_URL;
  const VITE_GLOB_MIDDLE_LOGIN_WANDA_URL = import.meta.env.VITE_GLOB_MIDDLE_LOGIN_WANDA_URL;
  loginService.logout().then((response) => {
    if (response.success) {
      ElMessage({
        message: response.data.message,
        type: 'success'
      });

      user.removeToken();
      menu.unloadMenu();
      tab.unloadTab();
      // const u1 = encodeURIComponent(location.origin);
      const u1 = encodeURIComponent(location.href);
      const pid = user.user_cas_pid;
      if (pid === 'parkingcm') {
        location.replace(`${VITE_GLOB_MIDDLE_LOGIN_URL}/cas/logout?pid=${pid}&redirect_uri=${u1}`);
      } else {
        location.replace(`${VITE_GLOB_MIDDLE_LOGIN_WANDA_URL}/cas/logout?pid=${pid}&redirect_uri=${u1}`);
      }
    }
  });
};

// 修改密码
const changePasswd = () => {
  data.form = {
    old_passwd: undefined,
    new_passwd: undefined,
    repeat_passwd: undefined
  };
  changePasswdDialogVisible.value = true;
};

// 提交修改密码
const submitChangePasswd = (form) => {
  form.validate().then(() => {
    employeeService.changePasswd(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message + '，正在跳转登录...',
          type: 'success'
        });
        changePasswdDialogVisible.value = false;

        user.removeToken();
        router.push('/login');
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

// 切换角色
const handleCommand = (command) => {
  if (user.$state.role_id === command) {
    return;
  }

  roleService.switchRole(command).then((res) => {
    if (res.success) {
      selectedRole.value = data.roles.filter((item) => item.id === command)[0].name;

      user.$state.role_id = command;
      user.$state.token = res.data;

      menu.unloadMenu();
      tab.unloadTab();

      router.push('/');
    }
  });
};

// 设备报警
const handleClickAlarm = () => {
  activeRouteTab({
    path: '/device/alarmHistory'
  });
};

// 点击待审批
const handleClickAudit = () => {
  activeRouteTab({
    path: '/bizAudit/BizAudit',
    query: {
      audit_states: [0]
    }
  });
};
</script>

<style lang="scss" scoped>
$frame-header-height: 52px;

.frame-header {
  width: 100%;
  height: 100%;
  background: #005bac;
  box-shadow: 0px 3px 13px 0px rgba(155, 155, 155, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: #fff;
  font-family: 'SC-Medium';

  .logo-area {
    width: 200px;
    height: $frame-header-height;
    display: flex;
    align-items: center;
    padding: 0 20px;
    .logo {
      width: 30px;
      margin-right: 40px;
    }
    .icon-warp {
      width: 30px;
      height: 30px;
      border-radius: 4px;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    .active {
      background-color: #005199;
    }
    .icon {
      width: 20px;
      cursor: pointer;
    }
  }
  .title {
    font-size: 24px;
    font-weight: 600;
  }

  .info-area {
    height: $frame-header-height;

    .user {
      font-size: 14px;
      cursor: pointer;
      display: flex;
      align-items: center;

      .username {
        display: inline-block;
      }
    }
  }
}

:deep(.el-badge__content) {
  border: none !important;
}

.el-divider--horizontal {
  margin: 8px 0;
}
.notice-item {
  &:hover {
    font-weight: bold;
  }
  width: 100%;
  text-align: center;
  cursor: pointer;
}
</style>
