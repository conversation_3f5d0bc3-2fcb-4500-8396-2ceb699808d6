import * as deviceStatusApi from '@/api/device/DeviceStatusApi';

/**
 * 设备状态
 */
export default {
  /**
   * 分页查询设备状态
   */
  pagingDeviceStatus(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.pagingDeviceStatus(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询设备的报警数量
   */
  getWarningCount(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.getWarningCount(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 开启报警
   */
  turnOnAlarm(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.turnOnAlarm(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 关闭报警
   */
  turnOffAlarm(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.turnOffAlarm(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询设备事件
   */
  pageDeviceEvent(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.pageDeviceEvent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 清除所有告警信息
   */
  clearAllEvent(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.clearAllEvent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 清除一批告警信息
   */
  clearWarnInfo(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.clearWarnInfo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 移出所有的事件
   */
  deleteAllEvent(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.deleteAllEvent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除指定的事件
   */
  deleteEvent(data) {
    return new Promise((resolve, reject) => {
      try {
        deviceStatusApi.deleteEvent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
