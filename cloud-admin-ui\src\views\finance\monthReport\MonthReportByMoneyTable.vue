<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="time" label="月份" align="center" min-width="120" fixed="left" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="200" fixed="left" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="location" label="所在地" align="center" min-width="180" />
        <el-table-column prop="should_pay_money" label="应收（元）" align="center" min-width="180" />
        <el-table-column prop="payed_money" label="实收（元）" align="center" min-width="180" />
        <el-table-column prop="etc_money" label="ETC支付（元）" align="center" min-width="180" />
        <el-table-column prop="ali_money" label="支付宝支付（元）" align="center" min-width="180" />
        <el-table-column prop="wx_money" label="微信支付（元）" align="center" min-width="180" />
        <el-table-column prop="parking_third_party_income" label="第三方会员收入（元）" align="center" min-width="180" />
        <el-table-column prop="cash_money" label="现金支付（元）" align="center" min-width="180" />
        <el-table-column prop="special_money" label="特殊处理（元）" align="center" min-width="180" />
        <el-table-column prop="debate_money" label="优免抵扣（元）" align="center" min-width="180" />
        <el-table-column prop="special_loss_money" label="特殊处理损失（元）" align="center" min-width="180" />
        <el-table-column prop="flush_loss_money" label="被冲车辆损失（元）" align="center" min-width="180" />
        <el-table-column prop="manual_money" label="手动抬杆（元）" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="MonthReportByMoneyTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import monthReportService from '@/service/finance/MonthReportService';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  monthReportService.pagingMonthReportByMoney(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
