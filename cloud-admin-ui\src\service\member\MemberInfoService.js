import * as memberInfo from '@/api/member/MemberInfoApi';

/**
 * 会员基本信息
 */
export default {
  /**
   * 会员表格数据查询
   */
  pagingMembers(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMembers(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 会员详情数据查询
   */
  getMemberById(data) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.getMemberById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 会员信息数据查询
   */
  pagingMemberMessage(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberMessage(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 缴费记录
   */
  pagingMemberPayRecords(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberPayRecords(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 免费车辆
   */
  pagingMemberWhiteCar(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberWhiteCar(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租车辆
   */
  pagingMemberRentCar(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberRentCar(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 临停车辆
   */
  pagingMemberStopCar(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberStopCar(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 已绑车辆
   */
  pagingMemberBindCar(param) {
    return new Promise((resolve, reject) => {
      try {
        memberInfo.pagingMemberBindCar(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
