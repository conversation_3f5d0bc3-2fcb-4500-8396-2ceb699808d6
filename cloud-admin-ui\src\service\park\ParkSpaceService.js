import * as parkSpace<PERSON>pi from '@/api/park/ParkSpaceApi';

/**
 * 车位管理
 */
export default {
  /**
   * 分页查询
   */
  pagingParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.pagingParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建车位
   */
  createParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.createParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改车位
   */
  updateParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.updateParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除车位
   */
  deleteParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.deleteParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 车场列表
   */
  parkList(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.parkList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 子场列表
   */
  listParkRegion(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.listParkRegion(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通过车场查询车位
   * @param {*} data
   * @returns
   */
  listParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.listParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导入
   */
  importExcel(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.importExcel(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取新增时可用的长租车位
   * @param {*} data
   * @returns
   */
  listAvailableLongRentSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.listAvailableLongRentSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取编辑时可用的长租车位
   * @param {*} data
   * @returns
   */
  listRentSpaceById(parkId, rentApplyId) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceApi.listRentSpaceById(parkId, rentApplyId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
