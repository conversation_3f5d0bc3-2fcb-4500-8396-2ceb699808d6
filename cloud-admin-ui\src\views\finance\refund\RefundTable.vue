<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="180">
          <template v-slot="scope" style="display: flex">
            <el-button link type="success" v-if="scope.row.refund_state == 1" @click="clickHandle(scope.row)">退款操作</el-button>
            <!-- <el-button link type="success" v-if="scope.row.refund_state_desc == '待退款'" @click="refundMoney(scope.row)">申请退款</el-button> -->
          </template>
        </el-table-column>
        <el-table-column prop="id" label="退款单号" align="center" width="100" />
        <el-table-column prop="park_name" label="停车场名称" align="center">
          <template v-slot="scope">
            <el-button type="text" @click="clickHandle(scope.row, 'detail')">{{ scope.row.park_name }}</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="plate_nos" label="车牌号" align="center" show-overflow-tooltip />
        <el-table-column prop="refund_money" label="退款金额" align="center" />
        <el-table-column prop="refund_type_desc" label="退款类型" align="center" width="120" />
        <el-table-column label="退款状态" align="center" width="120">
          <template v-slot="scope">
            <span :class="getClass(scope.row.refund_state)">{{ scope.row.refund_state_desc }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="apply_operator" label="申请人" align="center" width="120" />
        <el-table-column prop="created_at" label="申请时间" align="center" width="180" />
        <el-table-column prop="" label="审核人" align="center" width="120">
          <template v-slot="scope">
            <span>{{ scope.row.audit_operator || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="审核完成时间" align="center" width="180">
          <template v-slot="scope">
            <span>{{ scope.row.audit_time || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="" label="退款完成时间" align="center" width="180">
          <template v-slot="scope">
            <span>{{ scope.row.refund_time || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audit_reason" label="退款驳回原因" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!-- 临停退款详情 -->
    <el-dialog v-if="stopDetailDialogVisible" width="800px" title="临停退款详情" v-model="stopDetailDialogVisible" :before-close="handleClose">
      <el-form ref="stopDetail" label-width="110px" :model="data.stopRefundDetail">
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 订单编号">
              <span v-if="data.stopRefundDetail.order_no !== null">{{ data.stopRefundDetail.order_no }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 停车场名称">
              <span v-if="data.stopRefundDetail.park_name !== null">{{ data.stopRefundDetail.park_name }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 子场名称">
              <span v-if="data.stopRefundDetail.park_region_name !== null">{{ data.stopRefundDetail.park_region_name }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 入场时间">
              <span v-if="data.stopRefundDetail.in_time !== null">{{ data.stopRefundDetail.in_time }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 入场通道">
              <span v-if="data.stopRefundDetail.in_gateway_name !== null">{{ data.stopRefundDetail.in_gateway_name }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 出场时间">
              <span v-if="data.stopRefundDetail.out_time !== null">{{ data.stopRefundDetail.out_time }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 出场通道">
              <span v-if="data.stopRefundDetail.out_gateway_name !== null">{{ data.stopRefundDetail.out_gateway_name }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 停车时长">
              <span v-if="data.stopRefundDetail.duration !== null">{{ data.stopRefundDetail.duration }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 车牌号码">
              <span v-if="data.stopRefundDetail.plate_no !== null">{{ data.stopRefundDetail.plate_no }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 缴费金额">
              <span v-if="data.stopRefundDetail.should_pay_money !== null">{{ data.stopRefundDetail.should_pay_money }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 退款人姓名">
              <span v-if="data.stopRefundDetail.refund_user !== null">{{ data.stopRefundDetail.refund_user }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 退款人手机号">
              <span v-if="data.stopRefundDetail.card_mobile !== null">{{ data.stopRefundDetail.card_mobile }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 退款金额">
              <span v-if="data.stopRefundDetail.refund_money !== null">{{ data.stopRefundDetail.refund_money }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 退款渠道">
              <span v-if="data.stopRefundDetail.refund_channel_desc !== null">{{ data.stopRefundDetail.refund_channel_desc }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label=" 退款账号">
              <span v-if="data.stopRefundDetail.refund_account !== null">{{ data.stopRefundDetail.refund_account }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label=" 退款原因">
              <span v-if="data.stopRefundDetail.refund_reason !== null">{{ data.stopRefundDetail.refund_reason }}</span>
              <span v-else>无</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <template v-if="dialogType != 'detail'">
            <el-button v-if="data.stopRefundDetail.refund_state === 1" @click="cancelStop(stopDetail)">取消打款</el-button>
            <template v-if="data.stopRefundDetail.refund_state === 1">
              <el-button type="primary" v-if="data.stopRefundDetail.refund_channel == 7" @click="returnHandle">原路返回</el-button>
              <el-button type="primary" v-else-if="data.stopRefundDetail.refund_channel == 3" @click="returnETCHandle">ETC退款申请</el-button>
              <el-button type="primary" v-else @click="stopSubmit(stopDetail)">打款完成</el-button>
            </template>
          </template>
          <el-button @click="stopDetailDialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 长租退款详情 -->
    <el-dialog
      v-if="leaseDetailDialogVisible"
      width="800px"
      :title="dialogType === 'detail' ? '长租退款详情' : '长租退款审核'"
      v-model="leaseDetailDialogVisible"
      :before-close="handleClose"
    >
      <span class="title">长租基础信息</span>
      <el-form :model="data.leaseRefundDetail" style="padding-left: 12px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="停车场名称:">
              <span>{{ data.leaseRefundDetail.park_name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车主姓名:">
              <span>{{ data.leaseRefundDetail.mbr_member_name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车牌号码:">
              <el-tooltip :content="data.leaseRefundDetail.plate_nos || '--'" placement="top">
                <span style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis"> {{ data.leaseRefundDetail.plate_nos || '--' }}</span>
              </el-tooltip>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="手机号码:">
              <span>{{ data.leaseRefundDetail.mbr_member_mobile || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户身份:">
              <span>{{ data.leaseRefundDetail.user_identity_desc || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="车位编号:">
              <span>{{ data.leaseRefundDetail.space_code || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="规则名称:">
              <span>{{ data.leaseRefundDetail.rent_rule_name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品金额:">
              <span>{{ (data.leaseRefundDetail.order_money || '--') + '元' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品类型:">
              <span>{{ data.leaseRefundDetail.product_name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长租类型:">
              <span>{{ data.leaseRefundDetail.rent_rule_type_desc || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="产品周期:">
              <!-- <span>{{ data.leaseRefundDetail.product_range_desc || '--' }}</span> -->
              <span>
                {{ formatRentProductRangeText(rentProductRanges, data.leaseRefundDetail.product_range, data.leaseRefundDetail.product_type) }}
              </span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长租时段:">
              <span>{{ data.leaseRefundDetail.rent_time || '全时段' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长租开始时间:">
              <span>{{ data.leaseRefundDetail.valid_start_time || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="长租结束时间:">
              <span>{{ data.leaseRefundDetail.valid_end_time || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <span class="title">退款申请信息</span>
      <el-form ref="leaseRefundDetailRef" :model="data.leaseRefundDetail" :rules="leaseRefundDetailRules" style="padding-left: 12px">
        <el-row>
          <el-col :span="8">
            <el-form-item label="长租状态:">
              <span> {{ data.leaseRefundDetail.rent_state_desc || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请人:">
              <span> {{ data.leaseRefundDetail.apply_operator || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间:">
              <span> {{ data.leaseRefundDetail.created_at || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原支付金额:">
              <span>{{ (data.leaseRefundDetail.payed_money || '--') + '元' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="原支付方式:">
              <span
                >{{ data.leaseRefundDetail.pay_method_desc
                }}{{ data.leaseRefundDetail.pay_method === 4 ? '（' + data.leaseRefundDetail.pay_type_desc + '）' : '' }}</span
              >
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款金额:">
              <span>{{ (data.leaseRefundDetail.refund_money || 0) + '元' }}</span>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="(data.leaseRefundDetail.rent_state == 1 || data.leaseRefundDetail.rent_state == 2) && data.leaseRefundDetail.refund_days"
          >
            <el-form-item label="长租终止日期:">
              <span>{{ data.leaseRefundDetail.rent_end_time }}</span>
            </el-form-item>
          </el-col>
          <el-col
            :span="8"
            v-if="(data.leaseRefundDetail.rent_state == 1 || data.leaseRefundDetail.rent_state == 2) && data.leaseRefundDetail.refund_days"
          >
            <el-form-item label="退款天数:">
              <span>{{ (data.leaseRefundDetail.refund_days || 0) + '天' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="退款方式:">
              <span>{{ data.leaseRefundDetail.refund_channel_desc || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.leaseRefundDetail.pay_method != 4">
            <el-form-item label="收款银行:">
              <span>{{ data.leaseRefundDetail.card_bank || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="8" v-if="data.leaseRefundDetail.pay_method != 4">
            <el-form-item label="收款账号:">
              <span>{{ data.leaseRefundDetail.refund_account || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="16">
            <el-form-item label="退款原因:">
              <span> {{ data.leaseRefundDetail.refund_reason || '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <template v-if="dialogType != 'detail'">
          <el-row>
            <el-form-item prop="audit_result" label="审核结果:">
              <el-select v-model="data.leaseRefundDetail.audit_result" placeholder="请选择审核结果" style="width: 200px">
                <el-option label="同意退款" value="1" />
                <el-option label="驳回退款" value="0" />
              </el-select>
            </el-form-item>
          </el-row>
          <el-row v-if="data.leaseRefundDetail.audit_result == 1 && data.leaseRefundDetail.refund_channel == 8">
            <el-form-item prop="refund_finish_time" label="退款完成时间:">
              <el-date-picker
                v-model="data.leaseRefundDetail.refund_finish_time"
                type="datetime"
                value-format="YYYY-MM-DD HH:mm:ss"
                :default-time="new Date(2000, 2, 1, 23, 59, 59)"
                placeholder="请选择"
                style="width: 200px"
              />
            </el-form-item>
          </el-row>
          <el-row v-if="data.leaseRefundDetail.audit_result == 0">
            <el-form-item prop="audit_reason" label="驳回原因:">
              <el-input
                v-model="data.leaseRefundDetail.audit_reason"
                :rows="3"
                type="textarea"
                maxlength="50"
                style="width: 600px"
                placeholder="请输入驳回退款原因，限50字以内"
              />
            </el-form-item>
          </el-row>
        </template>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <!-- <template v-if="dialogType != 'detail'">
            <el-button v-if="data.leaseRefundDetail.refund_state === 1" @click="cancelLease">取消打款</el-button>
            <template v-if="data.leaseRefundDetail.refund_state === 1">
              <el-button type="primary" v-if="data.leaseRefundDetail.refund_channel == 7" @click="returnHandle">原路返回</el-button>
              <el-button type="primary" v-else @click="leaseSubmit">打款完成</el-button>
            </template>
          </template> -->
          <el-button @click="leaseDetailDialogVisible = false">取 消</el-button>
          <el-button v-if="dialogType != 'detail'" type="primary" @click="leaseSubmit">确认</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-if="refundReasonDialogVisible"
      width="500px"
      title="退款驳回原因"
      v-model="refundReasonDialogVisible"
      :close-on-click-modal="false"
      @close="closeDialog(refundForm)"
    >
      <el-form ref="refundForm" label-width="90px" :rules="data.rules" :model="data.form">
        <el-form-item prop="audit_reason" label="取消原因">
          <el-input v-model="data.form.audit_reason" placeholder="请输入取消原因" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="refundReasonDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submit(refundForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>
</template>

<script name="RefundTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import refundService from '@/service/finance/RefundService';
import { getIamTokenOpen, getOpenUrl } from '@/utils/iamFlow';
import { getToken } from '@/utils/common';
import { rentProductRanges, getRentProductRangeText } from '@/views/park/longRentRule/enums';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const id = ref('');
const refundType = ref('');
const stopDetail = ref();
const leaseDetail = ref();
const refundForm = ref();
const dialogType = ref('detail');
const refundReasonDialogVisible = ref(false);
const stopDetailDialogVisible = ref(false);
const leaseDetailDialogVisible = ref(false);
const data = reactive({
  rules: {
    audit_reason: [
      {
        required: true,
        message: '请输入取消原因',
        trigger: 'blur'
      }
    ]
  },
  form: {
    audit_reason: undefined
  },
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  stopRefundDetail: {},
  leaseRefundDetail: {}
});

const leaseRefundDetailRef = ref();
const leaseRefundDetailRules = {
  audit_result: [
    {
      required: true,
      message: '请选择审核结果',
      trigger: 'change'
    }
  ],
  refund_finish_time: [
    {
      required: true,
      message: '请选择退款完成时间',
      trigger: 'change'
    }
  ],
  audit_reason: [
    {
      required: true,
      message: '请输入驳回退款原因',
      trigger: 'blur'
    }
  ]
};

onMounted(() => {
  // getList(data.queryParams);
});
const getTitle = (row) => {
  let str = '退款操作 - ' + row.park_name;
  if (row.refund_money) {
    str += ' - 金额' + row.refund_money + '元';
  }
  if (row.refund_time) {
    str += ' - ' + row.refund_time;
  }
  return str;
};
const getClass = (status) => {
  let str = 'text_green';
  if (status == 1) {
    str = 'text_red';
  } else if (status == 2) {
    str = 'text_blue';
  }
  return str;
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  refundService.pagingRefundOrder(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 申请退款
const refundMoney = (row) => {
  if (row.refund_type_desc == '临停退款') {
    getIamTokenOpen(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/TemporaryRefund?id=${row.id}&parkToken=${getToken()}`));
  }
  if (row.refund_type_desc == '租赁退款') {
    getIamTokenOpen(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/LongRefund?id=${row.id}&parkToken=${getToken()}`));
  }
};
// 详情
const clickHandle = (row, type) => {
  id.value = row.id;
  dialogType.value = type || '';
  refundType.value = row.refund_type;

  if (row.refund_type === 1) {
    stopDetailDialogVisible.value = true;
    refundService.getRefundStopDetail(row.id).then((response) => {
      if (response.success) {
        data.stopRefundDetail = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    leaseDetailDialogVisible.value = true;
    refundService.getRefundLeaveDetail(row.id).then((response) => {
      if (response.success) {
        data.leaseRefundDetail = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  }
};

// 临停退款打款完成
const stopSubmit = () => {
  const param = {
    id: id.value
  };
  refundService.stopComplete(param).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      stopDetailDialogVisible.value = false;
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

// 长租退款同意退款和驳回退款处理
const leaseSubmit = () => {
  if (data.leaseRefundDetail.audit_result == 0) {
    leaseRefundDetailRef.value.validate().then(() => {
      const params = {
        id: id.value,
        audit_reason: data.leaseRefundDetail.audit_reason
      };
      refundService.leavingCancel(params).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          leaseDetailDialogVisible.value = false;
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    });
  } else {
    if (data.leaseRefundDetail.refund_channel == 8) {
      leaseRefundDetailRef.value.validate().then(() => {
        const param = {
          id: id.value,
          refund_finish_time: data.leaseRefundDetail?.refund_finish_time,
          refund_channel: data.leaseRefundDetail.refund_channel
        };
        refundService.leavingComplete(param).then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            leaseDetailDialogVisible.value = false;
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        });
      });
    } else {
      // 除了线下支付，其余的退款方式按原来接口处理
      leaseRefundDetailRef.value.validate().then(() => {
        let params = {
          id: id.value
        };
        refundService.yibaoCancel(params).then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            leaseDetailDialogVisible.value = false;
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        });
      });
    }
  }
};

//原路返回
const returnHandle = () => {
  let params = {
    id: id.value
  };
  refundService.yibaoCancel(params).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      stopDetailDialogVisible.value = false;
      leaseDetailDialogVisible.value = false;
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const returnETCHandle = () => {
  const param = {
    id: id.value
  };
  refundService
    .etcRefund(param)
    .then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        stopDetailDialogVisible.value = false;
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    })
    .catch((err) => {
      ElMessage({
        message: err.response.detail_message != '' ? err.response.detail_message : err.response.message,
        type: 'error'
      });
    });
};

const closeDialog = (refundForm) => {
  refundForm.resetFields();
};
// 提交取消原因并取消打款
const submit = (refundForm) => {
  if (refundType.value == 1) {
    refundForm.validate().then(() => {
      const params = {
        id: undefined,
        audit_reason: undefined
      };
      params.id = id.value;
      params.audit_reason = data.form.audit_reason;
      refundService.stopCancel(params).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          refundReasonDialogVisible.value = false;
          if (stopDetailDialogVisible.value === true) {
            stopDetailDialogVisible.value = false;
          }
          if (leaseDetailDialogVisible.value === true) {
            leaseDetailDialogVisible.value = false;
          }
          getList(data.queryParams);
          refundForm.resetFields();
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    });
  } else if (refundType.value == 2) {
    refundForm.validate().then(() => {
      const params = {
        id: undefined,
        audit_reason: undefined
      };
      params.id = id.value;
      params.audit_reason = data.form.audit_reason;
      refundService.leavingCancel(params).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          refundReasonDialogVisible.value = false;
          if (stopDetailDialogVisible.value === true) {
            stopDetailDialogVisible.value = false;
          }
          if (leaseDetailDialogVisible.value === true) {
            leaseDetailDialogVisible.value = false;
          }
          getList(data.queryParams);
          refundForm.resetFields();
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    });
  }
};

// 临停退款取消退款
const cancelStop = () => {
  refundReasonDialogVisible.value = true;
};

// 长租退款取消退款
const cancelLease = () => {
  refundReasonDialogVisible.value = true;
};

// 产品周期内容格式化
const formatRentProductRangeText = (rentProductRanges, productRange, productType) => {
  if (productType == 8) {
    return productRange + '天';
  } else if (productType == 9) {
    return productRange + '周';
  } else {
    return getRentProductRangeText(rentProductRanges, productRange) || '--';
  }
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.text_green {
  color: #67c23a;
}

.text_blue {
  color: #409eff;
}

.text_red {
  color: #f56c6c;
}

.title {
  font-size: 16px;
  display: flex;
  align-items: center;
  margin: 6px 0;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 4px;
    background-color: #409eff;
  }
}
.el-form-item {
  margin-bottom: 2px;
}
</style>
