/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 免费车分页数据
export const pagingFreeCar = (data) => {
  return $({
    url: '/console/park/white/list/pagingWhiteLists',
    method: 'post',
    data
  });
};

// 通过车场id查询车位信息查询车位编号
export const listParkSpace = (parkId) => {
  return $({
    url: '/console/park/white/list/listWhiteListSpace?parkId=' + parkId,
    method: 'get'
  });
};

// 新增白名单
export const createWhiteList = (data) => {
  return $({
    url: '/console/park/white/list/createWhiteList',
    method: 'post',
    data
  });
};
// 修改白名单
export const updateWhiteList = (data) => {
  return $({
    url: '/console/park/white/list/updateWhiteList',
    method: 'post',
    data
  });
};
// 删除白名单
export const deleteWhiteList = (id) => {
  return $({
    url: '/console/park/white/list/deleteWhiteList/' + id,
    method: 'post'
  });
};

// 批量删除白名单
export const batchDeleteWhiteList = (data) => {
  return $({
    url: '/console/park/white/list/batchDeleteWhiteList',
    method: 'post',
    data
  });
};

// 导出白名单
export const exportWhiteLists = (data) => {
  return $({
    url: '/console/park/white/list/exportWhiteLists',
    method: 'post',
    data
  });
};
//导入（弃用）
export const importExcel = (data) => {
  return $({
    url: '/console/park/space/importExcel',
    method: 'post',
    data
  });
};

//提交审核
export const submitAuditWhiteListsApply = (data) => {
  return $({
    url: '/console/park/white/list/submitAuditWhiteListsApply',
    method: 'post',
    data
  });
};

//撤销
export const cancelAuditWhiteListsApply = (data) => {
  return $({
    url: '/console/park/white/list/cancelAuditWhiteListsApply',
    method: 'post',
    data
  });
};
