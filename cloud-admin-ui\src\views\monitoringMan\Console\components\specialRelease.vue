<template>
  <el-dialog v-model="dialogVisible" title="特殊放行" width="400px" :before-close="handleSpecialPassClose"
    :close-on-click-modal="false">
    <el-form ref="specialPassFormRef" :model="specialPass.form" :rules="specialPass.rules" label-position="top">
      <el-form-item prop="money" label="减免金额(元)">
        <el-input v-model="specialPass.form.money" readonly placeholder="请输入" style="width: 100%" :min="0"
          :precision="2" />
      </el-form-item>
      <el-form-item prop="out_reason" label="放行原因">
        <el-select v-model="specialPass.form.out_reason" placeholder="请选择" style="width: 100%">
          <el-option v-for="item in out_reason_options" :key="item.value" :label="item.key" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item prop="memo" label="备注">
        <el-input type="textarea" :rows="4" v-model="specialPass.form.memo" placeholder="请输入" style="width: 100%" />
      </el-form-item>
    </el-form>
    <div style="margin-top: 30px; text-align: center">
      <el-space>
        <el-button type="primary" @click="calcSpecialPassCarFee(specialPassFormRef)">车辆计费</el-button>
        <el-button @click="resetSpecialPassCarFee(specialPassFormRef)">重 置</el-button>
      </el-space>
    </div>
  </el-dialog>
</template>
<script name="onTocars" setup>
import commonService from '@/service/common/CommonService';
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
const duty = useDuty();
const out_reason_options = ref([]);
const specialPassFormRef = ref(null);
const param = [
  {
    enum_key: 'out_reason_options',
    enum_value: 'EnumOutReason'
  }
];
commonService.findEnums('park', param).then((response) => {
  out_reason_options.value = response.data.out_reason_options;
});
const dialogVisible = ref(false);
const specialPass = reactive({
  dialogVisible: false,
  form: {
    money: undefined,
    out_reason: undefined,
    memo: ''
  },
  rules: {
    // money: [{ required: true, message: '请输入放行金额', trigger: 'blur' }],
    out_reason: [{ required: true, message: '请选择放行原因', trigger: 'change' }]
  }
});
const emits = defineEmits(['clearData']);
// 特殊放行 - 车辆计费
const calcSpecialPassCarFee = (formRef) => {
  // 重置出场类型，特殊放行
  // 重置出场类型，特殊放行
  duty.callInfo.out_type = 3;

  if (duty.callInfo.parkOrder?.car_in_biz_no == null) {
    ElMessage({
      message: '无订单信息，不允许特殊放行',
      type: 'warning'
    });
    return;
  }
  if (duty.callInfo.lastParkInRecord?.car_in_biz_no == null) {
    ElMessage({
      message: '无最近入场记录，不允许特殊放行',
      type: 'warning'
    });
    return;
  }
  // if (duty.callInfo.car_out_biz_no == null || duty.callInfo.car_out_biz_no == undefined) {
  //   ElMessage({
  //     message: '无出场流水号，不允许特殊放行',
  //     type: 'warning'
  //   });
  //   return;
  // }

  formRef.validate().then(() => {
    const param = {
      park_order_no: duty.callInfo.park_order_no,
      car_in_biz_no: duty.callInfo.car_in_biz_no,
      car_out_biz_no: duty.callInfo.car_out_biz_no,
      money: specialPass.form.money,
      out_reason: specialPass.form.out_reason,
      memo: specialPass.form.memo,
      park_id: duty.callInfo.park_id,
      plate_no: duty.callInfo.plate_no,
      gateway_id: duty.callInfo.gateway_out_id
    };

    UnattendedApi.cloudSpecialPass(param).then((res) => {
      if (res.success) {
        ElMessage({
          message: '特殊放行成功',
          type: 'success'
        });
        formRef.resetFields();
        emits('clearData', true);
        dialogVisible.value = false;

        // clearData();
      } else {
        ElMessage({
          dangerouslyUseHTMLString: true,
          message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
          type: 'error'
        });
      }
    });
  });
};

// 特殊放行 - 重置
const resetSpecialPassCarFee = (formRef) => {
  if (!formRef) return;
  formRef.resetFields();
};
onMounted(() => {
  // getList(data.queryParams);
});

defineExpose({
  dialogVisible,
  specialPass
});
</script>
<style lang="scss" scoped></style>
