<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">访客车申请</el-button>
      </el-space>
      <el-space>
        <div>
          <DownloadButton
            btnType="default"
            :exportFunc="CarVisitorService.exportVisitorApply"
            :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
            :params="data.queryParams"
          ></DownloadButton>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 324px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="240">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleCheck(scope.row)"> 查看 </el-button>
            <el-button
              link
              type="primary"
              v-if="(scope.row.audit_state === 0 || scope.row.audit_state === 2 || scope.row.audit_state === 3) && scope.row.visit_status === 1"
              @click="handleEdit(scope.row)"
            >
              修改
            </el-button>
            <el-button link type="danger" v-if="scope.row.audit_state === 0 && scope.row.visit_status === 1" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
            <el-button
              link
              type="primary"
              v-if="(scope.row.audit_state === 0 || scope.row.audit_state === 3) && scope.row.visit_status === 1"
              @click="handleAudit(scope.row.id)"
            >
              提交审核
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="申请编号" align="center" min-width="100" />
        <el-table-column prop="park_name" label="停车场名称" align="center" min-width="180" />
        <el-table-column prop="visitor_name" label="访客姓名" align="center" min-width="180" />
        <el-table-column prop="visitor_phone" label="手机号码" align="center" min-width="180" />
        <el-table-column prop="visitor_type_desc" label="访客类型" align="center" min-width="180" />
        <el-table-column prop="visitor_plate_no" label="车牌号" align="center" min-width="180" />
        <el-table-column prop="park_name" label="预约起止时间" align="center" min-width="180">
          <template v-slot="scope">
            <span>{{ scope.row.start_time + '~' + scope.row.end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="visit_time" label="到访时间" align="center" min-width="180">
          <template v-slot="scope">
            <span>{{ scope.row.visit_time || '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="audit_state_desc" label="审核状态" align="center" min-width="180" />
        <el-table-column prop="visit_status_desc" label="到访状态" align="center" min-width="180" />
        <el-table-column prop="updated_at" label="最后修改时间" align="center" min-width="180" />
        <el-table-column prop="created_at" label="申请时间" align="center" min-width="180" />
        <el-table-column prop="updator_name" label="申请人" align="center" min-width="180" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>

  <!-- 车场查找带回 -->
  <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
    <park-find-back :park_id="park_id" :park_name="park_name" :mode="flag" @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
  </el-dialog>
  <car-visitor-dialog ref="carVisitorDialogRef" @select="handleSelect" @submit="getList(data.queryParams)" />
</template>

<script name="CarVisitorServiceTable" setup>
import { reactive, ref, onActivated, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import CarVisitorService from '@/service/car/CarVisitorService';
import ParkFindBack from './ParkFindBack.vue';
import carVisitorDialog from './components/carVisitorDialog.vue';
import { getToken } from '@/utils/common';
import { getIamTokenOpen, getOpenUrl, getIamAndNormal } from '@/utils/iamFlow';
// import { useUser } from '@/stores/user';
import { dayjs } from 'element-plus';
import DownloadButton from '@/components/DownloadButton.vue';

// const user = useUser();
const parkInfoDialogVisible = ref(false);
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const carVisitorDialogRef = ref();
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  saveAuditForm: {
    name: ''
  }
});

onActivated(() => {
  getList(data.queryParams);
});
onMounted(() => {
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  CarVisitorService.pagingVisitorApply(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleCreate = () => {
  carVisitorDialogRef.value.showDialog();
};

const handleSelect = (type, childData) => {
  if (type === 'parking') {
    authCharge(true, childData);
  }
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleCheck = (val) => {
  console.log(val);
  carVisitorDialogRef.value.showDialog(val, 'check');
};

const handleEdit = (val) => {
  carVisitorDialogRef.value.showDialog(val);
};

const handleAudit = (id) => {
  if (!getIamAndNormal(getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/Longrent?id=${id}&parkToken=${getToken()}`))) {
    ElMessageBox.confirm('请确认是否提交审核？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      CarVisitorService.submitAuditVisitorApply(id)
        .then((response) => {
          if (response.data?.detailMessage) {
            ElMessage.error(response.data.detailMessage);
          } else {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }
  // 中台审批流
  // return window.open(`http://localhost/hdwaCommonBpm/hdwaCommonBpm/export/Longrent?id=${id}&parkToken=${getToken()}`, '_blank');
  // 原审批流
};

const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const params = {
      id: id
    };
    CarVisitorService.deleteVisitorApply(params)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};

const authCharge = (visible, childData) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    park_id.value = childData?.park_id;
    park_name.value = childData?.park_name;
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  carVisitorDialogRef.value.dealSelect(val, 'parking');
};

defineExpose({
  getList
});
</script>
