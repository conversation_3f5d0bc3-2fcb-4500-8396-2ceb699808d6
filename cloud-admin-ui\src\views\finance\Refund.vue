<template>
  <div class="container">
    <refund-search @form-search="searchRefundList" @reset="resetParamsAndData" />
    <refund-table ref="table" />
  </div>
</template>

<script name="Refund" setup>
import RefundSearch from './refund/RefundSearch.vue';
import RefundTable from './refund/RefundTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchRefundList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
