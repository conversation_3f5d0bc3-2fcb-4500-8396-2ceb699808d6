<template>
  <div>
    <el-dialog v-model="dialogVisible" title="历史欠费订单" width="1000px" :before-close="handleSpecialPassClose" :close-on-click-modal="false">
      <div>车牌号码：{{ duty.callInfo.plate_no }}</div>
      <div style="display: flex; align-items: center; justify-content: space-between; margin-top: 10px; margin-bottom: 10px">
        <div style="color: #f5222d">历史欠费合计：{{ allNum }}</div>
        <div>历史订单笔数：{{ tableData.length }}</div>
      </div>
      <el-table :data="tableData" v-loading="loading" border style="max-height: calc(100vh - 430px)">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <el-table-column prop="should_pay_money" label="欠缴金额" align="center" />
        <el-table-column prop="out_gateway_name" label="出场通道" align="center" />
        <el-table-column prop="event_type" label="事件类型" align="center">
          <template #default="scope">
            <span>{{ scope.row.event_type ? scope.row.event_type : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="time" label="停车时长" align="center" />
        <el-table-column prop="event_data" label="事件抓拍图" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture2(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_video" label="事件短视频" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="checkInPicture2(scope.row, 'video')">查看视频</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="event_no" label="事件编号" align="center">
          <template #default="scope">
            <span>{{ scope.row.event_no ? scope.row.event_no : '--' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="event_time" label="事件时间" align="center">
          <template #default="scope">
            <span>{{ scope.row.event_time ? scope.row.event_time : '--' }}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog v-model="dialogVisible2" :title="title" width="40%">
      <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>
<script name="onTocars" setup>
import { useDuty } from '@/stores/duty';
import { ElMessage } from 'element-plus';
import { onMounted, ref } from 'vue';

const duty = useDuty();
const tableData = ref([]);
const dialogVisible = ref(false);
const dialogVisible2 = ref(false);
const currentDate = ref('');
const lastYearFirstDay = ref('');
const lastYearToday = ref('');

// 格式化日期为 YYYY-MM-DD
const formatDate = (date) => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  return `${year}-${month}-${day}`;
};

onMounted(() => {
  const today = new Date();
  currentDate.value = formatDate(today);

  // 获取去年今天的日期
  const lastYearTodayDate = new Date(today);
  lastYearTodayDate.setFullYear(today.getFullYear() - 1);
  lastYearToday.value = formatDate(lastYearTodayDate);

  // 获取去年第一天的日期（1月1日）
  const lastYearFirstDayDate = new Date(today.getFullYear() - 1, 0, 1);
  lastYearFirstDay.value = formatDate(lastYearFirstDayDate);
  // getList();
});
const allNum = ref(0);
const getList = (data) => {
  tableData.value = data;
  data.forEach((item) => {
    allNum.value += item.should_pay_money;
  });
  allNum.value = allNum.value.toFixed(2);
};
// 事件抓拍图
const title = ref('');
const dialogImageUrl = ref('');
const checkInPicture2 = (row, type) => {
  if (type) {
    ElMessage({
      message: '设备暂不支持视频',
      type: 'warning'
    });
    return;
  }
  if (row.event_data === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible2.value = true;
    title.value = '事件抓拍图';
    dialogImageUrl.value = row.event_data;
  }
};
defineExpose({
  dialogVisible,
  getList
});
</script>
<style lang="scss" scoped></style>
