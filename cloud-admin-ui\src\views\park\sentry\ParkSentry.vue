<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-button type="primary" @click="handleCreate()">添加岗亭</el-button>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="primary" @click="authCharge(true, scope.row.id)"> 授权通道 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="岗亭名称" align="center" />
        <el-table-column prop="park_region_name" label="所属子场" align="center" />
        <el-table-column label="关联通道" align="center">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleGateway(scope.row.id)"> {{ scope.row.gate_way_count }} </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <el-dialog title="添加停车场岗亭" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
      <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
        <el-form-item prop="name" label="岗亭名称">
          <el-input v-model="data.form.name" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.form.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="createCancel(addForm)">取 消</el-button>
          <el-button type="primary" @click="createSentry(addForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog title="编辑停车场岗亭" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
      <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
        <el-form-item prop="name" label="岗亭名称">
          <el-input v-model="data.updateForm.name" maxlength="20" show-word-limit />
        </el-form-item>
        <el-form-item label="所属子场" prop="park_region_id">
          <el-select v-model="data.updateForm.park_region_id" placeholder="请选择所属子场" style="width: 100%" clearable>
            <el-option v-for="item in parkRegions" :key="item.id" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="updateCancel(editForm)">取 消</el-button>
          <el-button type="primary" @click="updateSentry(editForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <el-dialog title="查看关联通道" v-model="dialogVisible" :close-on-click-modal="false" width="800px">
      <el-table :data="gatewayList" border v-loading="loading">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" />
        <el-table-column prop="name" label="通道名称" align="center" />
        <el-table-column prop="longitude" label="经度" align="center" />
        <el-table-column prop="latitude" label="纬度" align="center" />
        <el-table-column prop="type_desc" label="通道类型" align="center" />
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 通道查找带回 -->
    <el-dialog v-if="gatewayDialogVisible" width="800px" title="选择通道" v-model="gatewayDialogVisible" :before-close="handleClose">
      <park-gateway-find-back :park_id="park_id" :sentry_id="sentry_id" @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
    </el-dialog>
  </el-card>
</template>

<script name="ParkSentry" setup>
import { onMounted, reactive, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import ParkGatewayFindBack from './ParkGatewayFindBack.vue';
import parkSentryService from '@/service/park/ParkSentryService';
import parkGatewayService from '@/service/park/ParkGatewayService';
import parkRegionService from '@/service/park/ParkRegionService';

const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const gatewayList = ref([]);
const parkRegions = ref([]);
const park_id = ref('');
const sentry_id = ref('');
const total = ref(0);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const dialogVisible = ref(false);
const gatewayDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    park_id: undefined
  },
  form: {
    park_id: undefined,
    name: undefined,
    park_region_id: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入岗亭名称',
        trigger: 'blur'
      }
    ],
    park_region_id: [
      {
        required: true,
        message: '请选择所属子场',
        trigger: 'change'
      }
    ]
  }
});

const getRegions = () => {
  parkRegions.value = [];
  parkRegionService.listParkRegion(park_id.value).then((response) => {
    parkRegions.value = response;
  });
};
const getList = (params) => {
  loading.value = true;
  if (params.park_id != undefined && params.park_id != '') {
    park_id.value = params.park_id;
  }

  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkSentryService.pagingParkSentries(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: park_id.value,
    name: undefined,
    park_region_id: undefined
  };
  getRegions();
  createDialogVisible.value = true;
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createSentry = (addForm) => {
  addForm.validate().then(() => {
    parkSentryService
      .createParkSentry(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: park_id.value,
    name: row.name,
    park_region_id: row.park_region_id
  };
  getRegions();
  updateDialogVisible.value = true;
};
const updateSentry = (editForm) => {
  editForm.validate().then(() => {
    parkSentryService
      .updateParkSentry(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkSentryService
      .deleteParkSentry(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

//通道列表
const handleGateway = (val) => {
  gatewayList.value = [];
  const data = {
    park_id: park_id.value,
    sentry_id: val
  };
  loading.value = true;
  parkGatewayService.listParkSentryGateway(data).then((response) => {
    if (response.success === true) {
      gatewayList.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
  dialogVisible.value = true;
};
//通道查找带回
const handleClose = () => {
  gatewayDialogVisible.value = false;
};
const authCharge = (visible, val) => {
  if (visible === false) {
    gatewayDialogVisible.value = false;
  } else {
    sentry_id.value = val;
    gatewayDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  const arr = [];
  val.forEach((item) => {
    arr.push(item.id);
  });
  const dataParams = {
    sentry_id: sentry_id.value,
    park_id: park_id.value,
    gateway_ids: arr
  };
  parkSentryService
    .accreditGateway(dataParams)
    .then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    })
    .catch(() => {
      getList(data.queryParams);
    });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
