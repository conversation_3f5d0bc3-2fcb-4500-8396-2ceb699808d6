<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.renew_states" style="width: 100%" placeholder="续费类型" multiple>
        <el-option v-for="item in openSignState" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.member_mobile" placeholder="手机号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.refund_states" style="width: 100%" placeholder="退款状态" multiple>
        <el-option v-for="item in refundStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.channels" style="width: 100%" placeholder="申请来源" multiple>
        <el-option v-for="item in applyList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="searchDateRange" type="datetimerange" style="width: 100%" range-separator="至"
        start-placeholder="支付开始日期" end-placeholder="支付结束日期" format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag"
      @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="LongRentPaySearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ParkFindBack from './ParkFindBack.vue';

const emits = defineEmits(['form-search']);

const route = useRoute();
const router = useRouter();
const refundStateList = ref([]);
const applyList = ref([]);
const openSignState = ref([]);
const longRentList = ref([]);
const searchDateRange = ref([dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')]);
watch(searchDateRange, (val) => {
  if (val) {
    form.queryParams.pay_start_time = val[0];
    form.queryParams.pay_end_time = val[1];
  } else {
    form.queryParams.pay_start_time = undefined;
    form.queryParams.pay_end_time = undefined;
  }
});
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: '',
    plate_no: '',
    member_mobile: '',
    renew_states: [],
    rent_states: [],
    refund_states: [],
    channels: [],
    pay_start_time: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'),
    pay_end_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    page: 1,
    limit: 30
  }
});

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);

onMounted(() => {
  initSelects();
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }

  if (undefined !== route.query.park_id) {
    form.queryParams.park_id = route.query.park_id;
    form.queryParams.park_name = route.query.park_name;
    form.queryParams.plate_no = route.query.plate_no;
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  } else if (user.park_ids !== undefined && user.park_ids.length == 1) {
    //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }

  if (user.role_id == 1) {
    return false;
  }
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'openSignState',
      enum_value: 'EnumOpenSignState'
    },
    {
      enum_key: 'refundStateList',
      enum_value: 'EnumRefundState'
    },
    {
      enum_key: 'longRentList',
      enum_value: 'EnumRentSpaceApplyRentState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    openSignState.value = response.data.openSignState;
    longRentList.value = response.data.longRentList;
  });
  commonService.findEnums('order', param).then((response) => {
    refundStateList.value = response.data.refundStateList;
  });
  commonService.findEnums('park', [{ "enum_key": "channel_type_list", "enum_value": "EnumRentChannelType" }]).then((response) => {
    applyList.value = response.data.channel_type_list;
  });
};

const handleDataSearch = () => {
  //判断是否寻找了车场
  if (form.queryParams?.park_id && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  form.queryParams = {
    park_id: undefined,
    park_name: '',
    plate_no: '',
    member_mobile: '',
    renew_states: [],
    rent_states: [],
    refund_states: [],
    pay_start_time: dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'),
    pay_end_time: dayjs().format('YYYY-MM-DD HH:mm:ss'),
    page: 1,
    limit: 30
  };
  searchDateRange.value = [dayjs().subtract(1, 'year').format('YYYY-MM-DD HH:mm:ss'), dayjs().format('YYYY-MM-DD HH:mm:ss')];
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
