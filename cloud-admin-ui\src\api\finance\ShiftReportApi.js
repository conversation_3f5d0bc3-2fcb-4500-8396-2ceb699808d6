/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询交接班报表
export const pagingShiftReport = (data) => {
  return $({
    url: '/console/park/finance/shiftHandoverRecords/pagingShiftHandoverRecords',
    method: 'post',
    data
  });
};

// 导出报表
export const exportReports = (data) => {
  return $({
    url: '/console/park/finance/shiftHandoverRecords/exportShiftHandoverRecords',
    method: 'post',
    data
  });
};
