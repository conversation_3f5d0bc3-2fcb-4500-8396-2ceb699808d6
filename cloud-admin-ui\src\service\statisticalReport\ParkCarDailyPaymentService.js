import * as parkCarDailyPaymentApi from '@/api/statisticalReport/ParkCarDailyPaymentApi';

/**
 * 临停车位日均收入
 */
export default {
  /**
   * 分页查询临停车位日均收入
   */
  pagingParkCarDailyPayment(data) {
    return new Promise((resolve, reject) => {
      try {
        parkCarDailyPaymentApi.pagingParkCarDailyPayment(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkCarDailyPaymentApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
