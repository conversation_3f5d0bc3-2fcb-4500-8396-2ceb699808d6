<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="turnround_rate_rank" label="排名" align="center" />
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="region_name" label="大区" align="center" />
          <el-table-column prop="organizational_structure" label="城市公司" align="center" />
          <el-table-column prop="province_name" label="所在省份" align="center" />
          <el-table-column prop="city_name" label="所在城市" align="center" />
          <el-table-column prop="district_name" label="所在区域" align="center" />
          <el-table-column label="车位总数" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.total_spaces }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="工作日（周一至周四）" align="center">
          <el-table-column label="全量出场车次（含长租）" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.workday_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_payed_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="全量出场车次周转率（含长租）" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_payed_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="周末（周五至周日）" align="center">
          <el-table-column label="全量出场车次（含长租）" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.weekend_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_payed_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="全量出场车次周转率（含长租）" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_payed_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="NaturalWeekTurnoverTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import naturalWeekTurnoverService from '@/service/statisticalReport/NaturalWeekTurnoverService';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

const getList = (params) => {
  loading.value = true;

  data.queryParams = params;
  naturalWeekTurnoverService.pagingNaturalWeek(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
