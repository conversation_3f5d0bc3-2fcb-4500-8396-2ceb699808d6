<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <!-- <div class="uodataClass">
      <el-tooltip>
        <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
        <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
      </el-tooltip>
      <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
    </div> -->
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 365px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="turnround_rate_rank" label="排名" align="center" />
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场ID" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.park_id }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="region_name" label="大区" align="center" />
          <el-table-column prop="organizational_structure" label="城市公司" align="center" />
          <el-table-column prop="province_name" label="所在省份" align="center" />
          <el-table-column prop="city_name" label="所在城市" align="center" />
          <el-table-column prop="district_name" label="所在区域" align="center" />
          <el-table-column label="车位总数" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.total_spaces }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="工作日（周一至周四）" align="center">
          <el-table-column label="全量出场车次（含长租）" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.workday_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_payed_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="全量出场车次周转率（含长租）" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.workday_paring_payed_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="周末（周五至周日）" align="center">
          <el-table-column label="全量出场车次（含长租）" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.weekend_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_payed_car_out_number }}</span>
            </template>
          </el-table-column>
          <el-table-column label="全量出场车次周转率（含长租）" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="总临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
          <el-table-column label="付费临停出场车次周转率" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.weekend_paring_payed_car_out_turnround_rate }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="NaturalWeekTurnoverTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import naturalWeekTurnoverService from '@/service/statisticalReport/NaturalWeekTurnoverService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(3);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  return row.statistics_date.split('-')[1] + '周';
};
const getList = (params) => {
  loading.value = true;

  data.queryParams = { ...data.queryParams, ...params };
  naturalWeekTurnoverService.pagingNaturalWeek(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      loading.value = false;
      total.value = Number(response.data.total);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
