/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 获取入场图片
export const getByCarInRecordId = (data) => {
  return $({
    url: '/console/park/fee/inRecords/getByCarInRecordId/' + data,
    method: 'get',
    data
  });
};
// 获取出场图片
export const getByCarOutRecordId = (data) => {
  return $({
    url: '/console/park/fee/outRecords/getByCarOutRecordId/' + data,
    method: 'get',
    data
  });
};
//-------------------- 特殊放行 --------------------//
// 分页查询特殊放行
export const pagingSpecialRelease = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingSpecialReleaseRecords',
    method: 'post',
    data
  });
};
// 导出特殊放行
export const exportSpecitalRelease = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportSpecialReleaseRecords',
    method: 'post',
    data
  });
};
//-------------------- 取消放行 --------------------//
// 分页查询取消放行
export const pagingCancelRelease = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingCancelReleaseRecords/',
    method: 'post',
    data
  });
};
// 导出取消放行
export const exportCancelRelease = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportCancelReleaseRecords',
    method: 'post',
    data
  });
};
//-------------------- 车牌号矫正 --------------------//
// 分页查询车牌号矫正
export const pagingPlateNoCorrect = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingCorrectRecords',
    method: 'post',
    data
  });
};
// 导出车牌号矫正
export const exportPlateNoCorrect = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportCorrectRecords',
    method: 'post',
    data
  });
};
//-------------------- 入口抬竿 --------------------//
// 分页查询入口抬竿
export const pagingEntranceRangePole = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingEntranceRecords',
    method: 'post',
    data
  });
};
// 导出入口抬竿
export const exportEntranceRangePole = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportEntranceRecords',
    method: 'post',
    data
  });
};
//-------------------- 出口抬竿 --------------------//
// 分页查询出口抬竿
export const pagingExitRangePole = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingExitRecords',
    method: 'post',
    data
  });
};
// 导出出口抬竿
export const exportExitRangePole = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportExitRecords',
    method: 'post',
    data
  });
};
//-------------------- 手动匹配出场 --------------------//
// 分页查询手动匹配
export const pagingManualMatchingExit = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingManualRecords',
    method: 'post',
    data
  });
};
// 导出手动匹配
export const exportManualMatchingExit = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportManualRecords',
    method: 'post',
    data
  });
};
//-------------------- 重复入场 --------------------//
// 分页查询重复入场
export const pagingRepeatEntrance = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingRepeatRecords',
    method: 'post',
    data
  });
};
// 导出重复入场
export const exportRepeatEntrance = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportRepeatRecords',
    method: 'post',
    data
  });
};
//-------------------- 被冲车辆 --------------------//
// 分页查询被冲车辆
export const pagingRushedCar = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingLossRecords',
    method: 'post',
    data
  });
};
// 导出被冲车辆
export const exportRushedCar = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportLossRecords',
    method: 'post',
    data
  });
};
//-------------------- 切换费率 --------------------//
// 分页查询切换费率
export const pagingChangeRate = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingRateRecords',
    method: 'post',
    data
  });
};
// 导出切换费率
export const exportChangeRate = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportRateRecords',
    method: 'post',
    data
  });
};
//-------------------- 手动补录 --------------------//
// 分页查询
export const pagingRepairInRecordRecords = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingRepairInRecordRecords',
    method: 'post',
    data
  });
};
// 导出
export const exportRepairInRecordRecords = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportRepairInRecordRecords',
    method: 'post',
    data
  });
};
//-------------------- 强制出场 --------------------//
// 分页查询强制出场
export const pagingForceExit = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/pagingForcedExitRecords',
    method: 'post',
    data
  });
};
// 导出强制出场
export const exportForceExit = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/exportForcedExitRecords',
    method: 'post',
    data
  });
};
// 统计操作类型日志数量总汇 (杨超林)
export const countAgentEventLogByType = (data) => {
  return $({
    url: '/console/park/fee/sentryBoxRecords/countAgentEventLogByType',
    method: 'post',
    data
  });
};
