<template>
  <normal-chart
    ref="barChartRef"
    :color="color"
    :legendPosition="{ top: 10 }"
    :grid="{ top: 50, bottom: 20, left: left, right: right }"
    :showYLabel="false"
  ></normal-chart>
</template>

<script setup>
import { statParkTrafficFlowsByInterval } from '@/api/home/<USER>';
import { dayjs } from 'element-plus';
import { ref } from 'vue';
import normalChart from './components/normalChart.vue';

const color = ['#1890FF', '#00DBF2'];
const barChartRef = ref(null);
const data = [
  {
    name: '临停平均时长（小时）',
    value: 0
  },
  {
    name: '长租平均时长（小时）',
    value: 0
  }
];
let xData = [''];
const leftWidth = (arr) => {
  if (arr.length == 0) {
    return;
  }
  let left = 30;
  let lengthList = arr.map((item) => {
    return String(item).length;
  });
  lengthList = lengthList.sort((a, b) => {
    return b - a;
  });
  left = lengthList[0] < 3 ? 30 : lengthList[0] * 11;
  return left;
};
const left = ref(30);
const right = ref(30);
const fetchData = async (params) => {
  const yAxis = data.map((item) => item.name);
  const { data: resData } = await statParkTrafficFlowsByInterval(params);
  console.log(resData);
  let arr = [];
  data[0].value = resData?.map((item) => item.parking_time || 0);
  arr.push(leftWidth(data[0].value));
  data[1].value = resData?.map((item) => item.rent_time || 0);
  arr.push(leftWidth(data[1].value));
  left.value = leftWidth(data[0].value);
  right.value = leftWidth(data[1].value);
  console.log(left.value, ' left.value111111');
  barChartRef.value.pLeft = left.value;
  barChartRef.value.pRight = right.value;
  xData = resData?.map((item) => {
    if (params.time_unit === 3) return dayjs(item.time).format('MM月DD日');
    else return item.time.slice(5);
  });

  barChartRef.value.setData(data, xData, yAxis);
};

defineExpose({
  fetchData
});
</script>
<style scoped lang="sass"></style>
