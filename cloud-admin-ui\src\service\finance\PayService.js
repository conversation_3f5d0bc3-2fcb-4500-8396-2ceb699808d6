import * as payApi from '@/api/finance/PayApi';

/**
 * 支付渠道
 */
export default {
  /**
   * 分页查询微信支付渠道
   */
  pageWxPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.pageWxPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增微信支付渠道
   */
  createWxPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.createWxPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改微信支付渠道
   */
  updateWxPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.updateWxPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取微信支付渠道详情
   */
  getWxPayChannelDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.getWxPayChannelDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改微信支付渠道状态
   */
  updateWxPayChannelState(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.updateWxPayChannelState(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 授权微信支付渠道到车场
   */
  authWxPayChannelPark(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.authWxPayChannelPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分页查询支付宝支付渠道
   */
  pageAliPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.pageAliPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增支付宝支付渠道
   */
  createAliPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.createAliPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改支付宝支付渠道
   */
  updateAliPayChannel(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.updateAliPayChannel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   *获取支付宝支付渠道详情
   */
  getAliPayChannelDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.getAliPayChannelDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改支付宝支付渠道状态
   */
  updateAliPayChannelState(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.updateAliPayChannelState(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 授权支付宝支付渠道到车场
   */
  authAliPayChannelPark(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.authAliPayChannelPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分页查询支付宝渠道车场
   */
  pageAliPayChannelPark(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.pageAliPayChannelPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询微信支付渠道车场
   */
  pageWxPayChannelPark(data) {
    return new Promise((resolve, reject) => {
      try {
        payApi.pageWxPayChannelPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
