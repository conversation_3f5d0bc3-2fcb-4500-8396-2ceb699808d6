/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询电子支付数据
export const pagingElectronicPaymentData = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/electronic/payment/exportByPeriod',
    method: 'post',
    data
  });
};
