/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找厂商
export const pagingFactory = (data) => {
  return $({
    url: '/console/park/device/factory/pagingDeviceFactories',
    method: 'post',
    data
  });
};

// 新建厂商
export const createFactory = (data) => {
  return $({
    url: '/console/park/device/factory/createDeviceFactory',
    method: 'post',
    data
  });
};

// 修改厂商
export const updateFactory = (data) => {
  return $({
    url: '/console/park/device/factory/updateDeviceFactory',
    method: 'post',
    data
  });
};

// 删除厂商
export const deleteFactory = (id) => {
  return $({
    url: '/console/park/device/factory/deleteDeviceFactory/' + id,
    method: 'post'
  });
};
