<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.car_types" style="width: 100%" placeholder="车辆类型" multiple>
        <el-option v-for="item in carTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.outDateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="出场开始日期"
        end-placeholder="出场结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.inDateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="入场开始日期"
        end-placeholder="入场结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarOutRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import commonService from '@/service/common/CommonService';
import { reactive, ref, onMounted } from 'vue';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';

const emits = defineEmits(['form-search']);
const carTypeList = ref([]);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: undefined,
    out_end_time: undefined,
    page: 1,
    limit: 30
  },
  inDateRange: [],
  outDateRange: []
});

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);

onMounted(() => {
  initSelects();
  form.outDateRange = [dayjs().format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59'];
  form.queryParams.out_start_time = form.outDateRange[0];
  form.queryParams.out_end_time = form.outDateRange[1];

  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    carTypeList.value = response.data.carTypeList;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.inDateRange && form.inDateRange.length > 0) {
    form.queryParams.in_start_time = form.inDateRange[0];
    form.queryParams.in_end_time = form.inDateRange[1];
  }
  if (form.inDateRange === null) {
    form.queryParams.in_start_time = undefined;
    form.queryParams.in_end_time = undefined;
  }
  if (undefined !== form.outDateRange && form.outDateRange.length > 0) {
    form.queryParams.out_start_time = form.outDateRange[0];
    form.queryParams.out_end_time = form.outDateRange[1];
  }
  if (form.outDateRange === null) {
    form.queryParams.out_start_time = undefined;
    form.queryParams.out_end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  form.inDateRange = [];
  form.outDateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: undefined,
    out_end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
