<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.car_types" style="width: 100%" placeholder="车辆类型" multiple>
        <el-option v-for="item in carTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.out_type" style="width: 100%" placeholder="出场状态">
        <el-option v-for="item in out_typeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.outDateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="出场开始日期"
        end-placeholder="出场结束日期"
        :shortcuts="shortcuts"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.inDateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="入场开始日期"
        end-placeholder="入场结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        :shortcuts="shortcuts"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
    <template #back>
      <el-button v-if="sIp" @click="back">返 回</el-button>
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarOutRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import { useUser } from '@/stores/user';
import { activeRouteTab, closeCurrentTab } from '@/utils/tabKit';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import ParkFindBack from './ParkFindBack.vue';
const route = useRoute();
const router = useRouter();
const sIp = ref(null);
const emits = defineEmits(['form-search']);
const carTypeList = ref([]);
const out_typeList = ref([
  { key: '正常离场', value: '1' },
  { key: '人工放行', value: '2' },
  { key: '特殊放行', value: '3' },
  { key: '匹配放行', value: '4' },
  { key: '冲卡离场', value: '1000' },
  { key: '折返事件', value: '5' },
  { key: '断网补录离场', value: '6' },
  { key: '跟车离场', value: '7' }
]);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: undefined,
    out_end_time: undefined,
    page: 1,
    limit: 30
  },
  inDateRange: [],
  outDateRange: []
});

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);
const shortcuts = [
  {
    text: '最近三天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  }
  // {
  //   text: '最近三个月',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
  //     return [start, end];
  //   }
  // },
  // {
  //   text: '最近一年',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
  //     return [start, end];
  //   }
  // }
];
onMounted(() => {
  sIp.value = route.query?.sIp;
  initSelects();
  form.outDateRange = [dayjs().format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59'];
  form.queryParams.out_start_time = form.outDateRange[0];
  form.queryParams.out_end_time = form.outDateRange[1];

  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (Object.keys(route.query).length !== 0 && undefined !== route.query.parkId) {
    form.queryParams.park_id = route.query.parkId;
    form.queryParams.park_name = route.query.parkName;
    form.queryParams.plate_no = route.query.plateNo;
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
    return;
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }
});
const back = () => {
  // closeCurrentTab({
  //   path: '/charge/carOutRecord'
  // });
  closeCurrentTab({
    path: '/monitoringMan/Console/Console',
    query: {
      sIp: route.query?.sIp,
      gatewayType: route.query?.gatewayType
    }
  });
};
const initSelects = () => {
  const param = [
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    carTypeList.value = response.data.carTypeList;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.inDateRange && form.inDateRange.length > 0) {
    form.queryParams.in_start_time = form.inDateRange[0];
    form.queryParams.in_end_time = form.inDateRange[1];
  }
  if (form.inDateRange === null) {
    form.queryParams.in_start_time = undefined;
    form.queryParams.in_end_time = undefined;
  }
  if (undefined !== form.outDateRange && form.outDateRange.length > 0) {
    form.queryParams.out_start_time = form.outDateRange[0];
    form.queryParams.out_end_time = form.outDateRange[1];
  }
  if (form.outDateRange === null) {
    form.queryParams.out_start_time = undefined;
    form.queryParams.out_end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  form.inDateRange = [];
  form.outDateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    in_start_time: undefined,
    in_end_time: undefined,
    out_start_time: undefined,
    out_end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
