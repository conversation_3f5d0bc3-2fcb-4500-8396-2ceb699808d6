<template>
  <el-card class="table" shadow="never">
    <!-- <div class="updataClass">
      <el-tooltip>
        <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
        <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
      </el-tooltip>
      <div>数据最近更新：{{ newData ? newData : '暂无数据' }}</div>
    </div> -->
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column label="时间周期" align="center">
          <template #default="scope">
            <span>{{ getShowTime(scope.row.statistics_date) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="park_id" label="车场ID" align="center" />
        <el-table-column prop="region" label="大区" align="center" />
        <el-table-column prop="city_branch_district" label="城市分公司" align="center" />
        <el-table-column prop="provinces_and_municipalities" label="省/市/区" align="center" />
        <el-table-column prop="type_of_event_desc" label="事件类型" align="center" />
        <el-table-column prop="total_orders" label="总订单（笔）" align="center" />
        <el-table-column prop="total_amount" label="总金额（元）" align="center" />
        <el-table-column prop="overdue_payment_orders" label="欠费订单（笔）" align="center" />
        <el-table-column prop="overdue_payment_money" label="欠费金额（元）" align="center" />
        <el-table-column prop="retroactive_orders" label="已补缴订单（笔）" align="center" />
        <el-table-column prop="retroactive_money" label="已补缴金额（元）" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="FeeEvasionOrdersTable" setup>
import { getPageFeeEvasion } from '@/api/statisticalReport/FeeEvasionOrdersApi';
// import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
// const newData = ref();
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

// onMounted(() => {
//   getNewUpdateTimeData();
// });

const getShowTime = (date) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  return '星期' + week[new Date(date).getDay()];
  // switch (data.queryParams.time_type) {
  //   case '1':
  //     break;
  //   case '2':
  //   case '6':
  //     return row.statistics_date.split('-')[1] + '月';
  //   case '3':
  //     return '星期' + week[new Date(row.statistics_date).getDay()];
  //   case '4':
  //     break;
  //   case '5':
  //     return row.statistics_date.split('-')[1] + '周';
  //   default:
  //     break;
  // }
};

// 获取最新更新时间
// const getNewUpdateTimeData = async () => {
//   try {
//     const ruData = await getNewUpdateTmie(25);
//     if (ruData.code == 200) {
//       newData.value = ruData.data.last_job_time;
//     }
//   } catch (error) {
//     console.log('获取最新更新时间失败', error);
//   }
// };

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  getPageFeeEvasion(params)
    .then((response) => {
      if (response.success === true) {
        tableData.value = response.data.rows;
        total.value = parseInt(response.data.total);
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    })
    .catch(() => {
      loading.value = false;
    });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.updataClass {
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
  padding-bottom: 10px;
}
</style>
