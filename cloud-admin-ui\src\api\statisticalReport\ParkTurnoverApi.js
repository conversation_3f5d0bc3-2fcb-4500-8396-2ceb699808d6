/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询车场周转率
export const pagingParkTurnover = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/listTurnroundRates',
    method: 'post',
    data
  });
};

// 车场周转率总平均日周转率
export const turnroundRatesCount = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/turnroundRatesCount',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/exportTurnroundRates',
    method: 'post',
    data
  });
};

// 分页查询车场月周转率
export const pagingParkMonthTurnover = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/listMonthTurnroundRates',
    method: 'post',
    data
  });
};
// 导出
export const exportMonthData = (data) => {
  return $({
    url: '/console/statistics/turnround/rate/exportMonthNatureTurnroundRates',
    method: 'post',
    data
  });
};
//查询日/月
export const findTurnroundRatesCount = (data) => {
  return $.post("/console/statistics/turnround/rate/pagingByPeriod", data)
}
//日/月导出
export const exportAll = (data) => {
  return $.post("/console/statistics/turnround/rate/exportByPeriod", data)
}
export const exportHz = (data) => {
  return $.post("/console/statistics/turnround/rate/exportSummaryByPeriod", data)
}