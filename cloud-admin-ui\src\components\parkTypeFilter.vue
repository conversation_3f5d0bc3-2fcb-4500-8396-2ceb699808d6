<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-19 14:12:39
 * @LastEditTime: 2024-03-29 09:56:15
 * @LastEditors: 达万安 段世煜
 * @Description: 车场属性选择器
 * @FilePath: \cloud-admin-ui\src\components\parkTypeFilter.vue
-->
<template>
  <el-select v-model="activeValue" placeholder="车场属性" style="width: 100%" multiple clearable @change="handleChange">
    <el-option v-for="item in options" :key="item.value" :label="item.key" :value="item.value" />
  </el-select>
</template>

<script setup>
import { onMounted, ref, watch } from 'vue';
import { findEnums } from '@/api/common/CommonApi';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});

const emits = defineEmits(['update:modelValue', 'change']);

const activeValue = ref(props.modelValue);
watch(
  () => props.modelValue,
  (val) => {
    activeValue.value = val;
  }
);
const options = ref([]);
const fetchEnum = async () => {
  const { data } = await findEnums('park', [
    {
      enum_key: 'types',
      enum_value: 'EnumParkType'
    }
  ]);
  options.value = data.types;
};
onMounted(() => {
  fetchEnum();
});

function handleChange(val) {
  activeValue.value = val;
  emits('update:modelValue', val);
  emits('change', val);
}
</script>
