<!-- eslint-disable vue/multi-word-component-names -->
<!-- 交易对账 -->
<template>
  <div class="transaction_reconciliation">
    <FormSearch @search="searchHandle" @reset="resetHandle">
      <form-search-item>
        <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
      </form-search-item>
      <form-search-item>
        <el-date-picker
          style="width: 100%"
          v-model="form.queryParams.stl_date"
          type="date"
          placeholder="交易对账日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </form-search-item>
    </FormSearch>
    <div class="table_box">
      <TransactionSummary ref="transactionRef"></TransactionSummary>
      <div class="summar">
        <Summary title="收入汇总" :loading="loading" :tableData="tableData.incomeSummary"></Summary>
        <Summary title="退款汇总" :loading="loading" :tableData="tableData.refundSummary"></Summary>
        <Summary title="优免汇总" :loading="loading" :tableData="tableData.preferentialSummary"></Summary>
        <Summary title="手续费汇总" :loading="loading" :tableData="tableData.commissionSummary"></Summary>
        <!-- <el-pagination
          background
          :current-page="pagination.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="pagination.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        /> -->
      </div>
    </div>
    <!-- 关联车场 -->
    <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
      <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
    </el-dialog>
  </div>
</template>

<script setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from '@/views/finance/refund/ParkFindBack.vue';
import Summary from './components/Summary.vue';
import TransactionSummary from './components/TransactionSummary.vue';
import { ElMessage } from 'element-plus';
import requestApi from '@/service/finance/TransactionReconciliation';

import { reactive, ref } from 'vue';

const emits = defineEmits(['form-search']);

const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    stl_date: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});

const loading = ref(false);
const tableData = reactive({
  incomeSummary: [], //收入汇总
  refundSummary: [], //退款汇总
  preferentialSummary: [], //优免汇总
  commissionSummary: [] //手续费汇总
});
const transactionRef = ref();
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const pagination = ref({
  page: 1,
  limit: 30
});
const total = ref(0);

const searchHandle = () => {
  //判断是否寻找了车场
  if (!form.queryParams.park_id) {
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  } else if (!form.queryParams.stl_date) {
    ElMessage({
      message: '请选择交易对账日期进行查询',
      type: 'warning'
    });
    return false;
  }
  transactionRef.value.init({
    park_id: form.queryParams.park_id,
    park_name: form.queryParams.park_name,
    stl_date: form.queryParams.stl_date
  });
  init();
};
const resetHandle = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

const init = () => {
  loading.value = true;
  let params = form.queryParams;
  requestApi
    .getParkTypeSummary(params)
    .then((res) => {
      console.log('res', res);
      if (res.success) {
        tableData.incomeSummary = [res.data.income_summary];
        tableData.refundSummary = [res.data.refund_summary];
        tableData.preferentialSummary = [res.data.preferential_summary];
        tableData.commissionSummary = [res.data.commission_summary];
        loading.value = false;
      } else {
        ElMessage({
          message: res.detail_message != '' ? res.detail_message : res.message,
          type: 'error'
        });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

const handleCurrentChange = (val) => {
  form.queryParams.page = val;
  init();
};
const handleSizeChange = (val) => {
  form.queryParams.limit = val;
  init();
};
</script>
<style lang="scss" scoped>
.transaction_reconciliation {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #fff;
}

.table_box {
  flex: 1;
  display: flex;
  justify-content: space-between;
  padding: 20px 10px;
  overflow-y: auto;

  & > div {
    width: calc(50% - 20px);

    ::v-deep .title {
      text-align: center;
      font-weight: bold;
      font-size: 18px;
      margin-bottom: 20px;
      color: #444;
    }
  }
}
</style>
