<template>
  <div class="container">
    <change-rate-search @form-search="searchChangeRateList" @reset="resetParamsAndData" />
    <change-rate-table ref="table" />
  </div>
</template>

<script setup name="ChangeRate">
import ChangeRateSearch from './changeRate/ChangeRateSearch.vue';
import ChangeRateTable from './changeRate/ChangeRateTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageChangeRate = (queryParams) => {
  table.value.getList(queryParams);
};

const searchChangeRateList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageChangeRate
});
</script>
