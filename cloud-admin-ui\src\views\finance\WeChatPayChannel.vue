<template>
  <div style="margin: 10px 0px">
    <we-chat-pay-channel-search @form-search="searchWeChatPayChannelList" @reset="resetParamsAndData" />
    <we-chat-pay-channel-table ref="table" />
  </div>
</template>

<script name="WeChatPayChannel" setup>
import WeChatPayChannelSearch from './weChatPayChannel/WeChatPayChannelSearch.vue';
import WeChatPayChannelTable from './weChatPayChannel/WeChatPayChannelTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchWeChatPayChannelList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
defineExpose({
  searchWeChatPayChannelList
});
</script>
