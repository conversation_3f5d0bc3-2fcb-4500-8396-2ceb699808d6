<template>
  <div>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
      <form-search-item>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          style="width: 100%"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :shortcuts="shortcuts"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />
      </form-search-item>
      <form-search-item>
        <ClearableChargeInput v-model="queryParams.org_department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
      </form-search-item>
      <form-search-item>
        <el-select v-model="queryParams.device_id" placeholder="缴费机名称" clearable>
          <el-option v-for="item in payMachines" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </form-search-item>
      <template #button>
        <export-button :export-func="exportPayMachineWithdrawRecord" :params="queryParams"></export-button>
      </template>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="index" :index="(index) => index + 1" label="序号" align="center" width="80" />
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="group_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="withdraw_time" label="时间" align="center" />
        <el-table-column prop="device_name" label="缴费机名称" align="center">
          <template #="{ row }"> {{ row.gateway_name }}-{{ row.device_name }} </template>
        </el-table-column>
        <el-table-column prop="pay_machine_balance" label="缴费机余额（元）" align="center" />
        <el-table-column prop="withdraw_amount" label="提现金额（元）" align="center" />
        <el-table-column prop="operator" label="操作人" align="center" />
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="checkDetail(scope.row)">查看流水记录</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="table-pagination"
        background
        :current-page="queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!-- 关联组织架构 -->
    <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
      <org-find-back
        :organization_id="department_id"
        :department_name="department_name"
        @orgCharge="orgCharge(false)"
        :mode="flag"
        @renderOrgTableInput="renderOrgTableInput"
      />
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onActivated, defineEmits } from 'vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { exportPayMachineWithdrawRecord } from '@/api/charge/SelfServicePaymentApi';
import exportButton from '@/components/exportButton.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
// import OrgFindBack from '@/components/OrgFindBack.vue';
import OrgFindBack from './components/OrgFindBack.vue';
import { ElMessage, dayjs } from 'element-plus';
import selfServicePaymentService from '@/service/charge/SelfServicePaymentService';

const emit = defineEmits(['change-tab']);

const queryParams = ref({
  organization_ids: undefined,
  org_department_name: undefined,
  device_id: undefined,
  start_time: undefined,
  end_time: undefined,
  page: 1,
  limit: 30
});
const dateRange = ref([dayjs().subtract(2, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')]);
const tableData = ref([]);
const payMachines = ref([]);
const loading = ref(false);
const total = ref(0);
const department_id = ref('');
const department_name = ref('');
const relatedOrgDialogVisible = ref(false);

const shortcuts = [
  {
    text: '最近三天',
    value: () => {
      return [dayjs().subtract(2, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近七天',
    value: () => {
      return [dayjs().subtract(6, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      return [dayjs().subtract(1, 'months').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    }
  }
];
onActivated(() => {
  // 数据初始化
  selfServicePaymentService.listPayMachines().then((response) => {
    if (response.success === true) {
      payMachines.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
});

const handleDataSearch = (params) => {
  if (undefined !== dateRange.value && null !== dateRange.value && dateRange.value.length > 0) {
    queryParams.value.start_time = dateRange.value[0] + ' 00:00:00';
    queryParams.value.end_time = dateRange.value[1] + ' 23:59:59';
    // 选择的时间最长只能是31天
    const start = dayjs(dateRange.value[0]);
    const end = dayjs(dateRange.value[1]);
    // 判断是否超过一年
    if (end.diff(start, 'year', true) > 1) {
      ElMessage({
        message: '起止日期不能超过一年！',
        type: 'warning'
      });
      return false;
    }
  }
  if (dateRange.value === null) {
    queryParams.value.start_time = undefined;
    queryParams.value.end_time = undefined;
  }
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  selfServicePaymentService.pagingWithdrawalRecords(queryParams.value).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
  queryParams.value = {
    organization_ids: undefined,
    org_department_name: undefined,
    device_id: undefined,
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  dateRange.value = [dayjs().subtract(2, 'day').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
  handleDataSearch();
};
const clearDepartment = () => {
  queryParams.value.organization_ids = undefined;
  queryParams.value.org_department_name = undefined;
};
// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    department_id.value = queryParams.value.organization_ids;
    department_name.value = queryParams.value.org_department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  queryParams.value.organization_ids = arrId.toString();
  queryParams.value.org_department_name = arrName.toString();
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};

const checkDetail = (row) => {
  emit('change-tab', {
    tab: 'paymentRecord',
    searchParam: {
      withdraw_record_id: row.id
    }
  });
};

defineExpose({
  handleDataSearch
});
</script>

<style lang="scss" scoped></style>
