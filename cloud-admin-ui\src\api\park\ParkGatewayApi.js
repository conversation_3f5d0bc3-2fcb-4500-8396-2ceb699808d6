/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找通道信息
export const pagingParkGateways = (data) => {
  return $({
    url: '/console/park/gateway/pagingParkGateways',
    method: 'post',
    data
  });
};

//通道列表;
export const listParkGateway = (parkRegionId) => {
  return $({
    url: '/console/park/gateway/listParkGateway/' + parkRegionId,
    method: 'get'
  });
};

// 新增通道信息
export const createParkGateway = (data) => {
  return $({
    url: '/console/park/gateway/createParkGateway',
    method: 'post',
    data
  });
};

// 通道信息修改
export const updateParkGateway = (data) => {
  return $({
    url: '/console/park/gateway/updateParkGateway',
    method: 'post',
    data
  });
};

// 删除通道
export const deleteParkGateway = (id) => {
  return $({
    url: '/console/park/gateway/deleteParkGateway/' + id,
    method: 'post'
  });
};

//通道列表-岗亭id查询;
export const listParkSentryGateway = (data) => {
  return $({
    url: '/console/park/gateway/listParkSentryGateway',
    method: 'post',
    data
  });
};

//通道列表-查找带回;
export const listGateway = (data) => {
  return $({
    url: '/console/park/gateway/listGateway',
    method: 'post',
    data
  });
};
