<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-03-18 15:06:42
 * @LastEditors: 达万安 段世煜
 * @Description: tab按钮
 * @FilePath: \cloud-admin-ui\src\views\home\group\components\tabButton.vue
-->
<template>
  <div class="tab-container">
    <div class="item" v-for="item in props.options" :key="item.value" :class="{ active: props.modelValue === item.value }" @click="handleClick(item)">
      {{ item.label }}
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  // tab数据源
  options: {
    type: Array,
    default: () => []
  },
  // 默认选中项
  modelValue: {
    type: [String, Number],
    default: ''
  }
});
// 平分宽度
const width = ref(100 / (props.options.length || 1) + '%');
const emits = defineEmits(['change', 'update:modelValue']);
const handleClick = (val) => {
  emits('update:modelValue', val.value);
  emits('change', val.value);
};
</script>

<style lang="scss" scoped>
.tab-container {
  height: 30px;
  background-color: rgba($color: #0c3773, $alpha: 0.4);
  box-sizing: border-box;
  border: 1px solid #0b73ca;
  border-right: 0px;
  width: 100%;
  display: flex;
  align-items: center;
  cursor: pointer;
  .item {
    height: 30px;
    line-height: 30px;
    border-right: 1px solid #0b73ca;
    color: #90c2ff;
    font-size: 16px;
    width: v-bind(width);
    text-align: center;
  }
  .active {
    background-color: #035ba5;
    color: #fff;
  }
}
</style>
