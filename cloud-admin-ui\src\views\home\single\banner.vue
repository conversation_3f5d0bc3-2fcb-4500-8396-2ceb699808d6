<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-04-02 19:27:15
 * @LastEditors: 达万安 段世煜
 * @Description: 停车统计
 * @FilePath: \cloud-admin-ui\src\views\home\single\banner.vue
-->
<template>
  <warp-card height="21.3vh">
    <el-carousel height="22.3vh">
      <el-carousel-item v-for="url in imageData" :key="url">
        <el-image :src="url" style="width: 100%; height: 100%" />
      </el-carousel-item>
    </el-carousel>
  </warp-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import warpCard from './components/warpCard.vue';

const imageData = ref([]);

const fetchData = () => {
  imageData.value = ['https://parkimages.huidawanan.com/template/banner1.jpg', 'https://parkimages.huidawanan.com/template/banner2.jpg'];
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
:deep(.main-container) {
  padding: 0 !important;
}
</style>
