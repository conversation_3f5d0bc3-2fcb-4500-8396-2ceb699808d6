<template>
  <div class="container">
    <quota-invoice-application-search @form-search="searchInvoiceList" @reset="resetParamsAndData" />
    <quota-invoice-application-table ref="table" />
  </div>
</template>

<script name="QuotaInvoiceApplication" setup>
import QuotaInvoiceApplicationSearch from './quotaInvoiceApplication/QuotaInvoiceApplicationSearch.vue';
import QuotaInvoiceApplicationTable from './quotaInvoiceApplication/QuotaInvoiceApplicationTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchInvoiceList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
