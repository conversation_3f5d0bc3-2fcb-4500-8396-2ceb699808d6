<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="接口名称" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.http_method" placeholder="请求方式" clearable>
        <el-option v-for="item in methods" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.permission_group_id" placeholder="权限组名称" clearable>
        <el-option v-for="item in premissionGroupList" :key="item.id" :label="item.group_name" :value="item.id" />
      </el-select>
    </form-search-item>
  </FormSearch>
</template>

<script name="ApiSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import apiService from '@/service/system/ApiManageService';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    name: '',
    http_method: '',
    permission_group_id: '',
    page: 1,
    limit: 30
  }
});
const methods = ref([]);
const premissionGroupList = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
  getPermissionGroupList();
});

const initSelects = () => {
  const param = [{ enum_key: 'methods', enum_value: 'EnumHttpMethodType' }];
  commonService.findEnums('system', param).then((response) => {
    methods.value = response.data.methods;
  });
};

const getPermissionGroupList = () => {
  apiService.permissionGroupList().then((response) => {
    premissionGroupList.value = response.data;
  });
};
const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    name: '',
    http_method: '',
    permission_group_name: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
