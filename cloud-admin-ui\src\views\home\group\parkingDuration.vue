<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-28 09:04:00
 * @LastEditors: 达万安 段世煜
 * @Description: 停车时长数据
 * @FilePath: \cloud-admin-ui\src\views\home\group\parkingDuration.vue
-->
<template>
  <warp-card title="停车平均时长数据">
    <bar-chart3D ref="barChartRef" :title="title" :color="color" :gridbottom="30" :maxLeftY="24" />
  </warp-card>
</template>

<script setup>
import { nextTick, reactive, ref } from 'vue';

import { statParkTrafficFlowsByInterval } from '@/api/home/<USER>';

import { dayjs } from 'element-plus';
import barChart3D from './components/barChart3D.vue';
import warpCard from './components/warpCard.vue';

const color = [
  ['#0A80FD', '#8CDFFE'],
  ['#E5AA46', '#FDE286']
];
const title = ref('趋势图');
const data = reactive([
  {
    name: '临停平均时长（小时）',
    value: 0
  },
  {
    name: '长租平均时长（小时）',
    value: 0
  }
]);
let xData = [''];
const barChartRef = ref(null);
const maxL = ref(null);
const fetchData = async (val) => {
  title.value = `趋势图(${dealTitle(val.start_date, val.time_unit)} ~ ${dealTitle(val.end_date, val.time_unit)})`;
  try {
    const { data: resData } = await statParkTrafficFlowsByInterval(val);
    data[0].value = resData?.map((item) => item.parking_time || 0) || [0];
    data[1].value = resData?.map((item) => item.rent_time || 0) || [0];
    let ary = data[0].value.concat(data[1].value);
    let max = ary.sort((a, b) => {
      return b - a;
    })[0];
    maxL.value = max < 1 ? max : parseInt(max) + 1 < 24 ? parseInt(max) + 1 : 24;
    xData = resData?.map((item) => item.time || 0);
  } finally {
    nextTick(() => {
      // barChartRef.value.leftYMax = maxL.value;
      barChartRef.value && barChartRef.value.setData(data, xData);
    });
  }
};
const timeFormatter = {
  3: 'YYYY年MM月DD日',
  2: 'YYYY年MM月',
  5: 'YYYY年ww周'
};
const dealTitle = (val, unit) => {
  return dayjs(val).format(timeFormatter[unit]);
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped></style>
