<template>
  <div id="softphone-bar" class="inline-block clearFix">
    <div id="phone_bar" style="display: flex; flex-wrap: nowrap !important; align-items: center" class="clearFix">
      <div class="btn-group f-l">
        <!-- <el-button id="HoldEnable" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none" onclick="holly.hold()">
          保持
        </el-button>
        <el-button id="HoldGetEnable" type="primary" size="small" class="btn btn-primary margin-r5" onclick="holly.unHold()" style="display: none">
          恢复
        </el-button>
        <el-button id="MuteEnable" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none" onclick="holly.mute()">
          静音
        </el-button>
        <el-button id="UnMuteEnable" type="primary" size="small" class="btn btn-primary margin-r5" onclick="holly.unMute()" style="display: none">
          恢复
        </el-button>
        <el-button
          id="TransferEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.openTransferOrConsultPage('softphone_transfer')"
        >
          转接
        </el-button>
        <el-button
          id="ConsultTransferEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.transfer('9123456', 'number')"
        >
          转接
        </el-button>
        <el-button
          id="ConsultEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.openTransferOrConsult('softphone_consult')"
        >
          咨询
        </el-button>
        <el-button
          id="InvestigateEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.investigate()"
        >
          邀评
        </el-button>
        <el-button id="ValidateEnable" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none" onclick="holly.validate()">
          转验证
        </el-button>
        <el-button
          id="IVRMenuEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.openToMenuPage('softphone_transfer')"
        >
          转菜单
        </el-button>
        <el-button id="ThreeWayCallEnable" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none">三方</el-button>
        <el-button
          id="ConsultThreeWayCallEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.threeWayCall('9123456')"
        >
          三方
        </el-button>
        <el-button
          id="CallVideoEnable"
          type="primary"
          size="small"
          class="btn btn-primary margin-r5"
          style="display: none"
          onclick="holly.openVideoRoom()"
        >
          视频
        </el-button>
        <el-button
          id="StopConsultEnable"
          type="primary"
          size="small"
          class="btn btn-danger margin-r5"
          style="display: none"
          onclick="holly.stopConsult()"
        >
          结束咨询
        </el-button> -->
        <el-button id="HangupEnable" type="primary" size="small" class="btn btn-danger margin-r5" onclick="holly.hangup()" style="display: none">
          挂机
        </el-button>
      </div>
      <div class="f-l" style="position: relative; display: flex; align-items: center">
        <input
          id="dialout_input"
          :value="phoneNumber"
          class="span2"
          type="text"
          placeholder="请输入电话号码"
          style="display: none"
          onkeydown="if(event.keyCode==13){holly.dialout($('#dialout_input').val(), null, 'enable');}"
        />
        <!-- <el-input
          id="dialout_input"
          v-model="phoneNumber"
          clearable
          placeholder="请输入电话号码"
          type="text"
          onkeydown="if(event.keyCode==13){holly.dialout($('#dialout_input').val(), null, 'enable');}"
        /> -->
        <el-button
          id="DialEnable"
          class="btn btn-primary margin-r5"
          type="primary"
          size="small"
          onclick="holly.dialout($('#dialout_input').val())"
          style="display: none; margin-left: 10px; margin-right: 20px !important"
        >
          呼叫
        </el-button>
      </div>
      <div class="state_group f-l clearFix" style="display: flex; align-items: center">
        <div id="softphone_phonestate" class="f-l state" style="min-width: 70px">未签入</div>
        <div id="softphone_dropdown" class="f-l state_dropdown"><b id="softphone_dropdown_caret" class="caret"></b></div>
        <div id="softphone_timer" class="f-l state_time">00:00:00</div>
        <div id="softphone_otherstate" class="customer_db"></div>
      </div>
      <div class="f-l" style="margin-top: 2px">
        <el-button id="softPhoneBarKick" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none">签出</el-button>
        <el-button id="softPhoneBarPick" type="primary" size="small" class="btn btn-primary margin-r5" style="display: none">签入</el-button>
      </div>
      <div class="f-l margin-t5" id="softWaitCountTotalDiv" style="display: none">排队数：<span id="softWaitCountTotal">0</span></div>
      <!--以下代码仅使用webrtc时，需要引入-->
      <div id="AcceptBellingEnable" style="display: none; margin-left: 5px" onclick="holly.webRtc.processWebRTCButton('accept')">
        <img src="https://a6.7x24cc.com/softPhone/img/accept.png" alt="" style="width: 22px; vertical-align: 2px; cursor: pointer" />
      </div>
      <div id="RefuseBellingEnable" style="display: none; margin-left: 5px; margin-right: 40px" onclick="holly.webRtc.processWebRTCButton('reject')">
        <img src="https://a6.7x24cc.com/softPhone/img/refuse.png" alt="" style="width: 22px; vertical-align: 2px; cursor: pointer" />
      </div>
      <div class="btn-group-dial" id="DialPlateBtnEnable" style="margin-top: 2px; display: none; margin-right: 58px">
        <el-button id="" type="primary" size="small" class="btn btn-primary" onclick="holly.webRtc.showNum()">拨号盘</el-button>
        <!--拨号盘-->
        <div class="dial-wrap" id="dial_plate" style="display: none">
          <div class="dial-phone-num">
            <span class="pushed1" onclick="holly.webRtc.processWebRTCButton('dtmf', 1)">1</span>
            <span class="pushed2" onclick="holly.webRtc.processWebRTCButton('dtmf', 2)">2</span>
            <span class="pushed3" onclick="holly.webRtc.processWebRTCButton('dtmf', 3)">3</span>
            <span class="pushed4" onclick="holly.webRtc.processWebRTCButton('dtmf', 4)">4</span>
            <span class="pushed5" onclick="holly.webRtc.processWebRTCButton('dtmf', 5)">5</span>
            <span class="pushed6" onclick="holly.webRtc.processWebRTCButton('dtmf', 6)">6</span>
            <span class="pushed7" onclick="holly.webRtc.processWebRTCButton('dtmf', 7)">7</span>
            <span class="pushed8" onclick="holly.webRtc.processWebRTCButton('dtmf', 8)">8</span>
            <span class="pushed9" onclick="holly.webRtc.processWebRTCButton('dtmf', 9)">9</span>
            <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '*')">*</span>
            <span class="pushed0" onclick="holly.webRtc.processWebRTCButton('dtmf', 0)">0</span>
            <span class="" onclick="holly.webRtc.processWebRTCButton('dtmf', '#')">#</span>
          </div>
        </div>
      </div>
      <!--webrtc结束-->
    </div>
    <div id="softphone-bar-bgdiv" class="softphone-transfer-bg-div"></div>
    <div id="softphone_consult" class="softphone-transfer-div"></div>
    <div id="softphone_transfer" class="softphone-transfer-div"></div>
    <div id="icc_event"></div>
    <!--TODO 1、默认使用class is-close-video，id="call_local_stream"通过变量判断不显示
             2、开启镜头/关闭镜头控制 id="call_local_stream" 是否显示
             3、开启镜头后 使用class is-open-video，id="call_local_stream"显示
    -->
    <div id="callVideoRoom" class="clearFix is-open-video" style="display: none">
      <div id="call_remote_stream" name="call_remote_stream" class="f-l"></div>
      <div id="call_local_stream" name="call_local_stream" class="f-r"></div>
      <div class="chat-video-room-btn">
        <div id="video-timer" class="video_time">00:00:00</div>
        <el-button type="primary" size="small" onclick="holly.trtcClient.openVideo()" class="btn btn-primary margin-r5">开启镜头</el-button>
        <el-button type="primary" size="small" onclick="holly.trtcClient.closeVideo()" class="btn btn-primary margin-r5">关闭镜头</el-button>
        <el-button type="primary" size="small" onclick="holly.trtcClient.agentFinishVideo()" class="btn btn-primary margin-r5">挂断视频</el-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDuty } from '@/stores/duty';
import { useUser } from '@/stores/user';
import { activeRouteTab } from '@/utils/tabKit';
import duration from 'dayjs/plugin/duration';
import { dayjs } from 'element-plus';
import { onMounted, ref } from 'vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const duty = useDuty();
const user = useUser();
dayjs.extend(duration);
const props = defineProps({
  loginInfo: {
    type: Function,
    default: () => {}
  }
});
const phoneNumber = ref('');

const formatDuration = (start, end, format = 'DD天 HH小时 mm分钟 ss秒') => {
  const startDay = dayjs(start);
  const endDay = dayjs(end);
  const durationMs = endDay.diff(startDay);
  // 转换为持续时间对象
  const duration = dayjs.duration(durationMs);
  // 格式化输出
  return duration.format(format);
};
onMounted(() => {
  let link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'https://a6.7x24cc.com/softPhone/stylesheets/global.css';
  document.head.appendChild(link); // 引入css到页面中
  const scripts = [
    'https://a6.7x24cc.com/softPhone/javascripts/plugins.js',
    'https://a6.7x24cc.com/softPhone/javascripts/phone.min.js'
    // 更多的 URL...
  ];
  let loadedScripts = 0;
  scripts.forEach((src) => {
    let script = document.createElement('script');
    script.src = src;
    script.async = true;
    script.onload = () => {
      loadedScripts++;
      window.hollyglobal = {
        loginSuccessCallback: function (peer) {
          // 软电话条登录成功回调函数
          // 这里写登录成功后的逻辑
          console.log('loginSuccessCallback: ', peer);
        },
        loginFailureCallback: function (peer) {
          // 软电话条登录失败回调函数
          // 这里写登录失败后的逻辑
          console.log('loginFailureCallback: ', peer);
        },
        ringEvent: function (peer) {
          // 响铃回调函数，可用于实现弹屏。参数请参考“软电话条--> 事件API--> 坐席振铃事件”
          // 这里写响铃后的逻辑
          console.log('ringEvent: ', peer);
        },
        talkingEvent: function (peer) {
          // 接通事件回调函数。参数请参考“软电话条--> 事件API--> 坐席接通事件”
          // 这里写接通后的逻辑
          console.log('talkingEvent: ', peer);
          if (peer.ChannelType == 'dialout') {
            return;
          }
          duty.callInfo = {};
          duty.callInfo = {
            ...duty.callInfo,
            CallSheetID: peer.Data.CallSheetID,
            sip_no: peer.Exten
          };
          duty.isConnected = true;
          duty.talkBeginTime = new Date().getTime();

          if (peer.Data.CallSheetID) {
            duty.eventStartTime = new Date().getTime();
            activeRouteTab({
              path: '/monitoringMan/Console/Console',
              query: {
                sIp: peer.Exten
              }
            });
          }
        },
        hangupEvent: function (peer) {
          // 挂机事件回调函数。参数请参考“软电话条--> 事件API--> 坐席挂断事件”
          // 这里写挂机后的逻辑
          console.log('hangupEvent: ', peer);
          console.log('呼叫的号码: ', peer.FromDid);
          console.log('呼叫记录id: ', peer.Data.CallSheetID);
          console.log('通话时长: ', formatDuration(peer.Data.BeginTime * 1000, peer.Data.EndTime * 1000));
          duty.peer = peer;

          // dialout: 外呼通话，
          // normal：呼入通话，
          // dialTransfer: 外呼转接通话，
          // transfer: 呼入转接，
          // inner：内部通话（坐席之间通话）
          let call_type = null;
          let call_no = null;
          if (peer.ChannelType == 'dialout' || peer.ChannelType == 'dialTransfer') {
            call_type = 1;
            call_no = peer.FromDid;
          }
          if (peer.ChannelType == 'normal' || peer.ChannelType == 'transfer') {
            call_type = 2;
            call_no = peer.FromCid;
          }
          duty.call_type = call_type;
          duty.call_no = call_no;
          let params = {
            call_id: peer.Data.CallSheetID,
            park_id: duty.callInfo.park_id,
            gateway_id: duty.isLeave ? duty.callInfo.gateway_out_id : duty.callInfo.gateway_id,
            call_type: call_type,
            call_time: parseInt(peer.Data.EndTime - peer.Data.BeginTime),
            user_id: user.user_id,
            call_no: call_no,
            plate_no: duty.callInfo.plate_no || duty.callInfo.plate_noCopy,
            car_in_biz_no: duty.callInfo.car_in_biz_no || ''
          };
          if (peer.ChannelType == 'dialout') {
            return;
          }
          // if (duty.callInfo.gateway_type == 1) {
          //   // 出口
          //   params.car_in_biz_no = duty.callInfo.car_in_biz_no;
          // }
          // if (duty.callInfo.gateway_type == 2) {
          //   // 入口
          //   params.car_in_record_id = duty.callInfo.gateway_id;
          // }
          if (duty.isConnected) {
            // UnattendedApi.recordadd(params).then((res) => {
            //   if (res.success) {
            //     duty.isConnected = false;
            //     // ElMessageBox.alert('请填写呼叫原因完成本次服务', '提示', {
            //     //   confirmButtonText: '确认',
            //     //   callback: (action) => {
            //     //     // ElMessage({
            //     //     //   type: 'info',
            //     //     //   message: `action: ${action}`
            //     //     // });
            //     //   }
            //     // });
            //   }
            // });
          }
        },
        queueWaitCountEvent: function (peer) {
          // 技能组排队数事件回调函数。参数请参考“软电话条--> 事件API--> 技能组排队数事件”
          // 这里写排队数变化后的逻辑
          console.log('queueWaitCountEvent: ', peer);
        },
        setBusyEvent: function (peer) {
          // 坐席切换状态回调
          // 这里写坐席切换状态后的逻辑
          console.log('setBusyEvent: ', peer);
        },
        queueInfo: function (peer) {
          // 技能组发生变化回调
          // 这里写技能组发生变化后的逻辑
          console.log('queueInfo: ', peer);
        },
        registered: function (peer) {
          // 电话条注册成功、通话功能可正常使用回调
          // 这里写注册成功后的逻辑
          console.log('registered: ', peer);
        },
        isDisplayInvestigate: false, // 是否开启转满意功能
        isDisplayConsult: false, // 是否开启咨询功能
        isDisplayTransfer: false, // 是否开启转接
        isDisplayValidate: true, // 是否开启转验证
        isHiddenNumber: false, // 是否开启隐藏号码功能
        listenSelfEvent: 2
      };
      if (loadedScripts === scripts.length) {
        window.holly?.loginPhoneBar(duty.callWard.login_user, duty.callWard.login_password, 'sip');
      }
    };
    document.body.appendChild(script);
  });
});
</script>

<style scoped>
#dialout_input {
  color: #000;
}

#softphone-bar .state_time {
  color: #000;
  margin-top: 0 !important;
}

#softphone-bar {
  margin: 0 10px;
}
</style>
