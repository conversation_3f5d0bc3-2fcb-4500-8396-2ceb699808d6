<template>
  <park-fee-search ref="searchRef" @form-search="searchParkFeeList" /> <!-- @reset="resetParamsAndData" -->
  <park-fee-table ref="table" />  
</template>

<script setup name="ParkFee">
import ParkFeeSearch from './parkFee/ParkFeeSearch.vue';
import ParkFeeTable from './parkFee/ParkFeeTable.vue';
import { ref } from 'vue';

const searchRef = ref(null);
const table = ref(null);

const resetDataAndGetList = () => {
  searchRef.value.handleAllReset();
};

const searchParkFeeList = (queryParams) => {
  table.value.getList(queryParams);
};
// const resetParamsAndData = (queryParams) => {
//   table.value.getList(queryParams);
// };

defineExpose({
  searchParkFeeList,
  resetDataAndGetList
});
</script>
