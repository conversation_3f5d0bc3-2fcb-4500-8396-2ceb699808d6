import * as common from '@/api/common/CommonApi';

/**
 * 公共服务
 */
export default {
  /**
   * 查找枚举
   */
  findEnums(module, param) {
    return new Promise((resolve, reject) => {
      try {
        common.findEnums(module, param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取文件流
   */
  fileDownload(path) {
    return new Promise((resolve, reject) => {
      try {
        common.fileDownload(path).then((response) => {
          console.log(1111, response);

          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
