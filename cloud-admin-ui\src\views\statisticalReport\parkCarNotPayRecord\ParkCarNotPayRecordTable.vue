<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="parkCarNotPayRecordService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="150" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="200" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="100" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <el-table-column prop="cloud_watch_desc" label="是否无人值守" align="center" min-width="100" />
        <el-table-column prop="park_type_desc" label="车场类型" align="center" min-width="180" />
        <el-table-column prop="flush_loss_money_cnt" label="被冲笔数(次)" align="center" min-width="180" />
        <el-table-column prop="flush_loss_money" label="被冲金额(元)" align="center" min-width="180" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkCarNotPayRecordTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkCarNotPayRecordService from '@/service/statisticalReport/ParkCarNotPayRecordService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkCarNotPayRecordService.pagingParkCarNotPayRecord(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
