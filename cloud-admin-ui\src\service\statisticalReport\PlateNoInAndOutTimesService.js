import * as plateNoInAndOutTimesApi from '@/api/statisticalReport/PlateNoInAndOutTimesApi';

/**
 * 车牌进出频次
 */
export default {
  /**
   * 分页查询车牌进出频次
   */
  pagingPlateNoInAndOutTimes(data) {
    return new Promise((resolve, reject) => {
      try {
        plateNoInAndOutTimesApi.pagingPlateNoInAndOutTimes(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        plateNoInAndOutTimesApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
