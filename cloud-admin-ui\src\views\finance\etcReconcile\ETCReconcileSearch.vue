<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly @click="authCharge(true, '')" placeholder="请选择停车场" />
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.queryParams.stl_month" type="month" style="width: 100%" placeholder="请选择月份" valueFormat="YYYY-MM" />
    </form-search-item>
    <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
      <park-find-back mode="search" @authCharge="authCharge(false, '')" @renderTableInput="renderTableInput" />
    </el-dialog>
  </FormSearch>
</template>

<script name="ETCReconcileSearch" setup>
import { reactive, ref, onMounted } from 'vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import { useUser } from '@/stores/user';
import { ElMessage } from 'element-plus';
import { useRouter } from 'vue-router';

const emits = defineEmits(['form-search']);
const router = useRouter();
const parkInfoDialogVisible = ref(false);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    stl_month: undefined
  },
  dateRange: []
});

onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    if (form.queryParams.stl_month) {
      const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
      emits('form-search', query);
    }
  }
});

const handleClose = () => {
  parkInfoDialogVisible.value = false;
};

const authCharge = (visible) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

const handleDataSearch = () => {
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id == 'undefined' || form.queryParams.park_id == null || form.queryParams.park_id == '') {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
  if (typeof form.queryParams.stl_month == 'undefined' || form.queryParams.stl_month == null || form.queryParams.stl_month == '') {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择月份进行查询',
      type: 'warning'
    });
    return false;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    stl_month: undefined
  };
  emits('reset', form.queryParams);
};
</script>
<style lang="scss" scoped></style>
