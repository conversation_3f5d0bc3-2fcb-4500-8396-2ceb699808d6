<template>
  <div class="container">
    <device-status-search @form-search="searchDeviceStatusList" @reset="resetParamsAndData" />
    <device-status-table ref="table" />
  </div>
</template>

<script name="DeviceStatus" setup>
import DeviceStatusSearch from './deviceStatus/DeviceStatusSearch.vue';
import DeviceStatusTable from './deviceStatus/DeviceStatusTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchDeviceStatusList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
