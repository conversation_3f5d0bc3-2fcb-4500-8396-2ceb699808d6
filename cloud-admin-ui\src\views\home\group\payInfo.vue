<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-03-11 19:13:33
 * @LastEditors: 达万安 段世煜
 * @Description: 交易金额及交易笔数统计数据
 * @FilePath: \cloud-admin-ui\src\views\home\group\payInfo.vue
-->
<template>
  <warp-card size="mini" title="交易金额及交易笔数统计数据">
    <bar-chart ref="barChartRef" :color="color" bar-gap="0" :gridbottom="30" />
  </warp-card>
</template>

<script setup>
import warpCard from './components/warpCard.vue';
import barChart from './components/barChart.vue';
import * as echarts from 'echarts';
import { ref, reactive } from 'vue';

import { fetchTransaction } from '@/api/home/<USER>';

const color = [
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: '#00A3FF'
    },
    {
      offset: 1,
      color: '#0069F2'
    }
  ]),
  new echarts.graphic.LinearGradient(0, 0, 0, 1, [
    {
      offset: 0,
      color: '#FFA500'
    },
    {
      offset: 1,
      color: '#F27B00'
    }
  ])
];

const barChartRef = ref(null);
const data = reactive([
  {
    name: '交易金额(元)',
    value: 0
  },
  {
    name: '交易笔数(笔)',
    value: 0
  }
]);
let xData = [''];

const fetchData = async (val) => {
  const yAxis = data.map((item) => item.name);
  try {
    const { data: resData } = await fetchTransaction(val);
    data[0].value = resData.pay_money_list.map((item) => item.value || 0);
    data[1].value = resData.pay_num_list.map((item) => item.value || 0);
    xData = resData.pay_money_list.map((item) => item.time || 0);
  } finally {
    barChartRef.value.setData(data, xData, yAxis);
  }
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped></style>
