<!--
 * @Description: 
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-04-02 18:10:34
-->
<template>
  <div class="frame-menu">
    <a-menu :selectedKeys="[activedMenuIndex]" mode="vertical">
      <template v-for="item in menuList" :key="item.id + '_' + item.path">
        <template v-if="!item.children || item.children.length === 0">
          <a-menu-item :key="item.id + '_' + item.path" @click="activeRouteTab({ path: item.path })">
            <template #icon>
              <el-icon size="14">
                <component :is="item.icon"></component>
              </el-icon>
            </template>
            <span class="menu-item-text">{{ item.title }}</span>
          </a-menu-item>
        </template>
        <template v-else>
          <FrameMenuItem :menu="item" :key="item.path"></FrameMenuItem>
        </template>
      </template>
    </a-menu>
  </div>
</template>

<script setup>
import FrameMenuItem from './FrameMenuItem.vue';
import { useMenu } from '@/stores/menu';
import { computed } from 'vue';
import { activeRouteTab } from '@/utils/tabKit';

const menu = useMenu();

const menuList = computed(() => {
  return menu.state.menuList;
});

const activedMenuIndex = computed({
  get() {
    return menu.state.activedMenuPath;
  },
  set(val) {
    menu.state.activedMenuPath = val;
  }
});
</script>

<style lang="scss" scoped>
.frame-menu {
  height: calc(100vh - 48px);
  width: 100%;
  background-color: #005bac;
  box-shadow: 6px 2px 8px #f0f1f2;
  z-index: 1000;
  margin-top: -4px;
  :deep(.el-aside) {
    background-color: #005bac;
  }
  :deep(.ant-menu) {
    background-color: #005bac;
    color: #fff;
  }
  :deep(.ant-menu-submenu-selected),
  :deep(.ant-menu-submenu-selected .ant-menu-submenu-arrow),
  :deep(.ant-menu-submenu-active) {
    color: #005bac;
    background-color: #fff;
  }
  :deep(.ant-menu-submenu-arrow) {
    color: #fff;
  }
}
</style>
