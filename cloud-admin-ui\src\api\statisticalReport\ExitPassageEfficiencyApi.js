/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询出口通行效率
export const pagingExitPassageEfficiency = (data) => {
  return $({
    url: '/console/statistics/park/exit/traffic/efficiency/listParkExitTrafficEfficiencies',
    method: 'post',
    data
  });
};

// 按日导出
export const exportDataByDay = (data) => {
  return $({
    url: '/console/statistics/park/exit/traffic/efficiency/exportDayParkExitTrafficEfficiencies',
    method: 'post',
    data
  });
};

// 汇总导出
export const exportDataByGather = (data) => {
  return $({
    url: '/console/statistics/park/exit/traffic/efficiency/exportParkExitTrafficEfficienciesCollect',
    method: 'post',
    data
  });
};

// 汇总统计
export const parkExitTrafficEfficienciesCount = (data) => {
  return $({
    url: '/console/statistics/park/exit/traffic/efficiency/parkExitTrafficEfficienciesCount',
    method: 'post',
    data
  });
};
