{"name": "@types/svgo", "version": "2.6.4", "description": "TypeScript definitions for svgo", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/svgo", "license": "MIT", "contributors": [{"name": "Bradley Ayers", "url": "https://github.com/bradleyayers", "githubUsername": "bradleyayers"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/giladgray", "githubUsername": "giladgray"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/Aankhen", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/jankarres", "githubUsername": "jan<PERSON><PERSON>"}, {"name": "<PERSON>", "url": "https://github.com/gavingregory", "githubUsername": "gaving<PERSON>gory"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"name": "Re<PERSON><PERSON>", "url": "https://github.com/remcohaszing", "githubUsername": "rem<PERSON><PERSON><PERSON>"}, {"name": "<PERSON><PERSON>", "url": "https://github.com/petrzjunior", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/svgo"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "55fb26251b2398188e7540523cbb0d793942150bcc32da79955b654b60dbe21b", "typeScriptVersion": "4.0"}