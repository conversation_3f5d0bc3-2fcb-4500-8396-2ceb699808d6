import * as appletInfoManageApi from '@/api/park/AppletInfoManageApi';

/**
 * 车场-小程序信息管理
 */
export default {
  /**
   * 获取小程序公告信息
   */
  getParkMiniManageInfo(data) {
    return new Promise((resolve, reject) => {
      try {
        appletInfoManageApi.getParkMiniManageInfo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 保存小程序公告信息
   */
  updateParkMiniManageInfo(data) {
    return new Promise((resolve, reject) => {
      try {
        appletInfoManageApi.updateParkMiniManageInfo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
