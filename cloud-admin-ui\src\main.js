/*
 * @Description:
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:40
 * @LastEditTime: 2024-01-12 11:49:20
 */
import { createApp } from 'vue';
import ElementPlus, { dayjs } from 'element-plus';
import 'element-plus/dist/index.css';
import * as ElementPlusIconsVue from '@element-plus/icons-vue';
import App from './App.vue';
import router from './router';
import pinia from '@/stores';
import '@/styles/index.scss';
import permission from '@/utils/permissionKit';
// import lodash from 'lodash';
import 'dayjs/locale/zh-cn';
import 'default-passive-events';
import { Menu } from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';

// eslint-disable-next-line no-unused-vars
// import mock from '@/mock';

dayjs.locale('zh-cn');

const app = createApp(App);
app.use(ElementPlus);
app.use(Menu);
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component);
}

app.use(router);
app.use(pinia);

app.config.globalProperties.$_has = permission;
// app.config.globalProperties.lodash = lodash;

app.mount('#app');
