<template>
  <div class="container">
    <member-info-search @form-search="searchMemberInfoList" @reset="resetParamsAndData" />
    <member-info-table ref="table" />
  </div>
</template>

<script name="MemberInfo" setup>
import MemberInfoSearch from './memberInfo/MemberInfoSearch.vue';
import MemberInfoTable from './memberInfo/MemberInfoTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchMemberInfoList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
