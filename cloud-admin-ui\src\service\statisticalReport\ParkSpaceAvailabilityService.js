import * as parkSpaceAvailabilityApi from '@/api/statisticalReport/ParkSpaceAvailabilityApi';

/**
 * 车位利用率
 */
export default {
  /**
   * 分页查询车位利用率
   */
  pagingParkSpaceAvailability(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceAvailabilityApi.pagingParkSpaceAvailability(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkSpaceAvailabilityApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
