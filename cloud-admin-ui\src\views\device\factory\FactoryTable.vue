<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加厂商</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="厂商名称" align="center" />
        <el-table-column prop="code" label="厂商编码" align="center" />
        <el-table-column prop="contact_name" label="联系人姓名" align="center" />
        <el-table-column prop="contact_mobile" label="联系人手机号" align="center" />
        <el-table-column prop="address" label="厂商详细地址" align="center" />
        <el-table-column prop="memo" label="厂商简介" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog
        title="添加厂商信息"
        v-model="factoryCreateDialogVisible"
        :close-on-click-modal="false"
        @close="closeAddDialog(addForm)"
        width="800px"
      >
        <el-form ref="addForm" label-width="110px" :rules="data.rules" :model="data.form">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="name" label="厂商名称">
                <el-input v-model="data.form.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="code" label="厂商编码">
                <el-input v-model="data.form.code" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="contact_name" label="联系人姓名">
                <el-input v-model="data.form.contact_name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.form.contact_mobile" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.form.address" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item prop="memo" label="厂商简介">
            <el-input v-model="data.form.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="factoryCreateDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="createFactory(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="修改厂商信息"
        v-model="factoryUpdateDialogVisible"
        :close-on-click-modal="false"
        @close="closeEditDialog(editForm)"
        width="800px"
      >
        <el-form ref="editForm" label-width="110px" :rules="data.rules" :model="data.updateForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="name" label="厂商名称">
                <el-input v-model="data.updateForm.name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="code" label="厂商编码">
                <el-input v-model="data.updateForm.code" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="contact_name" label="联系人姓名">
                <el-input v-model="data.updateForm.contact_name" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="contact_mobile" label="联系人手机号">
                <el-input v-model="data.updateForm.contact_mobile" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="24">
              <el-form-item prop="address" label="详细地址">
                <el-input v-model="data.updateForm.address" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-form-item prop="memo" label="厂商简介">
            <el-input v-model="data.updateForm.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="factoryUpdateDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="updateFactory(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="FactoryTable" setup>
import { reactive, ref, onMounted, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import factoryService from '@/service/device/FactoryService';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const factoryCreateDialogVisible = ref(false);
const factoryUpdateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    name: '',
    contact_name: '',
    contact_mobile: '',
    page: 1,
    limit: 30
  },
  form: {
    name: undefined,
    code: undefined,
    contact_name: undefined,
    contact_mobile: undefined,
    address: undefined,
    memo: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入厂商名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入厂商编码',
        trigger: 'blur'
      }
    ],
    contact_name: [
      {
        required: true,
        message: '请输入联系人姓名',
        trigger: 'blur'
      }
    ],
    contact_mobile: [
      {
        required: true,
        message: '请输入联系人手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    address: [
      {
        required: true,
        message: '请输入详细地址',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
  status.value = true;
});
// 分页查询厂商列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  factoryService.pagingFactory(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 新建厂商
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form = {
    name: undefined,
    code: undefined,
    contact_name: undefined,
    contact_mobile: undefined,
    address: undefined,
    memo: undefined
  };
  factoryCreateDialogVisible.value = true;
  status.value = false;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建厂商
const createFactory = (addForm) => {
  addForm.validate().then(() => {
    factoryService
      .createFactory(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          factoryCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除厂商
const handleDelete = (val) => {
  batchDelete(val);
};
const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    factoryService
      .deleteFactory(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 修改厂商
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    name: row.name,
    code: row.code,
    contact_name: row.contact_name,
    contact_mobile: row.contact_mobile,
    address: row.address,
    memo: row.memo
  };
  factoryUpdateDialogVisible.value = true;
};
// 提交并保存修改厂商
const updateFactory = (editForm) => {
  editForm.validate().then(() => {
    factoryService
      .updateFactory(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          factoryUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
