<template>
  <el-card class="table table-warp" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()"> 添加角色 </el-button>
        <el-button type="danger" @click="batchDelete()"> 批量删除 </el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="280">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row.id)"> 修改 </el-button>
            <el-button link type="info" @click="handlePression(scope.row)"> 权限管理 </el-button>
            <el-button link type="info" @click="handleApiPression(scope.row)"> Api权限管理 </el-button>
            <el-button link v-if="scope.row.enabled == 0" type="primary" @click="enable(scope.row)"> 启用 </el-button>
            <el-button link v-if="scope.row.enabled == 1" type="danger" @click="disable(scope.row)"> 禁用 </el-button>
            <el-button link type="danger" v-if="scope.row.enabled == 0" @click="deleteRole(scope.row)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="角色名称" align="center" />
        <el-table-column prop="code" label="角色编码" align="center" />
        <el-table-column prop="type_display" label="角色类型" align="center" />
        <el-table-column prop="enabled" label="启用状态" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.enabled == 1">已启用</span>
            <span v-if="scope.row.enabled == 0">已禁用</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加角色" v-model="roleCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" :model="data.form" label-width="100px" :rules="data.rules">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="data.form.name" placeholder="请输入角色名称" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item label="角色编码" prop="code">
            <el-input v-model="data.form.code" placeholder="请输入角色编码" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item prop="type" label="角色类型">
            <el-select v-model="data.form.type" style="width: 100%">
              <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="enabled" label="启用状态">
            <el-switch v-model="data.form.enabled" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)"> 取 消 </el-button>
            <el-button type="primary" @click="createRole(addForm)"> 确 定 </el-button>
          </span>
        </template>
      </el-dialog>

      <el-dialog title="修改角色" v-model="roleUpdateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" :model="data.updateForm" label-width="80px" :rules="data.rules">
          <el-form-item label="角色名称" prop="name">
            <el-input v-model="data.updateForm.name" placeholder="请输入角色名称" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item label="角色编码" prop="code">
            <el-input v-model="data.updateForm.code" placeholder="请输入角色编码" show-word-limit maxlength="20" />
          </el-form-item>
          <el-form-item prop="type" label="角色类型">
            <el-select v-model="data.updateForm.type" style="width: 100%">
              <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="enabled" label="启用状态">
            <el-switch v-model="data.updateForm.enabled" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)"> 取 消 </el-button>
            <el-button type="primary" @click="updateRole(editForm)"> 确 定 </el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 权限配置 -->
      <el-dialog title="权限配置" v-model="configPermissionsDialogVisible" width="800px" :close-on-click-modal="false">
        <el-row :gutter="10">
          <el-col :span="6">
            <el-card shadow="never" class="card-box" :body-style="{ height: '540px', overflow: 'auto' }">
              <template #header>
                <div style="display: inline-block; line-height: 32px">菜单树</div>
              </template>
              <div>
                <el-tree
                  ref="permissionTree"
                  :data="permissionTreeData"
                  :props="data.defaultProps"
                  node-key="id"
                  :default-expand-all="true"
                  :default-checked-keys="treeSelectList"
                  show-checkbox
                />
              </div>
            </el-card>
          </el-col>
          <el-col :span="18">
            <el-card shadow="never" class="card-box">
              <template #header>
                <div style="display: inline-block; line-height: 32px">关联权限</div>
              </template>
            </el-card>
          </el-col>
        </el-row>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="configPermissionsDialogVisible = false"> 取 消 </el-button>
            <el-button type="primary" @click="save"> 确 定 </el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>

  <!-- Api权限配置 -->
  <el-dialog title="Api权限配置" v-model="configApiPermissionsDialogVisible" width="800px" :close-on-click-modal="false">
    <el-row :gutter="10">
      <el-col :span="10">
        <el-card shadow="never" class="card-box" :body-style="{ height: '550px', overflow: 'auto' }">
          <template #header>
            <div style="display: inline-block; line-height: 32px">Api权限树</div>
          </template>
          <div>
            <el-tree
              ref="permissionApiTree"
              :data="permissionApiTreeData"
              :props="data.defaultApiProps"
              node-key="id"
              :default-expand-all="true"
              @node-click="checkApiClick"
            />
          </div>
        </el-card>
      </el-col>
      <el-col :span="14">
        <el-card shadow="never" class="card-box" :body-style="{ height: '550px', overflow: 'auto' }">
          <template #header>
            <div style="display: inline-block; line-height: 32px">关联权限</div>
            <el-checkbox
              v-model="data.checked"
              true-label="1"
              false-label="0"
              @change="checkAllOrCancel(!isAllChecked)"
              size="large"
              style="display: inline; line-height: 32px; float: right"
              >全 选
            </el-checkbox>
          </template>
          <div v-for="(item, index) in checkApiList" :key="index" :label="item.name">
            <el-checkbox :key="index" v-model="item.mychecked" style="white-space: nowrap" @change="onChangeApi(item.id, $event)"> </el-checkbox>
            <span style="position: relative; top: -2px">&nbsp;{{ item.name }}</span>
          </div>
        </el-card>
      </el-col>
    </el-row>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="configApiPermissionsDialogVisible = false"> 取 消 </el-button>
        <el-button type="primary" @click="configApiPermissions"> 确 定 </el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup name="RoleTable">
import roleService from '@/service/system/RoleService';
import apiService from '@/service/system/ApiManageService';
import commonService from '@/service/common/CommonService';
import { reactive, onActivated, ref, getCurrentInstance, computed, watch } from 'vue';
import { ElMessage, ElMessageBox, ElTree } from 'element-plus';

const addForm = ref();
const editForm = ref();
const permissionTree = ref(ElTree);
const { proxy } = getCurrentInstance();
const roleId = ref('');
const tableData = ref([]);
const loading = ref(false);
const types = ref([]);
const permissionApiList = ref([]);
const roleIds = ref([]);
const checkList = ref([]);
const checkApiList = ref([]);
const treeSelectList = ref([]);
const treeSelectApiList = ref([]);
const permissionApiTreeData = ref([]);
const selectRowSavelist = ref([]);
const permissionTreeData = ref([]);
const total = ref(0);
const roleCreateDialogVisible = ref(false);
const roleUpdateDialogVisible = ref(false);
const configPermissionsDialogVisible = ref(false);
watch(configPermissionsDialogVisible, (val) => {
  if (!val) {
    permissionTreeData.value = [];
    treeSelectList.value = [];
  }
});

const configApiPermissionsDialogVisible = ref(false);
const data = reactive({
  checked: false,
  treeParam: {
    id: undefined
  },
  defaultProps: {
    children: 'children',
    label: 'name'
  },
  defaultApiProps: {
    children: 'children',
    label: 'group_name'
  },
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    name: undefined,
    code: undefined,
    type: undefined,
    enable: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入角色名称',
        trigger: 'blur'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入角色编码',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择所在部门',
        trigger: 'blur'
      }
    ],
    enabled: [
      {
        required: true,
        message: '请选择是否启用',
        trigger: 'blur'
      }
    ]
  }
});

const isAllChecked = computed(() => {
  let len = checkApiList.value.length;
  let total = 0;
  checkApiList.value.forEach((item) => {
    if (item.mychecked) {
      total++;
    }
  });
  return len === total && total !== 0;
});

watch(isAllChecked, (val) => {
  data.checked = val;
});

const checkAllOrCancel = (check) => {
  // console.log('check', check);
  if (check) {
    checkApiList.value.forEach((item) => {
      item.mychecked = true;
      if (!permissionApiList.value.includes(item.id)) {
        permissionApiList.value.push(item.id);
      }
    });
  } else {
    checkApiList.value.forEach((item) => {
      item.mychecked = false;
      if (permissionApiList.value.includes(item.id)) {
        permissionApiList.value.splice(permissionApiList.value.indexOf(item.id), 1);
      }
    });
  }
};

onActivated(() => {
  // 数据初始化
  getList(data.queryParams);
  treeSelectList.value = [];
  checkList.value = [];
  checkApiList.value = [];
  treeSelectApiList.value = [];
  permissionApiList.value = [];
  permissionApiTreeData.value = [];
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'types', enum_value: 'EnumRoleType' }];
  commonService.findEnums('system', param).then((response) => {
    types.value = response.data.types;
  });
};

// 点击树节点触发事件
// const checkChange = (data, val) => {
//   const dataArr = data.children;
//   const arr = val.checkedKeys;
//   console.log(arr);
//   console.log(data);
//   if (dataArr != null) {
//     arr.forEach(function (value) {
//       let result = dataArr.some((ele) => ele.id === value); //true
//       if (!result) {
//         arr.splice(arr.indexOf(value), 1);
//         //do something...
//       }
//     });
//   }
//   selectRowSavelist.value = arr;
// };
// 获取菜单树并获取关联的资源权限
const findMenuTree = (id) => {
  data.treeParam.id = id;
  roleService.findMenuTree(data.treeParam).then((response) => {
    permissionTreeData.value = response.roleMenuTreeList;
    if (permissionTreeData.value.length > 0) {
      treeSelectList.value = response.selectNodelist;
    }
  });
};
// 权限管理
const handlePression = (row) => {
  findMenuTree(row.id);
  roleId.value = row.id;
  configPermissionsDialogVisible.value = true;
};

// Api权限管理
const handleApiPression = (row) => {
  roleId.value = row.id;
  getPremissionGroupList();
  getRoleApiPermissionTree(row.id);
  configApiPermissionsDialogVisible.value = true;
};
// 获取菜单树并获取关联的资源权限
const getApiPermissionByGroupId = (id) => {
  checkApiList.value = [];
  apiService.getApiPermissionByGroupId(id).then((response) => {
    if (response.success === true) {
      checkApiList.value = response.data;
      checkApiList.value.forEach((item) => {
        if (permissionApiList.value.includes(item.id)) {
          item.mychecked = true;
        } else {
          item.mychecked = false;
        }
      });
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const checkApiClick = (data) => {
  getApiPermissionByGroupId(data.id);
};
const onChangeApi = (apiId, e) => {
  if (e === true) {
    permissionApiList.value.push(apiId);
  } else {
    permissionApiList.value.splice(permissionApiList.value.indexOf(apiId), 1);
  }
};
// 保存菜单权限关联
const save = () => {
  let halfCheckedKeys = proxy.$refs.permissionTree.getHalfCheckedKeys();
  let checkedKeys = proxy.$refs.permissionTree.getCheckedKeys();
  let param = {
    role_id: roleId.value,
    select_node_list: halfCheckedKeys.concat(checkedKeys)
  };
  roleService.configPermissions(param).then((response) => {
    if (response.success) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      configPermissionsDialogVisible.value = false;
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.message,
        type: 'warning'
      });
    }
  });
};

//全选按钮
// const AllChecked = (val) => {
//   if (val == 1) {
//     permissionApiList.value = [];
//   }
// };

//保存api权限
const configApiPermissions = () => {
  let param = {};
  const newArr = [];
  permissionApiList.value.forEach((item) => {
    if (!newArr.includes(item)) {
      newArr.push(item);
    }
  });
  param.role_id = roleId.value;
  param.permission_list = newArr;
  roleService.configApiPermissions(param).then((response) => {
    if (response.success) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      getList(data.queryParams);
      configApiPermissionsDialogVisible.value = false;
    } else {
      ElMessage({
        message: response.message,
        type: 'warning'
      });
    }
  });
};
const handleCreate = () => {
  data.form = {
    name: undefined,
    code: undefined,
    enabled: '1',
    type: undefined
  };
  roleCreateDialogVisible.value = true;
};
// 启用
const enable = (row) => {
  ElMessageBox.confirm('是否启用该角色？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    roleService.enableRole(row.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        getList(data.queryParams);
      }
    });
  });
};
// 禁用
const disable = (row) => {
  ElMessageBox.confirm('是否禁用该角色？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    roleService.disableRole(row.id).then((response) => {
      if (response.success) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        getList(data.queryParams);
      }
    });
  });
};
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  roleService.pagingRoles(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createRole = (addForm) => {
  addForm.validate().then(() => {
    roleService.createRole(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        addForm.resetFields();
        getList(data.queryParams);
        roleCreateDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
const batchDelete = () => {
  if (roleIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的角色',
      type: 'warning'
    });
  } else {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const pushIds = [];
      for (let i = 0; i < roleIds.value.length; i++) {
        pushIds.push(parseInt(roleIds.value[i].id));
      }
      data.form.id = pushIds;
      roleService
        .deleteRoles(data.form)
        .then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }
};
const handleSelectionChange = (val) => {
  roleIds.value = val;
};
const deleteRole = (val) => {
  roleIds.value[0] = val;
  batchDelete();
};
const handleEdit = (id) => {
  roleService.getRoleById(id).then((response) => {
    if (response.success === true) {
      data.updateForm = {
        id: response.data.id,
        name: response.data.name,
        code: response.data.code,
        type: response.data.type,
        enabled: response.data.enabled
      };
      roleUpdateDialogVisible.value = true;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const updateRole = (editForm) => {
  editForm.validate().then(() => {
    roleService
      .updateRole(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          roleUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const getPremissionGroupList = () => {
  permissionApiTreeData.value = [];
  apiService.permissionGroupList().then((response) => {
    permissionApiTreeData.value = response.data;
  });
};
const getRoleApiPermissionTree = (roleId) => {
  checkApiList.value = [];
  permissionApiList.value = [];
  apiService.getRoleApiPermissionTree(roleId).then((response) => {
    checkApiList.value = response;
    response.forEach(function (element) {
      permissionApiList.value.push(element.id);
    });
  });
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  roleCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  roleUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.card-box {
  height: 600px;
  border-radius: 4px;
  border: 1px solid #ebeef5;
}
</style>
