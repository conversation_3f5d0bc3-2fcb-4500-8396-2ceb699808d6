<template>
  <div class="container">
    <park-space-search @form-search="searchParkSpaceList" @reset="resetParamsAndData" />
    <park-space-table ref="table" />
  </div>
</template>

<script setup name="ParkSpace">
import ParkSpaceSearch from './parkSpace/ParkSpaceSearch.vue';
import ParkSpaceTable from './parkSpace/ParkSpaceTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkSpaceList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
