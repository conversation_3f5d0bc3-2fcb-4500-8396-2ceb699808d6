<template>
  <el-dialog v-model="dialogVisible" title="车场开放功能详情" width="400px" :before-close="handleSpecialPassClose" :close-on-click-modal="false">
    <div>
      <div style="display: flex; margin-top: 10px; margin-bottom: 10px" v-for="(item, i) in detailData" :key="i">
        <div style="width: 200px; text-align: right">{{ item.label }}：</div>
        <div>{{ item.type }}</div>
      </div>
    </div>
  </el-dialog>
</template>
<script name="onTocars" setup>
import { useDuty } from '@/stores/duty';
import { onMounted, reactive, ref } from 'vue';
const duty = useDuty();
const tableData = ref([]);
const dialogVisible = ref(false);
const detailData = reactive([
  { label: '电子发票开通状态', type: '已开通' },
  { label: '免费时长(分钟)', type: '15' }
]);
onMounted(() => {
  // getList(data.queryParams);
});

defineExpose({
  dialogVisible
});
</script>
<style lang="scss" scoped></style>
