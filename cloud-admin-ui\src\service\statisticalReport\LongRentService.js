import * as LongRent<PERSON><PERSON> from '@/api/statisticalReport/LongRentApi';

/**
 * 长租收入全览
 */
export default {
  /**
   * 分页查询长租收入全览
   */
  pagingLongRent(data) {
    return new Promise((resolve, reject) => {
      try {
        LongRentApi.pagingLongRent(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        LongRentApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
