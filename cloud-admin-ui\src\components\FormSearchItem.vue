<!--
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:21
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\components\FormSearchItem.vue
 * @Description: {}
-->
<template>
  <el-col :span="span" style="margin-top: 10px" v-show="visible">
    <slot></slot>
  </el-col>
</template>

<script>
import { $emit } from 'vue-happy-bus';

export default {
  props: ['spanValue'],
  data() {
    return {
      span: this.spanValue || 6,
      visible: !this.folded
    };
  },
  created() {
    const item = {
      show: this.show,
      hide: this.hide
    };
    $emit('addItem', item);
  },
  methods: {
    show() {
      this.visible = true;
    },
    hide() {
      this.visible = false;
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-select) {
  width: 100%;
}
</style>
