<template>
  <div class="box" v-for="(item, i) in tableData" :key="i">
    <div class="title">
      <div class="tit-l">{{ item.park_name }}</div>
      <div v-if="item.apply_begin_tim && tabIndex == 0" class="tit-r">
        值守时间段：{{ item.apply_begin_time || '~' }}至{{ item.apply_end_time || '~' }}
      </div>
      <div v-if="!item.apply_begin_tim && tabIndex == 0" class="tit-r">值守时间段：长期值守</div>
    </div>
    <div class="content">
      <div class="con-t">
        <div class="con-t-tit">入口信息</div>
        <div class="con-t-con">
          <ChannelInfo v-for="(m, n) in item.entrance_list" :key="n" :info="m"></ChannelInfo>
        </div>
      </div>
      <div class="con-b">
        <div class="con-b-tit">出口信息</div>
        <div class="con-b-con">
          <ChannelInfo v-for="(m, n) in item.export_list" :key="n" :info="m"></ChannelInfo>
        </div>
      </div>
    </div>
  </div>
  <el-empty v-if="tableData && tableData.length == 0" style="margin-top: 60px" description="暂无数据" />
</template>

<script setup>
import { ElEmpty } from 'element-plus';
import { ref } from 'vue';
import ChannelInfo from './ChannelInfo.vue';
const tabIndex = ref(0);
const tableData = ref(null);
defineExpose({
  tableData,
  tabIndex
});
</script>

<style lang="scss" scoped>
.box {
  // min-height: 260px;
  padding-bottom: 10px;
  margin-bottom: 10px;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid #f1f0f0;
  border-radius: 12px;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  .title {
    flex: 1.3;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding-right: 10px;
    padding-left: 10px;
    font-size: 14px;
    background-color: #bfe7ff;
    font-weight: 700;
    padding-top: 10px;
    padding-bottom: 10px;
  }
  .content {
    flex: 8.7;
    padding: 2px 10px;
    display: flex;
    flex-direction: column;
    > div {
      flex: 1;
      .con-t-tit {
        font-size: 14px;
        margin-bottom: 6px;
        margin-top: 10px;
      }
      .con-t-con {
        display: flex;
        gap: 10px;
        cursor: pointer;
      }
      .con-b-tit {
        font-size: 14px;
        margin-bottom: 6px;
        margin-top: 10px;
      }
      .con-b-con {
        display: flex;
        gap: 10px;
        cursor: pointer;
      }
    }
  }
}
</style>
