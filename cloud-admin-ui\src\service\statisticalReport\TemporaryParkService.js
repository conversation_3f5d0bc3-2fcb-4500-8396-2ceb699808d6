import * as temporaryParkApi from '@/api/statisticalReport/TemporaryParkApi';

/**
 * 临停收入全览
 */
export default {
  /**
   * 分页查询临停收入全览
   */
  pagingTemporaryPark(data) {
    return new Promise((resolve, reject) => {
      try {
        temporaryParkApi.pagingTemporaryPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        temporaryParkApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
