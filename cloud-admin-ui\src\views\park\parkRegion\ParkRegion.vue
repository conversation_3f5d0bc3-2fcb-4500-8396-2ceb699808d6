<template>
    <el-card class="table" shadow="never" style="margin-bottom: 10px">
        <div class="opers">
            <el-button type="primary" @click="handleCreate()">添加子场</el-button>
        </div>
        <div ref="table">
            <el-table :data="tableData" v-loading="loading" border>
                <el-table-column type="selection" style="text-align: center" width="40" />
                <el-table-column prop="action" label="操作" align="center" width="200">
                    <template v-slot="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
                        <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="name" label="子场名称" align="center" />
                <el-table-column prop="space_amount" label="车位数量" align="center" />
                <el-table-column prop="access_type_desc" label="准入类型" align="center" />
            </el-table>
            <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
                :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
                class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
        </div>
        <el-dialog title="添加子停车场" v-model="parkRegionCreateDialogVisible" :close-on-click-modal="false"
            @close="closeAddDialog(addForm)" width="500px">
            <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
                <el-form-item prop="name" label="子场名称">
                    <el-input v-model="data.form.name" />
                </el-form-item>
                <el-form-item prop="space_amount" label="车位数量">
                    <el-input-number v-model="data.form.space_amount" min="1" max="10000" style="width: 100%" />
                </el-form-item>
                <el-form-item prop="access_type" label="准入类型">
                    <el-select v-model="data.form.access_type" style="width: 100%" multiple>
                        <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="enabled" label="启用状态">
                    <el-switch v-model="data.form.delete_in_record" :active-value="1" :inactive-value="0" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="createCancel(addForm)">取 消</el-button>
                    <el-button type="primary" @click="createParkRegion(addForm)">确 定</el-button>
                </span>
            </template>
        </el-dialog>
        <el-dialog title="编辑子停车场" v-model="parkRegionUpdateDialogVisible" :close-on-click-modal="false"
            @close="closeEditDialog(editForm)" width="500px">
            <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
                <el-form-item prop="name" label="子场名称">
                    <el-input v-model="data.updateForm.name" />
                </el-form-item>
                <el-form-item prop="space_amount" label="车位数量">
                    <el-input-number v-model="data.updateForm.space_amount" min="1" max="10000" style="width: 100%" />
                </el-form-item>
                <el-form-item prop="access_type" label="准入类型">
                    <el-select v-model="data.updateForm.access_type" style="width: 100%" multiple>
                        <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
                    </el-select>
                </el-form-item>
                <el-form-item prop="enabled" label="启用状态">
                    <el-switch v-model="data.updateForm.delete_in_record" :active-value="1" :inactive-value="0" />
                </el-form-item>
            </el-form>
            <template #footer>
                <span class="dialog-footer">
                    <el-button @click="updateCancel(editForm)">取 消</el-button>
                    <el-button type="primary" @click="updateParkRegion(editForm)">确 定</el-button>
                </span>
            </template>
        </el-dialog>
    </el-card>
</template>

<script name="ParkRegion" setup>
import commonService from '@/service/common/CommonService';
import parkRegionService from '@/service/park/ParkRegionService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const park_id = ref('');
const types = ref([]);
const total = ref(0);
const parkRegionCreateDialogVisible = ref(false);
const parkRegionUpdateDialogVisible = ref(false);
const data = reactive({
    queryParams: {
        page: 1,
        limit: 30,
        park_id: undefined
    },
    form: {
        park_id: undefined,
        name: undefined,
        space_amount: 1,
        access_type: []
    },
    updateForm: {},
    rules: {
        name: [
            {
                required: true,
                message: '请输入子场名称',
                trigger: 'blur'
            }
        ],
        space_amount: [
            {
                required: true,
                message: '请输入车位数量',
                trigger: 'blur'
            }
        ],
        access_type: [
            {
                required: true,
                message: '请选择准入类型',
                trigger: 'change'
            }
        ]
    }
});

onMounted(() => {
    initSelects();
});

const initSelects = () => {
    const param = [{ enum_key: 'types', enum_value: 'EnumStopCarType' }];
    commonService.findEnums('park', param).then((response) => {
        types.value = response.data.types;
    });
};

const getList = (params) => {
    loading.value = true;
    park_id.value = params.park_id;
    params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
    params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
    data.queryParams = params;
    parkRegionService.pagingParkRegions(params).then((response) => {
        if (response.success === true) {
            tableData.value = response.data.rows;
            total.value = parseInt(response.data.total);
            loading.value = false;
        } else {
            ElMessage({
                message: response.detail_message != '' ? response.detail_message : response.message,
                type: 'error'
            });
            loading.value = false;
        }
    });
};
const handleCreate = () => {
    data.form = {
        park_id: park_id.value,
        name: undefined,
        space_amount: 1,
        access_type: []
    };
    parkRegionCreateDialogVisible.value = true;
};
const handleSizeChange = (val) => {
    data.queryParams.limit = val;
    getList(data.queryParams);
};
const handleCurrentChange = (val) => {
    data.queryParams.page = val;
    getList(data.queryParams);
};
const createParkRegion = (addForm) => {
    addForm.validate().then(() => {
        parkRegionService
            .createParkRegion(data.form)
            .then((response) => {
                if (response.success === true) {
                    ElMessage({
                        message: response.message,
                        type: 'success'
                    });
                    getList(data.queryParams);
                    addForm.resetFields();
                    parkRegionCreateDialogVisible.value = false;
                } else {
                    ElMessage({
                        message: response.detail_message != '' ? response.detail_message : response.message,
                        type: 'error'
                    });
                }
            })
            .catch(() => {
                getList(data.queryParams);
            });
    });
};

const handleEdit = (row) => {
    data.updateForm = {
        id: row.id,
        park_id: row.park_id,
        name: row.name,
        space_amount: row.space_amount
    };
    const arr = row.access_type.split(',');
    const type = [];
    arr.forEach(function (item) {
        type.push(parseInt(item));
    });
    data.updateForm.access_type = type;
    parkRegionUpdateDialogVisible.value = true;
};
const updateParkRegion = (editForm) => {
    editForm.validate().then(() => {
        parkRegionService
            .updateParkRegion(data.updateForm)
            .then((response) => {
                if (response.success === true) {
                    ElMessage({
                        message: response.message,
                        type: 'success'
                    });
                    getList(data.queryParams);
                    editForm.resetFields();
                    parkRegionUpdateDialogVisible.value = false;
                } else {
                    ElMessage({
                        message: response.detail_message != '' ? response.detail_message : response.message,
                        type: 'error'
                    });
                }
            })
            .catch(() => {
                getList(data.queryParams);
            });
    });
};
const handleDelete = (id) => {
    ElMessageBox.confirm('请确认是否删除？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
    }).then(() => {
        parkRegionService
            .deleteParkRegion(id)
            .then((response) => {
                if (response.success === true) {
                    ElMessage({
                        message: response.message,
                        type: 'success'
                    });
                    getList(data.queryParams);
                } else {
                    ElMessage({
                        message: response.detail_message != '' ? response.detail_message : response.message,
                        type: 'error'
                    });
                }
            })
            .catch(() => {
                getList(data.queryParams);
            });
    });
};

// 取消
const createCancel = (addForm) => {
    addForm.resetFields();
    parkRegionCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
    editForm.resetFields();
    parkRegionUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
    addForm.resetFields();
};
const closeEditDialog = (editForm) => {
    editForm.resetFields();
};
defineExpose({
    getList
});
</script>
<style lang="scss" scoped>
.example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
}
</style>
