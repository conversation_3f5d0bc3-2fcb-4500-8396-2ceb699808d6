/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找长租规则
export const pagingRentRule = (data) => {
  return $({
    url: '/console/park/rule/pagingRentRule',
    method: 'post',
    data
  });
};

// 新建长租规则
export const createRentRule = (data) => {
  return $({
    url: '/console/park/rule/createRentRule',
    method: 'post',
    data
  });
};

// 修改长租规则
export const updateRentRule = (data) => {
  return $({
    url: '/console/park/rule/updateRentRule',
    method: 'post',
    data
  });
};

// 删除长租规则
export const deleteRentRule = (id) => {
  return $({
    url: '/console/park/rule/deleteRentRule/' + id,
    method: 'post'
  });
};

// 启用长租规则
export const enableRentRule = (id) => {
  return $({
    url: '/console/park/rule/enableRentRule/' + id,
    method: 'post'
  });
};

// 禁用长租规则
export const disableRentRule = (id) => {
  return $({
    url: '/console/park/rule/disableRentRule/' + id,
    method: 'post'
  });
};

//查询当前车场下长租规则
export const listRentRule = (params) => {
  return $({
    url: '/console/park/rule/listRentRule',
    method: 'get',
    params
  });
};

//查询车场，当前长租规则下的产品信息
export const listProduct = (data) => {
  return $({
    url: '/console/park/rule/listProduct',
    method: 'post',
    data
  });
};

//提交审核
export const submitAuditRentRuleApply = (id) => {
  return $({
    url: '/console/park/rule/submitAuditRentRuleApply/' + id,
    method: 'post'
  });
};

//撤回
export const cancelAuditRentRuleApply = (data) => {
  return $({
    url: '/console/park/rule/cancelAuditRentRuleApply',
    method: 'post',
    data
  });
};

//长租规则校验
export const checkRentRule = (data) => {
  return $({
    url: '/console/park/rule/checkRentRule',
    method: 'post',
    data
  });
};

//长租规则名称校验
export const checkRepeatRentRuleName = (data) => {
  return $({
    url: '/console/park/rule/checkRepeatRentRuleName',
    method: 'post',
    data
  });
};