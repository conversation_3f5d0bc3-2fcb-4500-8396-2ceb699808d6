/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询日报表通过金额
export const pagingDayReportByMoney = (data) => {
  return $({
    url: '/console/park/finance/dayReport/pagingDayReports',
    method: 'post',
    data
  });
};

// 分页查询日报表通过次数
export const pagingDayReportByTimes = (data) => {
  return $({
    url: '/console/park/finance/dayReport/pagingDayReports',
    method: 'post',
    data
  });
};

// 卡片数据查询
export const searchBtnData = (data) => {
  return $({
    url: '/console/park/finance/dayReport/getTotal',
    method: 'post',
    data
  });
};

// 导出日报表（金额）
export const exportDayReportsByMoney = (data) => {
  return $({
    url: '/console/park/finance/dayReport/exportDayReports',
    method: 'post',
    data
  });
};

// 导出日报表（次数）
export const exportDayReportsByCount = (data) => {
  return $({
    url: '/console/park/finance/dayReport/exportDayReportCnt',
    method: 'post',
    data
  });
};
