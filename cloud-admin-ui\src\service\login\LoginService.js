import * as login from '@/api/login/LoginApi';

/**
 * 登录/退出登录
 */
export default {
  /**
   * 获取验证码
   */
  getCaptcha(param) {
    return new Promise((resolve, reject) => {
      try {
        login.getCaptcha(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 登录
   */
  login(param) {
    return new Promise((resolve, reject) => {
      try {
        login.login(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 退出登录
   */
  logout() {
    return new Promise((resolve, reject) => {
      try {
        login.logout().then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /*
   * 强制修改密码
   */
  forceUpdatePassword(param) {
    return new Promise((resolve, reject) => {
      try {
        login.forceUpdatePassword(param).then((response) => {
          resolve(response);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
