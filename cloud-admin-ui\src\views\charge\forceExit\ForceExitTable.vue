<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="sentryBoxRecordService.exportForceExit"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 384px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="plate_no" label="车牌号码" align="center" />
        <el-table-column prop="order_money" label="应收金额" align="center" />
        <el-table-column prop="initial_car_type" label="实收金额" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="to_time" label="出场时间" align="center" />
        <el-table-column prop="in_car_photo" label="入场图片" align="center">
          <template v-slot="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.in_car_photo" :fit="fit" />
          </template>
        </el-table-column>
        <el-table-column prop="out_car_photo" label="出场图片" align="center">
          <template v-slot="scope">
            <el-image style="width: 100px; height: 100px" :src="scope.row.out_car_photo" :fit="fit" />
          </template>
        </el-table-column>
        <el-table-column prop="updator_name" label="操作人" align="center" />
        <el-table-column prop="updated_at" label="操作时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ForceExitTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import sentryBoxRecordService from '@/service/charge/SentryBoxRecordService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  sentryBoxRecordService.pagingForceExit(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    loading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
