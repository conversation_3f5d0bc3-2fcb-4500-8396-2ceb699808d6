<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-04-01 16:41:02
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\finance\LongRentReport.vue
-->
<template>
  <div class="container">
    <long-rent-report-search @form-search="searchLongRentReportList" />
    <long-rent-report-search-btn-groups @search="searchLongRentReportFromGroups" ref="search_btn" />
    <long-rent-report-table ref="table" />
  </div>
</template>

<script name="LongRentReport" setup>
import LongRentReportSearch from './longRentReport/LongRentReportSearch.vue';
import LongRentReportSearchBtnGroups from './longRentReport/LongRentReportSearchBtnGroups.vue';
import LongRentReportTable from './longRentReport/LongRentReportTable.vue';
import { ref } from 'vue';

const table = ref(null);
const search_btn = ref(null);
const park_name = ref('');

const searchLongRentReportList = (queryParams) => {
  park_name.value = queryParams.park_name;
  search_btn.value.onActive = [null, null];
  search_btn.value.totalActive = true;
  search_btn.value.findLongRentReportByMoney(queryParams);
  table.value.getList(queryParams);
};

const searchLongRentReportFromGroups = (queryParams) => {
  queryParams.park_name = park_name.value;
  table.value.getList(queryParams);
};
</script>
