<template>
  <el-card class="table" shadow="never">
    <!-- <div class="uodataClass">
      <el-tooltip>
        <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
        <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
      </el-tooltip>
      <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
    </div> -->
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 280px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="180" />
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" />
        <el-table-column prop="space_number" label="临停车位数" align="center" min-width="180" />
        <el-table-column prop="parking_total_hours" label="临停车总时长（小时）" align="center" min-width="180" />
        <el-table-column prop="average_use_ratio" label="平均日利用率" align="center" min-width="180">
          <template #="{ row }">
            {{ row.average_use_ratio !== null && !isNaN(Number(row.average_use_ratio)) ? Number(row.average_use_ratio).toFixed(2) : '-' }}</template
          >
        </el-table-column>
        <el-table-column prop="use_ratio_plate_no" label="临停小于24小时有牌车车位利用率" align="center" min-width="180" />
        <el-table-column prop="use_ratio_no_plate_no" label="临停小于24小时无牌车车位利用率" align="center" min-width="180" />
        <el-table-column prop="midday_fastigium_use_ratio" label="午高峰利用率(12:00~15:00)" align="center" min-width="180" />
        <el-table-column prop="night_fastigium_use_ratio" label="晚高峰利用率(18:00~21:00)" align="center" min-width="180" />
        <el-table-column prop="business_hours_use_ratio" label="营业时间利用率(10:30~22:30)" align="center" min-width="180" />
        <el-table-column prop="morning_no_business_hours_use_ratio" label="非营业时间利用率（早）(00:00~10:29)" align="center" min-width="180" />
        <el-table-column prop="night_no_business_hours_use_ratio" label="非营业时间利用率（晚）(22:31~23:59)" align="center" min-width="180" />
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30, 100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ParkSpaceAvailabilityTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import parkSpaceAvailabilityService from '@/service/statisticalReport/ParkSpaceAvailabilityService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total = ref(0);
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(2);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case 1:
      break;
    case 6:
    case 2:
      return row.statistics_date.split('-')[1] + '月';
    case 3:
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case 4:
      break;
    case 5:
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  parkSpaceAvailabilityService.pagingParkSpaceAvailability(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap;
}
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
