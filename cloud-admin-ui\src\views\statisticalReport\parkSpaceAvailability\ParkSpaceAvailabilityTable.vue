<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" />
        <el-table-column prop="space_number" label="临停车位数" align="center" min-width="180" />
        <el-table-column prop="parking_total_hours" label="临停车总时长（小时）" align="center" min-width="180" />
        <el-table-column prop="average_use_ratio" label="平均日利用率" align="center" min-width="180" />
        <el-table-column prop="use_ratio_plate_no" label="临停小于24小时有牌车车位利用率" align="center" min-width="180" />
        <el-table-column prop="use_ratio_no_plate_no" label="临停小于24小时无牌车车位利用率" align="center" min-width="180" />
        <el-table-column prop="midday_fastigium_use_ratio" label="午高峰利用率(12:00~15:00)" align="center" min-width="180" />
        <el-table-column prop="night_fastigium_use_ratio" label="晚高峰利用率(18:00~21:00)" align="center" min-width="180" />
        <el-table-column prop="business_hours_use_ratio" label="营业时间利用率(10:30~22:30)" align="center" min-width="180" />
        <el-table-column prop="morning_no_business_hours_use_ratio" label="非营业时间利用率（早）(00:00~10:29)" align="center" min-width="180" />
        <el-table-column prop="night_no_business_hours_use_ratio" label="非营业时间利用率（晚）(22:31~23:59)" align="center" min-width="180" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ParkSpaceAvailabilityTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import parkSpaceAvailabilityService from '@/service/statisticalReport/ParkSpaceAvailabilityService';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  parkSpaceAvailabilityService.pagingParkSpaceAvailability(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap;
}
</style>
