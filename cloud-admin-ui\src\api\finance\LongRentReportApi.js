/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询日报表通过金额
export const pagingLongRentReport = (data) => {
  return $({
    url: '/console/park/finance/rentSpaceReport/pagingRentSpaceReports',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/park/finance/rentSpaceReport/exportRentSpaceReports',
    method: 'post',
    data
  });
};

// 长租总收入
export const getMoney = (data) => {
  return $({
    url: '/console/park/finance/rentSpaceReport/getMoney',
    method: 'post',
    data
  });
};
