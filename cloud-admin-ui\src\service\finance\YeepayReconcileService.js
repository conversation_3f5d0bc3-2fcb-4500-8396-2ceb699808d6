import * as yeepayReconcileApi from '@/api/finance/YeepayReconcileApi';

/**
 * Yeepay 对账
 */
export default {
  // 查询月汇总对账文件
  monthYeeSettle(data) {
    return new Promise((resolve, reject) => {
      try {
        yeepayReconcileApi.monthYeeSettle(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  // 查询日汇总对账文件
  dayYeeSettle(data) {
    return new Promise((resolve, reject) => {
      try {
        yeepayReconcileApi.dayYeeSettle(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  // 下载交易日对账文件到本地
  settleFileDownload(data) {
    return new Promise((resolve, reject) => {
      try {
        yeepayReconcileApi.settleFileDownload(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
