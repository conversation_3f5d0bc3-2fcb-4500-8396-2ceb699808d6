<template>
  <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
    <el-tab-pane label="金额" name="dayReportByMoney">
      <day-report-by-money ref="byMoney" />
    </el-tab-pane>
    <el-tab-pane label="按次" name="dayReportByTimes">
      <day-report-by-times ref="byTimes" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="DayReport" setup>
import { ref } from 'vue';
import dayReportByMoney from './DayReportByMoney.vue';
import dayReportByTimes from './DayReportByTimes.vue';

const activeName = ref('dayReportByMoney');
const byMoney = ref(null);
const byTimes = ref(null);

// onMounted(() => {
//   byMoney.value.searchList();
// });

const handleClick = (tab) => {
  console.log(tab.props.name)
  if (tab.props.name === 'dayReportByMoney') {
    byMoney.value.runSearchRef();
  }
  if (tab.props.name === 'dayReportByTimes') {
    byTimes.value.runSearchRef();
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 10px;
  background-color: #f6f6f6;
  padding-bottom: 18px;
}
</style>
