<template>
  <el-card shadow="never" style="margin: 10px 0px">
    <div class="opers">
      <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
        <el-form-item prop="stand_rent_license" label="协议方式">
          <el-select v-model="data.form.stand_rent_license" placeholder="协议方式" style="width: 150px" @change="change">
            <el-option v-for="item in agreementTypeList" :key="item.value" :label="item.key" :value="parseInt(item.value)" />
          </el-select>
        </el-form-item>
        <el-form-item prop="user_rent_license">
          <div style="border: 1px solid #ccc">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor
              style="height: 500px; overflow-y: hidden"
              v-model="data.form.user_rent_license"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="createParkRentAgreement(addForm)">保 存</el-button>
    </div>
  </el-card>
</template>

<script name="ParkRentAgreement" setup>
import '@wangeditor/editor/dist/css/style.css';
import parkRentAgreementService from '@/service/park/ParkRentAgreementService';
import agreementService from '@/service/system/AgreementService';
import commonService from '@/service/common/CommonService';
import { onBeforeUnmount, ref, shallowRef, reactive, onMounted } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { ElMessage } from 'element-plus';
{
  Editor, Toolbar;
}
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
// 内容 HTML
const agreementTypeList = ref([]);
const saveBtn = ref(false);
const mode = ref('default');
const addForm = ref();
const data = reactive({
  queryParams: {
    park_id: undefined
  },
  form: {
    park_id: undefined,
    stand_rent_license: undefined,
    stand_rent_license_desc: undefined,
    user_rent_license: undefined
  },
  rules: {
    stand_rent_license: [
      {
        required: true,
        message: '请选择协议方式',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  saveBtn.value = true;
});

// 获取租用车位协议
const getSpaceRentLicense = (params) => {
  data.queryParams = params;
  parkRentAgreementService.getSpaceRentLicense(params).then((response) => {
    data.form.stand_rent_license = response.data.stand_rent_license;
    data.form.park_id = response.data.park_id;
    if (response.data.user_rent_license === null || response.data.user_rent_license === '') {
      data.form.user_rent_license = '';
    } else {
      data.form.user_rent_license = response.data.user_rent_license;
    }
  });
};
const toolbarConfig = {};
const editorConfig = { placeholder: '请输入内容...' };
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

const initSelects = () => {
  // 长租类型
  const param = [{ enum_key: 'agreementTypeList', enum_value: 'EnumSpaceRentLicense' }];
  commonService.findEnums('park', param).then((response) => {
    agreementTypeList.value = response.data.agreementTypeList;
  });
};
// 保存
const createParkRentAgreement = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.user_rent_license === undefined || data.form.user_rent_license === '' || data.form.user_rent_license === null) {
      ElMessage({
        message: '租用车位协议内容不能为空！',
        type: 'error'
      });
      return false;
    }
    parkRentAgreementService.createParkRentAgreement(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

//协议方式变化
const change = (e) => {
  if (e === 1) {
    saveBtn.value = false;
    const editor = editorRef.value;
    editor.disable();
    const params = {
      licenseType: 1
    };
    agreementService.getStandardLicense(params.licenseType).then((response) => {
      data.form.user_rent_license = response.data.content;
    });
  } else {
    saveBtn.value = true;
    const editor = editorRef.value;
    editor.enable();
    parkRentAgreementService.getSpaceRentLicense(data.queryParams).then((response) => {
      data.form.user_rent_license = response.data.user_rent_license;
    });
  }
};

defineExpose({
  getSpaceRentLicense,
  initSelects
});
</script>
<style lang="scss" scoped>
.footer {
  padding-top: 50px;
  padding-bottom: 50px;
  text-align: center;
}
</style>
