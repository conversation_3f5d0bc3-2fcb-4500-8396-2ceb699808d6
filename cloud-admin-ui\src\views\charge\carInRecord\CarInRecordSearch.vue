<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.car_types" style="width: 100%" placeholder="车辆类型" multiple>
        <el-option v-for="item in carTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.out_park_states" style="width: 100%" placeholder="是否出场" multiple>
        <el-option v-for="item in outParkStateList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.in_gateway_id" style="width: 100%" placeholder="入场通道">
        <el-option v-for="item in inGatewayList" :key="item.id" :label="item.park_region_name + '-' + item.name" :value="item.id" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.days" placeholder="滞留天数" />
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.in_type" style="width: 100%" placeholder="入场类型" :clearable="true">
        <el-option v-for="item in inTypeList" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="入场开始日期"
        end-placeholder="入场结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :shortcuts="shortcuts"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarInRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import parkFeeService from '@/service/charge/ParkFeeService';
import commonService from '@/service/common/CommonService';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import ParkFindBack from './ParkFindBack.vue';
import { useRoute } from 'vue-router';
const route = useRoute();
const inGatewayList = ref([]);
const emits = defineEmits(['form-search']);
const carTypeList = ref([]);
const outParkStateList = ref([]);
const form = reactive({
  queryParams: {
    park_id: undefined,
    in_type: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    out_park_states: [],
    days: undefined,
    start_time: undefined,
    end_time: undefined,
    in_gateway_id: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);
const shortcuts = [
  {
    text: '最近三天',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
      return [start, end];
    }
  },
  {
    text: '最近一周',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
      return [start, end];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      return [start, end];
    }
  }
  // {
  //   text: '最近三个月',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
  //     return [start, end];
  //   }
  // },
  // {
  //   text: '最近一年',
  //   value: () => {
  //     const end = new Date();
  //     const start = new Date();
  //     start.setTime(start.getTime() - 3600 * 1000 * 24 * 365);
  //     return [start, end];
  //   }
  // }
];
onMounted(() => {
  initSelects();
  form.dateRange = [dayjs().format('YYYY-MM-DD') + ' 00:00:00', dayjs().format('YYYY-MM-DD') + ' 23:59:59'];
  form.queryParams.start_time = form.dateRange[0];
  form.queryParams.end_time = form.dateRange[1];

  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }

  if (Object.keys(route.query).length !== 0 && undefined !== route.query.parkId) {
    form.queryParams.park_id = route.query.parkId;
    form.queryParams.park_name = route.query.parkName;
    form.queryParams.plate_no = route.query.plateNo;
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
    return;
  }
  if (user.role_id == 1) {
    return false;
  }
  // 判断
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    console.log(user.park_ids, '是哦不搜索111111111111111111111111');
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }
});
const inTypeList = ref([
  { value: 1, key: '正常入场' },
  { value: 5, key: '手动补录' },
  { value: 6, key: '批量导入' },
  { value: 7, key: '跟车入场' },
  { value: 8, key: '断网补录入场' },
  { value: 9, key: '折返入场' },
  { value: 10, key: '手动匹配' },
  { value: 11, key: '模拟入场' }
]);
const initSelects = () => {
  const param = [
    {
      enum_key: 'outParkStateList',
      enum_value: 'EnumOutParkState'
    },
    {
      enum_key: 'carTypeList',
      enum_value: 'EnumCarType'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    outParkStateList.value = response.data.outParkStateList;
    carTypeList.value = response.data.carTypeList;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    ElMessage({
      message: '请选择停车场进行查询',
      type: 'warning'
    });
    return false;
  }
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    in_type: undefined,
    park_name: undefined,
    plate_no: undefined,
    car_types: [],
    out_park_states: [],
    days: undefined,
    start_time: undefined,
    end_time: undefined,
    in_gateway_id: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
  listParkGateway(val[0].park_id);
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const listParkGateway = (id) => {
  parkFeeService.gatewayListGateway({ park_id: id, in_out: 2 }).then((response) => {
    inGatewayList.value = response.data;
  });
};
</script>
<style lang="scss" scoped></style>
