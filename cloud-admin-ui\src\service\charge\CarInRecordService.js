import * as carInRecordApi from '@/api/charge/CarInRecordApi';

/**
 * 入场记录
 */
export default {
  /**
   * 分页查询入场记录
   */
  pagingCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.pagingCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 批量删除入场记录
   */
  deleteCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.deleteCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  submitAuditStayCarDelApply(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.submitAuditStayCarDelApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  recoverDelStayCar(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.recoverDelStayCar(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询已删除记录
   */
  pagingDeleteRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.pagingDeleteRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出入场记录
   */
  exportCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.exportCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出已删除记录
   */
  exportDeleteRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.exportDeleteRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
 * 查询滞留车辆导入记录(分页)-parkServer
 */
  listImportStayCarRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.listImportStayCarRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
* 通过导入记录ID查询可删除滞留车辆信息(分页)-parkServer
*/
  listImportStayCarRecordDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.listImportStayCarRecordDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
* 滞留车辆撤回记录(分页)-parkServer
*/
  withdrawDelStayCarRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.withdrawDelStayCarRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
};
