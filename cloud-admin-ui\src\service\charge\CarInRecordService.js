import * as carInRecordApi from '@/api/charge/CarInRecordApi';

/**
 * 入场记录
 */
export default {
  /**
   * 分页查询入场记录
   */
  pagingCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.pagingCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 批量删除入场记录
   */
  deleteCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.deleteCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询已删除记录
   */
  pagingDeleteRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.pagingDeleteRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出入场记录
   */
  exportCarInRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.exportCarInRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出已删除记录
   */
  exportDeleteRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        carInRecordApi.exportDeleteRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
