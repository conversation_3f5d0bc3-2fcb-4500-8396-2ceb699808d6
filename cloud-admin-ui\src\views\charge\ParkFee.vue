<template>
  <div class="container">
    <park-fee-search @form-search="searchParkFeeList" @reset="resetParamsAndData" />
    <park-fee-top-groups @search="searchParkFeeTopGroups" ref="search_btn" />
    <park-fee-table ref="table" />
  </div>
</template>

<script setup name="ParkFee">
import ParkFeeSearch from './parkFee/ParkFeeSearch.vue';
import ParkFeeTable from './parkFee/ParkFeeTable.vue';
import ParkFeeTopGroups from './parkFee/ParkFeeTopGroups.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const search_btn = ref(null);
const params = reactive({});

const searchParkFeeList = (queryParams) => {
  const { park_name, ...newQueryParams } = queryParams;
  search_btn.value.countParkPayRecord(newQueryParams);
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  params.order_states = [1, 2];
  // table.value.getList(params);
};
const searchParkFeeTopGroups = (queryParams) => {
  table.value.getList(queryParams);
};
</script>
