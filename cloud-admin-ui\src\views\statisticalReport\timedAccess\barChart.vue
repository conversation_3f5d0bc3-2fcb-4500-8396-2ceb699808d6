<template>
  <div style="width: 100%;padding: 0 20px; box-sizing: border-box;margin-top: 20px;">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="进场" name="0"></el-tab-pane>
      <el-tab-pane label="出场" name="1"></el-tab-pane>
    </el-tabs>
  </div>
  <div v-if="activeName == 0" ref="chartRef" style="width: 100%; height: 600px"></div>
  <div v-if="activeName == 1" ref="chartRef2" style="width: 100%; height: 600px"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
const activeName = ref('0')
const chartRef = ref(null);
const chartRef2 = ref(null);
let chartInstance = null;
const filterData = ref([])
const xData = ref([])
const sourceData = ref([])
const legendData = ref([])
const timeJson = [
  {
    "label": "00:00-01:00",
    "inField": "zero_hour_car_in",
    "outField": "zero_hour_car_out"
  },
  {
    "label": "01:00-02:00",
    "inField": "one_hour_car_in",
    "outField": "one_hour_car_out"
  },
  {
    "label": "02:00-03:00",
    "inField": "two_hour_car_in",
    "outField": "two_hour_car_out"
  },
  {
    "label": "03:00-04:00",
    "inField": "three_hour_car_in",
    "outField": "three_hour_car_out"
  },
  {
    "label": "04:00-05:00",
    "inField": "four_hour_car_in",
    "outField": "four_hour_car_out"
  },
  {
    "label": "05:00-06:00",
    "inField": "five_hour_car_in",
    "outField": "five_hour_car_out"
  },
  {
    "label": "06:00-07:00",
    "inField": "six_hour_car_in",
    "outField": "six_hour_car_out"
  },
  {
    "label": "07:00-08:00",
    "inField": "seven_hour_car_in",
    "outField": "seven_hour_car_out"
  },
  {
    "label": "08:00-09:00",
    "inField": "eight_hour_car_in",
    "outField": "eight_hour_car_out"
  },
  {
    "label": "09:00-10:00",
    "inField": "nine_hour_car_in",
    "outField": "nine_hour_car_out"
  },
  {
    "label": "10:00-11:00",
    "inField": "ten_hour_car_in",
    "outField": "ten_hour_car_out"
  },
  {
    "label": "11:00-12:00",
    "inField": "eleven_hour_car_in",
    "outField": "eleven_hour_car_out"
  },
  {
    "label": "12:00-13:00",
    "inField": "twelve_hour_car_in",
    "outField": "twelve_hour_car_out"
  },
  {
    "label": "13:00-14:00",
    "inField": "thirteen_hour_car_in",
    "outField": "thirteen_hour_car_out"
  },
  {
    "label": "14:00-15:00",
    "inField": "fourteen_hour_car_in",
    "outField": "fourteen_hour_car_out"
  },
  {
    "label": "15:00-16:00",
    "inField": "fifteen_hour_car_in",
    "outField": "fifteen_hour_car_out"
  },
  {
    "label": "16:00-17:00",
    "inField": "sixteen_hour_car_in",
    "outField": "sixteen_hour_car_out"
  },
  {
    "label": "17:00-18:00",
    "inField": "seventeen_hour_car_in",
    "outField": "seventeen_hour_car_out"
  },
  {
    "label": "18:00-19:00",
    "inField": "eighteen_hour_car_in",
    "outField": "eighteen_hour_car_out"
  },
  {
    "label": "19:00-20:00",
    "inField": "nineteen_hour_car_in",
    "outField": "nineteen_hour_car_out"
  },
  {
    "label": "20:00-21:00",
    "inField": "twenty_hour_car_in",
    "outField": "twenty_hour_car_out"
  },
  {
    "label": "21:00-22:00",
    "inField": "twenty_one_hour_car_in",
    "outField": "twenty_one_hour_car_out"
  },
  {
    "label": "22:00-23:00",
    "inField": "twenty_two_hour_car_in",
    "outField": "twenty_two_hour_car_out"
  },
  {
    "label": "23:00-24:00",
    "inField": "twenty_three_hour_car_in",
    "outField": "twenty_three_hour_car_out"
  }
]

const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: ''
  },
});
const initChart = () => {
  let option = {};
  if (activeName.value == 0) {
    option = {
      grid: {
        // 网格距离顶部距离
        top: 50,
        // 网格距离底部距,
        bottom: 60,
        // 网格距离左边距离
        left: 90,
        right: 30,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: legendData.value,
        bottom: 20,
      },
      xAxis: [
        {
          type: 'category',
          data: xData.value,
          axisLabel: {
            rotate: '20',
          },
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '单位：辆',
          min: 0,
          // max: 200,
          axisLabel: {
            formatter: '{value}'
          },
          axisTick: {
            show: true,
            inside: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#efefef',  // 设置辅助线颜色
              type: 'dashed'  // 使用虚线样式
            }
          }
        }
      ],
      dataset: {
        source: sourceData.value
      },
      series: [
        {
          name: props.title,
          type: 'line',
          barGap: 0,
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#5470c6'
          }
        },
      ]
    };
  } else {
    option = {
      grid: {
        // 网格距离顶部距离
        top: 50,
        // 网格距离底部距,
        bottom: 60,
        // 网格距离左边距离
        left: 90,
        right: 30,
        containLabel: true
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'cross'
        }
      },
      legend: {
        data: legendData.value,
        bottom: 20,
      },
      xAxis: [
        {
          type: 'category',
          data: xData.value,
          axisLabel: {
            rotate: '20',
          },
          axisPointer: {
            type: 'shadow'
          }
        }
      ],
      yAxis: [
        {
          type: 'value',
          name: '单位：辆',
          min: 0,
          // max: 200,
          axisLabel: {
            formatter: '{value}'
          },
          axisTick: {
            show: true,
            inside: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#efefef',  // 设置辅助线颜色
              type: 'dashed'  // 使用虚线样式
            }
          }
        }
      ],
      dataset: {
        source: sourceData.value
      },
      series: [
        {
          name: props.title,
          type: 'line',
          barGap: 0,
          emphasis: {
            focus: 'series'
          },
          itemStyle: {
            color: '#5470c6'
          }
        },
      ]
    };
  }

  chartInstance.setOption(option);
};
watch(
  () => props.tableData,
  () => {
    init();
  }, { deep: true }
);
watch(
  () => activeName.value,
  (val) => {
    if (val == 0) {
      legendData.value = [];
    } else {
      legendData.value = [];
    }
    init();
  }
);
onMounted(() => {
  legendData.value = [props.title]
  init();

});
const init = () => {
  nextTick(() => {
    if (activeName.value == 0) {
      chartInstance = echarts.init(chartRef.value);
    } else {
      chartInstance = echarts.init(chartRef2.value);
    }
    filterData.value = props.tableData
    // sourceData.value = [['月份', '进场（辆）', '出场（辆）', '临停停车费（元）']];
    sourceData.value = [];
    xData.value = [];

    filterData.value.forEach((item, i) => {
      for (let key in item) {
        let keyIndex = -1
        if (activeName.value == 0) {
          keyIndex = timeJson.findIndex(m => m.inField == key)
        } else {
          keyIndex = timeJson.findIndex(m => m.outField == key)
        }

        if (keyIndex != -1) {
          let keyItem = activeName.value == 0 ? timeJson.find(m => m.inField == key) : timeJson.find(m => m.outField == key)
          xData.value.push(item.statistics_date + " " + keyItem.label)
          sourceData.value.push([item.statistics_date + " " + keyItem.label, item[key]]);
        }
      }
    })
    initChart();
    // 响应窗口变化
    window.addEventListener('resize', () => {
      chartInstance.resize();
    });
  })
}
onBeforeUnmount(() => {
  // 组件卸载时销毁实例
  if (chartInstance) {
    window.removeEventListener('resize', chartInstance.resize);
    chartInstance.dispose();
  }
});
</script>