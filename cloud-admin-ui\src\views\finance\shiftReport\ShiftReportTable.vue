<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="shiftReportService.exportReports"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column label="班次信息" align="center">
          <el-table-column label="停车场名称" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
          <el-table-column label="当班人" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.shift_name }}</span>
            </template>
          </el-table-column>
          <el-table-column :render-header="renderHeader" label="班次开始时间|班次结束时间" align="center" min-width="130">
            <template #default="scope">
              <span>{{ scope.row.on_time }}<br />{{ scope.row.off_time }}</span>
            </template>
          </el-table-column>
          <el-table-column label="应收" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.should_pay_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="实收" align="center" min-width="80">
            <template #default="scope">
              <span>{{ scope.row.payed_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="电子支付" align="center">
          <el-table-column label="支付宝支付" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.ali_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="微信支付" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.wx_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="ETC支付" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.etc_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="现金收费" align="center">
          <el-table-column label="现金支付" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.cash_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="特殊处理" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.special_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="异常处理损失" align="center">
          <el-table-column label="特殊处理损失" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.special_loss_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="被冲车辆损失" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.flush_loss_money }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column prop="debate_money" label="优免抵扣" align="center" />
        <el-table-column prop="manual_money" label="手动抬杆" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="ShiftReportTable" setup>
import { reactive, ref, onMounted, h } from 'vue';
import { ElMessage } from 'element-plus';
import shiftReportService from '@/service/finance/ShiftReportService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  // getList(data.queryParams);
});

const renderHeader = ({ column, $index }) => {
  return h('span', {}, [h('span', {}, column.label.split('|')[0]), h('br'), h('span', {}, column.label.split('|')[1])]);
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  shiftReportService.pagingShiftReport(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}
</style>
