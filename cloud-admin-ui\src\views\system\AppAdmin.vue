<template>
  <div class="container my-table-container">
    <app-admin-search @form-search="searchAppAdminList" @reset="resetAllParamsAndData" />
    <app-admin-table ref="table" />
  </div>
</template>

<script setup name="AppAdmin">
import { reactive, ref } from 'vue';
import AppAdminSearch from './appAdmin/AppAdminSearch.vue';
import AppAdminTable from './appAdmin/AppAdminTable.vue';

const table = ref(null);
const params = reactive({});

const searchAppAdminList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetAllParamsAndData = () => {
  table.value.getList(params);
};
</script>
<style lang="scss" scoped></style>
