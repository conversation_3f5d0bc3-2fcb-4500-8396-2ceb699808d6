import{x as u,k as c}from"./@vue-05be3c3e.js";const i=new WeakMap,r=new function(){var n=this,t=this;this.on=function(e,o){return n.eventMap.has(e)||n.eventMap.set(e,[]),n.eventMap.get(e).push(o),function(){return n.off(e,o)}},this.once=function(e,o){var a=function f(){t.off(e,f),o.apply(void 0,[].slice.call(arguments))};return n.on(e,a),function(){return n.off(e,a)}},this.emit=function(e){var o=arguments,a=t.eventMap.get(e);Array.isArray(a)&&a.forEach(function(f){typeof f=="function"&&f.apply(void 0,[].slice.call(o,1))})},this.off=function(e,o){if(e)if(typeof o=="function"){if(n.eventMap.has(e)){var a=n.eventMap.get(e).filter(function(f){return f!==o});n.eventMap.set(e,a)}}else n.eventMap.delete(e);else n.eventMap.clear()},this.eventMap=new Map},s=new WeakSet,l=n=>{let t=c();if(t===null)return;const e=i.get(t)||i.set(t,[]).get(t);e==null||e.push(n),s.has(t)||(s.add(t),u(()=>{const o=t&&i.get(t);o==null||o.forEach(a=>a()),t=null},t))},v=(n,t)=>{const e=r.on(n,t);return l(e),e};r.off;const h=r.emit;export{v as a,h as c};
