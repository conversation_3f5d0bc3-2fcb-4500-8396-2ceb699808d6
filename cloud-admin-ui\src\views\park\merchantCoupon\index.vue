<template>
  <el-card shadow="never" style="margin: 10px 0px">
    <div style="width: 500px">
      <el-form ref="editForm" label-width="200px" :rules="data.rules" :model="data.updateForm">
        <el-form-item prop="share" label="优免券与免费时长：">
          <el-select v-model="data.updateForm.share">
            <el-option v-for="item in shareList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="stack" label="电子优免券叠加方式">
          <el-select v-model="data.updateForm.stack">
            <el-option v-for="item in stackList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="available_populations" label="优免券可用群体">
          <el-select v-model="data.updateForm.available_populations">
            <el-option v-for="item in populationList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="updateMerchant">保 存</el-button>
    </div>
  </el-card>
</template>

<script name="MerchantCoupon" setup>
import { reactive, ref } from 'vue';
import merchantCouponService from '@/service/park/MerchantCouponService';
import { ElMessage } from 'element-plus';

const editForm = ref(null);
const parkId = ref('');

const data = reactive({
  rules: {
    share: [{ required: true, message: '请选择优免券与免费时长', trigger: 'change' }],
    stack: [{ required: true, message: '请选择电子优免券叠加方式', trigger: 'change' }],
    available_populations: [{ required: true, message: '请选择优免券可用群体', trigger: 'change' }]
  },
  updateForm: {
    share: '',
    stack: '',
    available_populations: ''
  }
});
const shareList = [
  { key: '允许共享', value: 1 },
  { key: '不允许共享', value: 0 }
];
const stackList = [
  { key: '允许叠加', value: 1 },
  { key: '不允许叠加', value: 0 }
];
const populationList = [
  { key: '长租+临停', value: 1 },
  { key: '临停', value: 2 }
];

const initMerchantCouponConfig = async (id) => {
  parkId.value = id;
  const res = await merchantCouponService.getMerchant({ park_id: id });
  if (res.success == true) {
    data.updateForm = res.data;
  }
};

const updateMerchant = () => {
  editForm.value.validate().then(() => {
    merchantCouponService.updateByParkId({ ...data.updateForm, park_id: parkId.value }).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

defineExpose({
  initMerchantCouponConfig
});
</script>

<style lang="scss" scoped>
.footer {
  padding-top: 20px;
  padding-bottom: 20px;
  text-align: center;
}
</style>
