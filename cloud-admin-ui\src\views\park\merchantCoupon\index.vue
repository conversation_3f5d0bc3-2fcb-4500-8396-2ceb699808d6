<template>
  <el-card shadow="never" style="margin: 10px 0px">
    <div style="width: 700px">
      <el-form ref="editForm" label-width="200px" :rules="data.rules" :model="data.updateForm">
        <div class="form-title">商家优免</div>
        <el-form-item prop="share" label="优免券与免费时长：">
          <el-select v-model="data.updateForm.share">
            <el-option v-for="item in shareList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="stack" label="电子优免券叠加方式">
          <el-select v-model="data.updateForm.stack">
            <el-option v-for="item in stackList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="available_populations" label="优免券可用群体">
          <el-select v-model="data.updateForm.available_populations">
            <el-option v-for="item in populationList" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>

        <el-form-item label="全免劵及大额优免劵发放方式" label-width="240px" prop="coupon_send_type">
          <template #label>
            <div class="label-flex">
              <span>全免劵及大额优免劵发放方式</span>
              <el-tooltip
                content="人工发放+审核发放：全免及大额优免劵可通过人工和审核方式发放。审核发放：全免及大额优免只能通过审核方式发放"
                placement="top"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-select v-model="data.updateForm.coupon_send_type" placeholder="请选择" style="width: 240px">
            <el-option label="人工发放+审核发放" :value="1" />
            <el-option label="审核发放" :value="2" />
          </el-select>
        </el-form-item>
      </el-form>
      <el-form ref="parkInfoForm" label-width="200px" :model="parkInfo">
        <div class="form-title">岗亭端页面控制</div>
        <el-form-item label-width="240px" prop="page_work_statistics" label="区分账号权限访问班次统计详情页">
          <el-checkbox-group v-model="parkInfo.page_work_statistics">
            <el-checkbox v-for="item in controllClientRoles" :label="item.value" :key="item.value">{{ item.key }}</el-checkbox>
          </el-checkbox-group>
          <el-tooltip content="根据岗亭端登录的账号角色，勾选则有权访问班次统计详情页，不勾选则不能访问" placement="right" effect="dark">
            <el-icon class="icon" style="margin-left: 10px"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <el-form-item label-width="240px" prop="lift_pole" label="岗亭车位为0入口是否抬杆">
          <el-radio-group v-model="parkInfo.lift_pole">
            <el-radio v-for="item in liftPoles" :label="item.value" :key="item.value">{{ item.key }}</el-radio>
          </el-radio-group>
          <el-tooltip content="岗亭端空余车位显示为0时是否抬杆" placement="right" effect="dark">
            <el-icon class="icon" style="margin-left: 10px"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
        <div class="form-title">车场配置</div>

        <el-form-item label="月卡一位多车" prop="more_rent_switch">
          <template #label>
            <div class="label-flex">
              <span>月卡一位多车</span>
              <el-tooltip content="根据设置的数量，在办理月卡业务时，可支持一个车位添加同等数量车辆" placement="top">
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-input-number
            v-model="parkInfo.more_rent_switch"
            :max="5000"
            :min="1"
            :step="1"
            :precision="0"
            style="width: 100%"
            placeholder="请输入月卡一位多车数量"
          />
        </el-form-item>
        <el-form-item label="欠费是否强制追缴" label-width="200px" prop="more_rent_switch">
          <template #label>
            <div class="label-flex">
              <span>欠费是否强制追缴</span>
              <el-tooltip
                content='若选择"是", 在出口缴费或提前缴费时，必须缴清历史欠费才能出场；若选择"否",在出口缴费或提前缴费时，不强制用户缴纳历史欠费。'
                placement="top"
              >
                <el-icon>
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-select v-model="parkInfo.compulsory_recovery" placeholder="请选择欠费是否强制追缴" style="width: 240px">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="小程序月卡办理" label-width="200px" prop="allow_online_renew">
          <template #label>
            <div class="label-flex">
              <span>小程序月卡办理</span>
              <el-tooltip>
                <template #content>
                  1. 支持开通和续费：小程序月卡开通和续费入口对客可见，可在线自助办理。<br />
                  2. 不支持开通和续费：小程序月卡开通和续费入口对客不可见，无法线上办理。 <br />
                  3. 仅支持开通：小程序仅对客开放月卡开通入口，不支持办理续费。<br />
                  4. 仅支持续费：小程序仅对客开放月卡续费入口，不支持办理新开通。
                </template>
                <el-icon style="cursor: pointer"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <el-select v-model="parkInfo.allow_online_renew" placeholder="Select" style="width: 240px">
            <el-option v-for="item in downdata" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="free_space_car" label="特殊车辆免费停车">
          <el-checkbox-group v-model="parkInfo.free_space_car">
            <el-checkbox v-for="item in freeSpaceCar" :label="item.value" :key="item.value">{{ item.key }}</el-checkbox>
          </el-checkbox-group>
          <el-tooltip content="车辆类型勾选则停车免费，不勾选则停车收费" placement="right" effect="dark">
            <el-icon class="icon" style="margin-left: 10px"><QuestionFilled /></el-icon>
          </el-tooltip>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" @click="updateMerchant">保 存</el-button>
    </div>
  </el-card>
</template>

<script name="MerchantCoupon" setup>
import { reactive, ref } from 'vue';
import merchantCouponService from '@/service/park/MerchantCouponService';
import parkInfoService from '@/service/park/ParkInfoService';
import commonService from '@/service/common/CommonService';
import { ElMessage } from 'element-plus';
import { QuestionFilled } from '@element-plus/icons-vue';
import { getMiniProgramMonthCardEnum } from '@/api/park/ParkInfoApi'; //api接口

const editForm = ref(null);
const parkId = ref('');
const downdata = ref(); //获取的小程序月卡办理的枚举数据
const freeSpaceCar = ref([]); //特殊车辆免费停车的枚举数据
const controllClientRoles = ref([]); //控制客户端角色的枚举数据
const liftPoles = ref([
  { key: '抬杆', value: 1 },
  { key: '不抬杆', value: 0 }
]);
const getmoncarddata = async () => {
  try {
    const rudata = await getMiniProgramMonthCardEnum([{ enum_key: 'allowOnlineRenew', enum_value: 'EnumAllowOnlineRenew' }]);
    if (rudata.code == 200) {
      downdata.value = rudata.data.allowOnlineRenew;
    }
  } catch (error) {
    console.error('获取小程序月卡办理枚举失败', error);
  }

  const param = [
    { enum_key: 'freeSpaceCar', enum_value: 'EnumFreeSpaceCar' },
    {
      enum_key: 'roleList',
      enum_value: 'EnumSentryRole'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    freeSpaceCar.value = response.data.freeSpaceCar;
    controllClientRoles.value = response.data.roleList;
  });
};

const data = reactive({
  rules: {
    share: [{ required: true, message: '请选择优免券与免费时长', trigger: 'change' }],
    stack: [{ required: true, message: '请选择电子优免券叠加方式', trigger: 'change' }],
    available_populations: [{ required: true, message: '请选择优免券可用群体', trigger: 'change' }],
    coupon_send_type: [{ required: true, message: '全免劵及大额优免劵发放方式', trigger: 'change' }]
  },
  updateForm: {
    share: '',
    stack: '',
    available_populations: '',
    coupon_send_type: ''
  }
});
const shareList = [
  { key: '允许共享', value: 1 },
  { key: '不允许共享', value: 0 }
];
const stackList = [
  { key: '允许叠加', value: 1 },
  { key: '不允许叠加', value: 0 }
];
const populationList = [
  { key: '长租+临停', value: 1 },
  { key: '临停', value: 2 }
];

const parkInfo = ref({
  page_work_statistics: [],
  free_space_car: [],
  lift_pole: 1
});

const parkInfoForm = ref(null);

const initMerchantCouponConfig = async (id) => {
  parkId.value = id;
  const parkInfoRes = await parkInfoService.getParkById(id);
  console.log('parkInfoRes', parkInfoRes);
  parkInfo.value = parkInfoRes;
  parkInfo.value.page_work_statistics = parkInfo.value.page_work_statistics?.split(',').map((item) => parseInt(item));
  parkInfo.value.free_space_car = parkInfo.value.free_space_car?.split(',').map((item) => parseInt(item));
  const typeList = parkInfoRes.type;
  const arr = typeList.split(',');
  // arr去重
  const uniqueArr = [...new Set(arr)];
  parkInfo.value.type = []; //清空原有数据
  //将arr中的数据转换为数字类型
  uniqueArr.forEach(function (item) {
    parkInfo.value.type.push(parseInt(item));
  });

  parkInfo.value.page_work_statistics =
    !parkInfoRes.page_work_statistics || parkInfoRes.page_work_statistics.length === 0 ? [1] : parkInfoRes.page_work_statistics;
  parkInfo.value.free_space_car = !parkInfoRes.free_space_car || parkInfoRes.free_space_car.length === 0 ? [1, 2, 3, 4] : parkInfoRes.free_space_car;

  const res = await merchantCouponService.getMerchant({ park_id: id });
  if (res.success == true) {
    data.updateForm = res.data;
  }
  getmoncarddata();
};

const updateMerchant = async () => {
  try {
    await editForm.value.validate();
    await parkInfoForm.value.validate();
    const editResponse = await merchantCouponService.updateByParkId({ ...data.updateForm, park_id: parkId.value });

    const parkInfoResponse = await parkInfoService.updatePark({
      ...parkInfo.value,
      page_work_statistics: parkInfo.value.page_work_statistics.join(','),
      free_space_car: parkInfo.value.free_space_car.join(',')
    });
    console.log('editResponse', editResponse);
    console.log('parkInfoResponse', parkInfoResponse);
    if (editResponse.success === true && parkInfoResponse.success === true) {
      ElMessage({
        message: '保存成功',
        type: 'success'
      });
    } else {
      ElMessage({
        message: '保存失败',
        type: 'error'
      });
    }
  } catch (error) {
    // 表单校验失败时会抛出异常，这里无需额外处理
  }
};

defineExpose({
  initMerchantCouponConfig
});
</script>

<style lang="scss" scoped>
.footer {
  padding-top: 20px;
  padding-bottom: 20px;
  text-align: center;
}
.form-title {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  font-size: 16px;
  &::before {
    content: '';
    display: inline-block;
    width: 4px;
    height: 16px;
    margin-right: 4px;
    background-color: #409eff;
    vertical-align: sub;
  }
}
</style>
