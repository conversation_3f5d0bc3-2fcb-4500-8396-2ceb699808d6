export function findNavMenus() {
  const data = [
    {
      id: '2',
      name: 'Dashboard',
      title: '仪表盘',
      icon: 'Odometer',
      type: 'page',
      path: '/dashboard',
      component: '/src/views/Dashboard.vue'
    },
    {
      id: '3',
      name: '<PERSON>',
      title: '停车服务',
      icon: 'Compass',
      type: 'menu',
      children: [
        {
          id: '3-1',
          name: 'CarPark',
          title: '车场管理',
          type: 'menu',
          children: [
            {
              id: '3-1-1',
              name: 'CarParkAdmin',
              title: '车场维护',
              path: '/park/CarParkAdmin',
              type: 'page',
              component: '/src/views/park/CarParkAdmin.vue'
            }
          ]
        }
      ]
    },
    {
      id: '1',
      name: 'System',
      title: '系统管理',
      icon: 'Setting',
      type: 'menu',
      children: [
        {
          id: '1-1',
          name: 'Employee',
          title: '员工管理',
          path: '/system/employee',
          type: 'page',
          component: '/src/views/system/Employee.vue'
        },
        {
          id: '1-2',
          name: 'Role',
          title: '角色管理',
          path: '/system/role',
          type: 'page',
          component: '/src/views/system/Role.vue'
        },
        {
          id: '1-3',
          name: 'Department',
          title: '部门管理',
          path: '/system/department',
          type: 'page',
          component: '/src/views/system/Department.vue'
        },
        {
          id: '1-4',
          name: 'Menu',
          title: '菜单管理',
          path: '/system/menu',
          type: 'page',
          component: '/src/views/system/Menu.vue'
        },
        {
          id: '1-5',
          name: 'Demo',
          title: 'Demo',
          path: '/system/demo',
          type: 'page',
          cached: true,
          component: '/src/views/system/Demo.vue',
          permissions: ['demo:create', 'demo:delete']
        },
        {
          id: '1-6',
          name: 'permissionGroup',
          title: '权限组管理',
          path: '/system/permissionGroup',
          type: 'page',
          component: '/src/views/system/PermissionGroup.vue'
        },
        {
          id: '1-7',
          name: 'Api',
          title: 'Api接口管理',
          path: '/system/api',
          type: 'page',
          cached: true,
          component: '/src/views/system/Api.vue',
          permissions: ['api:create', 'api:delete']
        }
      ]
    }
  ];

  return {
    url: '/menu/findNavMenus',
    type: 'get',
    data: {
      code: 200,
      success: true,
      message: null,
      data: data
    }
  };
}
