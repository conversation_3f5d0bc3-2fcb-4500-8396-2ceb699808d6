<template>
  <el-dialog v-model="dialogVisible" title="手动匹配" width="1100px" :before-close="handleManualPassClose" :close-on-click-modal="false">
    <div style="display: flex; align-items: center; margin-bottom: 10px">
      <div v-if="!manualPass.flag" style="height: 32px; line-height: 32px"><span class="required">*</span>车牌号</div>
      <div v-if="manualPass.flag" style="height: 32px; line-height: 32px"><span class="required">*</span>入场时间</div>
      <div>
        <el-input
          v-if="!manualPass.flag"
          v-model="manualPass.query.plate_no"
          placeholder="请输入车牌号"
          style="width: 160px; margin: 0 10px"
        ></el-input>
        <el-date-picker
          v-if="!manualPass.flag"
          v-model="manualPass.daterange"
          type="daterange"
          range-separator="至"
          start-placeholder="入场开始时间"
          end-placeholder="入场结束时间"
          style="width: 325px; margin: 0 10px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <el-date-picker
          v-if="manualPass.flag"
          v-model="manualPass.daterange0"
          type="daterange"
          range-separator="至"
          start-placeholder="入场开始时间"
          end-placeholder="入场结束时间"
          style="width: 265px; margin: 0 10px"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
        />

        <el-button type="primary" @click="handleQueryParkInRecord">查 询</el-button>
        <el-button v-if="manualPass.flag" @click="handlePlate">返回</el-button>
        <el-button v-else @click="handleNoPlate">查询无牌车</el-button>
      </div>
    </div>
    <div v-show="!manualPass.flag" style="padding-bottom: 30px">
      <el-table :data="manualPass.table.data" border v-loading="manualPass.loading" style="max-height: 500px; overflow-y: auto">
        <el-table-column prop="plate_no" label="车牌号" align="center">
          <template v-slot="scope">
            {{ scope.row.plate_no }}
          </template>
        </el-table-column>
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入口" align="center" width="160" />
        <el-table-column label="入场图片" align="center" min-width="100">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="checkInPicture(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" align="center" width="90">
          <template v-slot="scope">
            <el-button v-if="scope.row.out_state === 0" link type="primary" @click="calcManualPassCarFee(scope.row)">车辆计费</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="manualPass.query.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="manualPass.query.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="manualPass.total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <div v-show="manualPass.flag" style="padding-bottom: 30px">
      <el-table :data="manualPass.table0.data" border v-loading="manualPass.loading">
        <el-table-column prop="plate_no" label="车牌号" align="center">
          <template v-slot="scope">
            {{ scope.row.plate_no }}
          </template>
        </el-table-column>
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入口" align="center" width="160" />
        <el-table-column label="入场图片" align="center" min-width="100" v-if="manualPass.flag">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.car_photo_url"
              style="width: 100px; height: 100px"
              :src="scope.row.car_photo_url"
              :fit="fit"
              @click="showImage(scope.row.car_photo_url)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="action" label="操作" align="center" width="90">
          <template v-slot="scope">
            <el-button v-if="scope.row.out_state === 0" link type="primary" @click="calcManualPassCarFee(scope.row)">车辆计费</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="manualPass.query0.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="manualPass.query0.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="manualPass.total0"
        class="table-pagination"
        @size-change="handleSize0Change"
        @current-change="handleCurrent0Change"
      />
    </div>
  </el-dialog>
  <el-dialog v-model="dialogVisibleimg" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>
<script name="onTocars" setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { ElMessage } from 'element-plus';
import { defineExpose, defineProps, onMounted, reactive, ref, watch } from 'vue';
const duty = useDuty();
const dialogVisible = ref(false);
const activeName = ref('有牌车');
const tabList = ref([
  { label: '有牌车', num: '' },
  { label: '无牌车', num: '' }
]);
// 手动匹配
const manualPass = reactive({
  dialogVisible: false,
  // 有牌车查询条件和列表数据
  table: {
    data: []
  },
  query: {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined,
    plate_no: undefined
  },
  total: 0,
  // 无牌车查询条件和列表数据
  table0: {
    data: []
  },
  query0: {
    page: 1,
    limit: 10,
    start_time: undefined,
    end_time: undefined
  },
  total0: 0,
  daterange: [],
  daterange0: [],
  loading: false,
  flag: false
});
const props = defineProps(['plateNo']);
watch(props, (newVal) => {
  console.log(newVal, 'newVal');
  manualPass.query.plate_no = newVal.plateNo;
});
// 查询手动匹配车辆最近入场记录
const handleQueryParkInRecord = () => {
  let param;
  if (!manualPass.flag) {
    if (manualPass.query.plate_no === undefined) {
      ElMessage({
        message: '请输入车牌号',
        type: 'info'
      });
      return;
    }
    if (manualPass.daterange.length > 0) {
      manualPass.query.start_time = manualPass.daterange[0] + ' 00:00:00';
      manualPass.query.end_time = manualPass.daterange[1] + ' 23:59:59';
    } else {
      ElMessage({
        message: '请选择入场时间',
        type: 'info'
      });
      return;
    }
    param = { ...manualPass.query, has_plate_no: !manualPass.flag, out_state: 0 };
  } else {
    if (manualPass.daterange0.length > 0) {
      manualPass.query0.start_time = manualPass.daterange0[0] + ' 00:00:00';
      manualPass.query0.end_time = manualPass.daterange0[1] + ' 23:59:59';
    } else {
      ElMessage({
        message: '请选择入场时间',
        type: 'info'
      });
      return;
    }
    param = { ...manualPass.query0, has_plate_no: !manualPass.flag, out_state: 0 };
  }
  manualPass.loading = true;
  param.park_id = duty.callInfo.park_id;
  UnattendedApi.pageParkInRecord(param).then((res) => {
    if (res.success) {
      if (!manualPass.flag) {
        manualPass.table.data = res.data.rows;
        manualPass.total = parseInt(res.data.total);
      } else {
        manualPass.table0.data = res.data.rows;
        manualPass.total0 = parseInt(res.data.total);
      }
    } else {
      ElMessage({
        dangerouslyUseHTMLString: true,
        message: '<div>' + res.message + '</div><div style="margin-top: 6px;">' + res.detail_message + '</div>',
        type: 'error'
      });
    }
    manualPass.loading = false;
  });
};
const handleSizeChange = (val) => {
  manualPass.query.limit = val;
  handleQueryParkInRecord();
};
const handleCurrentChange = (val) => {
  manualPass.query.page = val;
  handleQueryParkInRecord();
};
const handleSize0Change = (val) => {
  manualPass.query0.limit = val;
  handleQueryParkInRecord();
};
const handleCurrent0Change = (val) => {
  manualPass.query0.page = val;
  handleQueryParkInRecord();
};
const handlePlate = () => {
  manualPass.flag = false;
};

const handleNoPlate = () => {
  manualPass.flag = true;
};
onMounted(() => {
  // getList(data.queryParams);
});
const emits = defineEmits(['calcManualPassCarFee']);
const calcManualPassCarFee = (row) => {
  row.time1 = manualPass.query.start_time;
  row.time2 = manualPass.query.end_time;
  emits('calcManualPassCarFee', row);
};
const dialogVisibleimg = ref(false);
const title = ref('');
const dialogImageUrl = ref('');
const checkInPicture = (url) => {
  if (url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisibleimg.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = url;
  }
};
defineExpose({
  dialogVisible
});
</script>
<style lang="scss" scoped></style>
