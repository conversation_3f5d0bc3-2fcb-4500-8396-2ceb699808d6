<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加设备</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 修改 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="设备名称" align="center" />
        <el-table-column prop="model" label="设备型号" align="center" />
        <el-table-column prop="type_desc" label="设备类型" align="center" />
        <el-table-column prop="dev_factory_name" label="设备厂家" align="center" />
        <el-table-column prop="protocol_type_desc" label="支持协议类型" align="center" />
        <el-table-column prop="interface_version" label="接口版本" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加设备" v-model="deviceCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="800px">
        <el-form ref="addForm" label-width="110px" :rules="data.rules" :model="data.form">
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="name" label="设备名称"> <el-input v-model="data.form.name" placeholder="设备名称" /> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="model" label="设备型号"> <el-input v-model="data.form.model" placeholder="设备型号" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="dev_factory_id" label="设备厂家">
                <el-select v-model="data.form.dev_factory_id" style="width: 100%">
                  <el-option v-for="item in deviceFactoryList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12"
              ><el-form-item prop="type" label="设备类型">
                <el-select v-model="data.form.type" style="width: 100%">
                  <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="protocol_type" label="支持协议类型">
                <el-select v-model="data.form.protocol_type" multiple style="width: 100%">
                  <el-option v-for="item in agreementTypeList" :key="item.value" :label="item.name" :value="item.value" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="interface_version" label="接口版本">
                <el-input v-model="data.form.interface_version" placeholder="接口版本" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="dock_time" label="设备对接时间"
                ><el-date-picker v-model="data.form.dock_time" type="date" placeholder="请选择" style="width: 100%" /></el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="firmware_version" label="固件版本">
                <el-input v-model="data.form.firmware_version" placeholder="固件版本" /> </el-form-item
            ></el-col>
          </el-row>
          <el-form-item prop="memo" label="设备描述">
            <el-input v-model="data.form.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deviceCreateDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="createDevice(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改设备" v-model="deviceUpdateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="800px">
        <el-form ref="editForm" label-width="110px" :rules="data.rules" :model="data.updateForm">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item prop="name" label="设备名称"><el-input v-model="data.updateForm.name" placeholder="设备名称" /></el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item prop="model" label="设备型号"><el-input v-model="data.updateForm.model" placeholder="设备型号" /></el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="dev_factory_id" label="设备厂家">
                <el-select v-model="data.updateForm.dev_factory_id" style="width: 100%">
                  <el-option v-for="item in deviceFactoryList" :key="item.id" :label="item.name" :value="item.id" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="type" label="设备类型"
                ><el-select v-model="data.updateForm.type" style="width: 100%">
                  <el-option v-for="item in deviceTypeList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="protocol_type" label="支持协议类型">
                <el-select v-model="data.updateForm.protocol_type" multiple style="width: 100%">
                  <el-option v-for="item in agreementTypeList" :key="item.value" :label="item.key" :value="item.value" />
                </el-select> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="interface_version" label="接口版本">
                <el-input v-model="data.updateForm.interface_version" placeholder="接口版本" /> </el-form-item
            ></el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12"
              ><el-form-item prop="dock_time" label="设备对接时间">
                <el-date-picker v-model="data.updateForm.dock_time" type="date" placeholder="请选择" style="width: 100%" /> </el-form-item
            ></el-col>
            <el-col :span="12"
              ><el-form-item prop="firmware_version" label="固件版本">
                <el-input v-model="data.updateForm.firmware_version" placeholder="固件版本" /> </el-form-item
            ></el-col>
          </el-row>
          <el-form-item prop="memo" label="设备描述">
            <el-input v-model="data.updateForm.memo" type="textarea" :rows="6" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="deviceUpdateDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="updateDevice(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="DeviceTable" setup>
import { reactive, onMounted, ref, nextTick } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import deviceService from '@/service/device/DeviceService';
import commonService from '@/service/common/CommonService';
import dictService from '@/service/system/DictService';

const addForm = ref();
const editForm = ref();
const park_id = ref('');
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const deviceFactoryList = ref([]);
const deviceTypeList = ref([]);
const agreementTypeList = ref([]);
const total = ref(0);
const deviceCreateDialogVisible = ref(false);
const deviceUpdateDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    name: '',
    dev_factory_id: '',
    park_id: undefined,
    types: [],
    page: 1,
    limit: 30
  },
  form: {
    park_id: undefined,
    name: undefined,
    model: undefined,
    dev_factory_id: undefined,
    type: undefined,
    protocol_type: undefined,
    interface_version: undefined,
    dock_time: undefined,
    firmware_version: undefined,
    memo: undefined
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入设备名称',
        trigger: 'blur'
      }
    ],
    model: [
      {
        required: true,
        message: '请输入设备型号',
        trigger: 'blur'
      }
    ],
    dev_factory_id: [
      {
        required: true,
        message: '请选择设备厂家',
        trigger: 'blur'
      }
    ],
    type: [
      {
        required: true,
        message: '请选择设备类型',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
  getDeviceFactoryList();
  initSelects();
  status.value = true;
});

// 初始化数据
const initSelects = () => {
  const param = [
    {
      enum_key: 'deviceTypeList',
      enum_value: 'EnumDeviceType'
    }
  ];
  // 支持协议类型
  dictService.getDictsList('SUPPORT_AGREEMENT_TYPE').then((response) => {
    agreementTypeList.value = response;
  });
  commonService.findEnums('device', param).then((response) => {
    deviceTypeList.value = response.data.deviceTypeList;
  });
};
// 查询设备列表数据
const getDeviceFactoryList = (params) => {
  deviceService.getDeviceFactoryList(params).then((response) => {
    if (response.success === true) {
      deviceFactoryList.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
// 分页查询设备列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  deviceService.pagingDevice(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 新建设备
const handleCreate = (addForm) => {
  if (status.value === false) {
    nextTick(() => {
      addForm.clearValidate();
    });
  }
  data.form = {
    park_id: park_id.value,
    name: undefined,
    model: undefined,
    dev_factory_id: undefined,
    type: '',
    protocol_type: undefined,
    interface_version: undefined,
    dock_time: undefined,
    firmware_version: undefined,
    memo: undefined
  };
  deviceCreateDialogVisible.value = true;
  status.value = false;
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 提交并保存新建设备
const createDevice = (addForm) => {
  if (data.form.protocol_type.length === 0) {
    data.form.protocol_type = undefined;
  }
  addForm.validate().then(() => {
    deviceService
      .createDevice(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          deviceCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 删除设备
const handleDelete = (val) => {
  batchDelete(val);
};
const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceService
      .deleteDevice(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 修改设备
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: row.park_id,
    name: row.name,
    model: row.model,
    dev_factory_id: row.dev_factory_id,
    type: row.type,
    protocol_type_desc: row.protocol_type_desc,
    interface_version: row.interface_version,
    dock_time: row.dock_time,
    firmware_version: row.firmware_version,
    memo: row.memo
  };
  const array = [];
  if ((row.protocol_type != null) & (row.protocol_type != '')) {
    data.updateForm.protocol_type = row.protocol_type.split(',');
    for (var i = 0; i < data.updateForm.protocol_type.length; i++) {
      array.push(parseInt(data.updateForm.protocol_type[i]));
    }
    data.updateForm.protocol_type = array;
  }
  deviceUpdateDialogVisible.value = true;
};
// 提交并保存修改设备
const updateDevice = (editForm) => {
  // 判断协议类型是否为空
  if (data.updateForm.protocol_type != undefined && (data.updateForm.protocol_type == [] || data.updateForm.protocol_type.length == 0)) {
    data.updateForm.protocol_type.length = 0;
  }
  editForm.validate().then(() => {
    deviceService
      .updateDevice(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          deviceUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
