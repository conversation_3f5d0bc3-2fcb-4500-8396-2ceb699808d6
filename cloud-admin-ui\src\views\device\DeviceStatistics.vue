<template>
  <div class="container">
    <device-statistics-search @form-search="searchDeviceStatisticsList" @reset="resetParamsAndData" />
    <device-statistics-table ref="table" />
  </div>
</template>

<script name="DeviceStatistics" setup>
import DeviceStatisticsSearch from './deviceStatistics/DeviceStatisticsSearch.vue';
import DeviceStatisticsTable from './deviceStatistics/DeviceStatisticsTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchDeviceStatisticsList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
