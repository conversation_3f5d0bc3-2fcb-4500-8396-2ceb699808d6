<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-04-02 17:27:26
 * @LastEditors: 达万安 段世煜
 * @Description: 临停交易数据
 * @FilePath: \cloud-admin-ui\src\views\home\single\parkingPay.vue
-->
<template>
  <warp-card height="40%" :title="'临停交易数据(' + paramsData.start_date + ' 至 ' + paramsData.end_date + ')'">
    <div class="content">
      <div class="left item">
        <div class="chart-container">
          <pie-chart
            ref="pieChartRef"
            :color="color"
            :center="['50%', '60%']"
            radius="70%"
            labelPosition="outside"
            labelFormatter="{c}元"
            :legendPosition="{ top: 2 }"
          ></pie-chart>
        </div>
        <div class="card-info">
          <div class="item" v-for="item in data.pie" :key="item.name">
            <div class="label">
              {{ item.name }}
              <el-tooltip content="含 0 元收费" placement="right-end" effect="light" v-if="item.name === '现金'">
                <el-icon class="icon">
                  <InfoFilled />
                </el-icon>
              </el-tooltip>
            </div>
            <div class="value">{{ item.value.toFixed(1) + '元' }}</div>
          </div>
        </div>
      </div>
      <div class="right item">
        <div class="chart-container">
          <normal-chart
            ref="barChartRef"
            :showLabel="true"
            :labelFormatter="labelFormatterFn"
            itemLabelPosition="bottom"
            :color="color"
            barWidth="auto"
            :grid="{ top: 30, bottom: 60, left: 30, containLabel: true }"
            :legendPosition="{ top: 2 }"
            single
          />
        </div>
        <!-- <div class="card-info">
          <div class="item" v-for="item in data.bar" :key="item.name">
            <span class="label">{{ item.name }}</span>
            <span class="value">{{ item.value.toFixed(0) + '笔' }}</span>
          </div>
        </div> -->
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import { reactive, ref, computed, nextTick } from 'vue';
import warpCard from './components/warpCard.vue';
import pieChart from './components/pieChart.vue';
import normalChart from './components/normalChart.vue';
import { fetchParkingPay } from '@/api/home/<USER>';
import { InfoFilled } from '@element-plus/icons-vue';

const color = ['#1890FF', '#00DBF2', '#6DFACD', '#6F6BEC'];

const pieChartRef = ref(null);
const barChartRef = ref(null);
const data = computed(() => {
  return {
    pie: [
      { name: '现金', value: staticData.money.cash },
      { name: '电    子', value: staticData.money.electron },
      { name: 'ETC', value: staticData.money.etc },
      { name: '第三方', value: staticData.money.mallCoo }
    ],
    bar: [
      { name: '现金', value: staticData.count.cash },
      { name: '电子', value: staticData.count.electron },
      { name: 'ETC', value: staticData.count.etc },
      { name: '第三方', value: staticData.count.mallCoo }
    ]
  };
});
const staticData = reactive({
  money: {
    cash: 0,
    electron: 0,
    etc: 0,
    mallCoo: 0
  },
  count: {
    cash: 0,
    electron: 0,
    etc: 0,
    mallCoo: 0
  }
});

const labelFormatterFn = (params) => {
  return params.seriesName + '\n\n' + params.data + '笔';
};

const paramsData = ref({});
const fetchData = async (params) => {
  paramsData.value = params;
  try {
    const { data } = await fetchParkingPay(params);
    staticData.money.cash = data.cash_payed_money || 0;
    staticData.money.electron = data.electronic_payed_money || 0;
    staticData.money.etc = data.etc_payed_money || 0;
    staticData.money.mallCoo = data.mall_coo_payed_money || 0;

    staticData.count.cash = data.cash_payed_num || 0;
    staticData.count.electron = data.electronic_payed_num || 0;
    staticData.count.etc = data.etc_payed_num || 0;
    staticData.count.mallCoo = data.mall_coo_payed_num || 0;
  } finally {
    nextTick(() => {
      pieChartRef.value.setData(
        data.value.pie.map((item) => {
          return {
            ...item,
            label: {
              show: item.value > 0
            },
            labelLine: {
              show: item.value > 0
            }
          };
        })
      );
      barChartRef.value.setData(data.value.bar);
    });
  }
};

defineExpose({
  fetchData
});
</script>

<style scoped lang="scss">
.content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  height: 100%;
  .item {
    width: 48%;
  }
  .left {
    .chart-container {
      height: 65%;
    }
    .card-info {
      height: 35%;
      background-color: #f4f7fd;
      border-radius: 4px;
      display: flex;
      flex-wrap: wrap;
      padding-left: 30px;
      .item {
        width: 50%;
        display: flex;
        justify-content: flex-start;
        align-items: center;
        // padding: 15px 0;
        .label {
          font-size: 12px;
          display: flex;
          align-items: center;
          width: 52px;
          white-space: pre;
          .icon {
            cursor: pointer;
            font-size: 16px;
            margin-left: 5px;
            color: #f10a03;
          }
        }
        .value {
          font-size: 14px;
          font-weight: 550;
        }
      }
    }
  }
  .right {
    .chart-container {
      height: 100%;
    }
    .card-info {
      text-align: center;
      height: 30%;
      display: flex;
      justify-content: space-around;
      align-items: center;
      .item {
        width: auto;
        display: flex;
        flex-direction: column;
        .label {
          font-size: 12px;
        }
        .value {
          font-size: 14px;
          // font-weight: 550;
        }
      }
    }
  }
}
</style>
