<template>
  <div class="seatBox">
    <FormSearch @search="handleDataSearch" @reset="handleAllReset">
      <form-search-item>
        <el-input placeholder="请输入停车场名称" v-model="queryParams.park_name"></el-input>
      </form-search-item>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <div ref="table">
        <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 284px)">
          <!-- <el-table-column align="center" type="selection"></el-table-column> -->
          <el-table-column align="center" label="序号" type="index" width="80"></el-table-column>
          <el-table-column align="center" label="被托管停车场名称" prop="park_name"></el-table-column>
          <el-table-column align="center" label="云端值守车场名称" prop="console_park_name"></el-table-column>
          <el-table-column align="center" label="托管时间段" prop="user_name">
            <template v-slot="{ row }">
              <div v-if="row.apply_begin_time">{{ row.apply_begin_time }} ~ {{ row.apply_end_time }}</div>
              <div v-else>长期值守</div>
            </template>
          </el-table-column>
          <el-table-column align="center" label="创建时间" prop="created_at"></el-table-column>
        </el-table>
        <el-pagination
          background
          :current-page="queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import UnattendedApi from '@/service/system/Unattended';
import { ElMessage } from 'element-plus';
import { onActivated, onMounted, ref } from 'vue'; //引入“vue”
// import ParkFindBack from './ParkFindBack.vue'; //引入"关联车场"组件

const queryParams = ref({
  page: 1,
  limit: 30,
  park_name: undefined
});
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
onMounted(() => {
  // handleDataSearch(); // 页面加载时获取数据
});

const handleDataSearch = (params) => {
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  UnattendedApi.hostingpagingPark(queryParams.value).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  queryParams.value = {};
  console.log('handleAllReset');
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};
onActivated(() => {
  handleDataSearch();
});
</script>

<style lang="scss" scoped>
.formClass {
  // height: 150px;
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  .el-input {
    width: 100%;
  }
}
</style>
