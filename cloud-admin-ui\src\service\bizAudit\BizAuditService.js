import * as bizAudit from '@/api/bizAudit/BizAuditApi';

/**
 * 业务审核
 */
export default {
  /**
   *业务审核表格数据
   */
  pagingBizAudits(data) {
    return new Promise((resolve, reject) => {
      try {
        bizAudit.pagingBizAudits(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取业务审核表单
   */
  getBizAuditFormById(data) {
    return new Promise((resolve, reject) => {
      try {
        bizAudit.getBizAuditFormById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 审核通过
   */
  passAudit(data, type = 'single') {
    return new Promise((resolve, reject) => {
      try {
        if (type === 'single') {
          bizAudit.passAudit(data).then(function (res) {
            resolve(res);
          });
        } else {
          bizAudit.passAudits(data).then(function (res) {
            resolve(res);
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 审核拒绝
   */
  rejectAudit(data, type = 'single') {
    return new Promise((resolve, reject) => {
      try {
        if (type === 'single') {
          bizAudit.rejectAudit(data).then(function (res) {
            resolve(res);
          });
        } else {
          bizAudit.rejectAudits(data).then(function (res) {
            resolve(res);
          });
        }
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取待审核业务个数
   */
  getWaitAuditCount() {
    return new Promise((resolve, reject) => {
      try {
        bizAudit.getWaitAuditCount({}).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
