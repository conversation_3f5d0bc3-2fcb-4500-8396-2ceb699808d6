var yt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function pt(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function E(t){return E=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(o){return typeof o}:function(o){return o&&typeof Symbol=="function"&&o.constructor===Symbol&&o!==Symbol.prototype?"symbol":typeof o},E(t)}function rt(t,o){if(E(t)!="object"||!t)return t;var a=t[Symbol.toPrimitive];if(a!==void 0){var i=a.call(t,o||"default");if(E(i)!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(o==="string"?String:Number)(t)}function nt(t){var o=rt(t,"string");return E(o)=="symbol"?o:o+""}function ot(t,o,a){return(o=nt(o))in t?Object.defineProperty(t,o,{value:a,enumerable:!0,configurable:!0,writable:!0}):t[o]=a,t}function z(t,o){var a=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);o&&(i=i.filter(function(f){return Object.getOwnPropertyDescriptor(t,f).enumerable})),a.push.apply(a,i)}return a}function ht(t){for(var o=1;o<arguments.length;o++){var a=arguments[o]!=null?arguments[o]:{};o%2?z(Object(a),!0).forEach(function(i){ot(t,i,a[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(a)):z(Object(a)).forEach(function(i){Object.defineProperty(t,i,Object.getOwnPropertyDescriptor(a,i))})}return t}function B(){return B=Object.assign?Object.assign.bind():function(t){for(var o=1;o<arguments.length;o++){var a=arguments[o];for(var i in a)({}).hasOwnProperty.call(a,i)&&(t[i]=a[i])}return t},B.apply(null,arguments)}function at(t){if(Array.isArray(t))return t}function it(t,o){var a=t==null?null:typeof Symbol<"u"&&t[Symbol.iterator]||t["@@iterator"];if(a!=null){var i,f,b,y,v=[],p=!0,w=!1;try{if(b=(a=a.call(t)).next,o===0){if(Object(a)!==a)return;p=!1}else for(;!(p=(i=b.call(a)).done)&&(v.push(i.value),v.length!==o);p=!0);}catch(k){w=!0,f=k}finally{try{if(!p&&a.return!=null&&(y=a.return(),Object(y)!==y))return}finally{if(w)throw f}}return v}}function $(t,o){(o==null||o>t.length)&&(o=t.length);for(var a=0,i=Array(o);a<o;a++)i[a]=t[a];return i}function Q(t,o){if(t){if(typeof t=="string")return $(t,o);var a={}.toString.call(t).slice(8,-1);return a==="Object"&&t.constructor&&(a=t.constructor.name),a==="Map"||a==="Set"?Array.from(t):a==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(a)?$(t,o):void 0}}function ut(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function vt(t,o){return at(t)||it(t,o)||Q(t,o)||ut()}function ct(t){if(Array.isArray(t))return $(t)}function lt(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function ft(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function dt(t){return ct(t)||lt(t)||Q(t)||ft()}function st(t,o){if(t==null)return{};var a={};for(var i in t)if({}.hasOwnProperty.call(t,i)){if(o.indexOf(i)!==-1)continue;a[i]=t[i]}return a}function bt(t,o){if(t==null)return{};var a,i,f=st(t,o);if(Object.getOwnPropertySymbols){var b=Object.getOwnPropertySymbols(t);for(i=0;i<b.length;i++)a=b[i],o.indexOf(a)===-1&&{}.propertyIsEnumerable.call(t,a)&&(f[a]=t[a])}return f}function J(t,o,a,i,f,b,y){try{var v=t[b](y),p=v.value}catch(w){return void a(w)}v.done?o(p):Promise.resolve(p).then(i,f)}function gt(t){return function(){var o=this,a=arguments;return new Promise(function(i,f){var b=t.apply(o,a);function y(p){J(b,i,f,y,v,"next",p)}function v(p){J(b,i,f,y,v,"throw",p)}y(void 0)})}}var V={exports:{}},X={exports:{}};(function(t){function o(a){return t.exports=o=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(i){return typeof i}:function(i){return i&&typeof Symbol=="function"&&i.constructor===Symbol&&i!==Symbol.prototype?"symbol":typeof i},t.exports.__esModule=!0,t.exports.default=t.exports,o(a)}t.exports=o,t.exports.__esModule=!0,t.exports.default=t.exports})(X);(function(t){var o=X.exports.default;function a(){t.exports=a=function(){return f},t.exports.__esModule=!0,t.exports.default=t.exports;var i,f={},b=Object.prototype,y=b.hasOwnProperty,v=Object.defineProperty||function(n,e,r){n[e]=r.value},p=typeof Symbol=="function"?Symbol:{},w=p.iterator||"@@iterator",k=p.asyncIterator||"@@asyncIterator",G=p.toStringTag||"@@toStringTag";function g(n,e,r){return Object.defineProperty(n,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),n[e]}try{g({},"")}catch{g=function(r,u,l){return r[u]=l}}function Y(n,e,r,u){var l=e&&e.prototype instanceof F?e:F,c=Object.create(l.prototype),s=new C(u||[]);return v(c,"_invoke",{value:tt(n,r,s)}),c}function M(n,e,r){try{return{type:"normal",arg:n.call(e,r)}}catch(u){return{type:"throw",arg:u}}}f.wrap=Y;var H="suspendedStart",Z="suspendedYield",K="executing",L="completed",m={};function F(){}function P(){}function _(){}var N={};g(N,w,function(){return this});var R=Object.getPrototypeOf,T=R&&R(R(W([])));T&&T!==b&&y.call(T,w)&&(N=T);var S=_.prototype=F.prototype=Object.create(N);function U(n){["next","throw","return"].forEach(function(e){g(n,e,function(r){return this._invoke(e,r)})})}function A(n,e){function r(l,c,s,h){var d=M(n[l],n,c);if(d.type!=="throw"){var O=d.arg,x=O.value;return x&&o(x)=="object"&&y.call(x,"__await")?e.resolve(x.__await).then(function(j){r("next",j,s,h)},function(j){r("throw",j,s,h)}):e.resolve(x).then(function(j){O.value=j,s(O)},function(j){return r("throw",j,s,h)})}h(d.arg)}var u;v(this,"_invoke",{value:function(c,s){function h(){return new e(function(d,O){r(c,s,d,O)})}return u=u?u.then(h,h):h()}})}function tt(n,e,r){var u=H;return function(l,c){if(u===K)throw Error("Generator is already running");if(u===L){if(l==="throw")throw c;return{value:i,done:!0}}for(r.method=l,r.arg=c;;){var s=r.delegate;if(s){var h=q(s,r);if(h){if(h===m)continue;return h}}if(r.method==="next")r.sent=r._sent=r.arg;else if(r.method==="throw"){if(u===H)throw u=L,r.arg;r.dispatchException(r.arg)}else r.method==="return"&&r.abrupt("return",r.arg);u=K;var d=M(n,e,r);if(d.type==="normal"){if(u=r.done?L:Z,d.arg===m)continue;return{value:d.arg,done:r.done}}d.type==="throw"&&(u=L,r.method="throw",r.arg=d.arg)}}}function q(n,e){var r=e.method,u=n.iterator[r];if(u===i)return e.delegate=null,r==="throw"&&n.iterator.return&&(e.method="return",e.arg=i,q(n,e),e.method==="throw")||r!=="return"&&(e.method="throw",e.arg=new TypeError("The iterator does not provide a '"+r+"' method")),m;var l=M(u,n.iterator,e.arg);if(l.type==="throw")return e.method="throw",e.arg=l.arg,e.delegate=null,m;var c=l.arg;return c?c.done?(e[n.resultName]=c.value,e.next=n.nextLoc,e.method!=="return"&&(e.method="next",e.arg=i),e.delegate=null,m):c:(e.method="throw",e.arg=new TypeError("iterator result is not an object"),e.delegate=null,m)}function et(n){var e={tryLoc:n[0]};1 in n&&(e.catchLoc=n[1]),2 in n&&(e.finallyLoc=n[2],e.afterLoc=n[3]),this.tryEntries.push(e)}function D(n){var e=n.completion||{};e.type="normal",delete e.arg,n.completion=e}function C(n){this.tryEntries=[{tryLoc:"root"}],n.forEach(et,this),this.reset(!0)}function W(n){if(n||n===""){var e=n[w];if(e)return e.call(n);if(typeof n.next=="function")return n;if(!isNaN(n.length)){var r=-1,u=function l(){for(;++r<n.length;)if(y.call(n,r))return l.value=n[r],l.done=!1,l;return l.value=i,l.done=!0,l};return u.next=u}}throw new TypeError(o(n)+" is not iterable")}return P.prototype=_,v(S,"constructor",{value:_,configurable:!0}),v(_,"constructor",{value:P,configurable:!0}),P.displayName=g(_,G,"GeneratorFunction"),f.isGeneratorFunction=function(n){var e=typeof n=="function"&&n.constructor;return!!e&&(e===P||(e.displayName||e.name)==="GeneratorFunction")},f.mark=function(n){return Object.setPrototypeOf?Object.setPrototypeOf(n,_):(n.__proto__=_,g(n,G,"GeneratorFunction")),n.prototype=Object.create(S),n},f.awrap=function(n){return{__await:n}},U(A.prototype),g(A.prototype,k,function(){return this}),f.AsyncIterator=A,f.async=function(n,e,r,u,l){l===void 0&&(l=Promise);var c=new A(Y(n,e,r,u),l);return f.isGeneratorFunction(e)?c:c.next().then(function(s){return s.done?s.value:c.next()})},U(S),g(S,G,"Generator"),g(S,w,function(){return this}),g(S,"toString",function(){return"[object Generator]"}),f.keys=function(n){var e=Object(n),r=[];for(var u in e)r.push(u);return r.reverse(),function l(){for(;r.length;){var c=r.pop();if(c in e)return l.value=c,l.done=!1,l}return l.done=!0,l}},f.values=W,C.prototype={constructor:C,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=i,this.done=!1,this.delegate=null,this.method="next",this.arg=i,this.tryEntries.forEach(D),!e)for(var r in this)r.charAt(0)==="t"&&y.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=i)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if(e.type==="throw")throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function u(O,x){return s.type="throw",s.arg=e,r.next=O,x&&(r.method="next",r.arg=i),!!x}for(var l=this.tryEntries.length-1;l>=0;--l){var c=this.tryEntries[l],s=c.completion;if(c.tryLoc==="root")return u("end");if(c.tryLoc<=this.prev){var h=y.call(c,"catchLoc"),d=y.call(c,"finallyLoc");if(h&&d){if(this.prev<c.catchLoc)return u(c.catchLoc,!0);if(this.prev<c.finallyLoc)return u(c.finallyLoc)}else if(h){if(this.prev<c.catchLoc)return u(c.catchLoc,!0)}else{if(!d)throw Error("try statement without catch or finally");if(this.prev<c.finallyLoc)return u(c.finallyLoc)}}}},abrupt:function(e,r){for(var u=this.tryEntries.length-1;u>=0;--u){var l=this.tryEntries[u];if(l.tryLoc<=this.prev&&y.call(l,"finallyLoc")&&this.prev<l.finallyLoc){var c=l;break}}c&&(e==="break"||e==="continue")&&c.tryLoc<=r&&r<=c.finallyLoc&&(c=null);var s=c?c.completion:{};return s.type=e,s.arg=r,c?(this.method="next",this.next=c.finallyLoc,m):this.complete(s)},complete:function(e,r){if(e.type==="throw")throw e.arg;return e.type==="break"||e.type==="continue"?this.next=e.arg:e.type==="return"?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):e.type==="normal"&&r&&(this.next=r),m},finish:function(e){for(var r=this.tryEntries.length-1;r>=0;--r){var u=this.tryEntries[r];if(u.finallyLoc===e)return this.complete(u.completion,u.afterLoc),D(u),m}},catch:function(e){for(var r=this.tryEntries.length-1;r>=0;--r){var u=this.tryEntries[r];if(u.tryLoc===e){var l=u.completion;if(l.type==="throw"){var c=l.arg;D(u)}return c}}throw Error("illegal catch attempt")},delegateYield:function(e,r,u){return this.delegate={iterator:W(e),resultName:r,nextLoc:u},this.method==="next"&&(this.arg=i),m}},f}t.exports=a,t.exports.__esModule=!0,t.exports.default=t.exports})(V);var I=V.exports(),mt=I;try{regeneratorRuntime=I}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=I:Function("r","regeneratorRuntime = r")(I)}export{E as _,ht as a,dt as b,B as c,bt as d,ot as e,vt as f,gt as g,pt as h,yt as i,mt as r};
