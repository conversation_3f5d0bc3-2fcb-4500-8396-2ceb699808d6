/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 欠逃费订单统计分页-ParkUnderPaymentService（鲁鹏浩）
// POST /console/statistics/park/order/getPageFeeEvasion
// 接口ID：303949699
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-303949699
export const getPageFeeEvasion = (data) => {
  return $({
    url: '/console/statistics/park/order/getPageFeeEvasion',
    method: 'post',
    data
  });
};

// 导出欠逃费订单-ParkUnderPaymentService（鲁鹏浩）
// POST /console/statistics/park/order/exportPlaceOccupyExcel
// 接口ID：304731337
// 接口地址：https://app.apifox.com/link/project/5370326/apis/api-304731337
export const exportFeeEvasion = (data) => {
  return $({
    url: '/console/statistics/park/order/exportPlaceOccupyExcel',
    method: 'post',
    data
  });
};
