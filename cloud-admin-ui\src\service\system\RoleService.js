import * as role from '@/api/system/RoleApi';

/**
 * 角色
 */
export default {
  /**
   * 分页查询
   */
  pagingRoles(param) {
    return new Promise((resolve, reject) => {
      try {
        role.pagingRoles(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获取角色
   */
  findRoles(param) {
    return new Promise((resolve, reject) => {
      try {
        role.findRoles(param).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 添加角色
   */
  createRole(param) {
    return new Promise((resolve, reject) => {
      try {
        role.createRole(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改角色
   */
  updateRole(param) {
    return new Promise((resolve, reject) => {
      try {
        role.updateRole(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除角色
   */
  deleteRoles(param) {
    return new Promise((resolve, reject) => {
      try {
        role.deleteRoles(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用角色
   */
  enableRole(roleId) {
    return new Promise((resolve, reject) => {
      try {
        role.enableRole(roleId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 停用角色
   */
  disableRole(roleId) {
    return new Promise((resolve, reject) => {
      try {
        role.disableRole(roleId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 通过ID查询角色
   */
  getRoleById(data) {
    return new Promise((resolve, reject) => {
      try {
        role.getRoleById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 获得菜单树
   */
  findMenuTree(data) {
    return new Promise((resolve, reject) => {
      try {
        role.findMenuTree(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 保存配置权限
   */
  configPermissions(data) {
    return new Promise((resolve, reject) => {
      try {
        role.configPermissions(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 保存配置Api权限
   */
  configApiPermissions(data) {
    return new Promise((resolve, reject) => {
      try {
        role.configApiPermissions(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 角色列表
   */
  listRoles() {
    return new Promise((resolve, reject) => {
      try {
        role.listRoles().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 切换角色
   */
  switchRole(roleId) {
    return new Promise((resolve, reject) => {
      try {
        role.switchRole(roleId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
