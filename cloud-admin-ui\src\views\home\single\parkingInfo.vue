<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-25 14:55:53
 * @LastEditors: 达万安 段世煜
 * @Description: 停车统计
 * @FilePath: \cloud-admin-ui\src\views\home\single\parkingInfo.vue
-->
<template>
  <warp-card height="15%" title="全国车场客服电话">
    <div class="content">
      <div class="number item">车场编号：{{ infoData.id }}</div>
      <div class="number item">客服电话：17184037800</div>
      <!-- <div class="phone item">
        <div class="icon" />
        {{ infoData.phone }}
      </div> -->
    </div>
    <div class="code-container">
      <el-image :src="infoData.qrCode" class="qr-code" />
      <div class="text">微信小程序</div>
    </div>
  </warp-card>
</template>

<script setup>
import { reactive } from 'vue';
import warpCard from './components/warpCard.vue';

const infoData = reactive({
  id: '15269',
  phone: '010-0000-XXX转1',
  qrCode: new URL('../../../assets/singleImage/qrCode.png', import.meta.url).href
});
</script>

<style scoped lang="scss">
.content {
  display: flex;
  flex-direction: column;
  justify-content: space-around;
  height: 100%;
  margin-top: -10px;
  .item {
    color: #2d2d2d;
    font-size: 18px;
    font-weight: 400;
  }
  .phone {
    display: flex;
    align-items: center;
    color: #005bac;
    .icon {
      width: 18px;
      height: 18px;
      background-image: url('@/assets/icon/phone.png');
      background-size: 100% 100%;
      margin-right: 5px;
    }
  }
}
.code-container {
  position: absolute;
  top: 3%;
  right: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  .qr-code {
    width: 90px;
    height: 90px;
  }
  .text {
    text-align: center;
    color: #2d2d2d;
    font-size: 14px;
  }
}
</style>
