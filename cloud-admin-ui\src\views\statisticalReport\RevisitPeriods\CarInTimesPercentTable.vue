<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <!-- <div class="tooltuoclas">
          <el-tooltip>
            <template #content>
              目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />
              加此便于用户识别当前最新数据更新时间点
            </template>
            <el-icon><QuestionFilled /></el-icon>
          </el-tooltip>
          最近更新时间:{{ dataTime }}
        </div> -->
      </el-space>
    </div>
    <div ref="table" class="table-container">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column prop="statistics_date" label="日期" align="center" width="180" />
        <el-table-column prop="week_time" label="时间周期" align="center" min-width="180" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" min-width="180" />
        <el-table-column prop="region_name" label="大区" align="center" min-width="180" />
        <el-table-column prop="organizational_structure" label="城市分公司" align="center" min-width="180" />
        <!-- <el-table-column label="省市区" align="center" min-width="180">
          <template #default="scope">
            <span v-if="scope.row.province_name !== null && scope.row.province_name !== '' && scope.row.province_name !== undefined"
              >{{ scope.row.province_name }}/</span
            >
            <span v-if="scope.row.city_name !== null && scope.row.city_name !== '' && scope.row.city_name !== undefined"
              >{{ scope.row.city_name }}/</span
            >
            <span v-if="scope.row.district_name !== null && scope.row.district_name !== '' && scope.row.district_name !== undefined">{{
              scope.row.district_name
            }}</span>
          </template>
        </el-table-column> -->
        <el-table-column prop="province_name" label="所在省份" align="center" min-width="180" />
        <el-table-column prop="city_name" label="所在城市" align="center" min-width="180" />
        <el-table-column prop="district_name" label="所在区域" align="center" min-width="180" />
        <el-table-column prop="temporary_count" label="临停车次" align="center" min-width="180" />
        <el-table-column label="统计范围" align="center">
          <el-table-column prop="first_count" label="初次入场" align="center" min-width="180" />
          <el-table-column prop="less_one" label="小于24小时" align="center" min-width="180" />
          <el-table-column prop="one_to_three" label="1-3天" align="center" min-width="180" />
          <el-table-column prop="four_to_seven" label="4-7天" align="center" min-width="180" />
          <el-table-column prop="eight_to_fifteen" label="8-15天" align="center" min-width="210" />
          <el-table-column prop="sixteen_to_thirty" label="16-30天" align="center" min-width="210" />
          <el-table-column prop="thirty_one_to_sixty" label="31-60天" align="center" min-width="210" />
          <el-table-column prop="sixty_one_to_ninety" label="61-90天" align="center" min-width="210" />
          <el-table-column prop="ninety_one_to_one_hundred_eighty" label="90-180天" align="center" min-width="210" />
          <el-table-column prop="more_than_one_hundred_eighty_one" label="181天以上" align="center" min-width="210" />
        </el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[10, 30, 100, 200, 300, 400]"
        :background="background"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="CarInTimesPercentTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import RevisitPeriodsService from '@/service/statisticalReport/RevisitPeriodsService';
import { getNewUpdateTmie } from '@/api/statisticalReport/RevisitPeriodsApi';
const background = ref(true);
const tableData = ref([]);
const loading = ref(false);
const dataTime = ref('暂无数据');
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
//获取最近更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(20);
    if (rudata.code == 200) {
      dataTime.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};

onMounted(() => {
  // getList(data.queryParams);
  // getNewUpdateTmieData();
});
//limit 改变
const handleSizeChange = (size) => {
  console.log('size', size);
  data.queryParams.limit = size;
  getList({});
};
//page改变
const handleCurrentChange = (page) => {
  console.log('page', page);
  data.queryParams.page = page;
  getList({});
};
const getList = (params) => {
  loading.value = true;
  data.queryParams = { ...data.queryParams, ...params };
  console.log('params', data.queryParams);
  RevisitPeriodsService.getRevisitPeriods(data.queryParams).then((response) => {
    if (response.success === true) {
      console.log('response', response.data.data.rows);
      tableData.value = response.data.data.rows;
      total.value = Number(response.data.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.el-table th.el-table__cell > .cell {
  // white-space: pre;
  white-space: pre-wrap; // 也行。
}

.pagination {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.tooltuoclas {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #00000080;
  font-size: 12px;
  margin-right: 20px;
}
</style>
