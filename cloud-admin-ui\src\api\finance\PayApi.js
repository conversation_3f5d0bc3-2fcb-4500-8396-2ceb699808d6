/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询微信支付渠道
export const pageWxPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/pageWxPayChannel',
    method: 'post',
    data
  });
};

//新增微信支付渠道
export const createWxPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/createWxPayChannel',
    method: 'post',
    data
  });
};

//修改微信支付渠道
export const updateWxPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/updateWxPayChannel',
    method: 'post',
    data
  });
};

//获取微信支付渠道详情
export const getWxPayChannelDetail = (id) => {
  return $({
    url: '/console/pay/channel/getWxPayChannelDetail/' + id,
    method: 'post'
  });
};

//修改微信支付渠道状态
export const updateWxPayChannelState = (data) => {
  return $({
    url: '/console/pay/channel/updateWxPayChannelState',
    method: 'post',
    data
  });
};

//授权微信支付渠道到车场
export const authWxPayChannelPark = (data) => {
  return $({
    url: '/console/pay/channel/authWxPayChannelPark',
    method: 'post',
    data
  });
};

//支付宝

// 分页查询支付宝支付渠道
export const pageAliPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/pageAliPayChannel',
    method: 'post',
    data
  });
};

//新增支付宝支付渠道
export const createAliPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/createAliPayChannel',
    method: 'post',
    data
  });
};

//修改支付宝支付渠道
export const updateAliPayChannel = (data) => {
  return $({
    url: '/console/pay/channel/updateAliPayChannel',
    method: 'post',
    data
  });
};

// 获取支付宝支付渠道详情
export const getAliPayChannelDetail = (id) => {
  return $({
    url: '/console/pay/channel/getAliPayChannelDetail/' + id,
    method: 'post'
  });
};

//修改支付宝支付渠道状态
export const updateAliPayChannelState = (data) => {
  return $({
    url: '/console/pay/channel/updateAliPayChannelState',
    method: 'post',
    data
  });
};

//授权支付宝支付渠道到车场
export const authAliPayChannelPark = (data) => {
  return $({
    url: '/console/pay/channel/authAliPayChannelPark',
    method: 'post',
    data
  });
};

//分页查询支付宝渠道车场
export const pageAliPayChannelPark = (data) => {
  return $({
    url: '/console/pay/channel/pageAliPayChannelPark',
    method: 'post',
    data
  });
};

//分页查询微信支付渠道车场
export const pageWxPayChannelPark = (data) => {
  return $({
    url: '/console/pay/channel/pageWxPayChannelPark',
    method: 'post',
    data
  });
};
