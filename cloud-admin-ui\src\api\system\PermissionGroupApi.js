import $ from '@/utils/axios';

/**
 * 权限组接口层
 */

// 分页查询权限组
export const pagingPermissionGroup = (data) => {
  return $({
    url: '/console/permissionGroup/pagingPermissionGroup',
    method: 'post',
    data
  });
};
// 保存权限组
export const createPermissionGroup = (data) => {
  return $({
    url: '/console/permissionGroup/createPermissionGroup',
    method: 'post',
    data
  });
};
//根据权限组ID查询权限信息
export const getPermissionById = (data) => {
  return $({
    url: '/console/permissionGroup/getPermissionById/',
    method: 'post',
    data
  })
}

// 修改权限组
export const updatePermissionGroup = (data) => {
  return $({
    url: '/console/permissionGroup/updatePermissionGroup',
    method: 'post',
    data
  });
};

// 删除权限组
export const deletePermissionGroup = (id) => {
  return $({
    url: '/console/permissionGroup/deletePermissionGroup/' + id,
    method: 'post'
  });
};

// 权限组列表
export const permissionGroupList = (data) => {
  return $({
    url: '/console/permissionGroup/permissionGroupList',
    method: 'get',
    data
  });
};
