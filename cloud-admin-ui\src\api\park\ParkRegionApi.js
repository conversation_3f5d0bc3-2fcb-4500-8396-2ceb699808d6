/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找车场信息
export const pagingParkRegions = (data) => {
  return $({
    url: '/console/park/region/pagingParkRegions',
    method: 'post',
    data
  });
};

//子场列表;
export const listParkRegion = (parkId) => {
  return $({
    url: '/console/park/region/listParkRegion/' + parkId,
    method: 'get'
  });
};

// 新增车场信息
export const createParkRegion = (data) => {
  return $({
    url: '/console/park/region/createParkRegion',
    method: 'post',
    data
  });
};

// 车场信息修改
export const updateParkRegion = (data) => {
  return $({
    url: '/console/park/region/updateParkRegion',
    method: 'post',
    data
  });
};

// 删除子场
export const deleteParkRegion = (id) => {
  return $({
    url: '/console/park/region/deleteParkRegion/' + id,
    method: 'post'
  });
};
