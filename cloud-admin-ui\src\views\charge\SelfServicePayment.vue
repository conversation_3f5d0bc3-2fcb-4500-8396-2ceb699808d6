<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="缴费机管理" name="paymentMachine">
        <payment-machine ref="paymentMachineRef" @change-tab="changeTab" />
      </el-tab-pane>
      <el-tab-pane label="缴费机提现记录" name="cashRecord">
        <cash-record ref="cashRecordRef" @change-tab="changeTab" />
      </el-tab-pane>
      <el-tab-pane label="现金缴费记录" name="paymentRecord">
        <payment-record ref="paymentRecordRef" :searchParam="paymentRecordSearchParams" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="ActionLog" setup>
import paymentMachine from './selfServicePayment/paymentMachine.vue';
import cashRecord from './selfServicePayment/cashRecord.vue';
import paymentRecord from './selfServicePayment/paymentRecord.vue';

import { ref, reactive, onActivated, nextTick } from 'vue';

const activeName = ref('paymentMachine');
const paymentMachineRef = ref(null);
const cashRecordRef = ref(null);
const paymentRecordRef = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const paymentRecordSearchParams = ref({});

onActivated(() => {
  // 重新加载打开的tab
  activeTab(activeName.value);
});

const handleClick = (tab) => {
  activeTab(tab.props.name);
};

const activeTab = (name) => {
  if (name === 'paymentMachine') {
    paymentRecordSearchParams.value = {};
    paymentMachineRef.value.handleDataSearch(params);
  }
  if (name === 'cashRecord') {
    paymentRecordSearchParams.value = {};
    cashRecordRef.value.handleDataSearch(params);
  }
  if (name === 'paymentRecord') {
    paymentRecordRef.value.handleDataSearch(params);
  }
};

const changeTab = (params) => {
  console.log('changeTab', params);
  paymentRecordSearchParams.value = params.searchParam || {};
  nextTick(() => {
    activeName.value = params.tab;
    activeTab(activeName.value);
  });
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
