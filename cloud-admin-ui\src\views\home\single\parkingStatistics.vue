<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-25 16:10:06
 * @LastEditors: 达万安 段世煜
 * @Description: 停车统计
 * @FilePath: \cloud-admin-ui\src\views\home\single\parkingStatistics.vue
-->
<template>
  <warp-card height="35%" title="停车统计">
    <div class="content">
      <div class="left item">
        <pie-chart ref="pieChartRef" :center="['50%', '45%']" :radius="['40%', '60%']" labelFormatter="{c}辆"></pie-chart>
      </div>
      <div class="right item">
        <el-table :data="[staticData]" border style="width: 100%">
          <el-table-column prop="all" label="总车位" align="center"></el-table-column>
          <el-table-column prop="parking" label="临停在场车辆" align="center"></el-table-column>
          <el-table-column prop="rent" label="长租在场车辆" align="center"></el-table-column>
        </el-table>
        <el-table :data="[staticData]" border style="width: 100%">
          <el-table-column prop="empty" label="空闲车位" align="center"></el-table-column>
          <el-table-column prop="park_duration" label="平均停车时长" align="center"></el-table-column>
          <el-table-column prop="space_value" label="单车位价值" align="center"></el-table-column>
        </el-table>
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import { reactive, ref, computed, nextTick } from 'vue';
import warpCard from './components/warpCard.vue';
import pieChart from './components/pieChart.vue';

import { fetchStaticInfo } from '@/api/home/<USER>';

const pieChartRef = ref(null);
const data = computed(() => {
  return [
    { name: '临停在场', value: staticData.parking },
    { name: '长租在场', value: staticData.rent },
    { name: '空闲车位', value: staticData.empty }
  ];
});
const staticData = reactive({
  all: 0,
  parking: 0,
  rent: 0,
  empty: 0,
  park_duration: 0,
  space_value: 0
});

const fetchData = async (params) => {
  try {
    const { data } = await fetchStaticInfo(params);
    staticData.all = data.space_cnt || 0;
    staticData.parking = data.parking_plate_cnt || 0;
    staticData.rent = data.rent_plate_cnt || 0;
    staticData.park_duration = data.park_duration || 0;
    staticData.space_value = data.space_value || 0;
    staticData.empty = staticData.all - (staticData.parking + staticData.rent);
    pieChartRef.value.setData(data.value);
  } finally {
    nextTick(() => {
      pieChartRef.value && pieChartRef.value.setData(data.value);
    });
  }
};

defineExpose({
  fetchData
});
</script>

<style scoped lang="scss">
.content {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: space-between;
  .item {
    width: 45%;
  }
  .right {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 60%;
  }
}
</style>
