<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space> </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column prop="park_name" label="停车场名称" align="center" width="200px" />
        <el-table-column prop="code" label="车位编号" align="center" width="100px" />
        <el-table-column prop="rule_name" label="规则名称" align="center" width="100px" />
        <el-table-column prop="long_rent_type_desc" label="长租类型" align="center" width="100px" />
        <el-table-column prop="product_name" label="产品名称" align="center" width="100px" />
        <el-table-column prop="product_price" label="产品金额" align="center" width="100px" />
        <el-table-column label="长租有效期" align="center" width="200px">
          <template #default="scope">
            <span>{{ scope.row.valid_start_time }}~{{ scope.row.valid_end_time }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="pay_state_desc" label="支付状态" align="center" width="100px" />
        <el-table-column prop="rent_state_desc" label="长租状态" align="center" width="100px" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="mbr_member_nickname" label="车主姓名" align="center" width="100px" />
        <el-table-column prop="mbr_member_mobile" label="手机号" align="center" />
        <el-table-column prop="renew_state_desc" label="是否续费" align="center" width="100px" />
        <el-table-column prop="refund_state_desc" label="退款状态" align="center" width="100px" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="LongRent" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import invoiceService from '@/service/invoice/InvoiceService';

const park_id = ref('');
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    id: undefined,
    mobile: undefined,
    page: 1,
    limit: 30
  }
});

// 关联长租费用订单的分页数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  park_id.value = params.park_id;
  data.queryParams = params;
  invoiceService.relativeRentOrderPaging(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
