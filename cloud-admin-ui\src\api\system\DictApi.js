/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找字典分类
export const pagingDictType = (data) => {
  return $({
    url: '/console/dict/pagingDictTypes',
    method: 'post',
    data
  });
};

// 新建字典分类
export const createDictType = (data) => {
  return $({
    url: '/console/dict/createDictType',
    method: 'post',
    data
  });
};

// 修改字典分类
export const updateDictType = (data) => {
  return $({
    url: '/console/dict/updateDictType',
    method: 'post',
    data
  });
};

// 删除字典分类
export const deleteDictType = (id) => {
  return $({
    url: '/console/dict/deleteDictType/' + id,
    method: 'post'
  });
};

// 分页查找字典
export const pagingDict = (data) => {
  return $({
    url: '/console/dict/pagingDicts',
    method: 'post',
    data
  });
};

// 新建字典
export const createDict = (data) => {
  return $({
    url: '/console/dict/createDictData',
    method: 'post',
    data
  });
};

// 修改字典
export const updateDict = (data) => {
  return $({
    url: '/console/dict/updateDictData',
    method: 'post',
    data
  });
};

// 删除字典
export const deleteDict = (data) => {
  return $({
    url: '/console/dict/deleteDictData',
    method: 'post',
    data
  });
};

// 查询单条字典
export const getDictById = (id) => {
  return $({
    url: '/console/dict/getDictById/' + id,
    method: 'post'
  });
};

// 查询字典分类
export const getDictType = () => {
  return $({
    url: '/console/dict/getDictType',
    method: 'post'
  });
};

// 查询到期时间字典
export const getExpirationTime = () => {
  return $({
    url: '/console/park/rent/space/apply/getExpirationTime',
    method: 'post'
  });
};

// 通过code查询字典列表
export const getDictsList = (code) => {
  return $({
    url: '/console/dict/getDictsList/' + code,
    method: 'post'
  });
};
