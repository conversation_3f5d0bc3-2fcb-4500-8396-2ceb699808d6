<template>
  <div>
    <el-row :gutter="10" style="overflow: hidden">
      <el-col :span="16">
        <div>
          <p class="tips">选择</p>
          <div style="border: 1px solid #dcdfe6; padding: 20px; margin-top: 10px; height: 550px">
            <el-input v-model="filterText" @input="changeText" placeholder="输入车场名称进行过滤" clearable />
            <div style="height: 490px; overflow: auto; margin-top: 10px">
              <el-table
                ref="multipleTable"
                border
                :header-cell-style="{ background: '#f9f9f9', color: '#606266' }"
                :data="tableData"
                v-loading="loading"
                :row-class-name="tableRowClassName"
                tooltip-effect="dark"
                style="width: 100%"
                @row-click="handleCurrentChange"
                @selection-change="handleCheckboxChange"
              >
                <el-table-column prop="name" label="车场名称" align="center" />
                <el-table-column prop="state_display" label="车场状态" align="center" />
                <el-table-column prop="city_name" label="所在城市" align="center" />
              </el-table>
              <el-pagination
                background
                :current-page="data.queryParams.page"
                :page-sizes="[10, 30, 50, 100]"
                :page-size="data.queryParams.limit"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                class="table-pagination"
                @size-change="handleSizeChange"
                @current-change="handleCurrentPageChange"
              />
            </div>
          </div>
        </div>
      </el-col>
      <el-col :span="8">
        <div class="grid-content">
          <p class="tips">
            <span>已选</span>
            <el-button class="reset-checked" link type="primary" @click="resetChecked"> 清空 </el-button>
          </p>
          <div style="border: 1px solid #dcdfe6; padding: 20px; margin-top: 10px; height: 550px">
            <ul>
              <li v-for="(item, key) in orignalData" :key="key" :data-id="item.park_id">
                <span>{{ item.park_name }}</span>
                <el-icon :size="16" class="icon" @click="removeItem(item.park_id)"><RemoveFilled /></el-icon>
              </li>
            </ul>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="margin-top: 10px; text-align: right">
          <el-button @click="cancel"> 取 消 </el-button>
          <el-button type="primary" @click="submitTableData"> 确 定 </el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script name="ParkFindBack" setup>
import employeeService from '@/service/system/EmployeeService';
import { RemoveFilled } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { ref, getCurrentInstance, reactive, onMounted } from 'vue';
const props = defineProps({
  title: {
    type: String,
    default: '表格型查找带回'
  },
  park_id: {
    type: String,
    required: true
  },
  park_name: {
    type: String,
    required: true
  },
  mode: {
    type: String,
    required: true
  }
});
const { proxy } = getCurrentInstance();
const filterText = ref('');
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const multipleSelection = ref([]);
const orignalData = ref([]);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30,
    name: ''
  }
});

onMounted(() => {
  // 加载已关联车场列表
  loadSiteList();
  // 加载可关联车场列表
  loadCanSiteList(data.queryParams);
  for (let i = 0; i < orignalData.value.length; i++) {
    for (let j = 0; j < tableData.value.length; j++) {
      if (orignalData.value[i].park_id === tableData.value[j].park_id) {
        proxy.$refs.multipleTable.toggleRowSelection(tableData[j].value, true);
      }
    }
  }
});

const changeText = (val) => {
  if (val !== undefined && val.trim() !== '') {
    data.queryParams.name = val;
    data.queryParams.page = 1;
  } else {
    delete data.queryParams.name;
  }
  loadCanSiteList(data.queryParams);
};
const loadSiteList = () => {
  if (props.mode == 'add') {
    orignalData.value = [];
    multipleSelection.value = [];
  } else {
    const parkInfo = {
      park_id: props.park_id,
      park_name: props.park_name
    };
    orignalData.value.push(parkInfo);
    // 初始化右侧列表的值
    multipleSelection.value.push(parkInfo);
  }
};
const loadCanSiteList = (params) => {
  loading.value = false;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  employeeService.getParkList(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const tableRowClassName = ({ row, rowIndex }) => {
  for (let i = 0; i < orignalData.value.length; i++) {
    if (orignalData.value[i].park_id == row.id) {
      return 'success-row';
    }
  }
  return '';
};
const resetChecked = () => {
  if (multipleSelection.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning'
    });
  } else {
    ElMessageBox.confirm('确定要清空已选项吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
      .then(() => {
        multipleSelection.value = [];
        orignalData.value = [];
        proxy.$refs.multipleTable.clearSelection();
      })
      .catch(() => {});
  }
};
// 左侧点选
const handleCurrentChange = (row, event, column) => {
  const res = orignalData.value.some((item) => {
    if (item.park_id === row.id) {
      return true;
    }
  });
  if (!res) {
    let arr = {
      park_id: undefined,
      park_name: undefined
    };
    arr.park_id = row.id;
    arr.park_name = row.name;
    proxy.$refs.multipleTable.toggleRowSelection(row, true); // 点击选中
    multipleSelection.value.push(row.id);
    orignalData.value = [];
    orignalData.value.push(arr);
  } else {
    proxy.$refs.multipleTable.toggleRowSelection(row, false); // 点击选中
    for (let i = 0; i < multipleSelection.value.length; i++) {
      if (multipleSelection.value[i] === row.id) {
        multipleSelection.value.splice(i, 1);
      }
    }
    for (let i = 0; i < orignalData.value.length; i++) {
      if (orignalData.value[i].park_id === row.id) {
        orignalData.value.splice(i, 1);
      }
    }
  }
};
const handleCheckboxChange = (val) => {
  multipleSelection.value = val;
};
// 右侧删除
const removeItem = (id) => {
  for (let i = 0; i < orignalData.value.length; i++) {
    if (orignalData.value[i].park_id == id) {
      orignalData.value.splice(i, 1);
    }
  }
};
// 关闭弹框
const cancel = () => {
  proxy.$emit('authCharge', 'false');
};
// 关闭弹框渲染input
const submitTableData = () => {
  if (orignalData.value.length < 0 || orignalData.value.length === 0) {
    ElMessage({
      message: '当前暂无已选项，请先添加！',
      type: 'warning'
    });
    return false;
  }
  orignalData.value[0].mode = props.mode;
  proxy.$emit('renderTableInput', orignalData.value);
  proxy.$emit('authCharge', 'false');
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  loadCanSiteList(data.queryParams);
};
const handleCurrentPageChange = (val) => {
  data.queryParams.page = val;
  loadCanSiteList(data.queryParams);
};
</script>
<style scoped lang="scss">
.grid-conten {
  padding: 0 10px;
}

.tips {
  display: flex;
  justify-content: space-between;
  margin-top: 0;
  overflow: hidden;
}

.reset-checked {
  color: #1e9fff;
  cursor: pointer;
  padding: 0;
}

.reset-checked:hover {
  color: #ff6c65;
}

ul {
  list-style: none;
  padding: 0;
  margin: 0;
  height: 564px;
  overflow: auto;
}

.icon {
  color: #999;
  cursor: pointer;
  margin-top: 5px;
}
.icon:hover {
  color: #ff6c65;
  text-shadow: #ff6c65 0px 0px 2px;
}
ul li {
  display: flex;
  justify-content: space-between;
  line-height: 28px;
  padding-left: 10px;
}

ul li:hover {
  background-color: #f5f7fa;
}
.el-table .success-row {
  --el-table-tr-bg-color: var(--el-color-success-light-9);
}
</style>
