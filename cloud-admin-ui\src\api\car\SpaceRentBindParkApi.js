/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询长租车申请
export const pagingRentSpaceBindParks = (data) => {
  return $({
    url: '/console/park/rent/space/bind/park/pagingRentSpaceBindParks',
    method: 'post',
    data
  });
};

// 保存长租车关联车场
export const saveRentSpaceBindPark = (data) => {
  return $({
    url: '/console/park/rent/space/bind/park/saveRentSpaceBindPark',
    method: 'post',
    data
  });
};

// 获得车场列表 --去除当前车场
export const listParkNotCurrentPark = (data) => {
  return $({
    url: '/console/park/park/listParkNotCurrentPark',
    method: 'post',
    data
  });
};

// 获取长租申请已关联的车场
export const ListRentSpaceBindPark = (data) => {
  return $({
    url: '/console/park/rent/space/bind/park/ListRentSpaceBindPark',
    method: 'post',
    data
  });
};
