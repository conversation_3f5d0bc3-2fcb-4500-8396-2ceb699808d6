<template>
  <div class="container">
    <coupon-details-search @form-search="searchCouponDetails" @reset="resetParamsAndData" />
    <coupon-details-table ref="table" />
  </div>
</template>

<script setup name="CouponDetails">
import CouponDetailsSearch from './coupondetails/CouponDetailsSearch.vue';
import CouponDetailsTable from './coupondetails/CouponDetailsTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

const searchCouponDetails = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
