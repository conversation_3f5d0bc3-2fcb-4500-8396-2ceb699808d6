<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 08:40:14
 * @LastEditTime: 2024-04-01 13:39:25
 * @LastEditors: 达万安 段世煜
 * @Description: 集团版首页
 * @FilePath: \cloud-admin-ui\src\views\home\group\groupIndex.vue
-->
<template>
  <div class="main-container group-empty-warp" id="fullScreenContainer">
    <div class="header">
      <div class="left">
        <div class="logo"></div>
        <div class="time">{{ time }}</div>
      </div>
      <div class="title">惠达云停车数据平台</div>
      <full-screen-button />
    </div>
    <div class="main">
      <div class="row">
        <ranking-static ref="rankingStaticRef" />
        <map-info @filter="handleFilter" ref="mapDataRef" :data="deptData" />
        <div class="column">
          <parking-pay-pie ref="parkingPayPieRef" />
          <traffic-efficiency ref="trafficEfficiencyRef" />
        </div>
      </div>
      <div class="row">
        <rent-info ref="rentInfoRef" />
        <parking-info ref="parkingInfoRef" />
        <pay-info ref="payInfoRef" />
      </div>
      <div class="row">
        <in-out ref="inoutRef" />
        <parking-duration ref="parkingDurationRef" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import { dayjs } from 'element-plus';

import { departmentTree } from '@/api/system/DepartmentApi';

import fullScreenButton from '@/components/fullScreenButton.vue';
import rankingStatic from './rankingStatic.vue';
import inOut from './inOut.vue';
import mapInfo from './mapInfo.vue';
import parkingDuration from './parkingDuration.vue';
import parkingInfo from './parkingInfo.vue';
import trafficEfficiency from './trafficEfficiency.vue';
import parkingPayPie from './parkingPayPie.vue';
import payInfo from './payInfo.vue';
import rentInfo from './rentInfo.vue';

const time = ref('----年--月--日 -- --:--:--');
let timer = null;
onMounted(() => {
  starTimer();
  fetchDept();
  window.addEventListener('resize', handleResize);
  handleResize();
});
onUnmounted(() => {
  destoryTimer();
});

const starTimer = () => {
  destoryTimer();
  time.value = dayjs().format('YYYY年MM月DD日 dddd HH:mm:ss');
  timer = setInterval(() => {
    time.value = dayjs().format('YYYY年MM月DD日 dddd HH:mm:ss');
  }, 1000);
};

const destoryTimer = () => {
  clearInterval(timer);
  timer = null;
};

const deptData = ref([]);
const deptId = ref('');
const fetchDept = async () => {
  const { data } = await departmentTree();
  deptId.value = data[0].children[0].id;
  deptData.value = data[0].children[0].children;
  mapDataRef.value.initVisaual();
};

const rankingStaticRef = ref(null);
const parkingPayPieRef = ref(null);
const trafficEfficiencyRef = ref(null);
const rentInfoRef = ref(null);
const parkingInfoRef = ref(null);
const payInfoRef = ref(null);
const inoutRef = ref(null);
const parkingDurationRef = ref(null);
const mapDataRef = ref(null);

/**
 * @description 触发筛选事件
 */
const handleFilter = (val) => {
  console.log('val', val);
  const useKey = ['org_id', 'time_unit', 'start_date', 'end_date', 'page', 'limit'];
  const searchParmas = {
    org_id: deptId.value,
    page: 1,
    limit: 2000
  };
  for (const key in val) {
    if (useKey.includes(key) && val[key]) {
      searchParmas[key] = val[key];
    }
  }
  rankingStaticRef.value.fetchData(searchParmas);
  rentInfoRef.value.fetchData(searchParmas);
  inoutRef.value.fetchData(searchParmas);
  parkingInfoRef.value.fetchData(searchParmas);
  parkingDurationRef.value.fetchData(searchParmas);
  parkingPayPieRef.value.fetchData(searchParmas);
  trafficEfficiencyRef.value.fetchData(searchParmas);
  payInfoRef.value.fetchData(searchParmas);
  mapDataRef.value.fetchData(searchParmas);
};

const ratio = ref('scale(0.883)');
const handleResize = () => {
  const targetX = 1920;
  // 获取html的宽度和高度（不包含滚动条）
  const currentX = document.querySelector('.frame-content').clientWidth;
  // 计算缩放比例
  const ratioNumber = currentX / targetX;
  ratio.value = `scale(${ratioNumber})`;
};
</script>

<style scoped lang="scss">
.main-container {
  width: 1920px;
  height: 1080px;
  transform: v-bind(ratio);
  transform-origin: left top;
  background-image: url('@/assets/groupImage/bg.png');
  background-size: 100% 100%;
  font-family: 'SC-Medium';
  z-index: 1;
  .header {
    width: 100%;
    height: 60px;
    background-image: url('@/assets/groupImage/header-bg.png');
    background-size: 100% 100%;
    position: relative;
    display: flex;
    justify-content: center;
    width: 100%;
    .left {
      position: absolute;
      left: 30px;
      display: flex;
      height: 100%;
      .logo {
        width: 40px;
        height: 30px;
        background-image: url('@/assets/groupImage/logo.png');
        background-size: 100% 100%;
        margin-top: 10px;
      }
      .time {
        font-size: 20px;
        color: #c7e0ff;
        margin: 5px 0 0 20px;
        height: 28px;
        line-height: 28px;
      }
    }
    .title {
      width: 432px;
      font-weight: 550;
      font-size: 42px;
      color: #fff;
      text-align: center;
      font-style: normal;
      font-family: 'RuiZi';
    }
  }
  ::-webkit-scrollbar-track {
    background-color: #000; /* 设置滑道背景色 */
  }
  ::-webkit-scrollbar-thumb {
    background-color: #30bfff;
  }
  .main {
    box-sizing: border-box;
    padding: 0 20px;
    height: calc(100% - 60px);
    overflow: auto;
    .row {
      margin-top: 10px;
      display: flex;
      justify-content: space-between;
      .column {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
    }
  }
  :deep(.el-empty__description p) {
    color: #90c2ff;
  }
}
</style>
