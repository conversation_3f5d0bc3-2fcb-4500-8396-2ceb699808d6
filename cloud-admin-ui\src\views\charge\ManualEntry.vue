<template>
  <div class="container">
    <manual-entry-search @form-search="searchManualEntryList" @reset="resetParamsAndData" />
    <manual-entry-table ref="table" />
  </div>
</template>

<script setup name="ManualEntry">
import ManualEntrySearch from './manualEntry/ManualEntrySearch.vue';
import ManualEntryTable from './manualEntry/ManualEntryTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageManualEntry = (queryParams) => {
  table.value.getList(queryParams);
};

const searchManualEntryList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageManualEntry
});
</script>
