<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-28 16:18:43
 * @LastEditors: 达万安 段世煜
 * @Description: 车场排名统计
 * @FilePath: \cloud-admin-ui\src\views\home\group\rankingStatic.vue
-->
<template>
  <warp-card size="middle" title="车场排名统计">
    <tab-button :options="tabOptions" v-model="activeTable" @change="handleTabChange" />
    <el-table :data="tableData" height="400" stripe style="width: 100%; margin-top: 10px" v-if="tableData && tableData.length">
      <el-table-column label="排名" width="60">
        <template #default="scope">
          <div class="index-square" :class="`index_${scope.$index + 1}`">{{ scope.$index + 1 }}</div>
        </template>
      </el-table-column>
      <el-table-column
        prop="organizational_structure"
        v-if="activeTable === '指标达成率'"
        label="广场名称"
        width="180px"
        :formatter="formatterValue"
        show-overflow-tooltip
      />
      <el-table-column prop="org_department_name" v-else label="广场名称" width="180px" :formatter="formatterValue" show-overflow-tooltip />
      <el-table-column prop="city_name" label="地区" :formatter="formatterValue" />
      <el-table-column prop="trade_money" label="交易总金额(元)" width="120" v-if="activeTable === '交易总额(临停)'" :formatter="formatterValue" />
      <el-table-column prop="trade_num" label="订单笔数(笔)" v-if="activeTable === '订单笔数'" :formatter="formatterValue" />
      <el-table-column prop="space_value" label="单车位价值(元)" width="120" v-if="activeTable === '单车位价值'" :formatter="formatterValue" />
      <el-table-column
        prop="electronic_payment_money_proportion"
        label="指标达成率"
        v-if="activeTable === '指标达成率'"
        :formatter="formatterValuePercent"
      />
    </el-table>
    <el-empty :image="emptyImage" :image-size="400" description="暂无数据" v-else />
  </warp-card>
</template>

<script setup>
import { ref } from 'vue';

import { fetchRankingData } from '@/api/home/<USER>';

import warpCard from './components/warpCard.vue';
import tabButton from './components/tabButton.vue';

const emptyImage = new URL('../../../assets/groupImage/empty/unList.svg', import.meta.url).href;
const tabOptions = [
  {
    value: '交易总额(临停)',
    label: '交易总额(临停)'
  },
  {
    value: '订单笔数',
    label: '订单笔数'
  },
  {
    value: '单车位价值',
    label: '单车位价值'
  }
  // {
  //   value: '指标达成率',
  //   label: '指标达成率'
  // }
];
const activeTable = ref('交易总额(临停)');
const globalParams = ref({});
const handleTabChange = (val) => {
  fetchData();
};

const formatterValue = (row, column, cellValue) => {
  return cellValue || cellValue === 0 ? cellValue : '- -';
};
const formatterValuePercent = (row, column, cellValue) => {
  return cellValue || cellValue === 0 ? (cellValue * 100).toFixed(1) + '%' : '- -';
};

const tableData = ref([]);
const fetchData = async (val) => {
  if (val) globalParams.value = val;
  tableData.value = [];
  const params = {
    ...globalParams.value,
    page: 1,
    limit: 25
  };
  const { data } = await fetchRankingData(activeTable.value, params);
  //TODO 暂无此项 假数据
  if (activeTable.value === '指标达成率') {
    data.rows.forEach(
      (item) =>
        (item.electronic_payment_money_proportion =
          item.electronic_payment_money_proportion * 1.1 < 1 ? item.electronic_payment_money_proportion * 1.1 : 1)
    );
    tableData.value = data.rows;
    return;
  }
  tableData.value = data.rows || data;
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped>
:deep(.el-table__cell) {
  text-align: left !important;
}
:deep(.el-table__header .cell) {
  font-size: 14px;
  font-weight: 400;
  color: #59baf2;
}
:deep(.cell) {
  color: #fff;
}
:deep(.el-table thead tr) {
  background: none;
}
:deep(.el-table thead .el-table__cell) {
  background: none;
}
:deep(.el-table .el-table__row--striped) {
  background: none;
}
:deep(.el-table) {
  --el-table-border: none;
  --el-fill-color-blank: none;
  --el-fill-color-lighter: none !important;
  --el-table-tr-bg-color: #0c3773;
  --el-table-row-hover-bg-color: #2379ff50;
  --el-border-color-lighter: none;
}
:deep {
  .el-table .el-table__cell {
    padding: 6px 0;
  }
}
.index-square {
  width: 24px;
  height: 24px;
  line-height: 24px;
  text-align: center;
  border-radius: 5px;
  background: #2379ff;
}
.index_1 {
  background: #ff4b01;
}
.index_2 {
  background: #ff7b00;
}
.index_3 {
  background: #ffd000;
}
</style>
