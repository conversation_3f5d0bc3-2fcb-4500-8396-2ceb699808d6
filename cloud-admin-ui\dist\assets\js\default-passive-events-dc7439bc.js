var n,u=["scroll","wheel","touchstart","touchmove","touchenter","touchend","touchleave","mouseout","mouseleave","mouseup","mousedown","mousemove","mouseenter","mousewheel","mouseover"];if(function(){var t=!1;try{var o=Object.defineProperty({},"passive",{get:function(){t=!0}});window.addEventListener("test",null,o),window.removeEventListener("test",null,o)}catch{}return t}()){var c=EventTarget.prototype.addEventListener;n=c,EventTarget.prototype.addEventListener=function(t,o,e){var a,i=typeof e=="object"&&e!==null,v=i?e.capture:e;(e=i?function(r){var s=Object.getOwnPropertyDescriptor(r,"passive");return s&&s.writable!==!0&&s.set===void 0?Object.assign({},r):r}(e):{}).passive=(a=e.passive)!==void 0?a:u.indexOf(t)!==-1&&!0,e.capture=v!==void 0&&v,n.call(this,t,o,e)},EventTarget.prototype.addEventListener._original=n}
