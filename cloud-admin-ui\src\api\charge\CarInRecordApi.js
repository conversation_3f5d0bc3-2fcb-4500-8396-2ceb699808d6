/* jshint esversion: 6 */
import $ from '@/utils/axios';
// 分页查询入场记录
export const pagingCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/pagingInRecords',
    method: 'post',
    data
  });
};

// 批量删除入场记录
export const deleteCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/deleteInRecords',
    method: 'post',
    data
  });
};
export const submitAuditStayCarDelApply = (data) => {
  return $({
    url: '/console/fee/stayCarDelApply/submitAuditStayCarDelApply',
    method: 'post',
    data
  });
};
export const recoverDelStayCar = (data) => {
  return $({
    url: '/console/fee/stayCarDelApply/recoverDelStayCar',
    method: 'post',
    data
  });
};

// 分页查询已删除记录
export const pagingDeleteRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/pagingDeleteRecords',
    method: 'post',
    data
  });
};

// 导出入场记录
export const exportCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/exportInRecords',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};

// 导出已删除记录
export const exportDeleteRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/exportDeleteRecords',
    method: 'post',
    data
  });
};

// 查询滞留车辆导入记录(分页)-parkServer
export const listImportStayCarRecord = (data) => {
  return $({
    url: '/console/fee/stayCarDelApply/listImportStayCarRecord',
    method: 'post',
    data
  });
};
// 通过导入记录ID查询可删除滞留车辆信息(分页)-parkServer Copy
export const listImportStayCarRecordDetail = (data) => {
  return $({
    url: '/console/fee/stayCarDelApply/listImportStayCarRecordDetail',
    method: 'post',
    data
  });
};
// 滞留车辆撤回记录
export const withdrawDelStayCarRecord = (data) => {
  return $({
    url: '/console/fee/stayCarDelApply/withdrawDelStayCarRecord',
    method: 'post',
    data
  });
};