/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询入场记录
export const pagingCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/pagingInRecords',
    method: 'post',
    data
  });
};

// 批量删除入场记录
export const deleteCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/deleteInRecords',
    method: 'post',
    data
  });
};

// 分页查询已删除记录
export const pagingDeleteRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/pagingDeleteRecords',
    method: 'post',
    data
  });
};

// 导出入场记录
export const exportCarInRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/exportInRecords',
    method: 'post',
    data,
    timeout: 1000 * 150
  });
};

// 导出已删除记录
export const exportDeleteRecord = (data) => {
  return $({
    url: '/console/park/fee/inRecords/exportDeleteRecords',
    method: 'post',
    data
  });
};
