import * as parkInfo from '@/api/park/ParkInfoApi';

/**
 * 车场
 */
export default {
  /**
   * 分页查询
   */
  pagingParks(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.pagingParks(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 车场详情数据查询
   */
  getParkById(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.getParkById(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增车场
   */
  createPark(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.createPark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改车场
   */
  updatePark(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.updatePark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用车场
   */
  enablePark(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.enablePark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 禁用车场
   */
  disablePark(data) {
    return new Promise((resolve, reject) => {
      try {
        parkInfo.disablePark(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
