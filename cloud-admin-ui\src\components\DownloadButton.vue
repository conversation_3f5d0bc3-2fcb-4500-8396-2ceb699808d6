<template>
  <el-button :type="props.btnType" @click="handleExport" :loading="downLoading">
    <slot>导出</slot>
  </el-button>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import commonService from '@/service/common/CommonService';
import { saveToFile } from '@/utils/utils.js';
import { ref } from 'vue';

const props = defineProps({
  btnType: {
    type: String,
    default: 'success'
  },
  exportFunc: {
    type: Function,
    default: () => {}
  },
  params: {
    type: Object,
    default: () => {}
  },
  // 导出必要条件
  // rules: [{
  //   name: 'park_id',
  //   required: true,
  //   message: '请选择停车场进行统计'
  //  }]
  rules: {
    type: Array,
    default: () => []
  }
});

const downLoading = ref(false);
const handleExport = async () => {
  if (props.rules) {
    const requiredItem = props.rules.filter((item) => item.required);
    for (let i = 0; i < requiredItem.length; i++) {
      if (!props.params[requiredItem[i]?.name]) {
        ElMessage.warning(requiredItem[i].message);
        return false;
      }
    }
  }
  if (!props.exportFunc) return;
  const { data, success, detail_message, message } = await props.exportFunc(props.params);
  if (success == true) {
    downLoading.value = true;
    commonService
      .fileDownload(data)
      .then((res) => {
        let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
        saveToFile(res.data, decodeURIComponent(fileName));
        downLoading.value = false;
      })
      .catch(() => {
        downLoading.value = false;
      });
  } else {
    ElMessage({
      message: detail_message != '' ? detail_message : message,
      type: 'error'
    });
    downLoading.value = false;
  }
};
</script>
