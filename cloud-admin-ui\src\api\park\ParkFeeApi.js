/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 临停规则表格数据查询
export const pagingParkFees = (data) => {
  return $({
    url: '/console/park/fee/pagingParkFees',
    method: 'post',
    data
  });
};

// 查询单条临停规则
export const getParkFeeById = (id) => {
  return $({
    url: '/console/park/fee/getParkFeeById/' + id,
    method: 'get'
  });
};

// 新增临停规则
export const createParkFee = (data) => {
  return $({
    url: '/console/park/fee/createParkFee',
    method: 'post',
    data
  });
};

// 临停规则修改
export const updateParkFee = (data) => {
  return $({
    url: '/console/park/fee/updateParkFee',
    method: 'post',
    data
  });
};

// 删除临停规则
export const deleteParkFee = (id) => {
  return $({
    url: '/console/park/fee/deleteParkFee/' + id,
    method: 'post'
  });
};

//提交审核临停规则
export const submitAuditParkFee = (id) => {
  return $({
    url: '/console/park/fee/submitAuditParkFee/' + id,
    method: 'post'
  });
};

// 撤销临停规则
export const cancelParkFee = (id) => {
  return $({
    url: '/console/park/fee/cancelParkFee/' + id,
    method: 'post'
  });
};

// 预览规则
export const previewCalcModel = (data) => {
  return $({
    url: '/console/park/fee/previewCalcModel',
    method: 'post',
    data
  });
};
