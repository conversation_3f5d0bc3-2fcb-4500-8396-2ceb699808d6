/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-28 18:16:00
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\stores\user.js
 * @Description: {}
 */
import { defineStore } from 'pinia';

export const useDuty = defineStore('duty', {
  id: 'duty',
  state: () => {
    return {
      callInfo: {},
      talkBeginTime: null,
      isConnected: false,
      nowServiceEnd: true, // 是否完成本次服务
      query: {},
      isLeave: null,
      noIn: true, //无入场纪录 false有 true无
      cloud_park_ids: [],
      eventStartTime: null,
      peer: {},
      call_type: null,
      call_no: null,
      callWard: {
        login_user: null, // 登录用户
        login_password: undefined
      }
    };
  },
  actions: {
    removeToken() {
      this.callInfo = {};
      this.talkBeginTime = null;
      this.isConnected = false
      this.nowServiceEnd = false
      this.query = {}
      this.isLeave = null
      this.noIn = true
      this.cloud_park_ids = []
      this.eventStartTime = null
      this.peer = {}
      this.call_type = null
      this.call_no = null
      this.callWard = {
        login_user: null, // 登录用户
        login_password: undefined
      }
    }
  },
  persist: {
    enable: true
  }
});
