<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 355px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="id" label="车场ID" align="center" />
        <el-table-column prop="total_money" label="长租总收入（元）" align="center" />
        <el-table-column prop="new_money" label="新开通收入(元)" align="center" />
        <el-table-column prop="new_count" label="新开通笔数" align="center" />
        <el-table-column prop="renew_park_money" label="车场续费收入(元)" align="center" />
        <el-table-column prop="renew_park_count" label="车场续费笔数" align="center" />
        <el-table-column prop="renew_money" label="小程序续费收入(元)" align="center" />
        <el-table-column prop="renew_count" label="小程序续费笔数" align="center" />
        <el-table-column prop="refund_money" label="退款支出(元)" align="center" />
        <el-table-column prop="refund_count" label="退款笔数" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="LongRentReportTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import longRentReportService from '@/service/finance/LongRentReportService';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
      park_name: undefined,
      park_id: undefined,
    page: 1,
    limit: 30
  }
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  longRentReportService.pagingLongRentReport(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
