<template>
  <div class="card-container">
    <div class="top">
      <div class="label">{{ data.label }}</div>
      <div class="value">
        <span>{{ data.value }}</span
        >个
      </div>
      <!-- <div class="desc">
        <img class="desc-icon" :src="getImageUrl(data.trend)" v-if="data.trend" />
        <span class="desc-icon" v-else>-</span>
        <div class="desc-value">{{ (data.trend === 'up' ? '增长' : '降低') + data.trendValue }}</div>
      </div> -->
    </div>
    <img class="bottom" :src="getImageUrl(data.type)" />
  </div>
</template>

<script setup>
defineProps({
  data: {
    type: Object,
    default: () => ({})
  }
});
const getImageUrl = (value) => {
  return new URL(`../../../../assets/groupImage/${value}.png`, import.meta.url).href;
};
</script>

<style lang="scss" scoped>
.card-container {
  color: #fff;
  text-align: center;
  .top {
    width: 150px;
    height: 70px;
    background-image: url('@/assets/groupImage/info-bg.png');
    background-size: 100% 100%;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    padding: 10px 0;
    .label {
      font-weight: 550;
    }
    .value {
      font-size: 24px;
      color: #00fff4;
      font-weight: 550;
      span {
        font-size: 26px;
        font-family: 'DS-Digital-Bold';
        margin-right: 5px;
      }
    }
    .desc {
      display: flex;
      justify-content: center;
      align-items: center;
      .desc-icon {
        background-image: url('@/assets/groupImage/up.png');
        background-size: 100% 100%;
        width: 11px;
        height: 14px;
        margin-right: 5px;
      }
      .down {
        background-image: url('@/assets/groupImage/down.png');
      }
    }
  }
  .bottom {
    margin-top: 15px;
    width: 88px;
    height: 92px;
  }
}
</style>
