<template>
  <div>
    <car-visitor-search @form-search="searchCarVisitorList" @reset="resetParamsAndData" />
    <car-visitor-table ref="table" />
  </div>
</template>

<script setup name="CarVisitor">
import CarVisitorSearch from './carVisitor/CarVisitorSearch.vue';
import CarVisitorTable from './carVisitor/CarVisitorTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchCarVisitorList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
