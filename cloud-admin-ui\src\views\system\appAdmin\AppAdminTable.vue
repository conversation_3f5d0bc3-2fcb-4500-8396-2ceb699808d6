<template>
  <el-card class="table table-warp" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleAppCreate(addForm)">创建应用</el-button>
      </el-space>
      <div>
        <el-space> </el-space>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="tableLoading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="40" style="text-align: center"> </el-table-column>
        <el-table-column fixed prop="handle" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)" size="small">详情</el-button>
            <el-button link type="primary" @click="handleUpdate(scope.row)" size="small">编辑</el-button>
            <el-button link type="success" @click="handlePublish(scope.row)" size="small">发布</el-button>
            <el-button link type="danger" @click="authCharge(scope.row, true)" size="small">授权</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="应用名称" align="center" min-width="130"></el-table-column>
        <el-table-column prop="app_type_desc" label="应用标识" align="center" min-width="100"></el-table-column>
        <el-table-column prop="current_version" label="当前版本" align="center" min-width="100"></el-table-column>
        <el-table-column prop="build_version" label="构建的版本" align="center" min-width="130"></el-table-column>
        <el-table-column prop="app_key" label="app_key" align="center" min-width="150"></el-table-column>
        <el-table-column prop="updator" label="更新人" align="center" min-width="120"></el-table-column>
        <el-table-column prop="updated_at" label="更新时间" align="center" min-width="160"></el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
        <!-- 分管车场授权 -->
        <el-dialog v-if="parkDialogVisible" width="80%" title="分管车场授权" v-model="parkDialogVisible" :before-close="handleClose">
            <app-find-back :app_id="appId" @authCharge="authCharge(undefined, false)" @renderTableInput="renderTableInput" />
        </el-dialog>
    </div>
  </el-card>
</template>

<script name="AppAdminTable" setup>
import { reactive, ref, onActivated } from 'vue';
import { activeRouteTab } from '@/utils/tabKit';
import appAdminService from '@/service/system/AppAdminService';
import {ElMessage} from "element-plus";
import AppFindBack from './AppFindBack.vue';

const tableLoading = ref(false);
const tableData = ref([]);
const total = ref(0);
const data = reactive({
  selectRows: [],
  queryParams: {
    page: 1,
    limit: 30
  }
});
const parkDialogVisible = ref(false);
const appId = ref('');

onActivated(() => {
  getList(data.queryParams);
});

const handleSelectionChange = (val) => {
  data.selectRows = val;
};

const getList = (params) => {
  tableLoading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  appAdminService.queryApp(params).then((response) => {
    tableData.value = response.data.rows;
    total.value = parseInt(response.data.total);
    tableLoading.value = false;
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};

const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const handleAppCreate = () => {
  activeRouteTab({
    path: '/system/appCreate'
  });
};

const handleDetail = (row) => {
  activeRouteTab({
    path: '/system/appDetail',
    query: {
      id: row.id,
      name: row.name,
      currentVersion: row.current_version,
      buildVersion: row.buildVersion,
      app_key: row.app_key,
      features: row.features,
      memo: row.memo
    }
  });
};

const handleUpdate = (row) => {
  activeRouteTab({
    path: '/system/appUpdate',
    query: {
      id: row.id
    }
  });
};

const handlePublish = (row) => {
  activeRouteTab({
    path: '/system/appPublish',
    query: {
      id: row.id,
      name: row.name,
      app_key: row.app_key,
      features: row.features,
      memo: row.memo,
      active: 1
    }
  });
};
const authCharge = (row, visible) => {
    if (visible === false) {
        parkDialogVisible.value = false;
    } else {
        appId.value = row.id;
        parkDialogVisible.value = true;
    }
};
const renderTableInput = (val) => {
    const park_id = [];
    const park_name = [];
    val.some((item) => {
        park_id.push(item.park_id);
        park_name.push(item.park_name);
    });
    const form = {
        app_id: appId.value,
        park_id: park_id,
        park_name: park_name
    };
    // 分管车场授权
    appAdminService.parkingAuthority(form).then((response) => {
        if (response.success === true) {
            ElMessage({
                message: '分管车场授权成功',
                type: 'success'
            });
            getList(data.queryParams);
        } else {
            ElMessage({
                message: response.detail_message != '' ? response.detail_message : response.message,
                type: 'error'
            });
        }
    });
};
const handleClose = () => {
    parkDialogVisible.value = false;
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
