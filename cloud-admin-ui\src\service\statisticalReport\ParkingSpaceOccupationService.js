import * as ParkingSpaceOccupationApi from '@/api/statisticalReport/ParkingSpaceOccupationApi';

/**
 * 分时段进出
 */
export default {
  /**
   * 分页查询分时段进出
   */
  getPlaceOccupyTablePage(data) {
    return new Promise((resolve, reject) => {
      try {
        ParkingSpaceOccupationApi.getPlaceOccupyTablePage(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        ParkingSpaceOccupationApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
