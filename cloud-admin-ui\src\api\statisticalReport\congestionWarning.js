import $ from '@/utils/axios';
// 获取拥堵预警数据
export const getCongestionData = (data) => {
    return $.post('/console/statistics/park/lane/congestion/pagingByPeriod', data)
}
//导出拥堵预警数据
export const exportData = (data) => {
    return $.post('/console/statistics/park/lane/congestion/exportByPeriod', data)
}
//汇总导出拥堵预警数据
export const exportDataHz = (data) => {
    return $.post('/console/statistics/park/lane/congestion/exportSummaryByPeriod', data)
}