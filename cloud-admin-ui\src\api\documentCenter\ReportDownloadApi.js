/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询报表下载
export const pagingReportExportRecords = (data) => {
  return $({
    url: '/console/report/pagingReportExportRecords',
    method: 'post',
    data
  });
};

// 下载报表
export const downloadRecord = (id) => {
  return $({
    url: '/console/report/download/' + id,
    method: 'post'
  });
};

// 删除报表导出记录
export const deleteReportExportRecord = (id) => {
  return $({
    url: '/console/report/deleteReportExportRecord/' + id,
    method: 'post'
  });
};
