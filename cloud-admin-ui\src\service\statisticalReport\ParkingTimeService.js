import * as parkingTimeApi from '@/api/statisticalReport/ParkingTimeApi';

/**
 * 停车时长
 */
export default {
  /**
   * 分页查询停车时长
   */
  pagingParkingTime(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingTimeApi.pagingParkingTime(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkingTimeApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
