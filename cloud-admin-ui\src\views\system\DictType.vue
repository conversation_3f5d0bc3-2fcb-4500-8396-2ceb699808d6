<template>
  <div class="container my-table-container">
    <dict-type-search @form-search="searchDictTypeList" @reset="resetParamsAndData" />
    <dict-type-table ref="table" />
  </div>
</template>

<script name="DictType" setup>
import DictTypeSearch from './dict/DictTypeSearch.vue';
import DictTypeTable from './dict/DictTypeTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchDictTypeList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
