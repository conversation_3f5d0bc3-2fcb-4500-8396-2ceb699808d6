import * as carInterceptRecord from '@/api/car/CarInterceptRecordApi';

/**
 * 拦截记录
 */
export default {
  /**
   * 分页查询
   */
  pagingCarInterceptRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        carInterceptRecord.pagingCarInterceptRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 添加拦截记录
   */
  createCarInterceptRecordByManual(data) {
    return new Promise((resolve, reject) => {
      try {
        carInterceptRecord.createCarInterceptRecordByManual(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 下载拦截记录模板
   */
  downloadInterceptRecordTemplate() {
    return new Promise((resolve, reject) => {
      try {
        carInterceptRecord.downloadInterceptRecordTemplate().then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 导出拦截记录
   */
  exportCarInterceptRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        carInterceptRecord.exportCarInterceptRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除拦截记录
   */
  deleteCarInterceptRecordById(data) {
    return new Promise((resolve, reject) => {
      try {
        carInterceptRecord.deleteCarInterceptRecordById(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
