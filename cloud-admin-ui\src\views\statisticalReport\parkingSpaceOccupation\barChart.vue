<template>
  <div style="width: 100%;padding: 0 20px; box-sizing: border-box;margin-top: 20px;">
  </div>
  <div ref="chartRef" style="width: 100%; height: 600px"></div>
</template>

<script setup>
import * as echarts from 'echarts';
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue';
const activeName = ref('0')
const chartRef = ref(null);
const chartRef2 = ref(null);
let chartInstance = null;
const filterData = ref([])
const xData = ref([])
const sourceData = ref([])
const legendData = ref(['临停车辆数', '长租车辆数'])
const props = defineProps({
  tableData: {
    type: Array,
    default: () => []
  }
});
const initChart = () => {
  let option = {};
  option = {
    grid: {
      // 网格距离顶部距离
      top: 50,
      // 网格距离底部距,
      bottom: 60,
      // 网格距离左边距离
      left: 30,
      right: 30,
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    legend: {
      data: legendData.value,
      bottom: 20,
    },
    xAxis: [
      {
        type: 'category',
        data: xData.value,
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: '单位：辆',
        min: 0,
        // max: 200,
        axisLabel: {
          formatter: '{value}'
        },
        axisTick: {
          show: true,
          inside: false
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: '#efefef',  // 设置辅助线颜色
            type: 'dashed'  // 使用虚线样式
          }
        }
      },
    ],
    dataset: {
      source: sourceData.value
    },
    series: [
      {
        name: '临停车辆数',
        type: 'line',
        barGap: 0,
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#5470c6'
        }
      },
      {
        name: '长租车辆数',
        type: 'line',
        emphasis: {
          focus: 'series'
        },
        itemStyle: {
          color: '#ee6666'
        }
      }
    ]
  };


  chartInstance.setOption(option);
};
const groupAndSumByDate = (data) => {
  const result = {};
  data.forEach(item => {
    const date = item.statistics_date;
    if (!result[date]) {
      result[date] = {
        parking_car_in: 0,
        rent_car_in: 0,
        total_car_in: 0,
        parking_car_out: 0,
        rent_car_out: 0,
        total_car_out: 0,
        total_car_in_out: 0
      };
    }

    result[date].parking_car_in += item.parking_car_in;
    result[date].rent_car_in += item.rent_car_in;
    result[date].total_car_in += item.total_car_in;
    result[date].parking_car_out += item.parking_car_out;
    result[date].rent_car_out += item.rent_car_out;
    result[date].total_car_out += item.total_car_out;
    result[date].total_car_in_out += item.total_car_in_out;
  });

  return result;
}
watch(
  () => props.tableData,
  () => {
    init();
  }, { deep: true }
);
onMounted(() => {
  init();

});
const init = () => {
  nextTick(() => {
    chartInstance = echarts.init(chartRef.value);
    filterData.value = props.tableData
    // sourceData.value = [['月份', '进场（辆）', '出场（辆）', '临停停车费（元）']];
    sourceData.value = [];
    xData.value = [];
    filterData.value.forEach((item) => {
      xData.value.push(item.statistics_date.split(' ')[0])
      sourceData.value.push([item.statistics_date.split(' ')[0], item['temp_occupy_number'], item['rent_occupy_number']]);
    })
    console.log(xData.value, sourceData.value, 'ssssssssssss')
    // for (let key in filterData.value) {
    //   xData.value.push(key);
    //   let obj = filterData.value[key];
    //   let ary = []
    //   if (activeName.value == 0) {
    //     ary = [key, obj.parking_car_in, obj.parking_car_out, obj.temp_car_amount];
    //   } else {
    //     ary = [key, obj.rent_car_in, obj.rent_car_out];
    //   }
    //   sourceData.value.push(ary);
    // }
    initChart();
    // 响应窗口变化
    window.addEventListener('resize', () => {
      chartInstance.resize();
    });
  })
}
onBeforeUnmount(() => {
  // 组件卸载时销毁实例
  if (chartInstance) {
    window.removeEventListener('resize', chartInstance.resize);
    chartInstance.dispose();
  }
});
</script>