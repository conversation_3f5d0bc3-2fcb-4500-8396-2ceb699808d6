<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly="true" @click="authCharge(true)" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <el-date-picker v-model="form.queryParams.time" type="date" placeholder="日期" style="width: 100%" :size="size"
        format="YYYY-MM-DD" value-format="YYYY-MM-DD" :clearable="false" />
    </form-search-item>
    <form-search-item>
      <park-type-filter v-model="form.queryParams.park_types" />
    </form-search-item>
    <template #button>
      <export-button :export-func="exportDayReportsByCount" :params="form.queryParams" require="time"></export-button>
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag"
      @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="DayReportByTimesSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from './ParkFindBack.vue';
import parkTypeFilter from '@/components/parkTypeFilter.vue';
import exportButton from '@/components/exportButton.vue';

import { reactive, ref, onMounted, defineExpose } from 'vue';
import { useUser } from '@/stores/user';
import { exportDayReportsByCount } from '@/api/finance/DayReportApi';

import { dayjs } from 'element-plus';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    time: undefined,
    park_types: undefined,
    page: 1,
    limit: 30
  }
});

const park_id = ref('');
const park_name = ref('');
const relatedParkDialogVisible = ref(false);
const defaultDay = dayjs().subtract(1, 'day').format('YYYY-MM-DD');
onMounted(() => {
  form.queryParams.time = defaultDay;
  const user = useUser();
  // if (user.role_id == 1) {
  //   handleDataSearch();
  //   return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  handleDataSearch();
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
defineExpose({
  handleDataSearch
});
const handleAllReset = () => {
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    time: defaultDay,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
