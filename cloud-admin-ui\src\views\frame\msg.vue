<template>
  <div class="scroll-container" @mouseenter="pauseScroll" @mouseleave="resumeScroll" @wheel="handleWheel">
    <!-- 滚动区域 -->
    <div v-if="shouldScroll" ref="scrollList" class="scroll-list" :style="{ transform: `translateY(${-currentPosition}px)` }">
      <!-- 消息列表（复制一份实现无缝滚动） -->
      <div @click="toConsole(item)" v-for="(item, index) in displayMessages" :key="`${item.id}-${index % messages.length}`" class="scroll-item">
        <div class="item-content">
          <span class="item-title">{{ (index % messages.length) + 1 }}.{{ item.name }}</span>
          <span class="item-time">{{ item.event_type_desc }}</span>
        </div>
      </div>
    </div>

    <!-- 不滚动时显示的消息 -->
    <div v-else class="static-list">
      <div @click="toConsole(item)" v-for="(item, index) in messages" :key="item.id" class="scroll-item">
        <div class="item-content">
          <span class="item-title">{{ index + 1 }}.{{ item.name }}</span>
          <span class="item-time">{{ item.event_type_desc }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useDuty } from '@/stores/duty';
import { activeRouteTab } from '@/utils/tabKit';
import { computed, onUnmounted, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
const duty = useDuty();
const router = useRouter();
const scrollList = ref(null);

// 消息数据
const messages = computed(() => duty.callInfo.msgList || []);
const displayMessages = computed(() => {
  // 当需要滚动时，复制2份消息确保无缝滚动
  return shouldScroll.value ? [...messages.value, ...messages.value] : messages.value;
});

// 滚动相关变量
const itemHeight = 25; // 每条消息高度
const containerHeight = 50; // 容器高度
const scrollSpeed = 20; // 滚动速度（像素/秒）
const wheelSpeed = 30; // 滚轮滚动速度

const currentPosition = ref(0);
let animationFrame = null;
let lastTimestamp = 0;
let isPaused = false;
let isScrollingByWheel = false;

// 是否需要滚动（消息超过2条时才滚动）
const shouldScroll = computed(() => messages.value.length > 2);

// 处理点击事件
const toConsole = (item) => {
  duty.talkBeginTime = new Date().getTime();
  activeRouteTab({
    path: '/monitoringMan/Console/Console',
    query: {
      sIp: item.id,
      gatewayType: item.type
    }
  });
};
// 处理滚轮事件
const handleWheel = (e) => {
  if (!shouldScroll.value) return;

  e.preventDefault();
  isScrollingByWheel = true;
  pauseScroll();

  // 限制滚动范围
  const maxScroll = messages.value.length * itemHeight;
  currentPosition.value = Math.max(0, Math.min(maxScroll, currentPosition.value + (e.deltaY > 0 ? wheelSpeed : -wheelSpeed)));

  // 重置自动滚动计时器
  clearTimeout(wheelScrollTimeout);
  wheelScrollTimeout = setTimeout(() => {
    isScrollingByWheel = false;
    resumeScroll();
  }, 1000);
};
let wheelScrollTimeout = null;

// 暂停滚动
const pauseScroll = () => {
  isPaused = true;
  if (animationFrame) {
    cancelAnimationFrame(animationFrame);
    animationFrame = null;
  }
};

// 恢复滚动
const resumeScroll = () => {
  if (!shouldScroll.value || isScrollingByWheel) return;

  isPaused = false;
  lastTimestamp = performance.now();
  if (!animationFrame) {
    animationFrame = requestAnimationFrame(scrollStep);
  }
};

// 滚动动画
const scrollStep = (timestamp) => {
  if (!lastTimestamp) lastTimestamp = timestamp;

  if (!isPaused) {
    const delta = timestamp - lastTimestamp;
    currentPosition.value += (scrollSpeed * delta) / 1000;

    // 当滚动到第一组消息的末尾时，重置位置实现无缝滚动
    const singleLoopHeight = messages.value.length * itemHeight;
    if (currentPosition.value >= singleLoopHeight) {
      currentPosition.value = 0;
    }
  }

  lastTimestamp = timestamp;
  animationFrame = requestAnimationFrame(scrollStep);
};

// 初始化滚动
const initScroll = () => {
  if (shouldScroll.value) {
    currentPosition.value = 0;
    lastTimestamp = performance.now();
    if (!animationFrame) {
      animationFrame = requestAnimationFrame(scrollStep);
    }
  } else {
    pauseScroll();
  }
};

// 监听消息变化
watch(
  messages,
  (newVal, oldVal) => {
    // 只有当消息内容或长度变化时才重新初始化
    if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
      initScroll();
    }
  },
  { immediate: true }
);

// 清理
onUnmounted(() => {
  pauseScroll();
  clearTimeout(wheelScrollTimeout);
});
</script>

<style scoped>
.scroll-container {
  height: 50px;
  overflow: hidden;
  position: relative;
  border-radius: 4px;
  box-shadow: 0 1px 6px rgba(0, 0, 0, 0.1);
  background-color: #ffd9c2;
}

.scroll-list,
.static-list {
  transition: transform 0.3s ease-out;
}

.scroll-item {
  height: 25px;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background 0.2s;
}

.scroll-item:hover {
  background-color: rgba(255, 255, 255, 0.3);
}

.item-content {
  display: flex;
  justify-content: space-between;
  width: 100%;
  align-items: center;
}

.item-title {
  font-size: 12px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  margin-right: 8px;
  color: #f5222d;
}

.item-time {
  font-size: 10px;
  color: #999;
  white-space: nowrap;
}
</style>
