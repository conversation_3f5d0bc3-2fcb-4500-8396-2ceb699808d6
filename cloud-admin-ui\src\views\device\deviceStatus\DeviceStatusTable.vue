<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="150">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.monitor_enabled == 0" @click="turnOnAlarm(scope.row.id)"> 开启报警 </el-button>
            <el-button link type="danger" v-if="scope.row.monitor_enabled == 1" @click="turnOffAlarm(scope.row.id)"> 关闭报警 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="dev_device_name" label="设备名称" align="center" />
        <el-table-column prop="sn" label="设备序列号" align="center" />
        <el-table-column prop="type_desc" label="设备类型" align="center" />
        <el-table-column prop="model" label="设备型号" align="center" />
        <el-table-column prop="dev_factory_name" label="设备厂家" align="center" />
        <el-table-column prop="park_region_name" label="所属子场" align="center" />
        <el-table-column prop="gateway_name" label="所属通道" align="center" />
        <el-table-column prop="ip" label="设备地址" align="center" />
        <el-table-column prop="port" label="控制端口" align="center" />
        <el-table-column prop="state_desc" label="设备状态" align="center" />
        <el-table-column prop="online_desc" label="在线状态" align="center" width="100" />
        <el-table-column prop="last_heart_time" label="最后心跳时间" align="center" width="160" />
        <el-table-column prop="monitor_enabled_desc" label="是否报警" align="center" width="160" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="DeviceStatusTable" setup>
import { reactive, onActivated, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import deviceStatusService from '@/service/device/DeviceStatusService';
import { useRoute } from 'vue-router';
const route = useRoute();

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

onActivated(() => {
  if ({} !== route.query && undefined !== route.query.gateway_device_id) {
    data.queryParams.dev_device_id = route.query.gateway_device_id;
  }
  getList(data.queryParams);
});
// 分页查询厂商列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  deviceStatusService.pagingDeviceStatus(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

//开启报警
const turnOnAlarm = (id) => {
  ElMessageBox.confirm('是否要开启报警？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceStatusService.turnOnAlarm(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        getList(data.queryParams);
      }
    });
  });
};
// 关闭报警
const turnOffAlarm = (id) => {
  ElMessageBox.confirm('是否要开启报警？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceStatusService.turnOffAlarm(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        getList(data.queryParams);
      }
    });
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
