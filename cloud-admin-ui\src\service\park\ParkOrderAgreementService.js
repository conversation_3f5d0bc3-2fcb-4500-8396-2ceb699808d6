import * as parkOrderAgreementApi from '@/api/park/ParkOrderAgreementApi';

/**
 * 车场-预约车位协议
 */
export default {
  /**
   * 获取预约车位协议
   */
  getSpaceReserveLicense(data) {
    return new Promise((resolve, reject) => {
      try {
        parkOrderAgreementApi.getSpaceReserveLicense(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建预约车位协议
   */
  createParkOrderAgreement(data) {
    return new Promise((resolve, reject) => {
      try {
        parkOrderAgreementApi.createParkOrderAgreement(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
