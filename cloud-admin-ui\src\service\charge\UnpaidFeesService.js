import * as unpaidFeesApi from '@/api/charge/UnpaidFeesApi';

/**
 * 欠逃费订单
 */
export default {
  /**
   * 分页查询列表
   */
  recordsPage(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.recordsPage(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },


  /**
 * 导出
 */
  exportParkPayRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.exportParkPayRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
 * 根据订单id查询到 核销记录
 */
  chargeofflistid(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.chargeofflistid(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
* 保存核销记录
*/
  chargeoffsave(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.chargeoffsave(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
/**
* 催缴
*/
  pushRemind(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.pushRemind(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
/**
* 查询欠缴总额和笔数
*/
  parkRecoveryPayRecordsdata(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.parkRecoveryPayRecordsdata(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询各项金额统计
   */
  countParkPayRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.countParkPayRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 申请退款
   */
  applyRefund(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.applyRefund(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询当前车场通道列表
   */
  listParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        unpaidFeesApi.listParkGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
