<template>
  <div class="lastCallBox">
    <div class="imgCol">
      <div>
        <div class="imgTit">入场图片</div>
        <div class="img">
          <img v-if="detailData.car_in_photo" :src="detailData.car_in_photo" alt="" />
          <el-empty v-if="!detailData.car_in_photo" description="暂无数据" />
        </div>
      </div>
      <div>
        <div class="imgTit">出场图片</div>
        <div class="img">
          <img v-if="detailData.car_photo" :src="detailData.car_photo" alt="" />
          <el-empty v-if="!detailData.car_photo" description="暂无数据" />
        </div>
      </div>
    </div>
    <div class="infoCol">
      <div class="infoCol-one">
        <div class="infoCol-t">
          <div class="fill"></div>
          呼叫信息
        </div>
        <div class="infoCol-b">
          <div>车场名称：{{ detailData.park_name || '-' }}</div>
          <div>开始时间：{{ detailData.begin_time || '-' }}</div>
          <div>呼叫位置：{{ detailData.gateway_type_desc == '入口' ? detailData.gateway_in_name : detailData.gateway_out_name }}</div>
          <div>结束时间：{{ detailData.end_time || '-' }}</div>
          <div>呼叫类型：{{ detailData.call_type_desc || '-' }}</div>
          <div>处理时长：{{ detailData.call_time_desc || '-' }}</div>
          <div>接听人员：{{ detailData.user_name || '-' }}</div>
        </div>
      </div>
      <div class="infoCol-two">
        <div v-if="!isLastType" class="infoCol-t">
          <div class="fill"></div>
          停车记录
        </div>
        <div v-else class="infoCol-t">
          <div class="fill"></div>
          {{ detailData.gateway_type_desc == '入口' ? '入场' : '出场' }}车辆
        </div>
        <div class="carInfo">
          <div>{{ detailData.plate_no }}</div>
        </div>
        <div class="infoCol-two-con">
          <div>车辆类型: {{ detailData.car_type_desc || '-' }}</div>
          <div>停车时长: {{ detailData.duration_text || '-' }}</div>
          <div>入场时间: {{ detailData.in_time || '-' }}</div>
          <div>出场时间: {{ detailData.park_out_time || '-' }}</div>
          <div>入场通道: {{ detailData.gateway_in_name || '-' }}</div>
          <div>出场通道: {{ detailData.gateway_out_name || '-' }}</div>
        </div>
      </div>
      <div class="infoCol-three">
        <div class="infoCol-t">
          <div class="fill"></div>
          收费信息
        </div>
        <div class="three-carInfo">
          <div>
            实际支付: <span style="color: #01a7f0">{{ detailData.payed_money || 0 }}元</span>
          </div>
          <div>当前费率: {{ detailData.car_type_desc || '-' }}</div>
        </div>
        <div class="infoCol-three-con">
          <div>应收费用: {{ detailData.should_pay_money || 0 }}元</div>
          <div>优免: {{ detailData.current_coupon_money || 0 }}元</div>
          <div>电子支付: {{ detailData.payed_money || 0 }}元</div>
          <div>已抵扣金额: {{ detailData.current_coupon_money || 0 }}元</div>
          <div>
            已支付: <span style="color: #00cb60">{{ detailData.payed_money || 0 }}元</span>
          </div>
          <div>
            找零金额: <span style="color: #ffac00">{{ detailData.change_amount || 0 }}元</span>
          </div>
        </div>
      </div>
    </div>
    <div class="reasonCol">
      <div class="infoCol-t">
        <div class="fill"></div>
        处理结果
      </div>
      <div class="reasonCol-lx">业务类型</div>
      <div class="buttonClass-reson">{{ detailData.reason_remark ? detailData.reason_remark : detailData.reason_type_desc }}</div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
const detailData = ref({});
const isLastType = ref(false);
defineExpose({
  detailData,
  isLastType
});
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  user-select: none;
}

.lastCallBox {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 10px;

  > div {
    border-radius: 5px;
  }

  .imgCol {
    flex: 3.5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    > div {
      flex: 1;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .imgTit {
        height: 40px;
        display: flex;
        font-size: 14px;
        align-items: center;
        padding-left: 10px;
        border-bottom: 1px solid #e7e7e7;
        font-weight: 700;
        color: #01a7f0;
      }

      .img {
        height: calc(100% - 40px);
        width: 100%;
        padding: 5px;

        img {
          height: 100%;
          width: 100%;
          border-radius: 5px;
        }
      }
    }
  }

  .infoCol-t {
    height: 40px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 700;

    .fill {
      height: 35%;
      width: 3px;
      border-radius: 3px;
      background-color: #01a7f0;
      margin-right: 5px;
    }
  }

  .infoCol {
    flex: 5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    > div {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    }

    .infoCol-one {
      flex: 3;
      padding: 0 10px 10px 10px;

      .infoCol-b {
        height: calc(100% - 40px);
        width: 100%;
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        padding: 5px;
        font-size: 14px;
        display: grid;
        background-color: #fafafa;
        grid-template-columns: repeat(2, 1fr);
        align-items: center;
      }
    }

    .infoCol-two {
      flex: 3;
      padding: 0 10px 10px 10px;
      font-size: 14px;

      .carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;
        color: #4b7a02;

        div:first-child {
          padding: 3px 20px;
          background-color: #01a7f0;
          border-radius: 5px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .infoCol-two-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
      }
    }

    .infoCol-three {
      flex: 3;
      padding: 0 10px 10px 10px;

      .infoCol-three-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
        font-size: 14px;
      }

      .three-carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 700;
        font-size: 14px;
      }
    }
  }

  .reasonCol {
    flex: 1.5;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    padding: 0 10px 10px 10px;
    font-size: 14px;

    .buttonClass-reson {
      width: fit-content;
      padding: 2px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      margin-top: 10px;
    }

    .reasonCol-lx {
      color: #7b7b7b;
    }
  }
}
</style>
