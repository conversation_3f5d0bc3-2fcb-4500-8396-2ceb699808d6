import * as invoice from '@/api/invoice/InvoiceApi';

/**
 * 发票管理
 */
export default {
  /**
   * 分页查找发票信息
   */
  pagingInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.pagingInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出电子发票申领
   */
  exportInvoices(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.exportInvoices(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 发送电子邮件
   */
  sendingEInvoiceMail(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.sendingEInvoiceMail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 手动查询发票结果
   */
  queryEInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.queryEInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 红冲-作废发票
   */
  cancelInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.cancelInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 保存发票
   */
  saveFile(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.saveFile(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询定额发票申领
   */
  pagingQuotaInvoices(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.pagingQuotaInvoices(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增定额发票申请
   */
  createQuotaInvoiceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.createQuotaInvoiceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改定额发票申请
   */
  updateQuotaInvoiceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.updateQuotaInvoiceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 删除定额发票申请
   */
  deleteQuotaInvoiceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.deleteQuotaInvoiceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出定额发票申领
   */
  exportQuotaInvoices(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.exportQuotaInvoices(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 领取定额发票
   */
  drawQuotaInvoice(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.drawQuotaInvoice(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 提交审核定额发票申请
   */
  submitAuditQuotaInvoiceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.submitAuditQuotaInvoiceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 撤回定额发票申请
   */
  revokeQuotaInvoiceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.revokeQuotaInvoiceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 通过车场ID获取开票公司信息
   */
  getTitleByParkId(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.getTitleByParkId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 关联临停费用订单的分页数据
   */
  relativeParkOrderPaging(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.relativeParkOrderPaging(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 关联预约车位订单的分页数据
   */
  relativeReserveOrderPaging(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.relativeReserveOrderPaging(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 关联长租费用订单的分页数据
   */
  relativeRentOrderPaging(data) {
    return new Promise((resolve, reject) => {
      try {
        invoice.relativeRentOrderPaging(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
