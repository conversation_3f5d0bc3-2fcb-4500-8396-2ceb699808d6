<template>
  <div ref="table" style="width: 100%; height: 100%; overflow: hidden; padding: 10px; box-sizing: border-box">
    <el-table :data="tableData" v-loading="loading" border style="height: calc(100% - 50px); overflow-y: auto">
      <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
      <el-table-column prop="action" label="操作" align="center" width="300">
        <template v-slot="scope">
          <el-button link type="primary" @click="toConsole(scope.row)"> 优先处理 </el-button>
        </template>
      </el-table-column>
      <el-table-column prop="event_time" label="事件时间" align="center" />
      <el-table-column prop="park_name" label="车场名称" align="center" />
      <el-table-column prop="gateway_name" label="通道名称" align="center" />
      <el-table-column prop="plate_no" label="车牌号" align="center" />
      <el-table-column prop="event_type" label="事件类型" align="center" />
      <el-table-column prop="duration" label="排队时间" align="center" />
    </el-table>
    <el-pagination
      background
      :current-page="data.queryParams.page"
      :page-sizes="[10, 30, 50, 100]"
      :page-size="data.queryParams.limit"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total"
      class="table-pagination"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
<script name="MemberInfoTable" setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { activeRouteTab } from '@/utils/tabKit';
import { ElMessage } from 'element-plus';
import { onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
const duty = useDuty();
const tableData = ref([]);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const timer = ref(null);
watch(
  () => duty.cloud_park_ids,
  (newId) => {
    if (newId) {
      getList(data.queryParams);
    }
  },
  { immediate: false } // 立即执行一次
);
onMounted(() => {
  timer.value = setInterval(() => {
    getList(data.queryParams);
  }, 15000); // 每30秒自动刷新数据
});
// 组件卸载前
onBeforeUnmount(() => {
  // 清除定时器
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
const emits = defineEmits(['updateData']);
// 分页查询设备列表数据
const getList = (params) => {
  // loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  params.park_ids = duty.cloud_park_ids;
  UnattendedApi.monitorpagingEvents(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      emits('total', total.value); // 触发父组件监听的事件
      // loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      // loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 跳转工作台
const toConsole = (item) => {
  duty.eventStartTime = new Date().getTime();
  activeRouteTab({
    path: '/monitoringMan/Console/Console',
    query: {
      sIp: item.gateway_id,
      gatewayType: item.gateway_type,
      type: 'dclycsj',
      plateNo: item.plate_no,
      eventTypeId: item.event_type_id,
      eventId: item.id
    }
  });
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
