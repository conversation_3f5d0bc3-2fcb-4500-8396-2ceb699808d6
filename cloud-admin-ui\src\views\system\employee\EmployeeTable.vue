<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-07-09 16:32:01
 * @LastEditors: Please set LastEditors
 * @Description: 
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\views\system\employee\EmployeeTable.vue
-->
<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate('wd')">添加员工</el-button>
        <el-button type="primary" @click="handleCreate('fw')">添加非万员工</el-button>
        <el-button type="danger" @click="batchDelete()">批量删除</el-button>
      </el-space>
    </div>
    <div ref="table" style="height: 62vh; overflow-y: auto">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row.id, scope.row)"> 修改 </el-button>
            <el-button link type="danger" v-if="scope.row.enabled == 0" @click="deleteEmployee(scope.row)"> 删除 </el-button>
            <el-button link v-if="scope.row.enabled == 0" type="primary" @click="enable(scope.row)"> 启用 </el-button>
            <el-button link v-if="scope.row.enabled == 1" type="danger" @click="disable(scope.row)"> 禁用 </el-button>
            <el-button link type="primary">
              <el-dropdown placement="bottom">
                <span class="el-dropdown-link" style="color: #409eff"> 更多 </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item @click="resetPassword(scope.row)">
                      <span>重置密码</span>
                    </el-dropdown-item>
                    <el-dropdown-item @click="authCharge(scope.row, true)">
                      <span>分管车场授权</span>
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="id" label="用户ID" align="center" />
        <el-table-column prop="login_name" label="万信号" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.login_name?.length > 0">{{ scope.row.login_name }}</span>
            <span v-else>{{ scope.row.mobile }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="name" label="姓名" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="role_name" label="系统角色" align="center" />
        <el-table-column prop="department_name" label="所在部门" align="center" />
        <el-table-column prop="state" label="启用状态" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.enabled == 1">已启用</span>
            <span v-if="scope.row.enabled == 0">已禁用</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加员工" v-model="employeeCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item v-if="isWd" prop="name" label="万信号">
            <el-select
              v-model="userName"
              filterable
              remote
              placeholder="请输入万信号搜索"
              :remote-method="remoteMethod"
              :loading="remoteLoading"
              @change="handleSelectUser"
              clearable
            >
              <el-option v-for="item in userOptions" :key="item.userId" :label="item.fullName" :value="item.userId">
                <div>
                  <span class="name">{{ item.fullName || '--' }}</span>
                  <span class="number">({{ item.userNo || '--' }})</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item v-if="isWd" prop="login_name" label="万信号">
            <el-input v-model="data.form.login_name" disabled />
          </el-form-item> -->
          <el-form-item v-if="!isWd" prop="name" label="姓名">
            <el-input v-model="data.form.name" />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号">
            <el-input v-model="data.form.mobile" />
          </el-form-item>
          <el-form-item prop="role_id" label="系统角色">
            <el-select v-model="data.form.role_id" style="width: 100%" multiple>
              <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="department_id" label="所在部门">
            <el-select ref="selectTree" v-model="data.form.department_name" placeholder="请选择所在部门" style="width: 100%">
              <el-option :value="localtionName" :label="data.form.department_name" style="height: 200px; overflow: auto; background-color: #fff">
                <el-tree :data="treeData" :check-strictly="true" accordion highlight-current @node-click="handleNodeClick" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="enabled" label="启用状态">
            <el-switch v-model="data.form.enabled" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createEmployee(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog
        title="修改员工"
        v-model="employeeUpdateDialogVisible"
        :close-on-click-modal="false"
        @close="closeEditDialog(editForm)"
        width="500px"
      >
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="name" label="姓名">
            <el-input v-model="data.updateForm.name" disabled />
          </el-form-item>
          <el-form-item v-if="isWd" prop="login_name" label="万信号">
            <el-input v-model="data.updateForm.login_name" disabled />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号">
            <el-input v-model="data.updateForm.mobile" />
          </el-form-item>
          <el-form-item prop="role_id" label="系统角色">
            <el-select v-model="data.updateForm.role_id" style="width: 100%" multiple>
              <el-option v-for="item in roleList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="department_id" label="所在部门">
            <el-select ref="updateSelectTree" v-model="data.updateForm.department_name" placeholder="请选择所在部门" style="width: 100%">
              <el-option
                :value="updateLocaltionName"
                :label="data.updateForm.department_name"
                style="height: 200px; overflow: auto; background-color: #fff"
              >
                <el-tree :data="treeData" :check-strictly="true" accordion highlight-current @node-click="handleUpdateNodeClick" />
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item prop="enabled" label="启用状态">
            <el-switch v-model="data.updateForm.enabled" active-value="1" inactive-value="0" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateEmployee(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 分管车场授权 -->
      <el-dialog v-if="parkDialogVisible" width="80%" title="分管车场授权" v-model="parkDialogVisible" :before-close="handleClose">
        <employee-find-back :employee_id="employeeId" @authCharge="authCharge(undefined, false)" @renderTableInput="renderTableInput" />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="EmployeeTable" setup>
import { searchUserFromPlatform } from '@/api/system/EmployeeApi';
import employeeService from '@/service/system/EmployeeService';
import { ElMessage, ElMessageBox } from 'element-plus';
import { debounce } from 'lodash';
import { getCurrentInstance, onActivated, reactive, ref } from 'vue';
import EmployeeFindBack from './EmployeeFindBack.vue';

const remoteLoading = ref(false);
const userOptions = ref([]);
const userName = ref('');
const remoteMethod = debounce(async (query) => {
  console.log(query);
  if (!query) return;
  remoteLoading.value = true;
  try {
    const { data } = await searchUserFromPlatform({ fullName: query });
    userOptions.value = data;
  } finally {
    remoteLoading.value = false;
  }
}, 500);

const handleSelectUser = (userId) => {
  if (!userId) {
    data.form.name = '';
    data.form.mobile = '';
    data.form.login_name = '';
  }
  const user = userOptions.value.find((item) => item.userId === userId);
  if (user) {
    data.form.name = user.fullName;
    data.form.mobile = user.mobile;
    data.form.login_name = user.userNo;
    data.form.id = user.userId;
  }
};

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};
const { proxy } = getCurrentInstance();
const addForm = ref();
const editForm = ref();
const employeeIds = ref([]);
const tableData = ref([]);
const loading = ref(false);
const employeeId = ref('');
const treeData = ref([]);
const roleList = ref([]);
const total = ref(0);
const localtionName = ref('');
const updateLocaltionName = ref('');
const employeeCreateDialogVisible = ref(false);
const employeeUpdateDialogVisible = ref(false);
const parkDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    id: [],
    name: undefined,
    mobile: undefined,
    department_id: undefined,
    department_name: undefined,
    enabled: undefined,
    role_id: []
  },
  updateForm: {},
  rules: {
    name: [
      {
        required: true,
        message: '请输入员工姓名',
        trigger: 'blur'
      }
    ],
    mobile: [
      {
        required: true,
        message: '请输入员工手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    role_id: [
      {
        required: true,
        message: '请选择系统角色',
        trigger: 'blur'
      }
    ],
    department_id: [
      {
        required: true,
        message: '请选择所在部门',
        trigger: 'blur'
      }
    ],
    enabled: [
      {
        required: true,
        message: '请选择是否启用',
        trigger: 'blur'
      }
    ]
  }
});

onActivated(() => {
  initDeptTree();
  getList(data.queryParams);
  getRoleList();
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  employeeService.pagingEmployees(params).then((response) => {
    console.log(response, '111111111111111');
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const isWd = ref(false);
const handleCreate = (v) => {
  if (v == 'wd') {
    isWd.value = true;
  } else {
    isWd.value = false;
  }
  initFormData();
  employeeCreateDialogVisible.value = true;
};
const initFormData = () => {
  data.form = {
    name: undefined,
    mobile: undefined,
    department_id: undefined,
    enabled: '1',
    role_id: [],
    login_name: ''
  };
};
const initDeptTree = () => {
  employeeService.departmentTree().then((response) => {
    if (response.success === true) {
      treeData.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const getRoleList = () => {
  employeeService.findRoles().then((response) => {
    if (response.success === true) {
      roleList.value = response.data;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const handleNodeClick = (node) => {
  data.form.department_id = node.id;
  data.form.department_name = node.label;
  localtionName.value = node.label;
  proxy.$refs.selectTree.blur();
};
const handleUpdateNodeClick = (node) => {
  data.updateForm.department_id = node.id;
  data.updateForm.department_name = node.label;
  updateLocaltionName.value = node.label;
  proxy.$refs.updateSelectTree.blur();
};
const createEmployee = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.department_id === undefined) {
      ElMessage({
        message: '请选择所在部门',
        type: 'error'
      });
      return false;
    }
    employeeService
      .createEmployee(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          employeeCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleSelectionChange = (val) => {
  employeeIds.value = val;
};
const batchDelete = () => {
  if (employeeIds.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的员工',
      type: 'warning'
    });
  } else {
    ElMessageBox.confirm('确定要删除吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }).then(() => {
      const pushIds = [];
      for (let i = 0; i < employeeIds.value.length; i++) {
        pushIds.push(parseInt(employeeIds.value[i].id));
      }
      data.form.id = pushIds;
      employeeService
        .deleteEmployees(data.form)
        .then((response) => {
          if (response.success === true) {
            ElMessage({
              message: response.message,
              type: 'success'
            });
            getList(data.queryParams);
          } else {
            ElMessage({
              message: response.detail_message != '' ? response.detail_message : response.message,
              type: 'error'
            });
          }
        })
        .catch(() => {
          getList(data.queryParams);
        });
    });
  }
};
const deleteEmployee = (val) => {
  employeeIds.value[0] = val;
  batchDelete();
};
const handleEdit = (id, row) => {
  if (row.login_name?.length > 0) {
    isWd.value = true;
  } else {
    isWd.value = false;
  }
  employeeService.getEmployeeById(id).then((response) => {
    if (response.success === true) {
      data.updateForm = {
        id: response.data.id,
        department_id: response.data.department_id,
        department_name: response.data.department_name,
        name: response.data.name,
        enabled: response.data.enabled + '',
        mobile: response.data.mobile,
        role_id: response.data.role_id,
        login_name: response.data.login_name
      };
      employeeUpdateDialogVisible.value = true;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const updateEmployee = (editForm) => {
  editForm.validate().then(() => {
    if (data.updateForm.department_id === undefined) {
      ElMessage({
        message: '请选择所在部门',
        type: 'error'
      });
      return false;
    }
    employeeService
      .updateEmployee(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          employeeUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const enable = (row) => {
  ElMessageBox.confirm('是否要启用该员工？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    employeeService.enable(row.id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
// 禁用
const disable = (row) => {
  ElMessageBox.confirm('是否要禁用该员工？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    employeeService.disable(row.id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};
const resetPassword = (val) => {
  employeeIds.value[0] = val;
  ElMessageBox.confirm('确定重置密码吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const pushIds = [];
    for (let i = 0; i < employeeIds.value.length; i++) {
      pushIds.push(parseInt(employeeIds.value[i].id));
    }
    employeeService
      .resetPassword(pushIds)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const authCharge = (row, visible) => {
  if (visible === false) {
    parkDialogVisible.value = false;
  } else {
    employeeId.value = row.id;
    parkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  const park_id = [];
  const park_name = [];
  val.some((item) => {
    park_id.push(item.park_id);
    park_name.push(item.park_name);
  });
  const form = {
    employee_id: employeeId.value,
    park_id: park_id,
    park_name: park_name
  };
  // 分管车场授权
  employeeService.parkingAuthority(form).then((response) => {
    if (response.success === true) {
      ElMessage({
        message: '分管车场授权成功',
        type: 'success'
      });
      getList(data.queryParams);
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};
const handleClose = () => {
  parkDialogVisible.value = false;
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  employeeCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  employeeUpdateDialogVisible.value = false;
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.example-showcase .el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}
</style>
