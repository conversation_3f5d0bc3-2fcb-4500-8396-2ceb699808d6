<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="请选择车场" />
    </form-search-item>
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" />
    </form-search-item>
    <form-search-item>
      <el-input v-model="form.queryParams.operator_name" placeholder="操作人" />
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="操作开始日期"
        end-placeholder="操作结束日期"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :shortcuts="shortcuts"
        @change="handleDateChange"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="department_id"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="CancelReleaseSearch" setup>
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import OrgFindBack from '@/components/OrgFindBack.vue';
import { useUser } from '@/stores/user';
import { ElMessage, dayjs } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import ParkFindBack from './ParkFindBack.vue';
const router = useRouter();

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    operator_name: undefined,
    in_start_time: undefined,
    in_end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: [dayjs().subtract(1, 'month').format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')]
});
const park_id = ref('');
const park_name = ref('');
const department_id = ref('');
const department_name = ref('');
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);

onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  if (user.role_id == 1) {
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  }
});
const shortcuts = [
  {
    text: '昨天',
    value: () => {
      return [dayjs().subtract(1, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '最近一周',
    value: () => {
      return [dayjs().subtract(6, 'day').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '最近一个月',
    value: () => {
      return [dayjs().subtract(1, 'months').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '最近三个月',
    value: () => {
      return [dayjs().subtract(3, 'months').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  },
  {
    text: '最近一年',
    value: () => {
      return [dayjs().subtract(1, 'year').format('YYYY-MM-DD') + '00:00:00', dayjs().format('YYYY-MM-DD') + '23:59:59'];
    }
  }
];
const handleDateChange = (val) => {
  if (val && val.length === 2) {
    const startDate = new Date(val[0]);
    const endDate = new Date(val[1]);
    const diffInDays = Math.floor((endDate - startDate) / (1000 * 60 * 60 * 24));

    if (diffInDays > 365) {
      ElMessage.warning('最大可选时间范围为一年');
      // Reset the selection or adjust it to one year
      // For example, you could automatically adjust the end date:
      // const newEndDate = new Date(startDate)
      // newEndDate.setFullYear(newEndDate.getFullYear() + 1)
      // this.dateRange = [val[0], newEndDate.toISOString().split('T')[0]]

      // Or simply clear the selection:
      form.dateRange = [];
    }
  }
};
const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.in_start_time = form.dateRange[0];
    form.queryParams.in_end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.in_start_time = undefined;
    form.queryParams.in_end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    plate_no: undefined,
    operator_name: undefined,
    in_start_time: undefined,
    in_end_time: undefined,
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};
const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.department_id = undefined;
  form.queryParams.department_name = undefined;
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    department_id.value = form.queryParams.department_id;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.department_id = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
