<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <div>
        <el-button type="primary" @click="deleteAll()">全部删除</el-button>
      </div>
      <div></div>
    </div>
    <div ref="table">
      <el-table :data="tableData" @selection-change="handleSelectionChange" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="150">
          <template v-slot="scope">
            <el-button link type="danger" @click="deleteRecord(scope.row.id)"> 删除记录 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="park_region_name" label="所属子场" align="center" />
        <el-table-column prop="gateway_name" label="所属通道" align="center" />
        <el-table-column prop="gateway_device_id" label="设备ID" align="center">
          <template v-slot="scope">
            <el-link type="primary" @click="goToDeviceStatus(scope.row.gateway_device_id)">{{ scope.row.gateway_device_id }}</el-link>
          </template>
        </el-table-column>
        <el-table-column prop="event_name" label="设备名称" align="center" />
        <el-table-column prop="done" label="是否处理" align="center">
          <template v-slot="scope">
            <span v-if="scope.row.done === 1" style="color: blue">已处理</span>
            <span v-if="scope.row.done === 0" style="color: red">未处理</span>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="创建时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </el-card>
</template>

<script name="AlarmHistoryTable" setup>
import { reactive, onActivated, ref } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import { activeRouteTab } from '@/utils/tabKit';
import deviceStatusService from '@/service/device/DeviceStatusService';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const ids = ref([]);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});

onActivated(() => {
  getList(data.queryParams);
});

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  deviceStatusService
    .pageDeviceEvent(params)
    .then((response) => {
      if (response.success === true) {
        tableData.value = response.data.rows;
        total.value = parseInt(response.data.total);
        loading.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        loading.value = false;
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const handleSelectionChange = (val) => {
  ids.value = val;
};

const goToDeviceStatus = (id) => {
  activeRouteTab({
    path: '/device/deviceStatus',
    query: {
      gateway_device_id: id
    }
  });
};

//删除记录
const deleteRecord = (id) => {
  ElMessageBox.confirm('是否要删除当前记录？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    const ids = [];
    ids.push(id);
    deviceStatusService.deleteEvent(ids).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        getList(data.queryParams);
      }
    });
  });
};

const deleteAll = (id) => {
  ElMessageBox.confirm('是否要删除所有记录？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deviceStatusService.deleteAllEvent(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        getList(data.queryParams);
      }
    });
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
