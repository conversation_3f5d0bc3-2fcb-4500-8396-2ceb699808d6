import $ from '@/utils/axios';

/**
 * 菜单接口层
 */

/**
 * 查询导航菜单
 * @returns {*}
 */
export const findNavMenus = () => {
  return $({
    url: '/console/menu/findNavMenus',
    method: 'get'
  });
};

/**
 * 查询菜单分类
 * @returns {*}
 */
export const listMenuCategories = () => {
  return $({
    url: '/console/menu/listMenuCategories',
    method: 'get'
  });
};

/**
 * 添加菜单
 * @returns {*}
 */
export const createMenu = (data) => {
  return $({
    url: '/console/menu/createMenu',
    method: 'post',
    data
  });
};

// 修改菜单
export const updateMenu = (data) => {
  return $({
    url: '/console/menu/updateMenu',
    method: 'post',
    data
  });
};

// 删除菜单
export const deleteMenu = (menuId) => {
  return $({
    url: '/console/menu/deleteMenu/' + menuId,
    method: 'post'
  });
};

/**
 * 查询菜单树
 * @returns {*}
 */
export const getMenuTree = (category) => {
  return $({
    url: '/console/menu/getMenuTree/' + category,
    method: 'get'
  });
};

/**
 * 获取菜单
 * @returns {*}
 */
export const getMenuById = (data) => {
  return $({
    url: '/console/menu/getMenuById',
    method: 'post',
    data
  });
};
