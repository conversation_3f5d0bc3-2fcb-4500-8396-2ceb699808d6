import { reactive } from 'vue';
import { defineStore } from 'pinia';
import router from '@/router';

export const useTabs = defineStore('tabs', () => {
  const state = reactive({
    // tab列表
    tabList: [],
    // 激活的tab序号
    activedTabIndex: '',
    // 已缓存的tab
    cachedTabs: []
  });

  function unloadTab() {
    state.tabList = [];
    state.activedTabIndex = '';
    state.cachedTabs = [];
  }

  function checkAndPushTab(obj) {
    let result;
    const cachedObj = state.cachedTabs.find((item) => item.path === obj.path);
    if (cachedObj) {
      if (obj.query !== undefined && obj.query !== {}) {
        const index = state.cachedTabs.findIndex((item) => item.path === obj.path);
        state.cachedTabs[index] = obj;
        result = obj;
      } else {
        result = cachedObj;
      }
    } else {
      state.cachedTabs.push(obj);
      result = obj;
    }

    return result;
  }

  return { state, unloadTab, checkAndPushTab };
});
