<template>
  <div>
    <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
      <form-search-item>
        <ClearableChargeInput v-model="queryParams.org_department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
      </form-search-item>
      <template #button>
        <export-button :export-func="exportInvoiceRecords" :params="queryParams"></export-button>
        <div style="display: flex; align-items: center; margin-left: 10px; cursor: pointer" @click="infoDialogVisible = true">
          <el-icon color="#F56C6C"><WarningFilled /></el-icon>
          <span>规则说明</span>
        </div>
      </template>
    </FormSearch>
    <el-card style="margin-top: 10px" shadow="never">
      <el-table :data="tableData" v-loading="loading" border>
        <el-table-column type="index" :index="(index) => index + 1" label="序号" align="center" width="80" />
        <el-table-column prop="park_name" label="车场名称" align="center" />
        <el-table-column prop="group_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="device_name" label="缴费机名称" align="center">
          <template #="{ row }"> {{ row.gateway_name }}-{{ row.device_name }} </template>
        </el-table-column>
        <el-table-column prop="last_withdraw_time" label="上次提现时间" align="center" />
        <el-table-column prop="current_cash" label="当前纸币（元）" align="center">
          <template #default="scope">
            <el-button type="primary" link @click="currentCash(scope.row)">{{ scope.row.current_cash || 0 }}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template #default="scope">
            <el-button type="primary" link v-if="scope.row.current_cash" @click="withdrawal(scope.row)">提现</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="device_status_desc" label="状态" align="center">
          <template #default="scope">
            <el-button :type="scope.row.device_status == 0 ? 'danger' : 'primary'" link>{{ scope.row.device_status_desc }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        class="table-pagination"
        background
        :current-page="queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </el-card>
    <!-- 关联组织架构 -->
    <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
      <org-find-back
        :organization_id="department_id"
        :department_name="department_name"
        @orgCharge="orgCharge(false)"
        :mode="flag"
        @renderOrgTableInput="renderOrgTableInput"
      />
    </el-dialog>
    <el-dialog v-model="infoDialogVisible" title="页面使用说明" width="800">
      <div>
        <div>
          ① 缴费机名称：请<span style="color: #409eff">尽量将缴费机名称命名准确</span
          >，例如加上楼层、摆放方位等，以方便区分不同的缴费机，因为名称是缴费机在后台管理中的唯一标识；
        </div>
        <div>
          <div>② 当前纸币(元)：</div>
          <li>此数值反映了该缴费机中当前纸币的总额；</li>
          <li>鼠标滑入，可以看到当前的纸币面额分布，如：1元纸币，5张；5元纸币，10张等数据；</li>
          <li>点击该金额，可跳转查看当前纸币金额的缴费记录，即上次提取纸币到当前时间之间的现金流水；</li>
          <li>请每次提取纸币的职员，一定记得<span style="color: #409eff">每次取出现金后，都将现金出币并保存</span>；详情见缴费机机器使用说明；</li>
        </div>
        <div>
          <div>③ 状态</div>
          <li>此数值反映了该缴费机中当前纸币的总额；</li>
          <li>状态反映了该缴费机当前的使用状态，所有缴费机的异常情况均会在此次列出；</li>
          <li>根据指示的异常，<span style="color: #409eff">都请及时通知相关人员对缴费机进行管理维护，方便车主随时可以使用</span>。</li>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, defineEmits } from 'vue';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import exportButton from '@/components/exportButton.vue';
import { exportInvoiceRecords } from '@/api/charge/SelfServicePaymentApi';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
// import OrgFindBack from '@/components/OrgFindBack.vue';
import OrgFindBack from './components/OrgFindBack.vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import selfServicePaymentService from '@/service/charge/SelfServicePaymentService';

const emit = defineEmits(['change-tab']);
const queryParams = ref({
  organization_ids: undefined,
  org_department_name: undefined,
  page: 1,
  limit: 30
});
const infoDialogVisible = ref(false);
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const department_id = ref('');
const department_name = ref('');
const relatedOrgDialogVisible = ref(false);

const currentCash = (row) => {
  emit('change-tab', {
    tab: 'paymentRecord',
    searchParam: {
      gateway_id: row.gateway_id
    }
  });
};
const withdrawal = (row) => {
  ElMessageBox.confirm(`当前缴费机金额${row.current_cash}元，点击确认后，则默认缴费机实际存款金额与显示的缴费机金额相符`, '提现确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    console.log('提现确认', row);
    const parmas = {
      ...row,
      operator: JSON.parse(localStorage.getItem('user')).user_id,
      pay_machine_balance: row.current_cash,
      withdraw_amount: row.current_cash
    };
    selfServicePaymentService.saveWithdrawalRecords(parmas).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: '提现成功',
          type: 'success'
        });
        handleDataSearch();
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

const handleDataSearch = (params) => {
  console.log('handleDataSearch', params);
  loading.value = true;
  queryParams.value = Object.assign(queryParams.value, params);
  selfServicePaymentService.pagingInvoiceRecords(queryParams.value).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleAllReset = () => {
  console.log('handleAllReset');
  queryParams.value = {
    organization_ids: undefined,
    org_department_name: undefined,
    page: 1,
    limit: 30
  };
  handleDataSearch();
};
const clearDepartment = () => {
  queryParams.value.organization_ids = undefined;
  queryParams.value.org_department_name = undefined;
};
// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    department_id.value = queryParams.value.organization_ids;
    department_name.value = queryParams.value.org_department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  queryParams.value.organization_ids = arrId.toString();
  queryParams.value.org_department_name = arrName.toString();
};
const handleSizeChange = (val) => {
  queryParams.value.limit = val;
  handleDataSearch();
};
const handleCurrentChange = (val) => {
  queryParams.value.page = val;
  handleDataSearch();
};

defineExpose({
  handleDataSearch
});
</script>

<style lang="scss" scoped></style>
