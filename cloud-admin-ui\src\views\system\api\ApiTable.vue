<template>
    <el-card class="table" shadow="never">
        <div class="opers">
            <el-space>
                <el-button type="primary" @click="handleCreate()">新 增</el-button>
                <el-button type="danger" @click="batchDelete()">批量删除</el-button>
            </el-space>
        </div>
        <div ref="table">
            <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" style="height: calc(100vh - 290px)">
                <el-table-column type="selection" style="text-align: center" width="40" />
                <el-table-column prop="action" label="操作" align="center" width="100">
                    <template v-slot="scope">
                        <el-button link type="primary" @click="handleEdit(scope.row.id)"> 修改 </el-button>
                        <el-button link type="danger" @click="deleteApi(scope.row)"> 删除 </el-button>
                    </template>
                </el-table-column>
                <el-table-column prop="id" label="ID" min-width="50" align="center" />
                <el-table-column prop="permission_group_name" label="权限组名" align="center" min-width="160" />
                <el-table-column prop="permission_name" label="权限名称" align="center" min-width="130" show-overflow-tooltip />
                <el-table-column prop="permission_code" label="权限代码" align="center" min-width="200" show-overflow-tooltip />
                <el-table-column prop="permission_type_display" label="权限类型" align="center" min-width="100" />
                <el-table-column prop="name" label="接口名称" align="center" min-width="130" show-overflow-tooltip />
                <el-table-column prop="path" label="包路径" align="center" min-width="200" show-overflow-tooltip />
                <el-table-column prop="http_method_desc" label="请求方法" align="center" min-width="100" />
                <el-table-column prop="url" label="URL地址" align="center" min-width="360" />
                <el-table-column prop="anonymous" label="是否需要权限" align="center" min-width="120">
                    <template v-slot="scope">
                        <span v-if="scope.row.anonymous == 1">是</span>
                        <span v-if="scope.row.anonymous == 0">否</span>
                    </template>
                </el-table-column>
                <el-table-column prop="created_at" label="创建时间" align="center" min-width="160" />
            </el-table>
            <el-pagination
                    background
                    :current-page="data.queryParams.page"
                    :page-sizes="[10, 30, 50, 100]"
                    :page-size="data.queryParams.limit"
                    layout="total, sizes, prev, pager, next, jumper"
                    :total="total"
                    class="table-pagination"
                    @size-change="handleSizeChange"
                    @current-change="handleCurrentChange"
            />
            <el-dialog title="添加" v-model="apiCreateDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
                <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
                    <el-form-item prop="permission_group_id" label="权限组">
                        <el-select v-model="data.form.permission_group_id" style="width: 100%">
                            <el-option v-for="item in premissionGroupList" :key="item.id" :label="item.group_name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="permission_name" label="权限名称">
                        <el-input v-model="data.form.permission_name" />
                    </el-form-item>
                    <el-form-item prop="permission_code" label="权限代码">
                        <el-input v-model="data.form.permission_code" />
                    </el-form-item>
                    <el-form-item prop="name" label="接口名称">
                        <el-input v-model="data.form.name" />
                    </el-form-item>
                    <el-form-item prop="path" label="包路径">
                        <el-input v-model="data.form.path" />
                    </el-form-item>
                    <el-form-item prop="http_method" label="请求方式">
                        <el-select v-model="data.form.http_method" style="width: 100%">
                            <el-option v-for="item in methods" :key="item.value" :label="item.key" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="url" label="URL地址">
                        <el-input v-model="data.form.url" />
                    </el-form-item>
                    <el-form-item prop="anonymous" label="是否需要权限">
                        <el-switch v-model="data.form.anonymous" active-value="1" inactive-value="0" />
                    </el-form-item>
                </el-form>
                <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createApi(addForm)">确 定</el-button>
          </span>
                </template>
            </el-dialog>
            <el-dialog title="修改" v-model="apiUpdateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
                <el-form ref="editForm" label-width="120px" :rules="data.rules" :model="data.updateForm">
                    <el-form-item prop="http_method" label="权限组">
                        <el-select v-model="data.updateForm.permission_group_id" style="width: 100%">
                            <el-option v-for="item in premissionGroupList" :key="item.id" :label="item.group_name" :value="item.id" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="permissionName" label="权限名称">
                        <el-input v-model="data.updateForm.permission_name" />
                    </el-form-item>
                    <el-form-item prop="path" label="权限代码">
                        <el-input v-model="data.updateForm.permission_code" />
                    </el-form-item>
                    <el-form-item prop="name" label="接口名称">
                        <el-input v-model="data.updateForm.name" />
                    </el-form-item>
                    <el-form-item prop="path" label="包路径">
                        <el-input v-model="data.updateForm.path" />
                    </el-form-item>
                    <el-form-item prop="http_method" label="请求方式">
                        <el-select v-model="data.updateForm.http_method" style="width: 100%">
                            <el-option v-for="item in methods" :key="item.value" :label="item.key" :value="item.value" />
                        </el-select>
                    </el-form-item>
                    <el-form-item prop="url" label="URL地址">
                        <el-input v-model="data.updateForm.url" />
                    </el-form-item>
                    <el-form-item prop="anonymous" label="是否需要权限">
                        <el-switch v-model="data.updateForm.anonymous" active-value="1" inactive-value="0" />
                    </el-form-item>
                </el-form>
                <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateApi(editForm)">确 定</el-button>
          </span>
                </template>
            </el-dialog>
        </div>
    </el-card>
</template>

<script name="ApiTable" setup>
    import { reactive, onActivated, ref, getCurrentInstance } from 'vue';
    import { ElMessage, ElMessageBox } from 'element-plus';
    import apiService from '@/service/system/ApiManageService';
    import commonService from '@/service/common/CommonService';

    const { proxy } = getCurrentInstance();
    const addForm = ref();
    const editForm = ref();
    const apiIds = ref([]);
    const tableData = ref([]);
    const loading = ref(false);
    const methods = ref([]);
    const types = ref([]);
    const premissionGroupList = ref([]);
    const total = ref(0);
    const apiCreateDialogVisible = ref(false);
    const apiUpdateDialogVisible = ref(false);
    const data = reactive({
        queryParams: {
            page: 1,
            limit: 30
        },
        form: {
            permission_group_id: undefined,
            permission_name: undefined,
            permission_code: undefined,
            name: undefined,
            path: undefined,
            http_method: undefined,
            url: undefined,
            anonymous: '1'
        },
        updateForm: {},
        rules: {
            permission_group_id: [
                {
                    required: true,
                    message: '请选择权限组',
                    trigger: 'blur'
                }
            ],
            permission_name: [
                {
                    required: true,
                    message: '请输入权限名称',
                    trigger: 'blur'
                }
            ],
            permission_code: [
                {
                    required: true,
                    message: '请输入权限代码',
                    trigger: 'blur'
                }
            ],
            name: [
                {
                    required: true,
                    message: '请输入接口名称',
                    trigger: 'blur'
                }
            ],
            path: [
                {
                    required: true,
                    message: '请输入包路径',
                    trigger: 'blur'
                }
            ],
            http_method: [
                {
                    required: true,
                    message: '请选择请求方式',
                    trigger: 'blur'
                }
            ],
            url: [
                {
                    required: true,
                    message: '请输入URL地址',
                    trigger: 'blur'
                }
            ],
            anonymous: [
                {
                    required: true,
                    message: '请选择是否需要权限',
                    trigger: 'blur'
                }
            ]
        }
    });

    onActivated(() => {
        initSelect();
        getList(data.queryParams);
        getPremissionGroupList();
    });

    const getList = (params) => {
        loading.value = true;
        params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
        params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
        data.queryParams = params;
        apiService.pagingApis(params).then((response) => {
            if (response.success === true) {
                tableData.value = response.data.rows;
                total.value = parseInt(response.data.total);
                loading.value = false;
            } else {
                ElMessage({
                    message: response.detail_message != '' ? response.detail_message : response.message,
                    type: 'error'
                });
                loading.value = false;
            }
        });
    };
    const handleCreate = () => {
        data.form = {
            permission_group_id: undefined,
            permission_name: undefined,
            permission_code: undefined,
            name: undefined,
            path: undefined,
            http_method: undefined,
            url: undefined,
            anonymous: '1'
        };
        apiCreateDialogVisible.value = true;
    };
    const initSelect = () => {
        const param = [
            { enum_key: 'methods', enum_value: 'EnumHttpMethodType' },
            { enum_key: 'types', enum_value: 'EnumPermissionType' }
        ];
        commonService.findEnums('system', param).then((response) => {
            methods.value = response.data.methods;
            types.value = response.data.types;
        });
    };
    const getPremissionGroupList = () => {
        apiService.permissionGroupList().then((response) => {
            if (response.success === true) {
                premissionGroupList.value = response.data;
            } else {
                ElMessage({
                    message: response.detail_message != '' ? response.detail_message : response.message,
                    type: 'error'
                });
            }
        });
    };

    const handleSizeChange = (val) => {
        data.queryParams.limit = val;
        getList(data.queryParams);
    };
    const handleCurrentChange = (val) => {
        data.queryParams.page = val;
        getList(data.queryParams);
    };
    const createApi = (addForm) => {
        addForm.validate().then(() => {
            apiService
                .createApi(data.form)
                .then((response) => {
                    if (response.success === true) {
                        ElMessage({
                            message: response.message,
                            type: 'success'
                        });
                        getList(data.queryParams);
                        addForm.resetFields();
                        apiCreateDialogVisible.value = false;
                    } else {
                        ElMessage({
                            message: response.detail_message != '' ? response.detail_message : response.message,
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    getList(data.queryParams);
                });
        });
    };
    const handleSelectionChange = (val) => {
        apiIds.value = val;
    };
    const batchDelete = () => {
        if (apiIds.value.length === 0) {
            ElMessage({
                message: '请勾选要删除的Api接口',
                type: 'warning'
            });
        } else {
            ElMessageBox.confirm('确定要删除吗？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                const pushIds = [];
                for (let i = 0; i < apiIds.value.length; i++) {
                    pushIds.push(parseInt(apiIds.value[i].id));
                }
                const param = {
                    ids: pushIds
                };
                apiService
                    .deleteApis(param)
                    .then((response) => {
                        if (response.success === true) {
                            ElMessage({
                                message: response.message,
                                type: 'success'
                            });
                            getList(data.queryParams);
                        } else {
                            ElMessage({
                                message: response.message,
                                type: 'error'
                            });
                        }
                    })
                    .catch(() => {
                        getList(data.queryParams);
                    });
            });
        }
    };
    const deleteApi = (val) => {
        apiIds.value[0] = val;
        batchDelete();
    };
    const handleEdit = (id) => {
        apiService.getApiById(id).then((response) => {
            if (response.success === true) {
                data.updateForm = {
                    id: response.data.id,
                    name: response.data.name,
                    path: response.data.path,
                    http_method: response.data.http_method,
                    url: response.data.url,
                    permission_id: response.data.permission_id,
                    permission_name: response.data.permission_name,
                    permission_code: response.data.permission_code,
                    permission_group_id: response.data.permission_group_id,
                    permission_group_name: response.data.permission_group_name,
                    anonymous: response.data.anonymous + ''
                };
                apiUpdateDialogVisible.value = true;
            } else {
                ElMessage({
                    message: response.detail_message != '' ? response.detail_message : response.message,
                    type: 'error'
                });
            }
        });
    };
    const updateApi = (editForm) => {
        editForm.validate().then(() => {
            apiService
                .updateApi(data.updateForm)
                .then((response) => {
                    if (response.success === true) {
                        ElMessage({
                            message: response.message,
                            type: 'success'
                        });
                        getList(data.queryParams);
                        editForm.resetFields();
                        apiUpdateDialogVisible.value = false;
                    } else {
                        ElMessage({
                            message: response.detail_message != '' ? response.detail_message : response.message,
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    getList(data.queryParams);
                });
        });
    };

    // 取消
    const createCancel = (addForm) => {
        addForm.resetFields();
        apiCreateDialogVisible.value = false;
    };
    // 取消
    const updateCancel = (editForm) => {
        editForm.resetFields();
        apiUpdateDialogVisible.value = false;
    };
    const closeAddDialog = (addForm) => {
        addForm.resetFields();
    };
    const closeEditDialog = (editForm) => {
        editForm.resetFields();
    };
    defineExpose({
        getList
    });
</script>
<style lang="scss" scoped>
  .example-showcase .el-dropdown-link {
    cursor: pointer;
    color: var(--el-color-primary);
    display: flex;
    align-items: center;
  }
</style>
