<template>
  <div style="width: 100%; padding: 0 20px; box-sizing: border-box; margin-top: 20px">
    <el-tabs v-model="activeName" class="demo-tabs">
      <el-tab-pane label="同比" name="0"></el-tab-pane>
      <el-tab-pane label="环比" name="1"></el-tab-pane>
    </el-tabs>
    <div v-if="activeName == '0'">
      <!-- 付费临停车次同比分析 -->
      <div ref="chartRef1" style="width: 100%; height: 400px"></div>
      <!-- 付费临停收入同比分析 -->
      <div ref="chartRef2" style="width: 100%; height: 400px"></div>
      <!-- 付费临停总时长同比分析 -->
      <div ref="chartRef3" style="width: 100%; height: 400px"></div>
    </div>
    <div v-else>
      <!-- 付费临停车次环比分析 -->
      <div ref="chartRef4" style="width: 100%; height: 400px"></div>
      <!-- 付费临停收入环比分析 -->
      <div ref="chartRef5" style="width: 100%; height: 400px"></div>
      <!-- 付费临停总时长环比分析 -->
      <div ref="chartRef6" style="width: 100%; height: 400px"></div>
    </div>
  </div>
</template>

<script setup>
import * as echarts from 'echarts';
import { onBeforeUnmount, ref, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { getCompareAnalysisChart } from '@/api/statisticalReport/ComparativeAnalysisApi';

const activeName = ref('0')
const chartRef1 = ref(null);
const chartRef2 = ref(null);
const chartRef3 = ref(null);
const chartRef4 = ref(null);
const chartRef5 = ref(null);
const chartRef6 = ref(null);
let chartInstance1 = null;
let chartInstance2 = null;
let chartInstance3 = null;
const sourceData = ref({});

// chartInstance-echart实例 titleText-图表标题 yAxisData-左侧纵坐标名称 unit-左侧纵坐标单位 seriesDatas-图表数据组
const initChart = (chartInstance, titleText, yAxisData, unit, seriesDatas) => {
  let option = {
    title: {
      text: titleText,
      top: 10,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        crossStyle: {
          color: '#999'
        }
      }
    },
    legend: {
      data: activeName.value === '0' ? ['当期', '对比期', '同比变化率'] : ['当期', '对比期', '环比变化率'],
      bottom: 10,
      left: 'center'
    },
    xAxis: [
      {
        type: 'category',
        data: sourceData.value.monthList || [],
        axisPointer: {
          type: 'shadow'
        }
      }
    ],
    yAxis: [
      {
        type: 'value',
        name: `${yAxisData}(${unit})`,
        axisLabel: {
          formatter: '{value}'
        },
      },
      {
        type: 'value',
        name: activeName.value === '0' ? '同比变化率(%)' : '环比变化率(%)',
        axisLabel: {
          formatter: '{value}'
        },
      }
    ],
    series: [
      {
        name: '当期',
        type: 'bar',
        color: '#009dff',
        tooltip: {
          valueFormatter: function (value) {
            return value + unit;
          }
        },
        data: seriesDatas[0]
      },
      {
        name: '对比期',
        type: 'bar',
        color: '#1ffd5f',
        tooltip: {
          valueFormatter: function (value) {
            return value + unit;
          }
        },
        data: seriesDatas[1]
      },
      {
        name: activeName.value === '0' ? '同比变化率' : '环比变化率',
        type: 'line',
        color: '#feb45d',
        yAxisIndex: 1,
        tooltip: {
          valueFormatter: function (value) {
            return value + '%';
          }
        },
        data: seriesDatas[2]
      }
    ]
  };
  chartInstance.setOption(option);
};

watch(
  () => activeName.value,
  () => {
    init();
  }
);

onBeforeUnmount(() => {
  // 组件卸载时销毁实例
  if (chartInstance1) {
    window.removeEventListener('resize', chartInstance1.resize);
    chartInstance1.dispose();
  }
  if (chartInstance2) {
    window.removeEventListener('resize', chartInstance2.resize);
    chartInstance2.dispose();
  }
  if (chartInstance3) {
    window.removeEventListener('resize', chartInstance3.resize);
    chartInstance3.dispose();
  }
});

const getCAChart = (queryParams) => {
  const params = {
    park_id: queryParams.park_id,
    start_time: queryParams.start_time,
    end_time: queryParams.end_time
  };
  getCompareAnalysisChart(params).then((response) => {
    if (response.success === true) {
      sourceData.value = response.data;
      init();
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const init = () => {
  nextTick(() => {
    if (activeName.value == '0') {
      // 同比
      chartInstance1 = echarts.init(chartRef1.value);
      chartInstance2 = echarts.init(chartRef2.value);
      chartInstance3 = echarts.init(chartRef3.value);
      const arr1 = [sourceData.value.chart1.currentCarList, sourceData.value.chart1.lastYearCarList, sourceData.value.chart2.carYoyRateList];
      const arr2 = [sourceData.value.chart3.currentIncomeList, sourceData.value.chart3.lastYearIncomeList, sourceData.value.chart4.incomeYoyRateList];
      const arr3 = [sourceData.value.chart5.currentHoursList, sourceData.value.chart5.lastYearHoursList, sourceData.value.chart6.hoursYoyRateList];
      initChart(chartInstance1, '付费临停车次同比分析', '车次', '次', arr1);
      initChart(chartInstance2, '付费临停收入同比分析', '金额', '元', arr2);
      initChart(chartInstance3, '付费临停总时长同比分析', '时长', '小时', arr3);
    } else {
      // 环比
      chartInstance1 = echarts.init(chartRef4.value);
      chartInstance2 = echarts.init(chartRef5.value);
      chartInstance3 = echarts.init(chartRef6.value);
      const arr1 = [sourceData.value.chart1.currentCarList, sourceData.value.chart1.lastMonthCarList, sourceData.value.chart2.carMomRateList];
      const arr2 = [
        sourceData.value.chart3.currentIncomeList,
        sourceData.value.chart3.lastMonthIncomeList,
        sourceData.value.chart4.incomeMomRateList
      ];
      const arr3 = [sourceData.value.chart5.currentHoursList, sourceData.value.chart5.lastMonthHoursList, sourceData.value.chart6.hoursMomRateList];
      initChart(chartInstance1, '付费临停车次环比分析', '车次', '次', arr1);
      initChart(chartInstance2, '付费临停收入环比分析', '金额', '元', arr2);
      initChart(chartInstance3, '付费临停总时长环比分析', '时长', '小时', arr3);
    }
    // 响应窗口变化
    window.addEventListener('resize', () => {
      chartInstance1.resize();
      chartInstance2.resize();
      chartInstance3.resize();
    });
  });
};

defineExpose({
  getCAChart
});
</script>