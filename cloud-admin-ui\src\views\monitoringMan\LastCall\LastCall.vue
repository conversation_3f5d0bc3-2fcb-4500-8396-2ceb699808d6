<template>
  <div class="lastCallBox">
    <detail ref="detailRef"></detail>
  </div>
</template>

<script setup>
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { useUser } from '@/stores/user';
import { onActivated, ref } from 'vue'; //引入vue
import { useRoute } from 'vue-router';
import detail from '../../system/callAcceptance/detail.vue';
const duty = useDuty();
const user = useUser();
const route = useRoute();
const detailRef = ref(null);
const detId = ref(null);
onActivated(() => {
  if (Object.keys(route.query).length !== 0 && undefined !== route.query.detId) {
    detId.value = route.query.detId;
  }
  console.log(route.query.type, 'routerouterouterouterouterouteroute');

  init();
});
const init = () => {
   detailRef.value.isLastType = true
  UnattendedApi.recordgetDetailById(detId.value).then((res) => {
    if (res.success && res.data) {
      // detailData.value = res.data;
      detailRef.value.detailData = res.data;
     
    }
  });
};
</script>

<style lang="scss" scoped>
* {
  box-sizing: border-box;
  user-select: none;
}

.lastCallBox {
  height: 100%;
  width: 100%;
  display: flex;
  gap: 10px;

  > div {
    border-radius: 5px;
  }

  .imgCol {
    flex: 3.5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    > div {
      flex: 1;
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;

      .imgTit {
        height: 40px;
        display: flex;
        font-size: 14px;
        align-items: center;
        padding-left: 10px;
        border-bottom: 1px solid #e7e7e7;
        font-weight: 700;
        color: #01a7f0;
      }

      .img {
        height: calc(100% - 40px);
        width: 100%;
        padding: 5px;

        img {
          height: 100%;
          width: 100%;
          border-radius: 5px;
        }
      }
    }
  }

  .infoCol-t {
    height: 40px;
    display: flex;
    align-items: center;
    font-size: 14px;
    font-weight: 700;

    .fill {
      height: 35%;
      width: 3px;
      border-radius: 3px;
      background-color: #01a7f0;
      margin-right: 5px;
    }
  }

  .infoCol {
    flex: 5;
    display: flex;
    flex-direction: column;
    gap: 10px;

    > div {
      background-color: #fff;
      border-radius: 5px;
      box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    }

    .infoCol-one {
      flex: 2;
      padding: 0 10px 10px 10px;

      .infoCol-b {
        height: calc(100% - 40px);
        width: 100%;
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        padding: 5px;
        font-size: 14px;
        display: grid;
        background-color: #fafafa;
        grid-template-columns: repeat(2, 1fr);
        align-items: center;
      }
    }

    .infoCol-two {
      flex: 4;
      padding: 0 10px 10px 10px;
      font-size: 14px;

      .carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 5px;
        color: #4b7a02;

        div:first-child {
          padding: 3px 20px;
          background-color: #01a7f0;
          border-radius: 5px;
          color: #fff;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }

      .infoCol-two-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
      }
    }

    .infoCol-three {
      flex: 4;
      padding: 0 10px 10px 10px;

      .infoCol-three-con {
        height: calc(100% - 40px - 40px);
        border: 1px solid #9fc1e1;
        border-radius: 5px;
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        background-color: #fafafa;
        align-items: center;
        padding: 5px;
        font-size: 14px;
      }

      .three-carInfo {
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-weight: 700;
        font-size: 14px;
      }
    }
  }

  .reasonCol {
    flex: 1.5;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.12);
    padding: 0 10px 10px 10px;
    font-size: 14px;

    .buttonClass-reson {
      width: fit-content;
      padding: 2px 10px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #d9d9d9;
      border-radius: 3px;
      margin-top: 10px;
    }

    .reasonCol-lx {
      color: #7b7b7b;
    }
  }
}
</style>
