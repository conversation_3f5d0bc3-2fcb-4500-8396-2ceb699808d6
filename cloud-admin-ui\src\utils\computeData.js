import decimal from 'decimal.js'
//相加
export const add = (num1, num2) => {
    return new decimal(num1).plus(num2).toNumber()
}
//相乘
export const multiply = (num1, num2) => {
    if (!num1 || !num2) return 0
    return new decimal(num1).mul(num2).toNumber()
}
//相减
export const subtract = (num1, num2) => {
    return new decimal(num1).minus(num2).toNumber()
}
//相除
export const divide = (num1, num2) => {
    return new decimal(num1).div(num2).toNumber()
}
