import * as appAdmin from '@/api/system/AppAdminApi';

/**
 * 应用管理
 */
export default {
  /**
   *分页查询APP应用
   */
  queryApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.queryApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 创建APP信息
   */
  createApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.createApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 编辑APP信息
   */
  updateApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.updateApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 分页查询版本历史信息
   */
  queryHistApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.queryHistApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询单个APP对象
   */
  getApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.getApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 提交新版本信息
   */
  releaseApp(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.releaseApp(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分管车场授权
   */
  parkingAuthority(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.parkingAuthority(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询员工授权车场信息
   */
  appParkList(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.appParkList(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询车场信息
   */
  getParkList(data) {
    return new Promise((resolve, reject) => {
      try {
        appAdmin.getParkList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
