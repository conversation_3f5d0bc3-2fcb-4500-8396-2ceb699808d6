<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item><el-input v-model="form.queryParams.park_name" placeholder="车场名称" readonly="true"
      @click="authCharge(true)" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.plate_no" placeholder="车牌号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.intercepted_name" placeholder="人员姓名" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.intercept_types" placeholder="拦截类型" multiple clearable>
        <el-option v-for="item in types" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.manual_flags" placeholder="是否人工录入" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="daterange"
        range-separator="至"
        start-placeholder="记录开始时间"
        end-placeholder="记录结束时间"
        style="width: 100%"
        format="YYYY-MM-DD"
        value-format="YYYY-MM-DD"
      />
    </form-search-item>
  </FormSearch>
    <!-- 关联车场 -->
    <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag"
      @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarInterceptRecordSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '@/views/car/ParkFindBack.vue';
import { reactive, ref, onMounted } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    plate_no: '',
    intercepted_name: '',
    manual_flags: [],
    intercept_types: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  },
  dateRange: []
});
const types = ref([]);
const states = ref([]);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
onMounted(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [
    { enum_key: 'states', enum_value: 'EnumInterceptRecordManual' },
    { enum_key: 'types', enum_value: 'EnumInterceptType' }
  ];
  commonService.findEnums('park', param).then((response) => {
    states.value = response.data.states;
    types.value = response.data.types;
  });
};

const handleDataSearch = () => {
  if (undefined !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_name: '',
    plate_no: '',
    intercepted_name: '',
    manual_flags: [],
    intercept_types: [],
    start_time: undefined,
    end_time: undefined,
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  console.log(val[0].park_name)
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
