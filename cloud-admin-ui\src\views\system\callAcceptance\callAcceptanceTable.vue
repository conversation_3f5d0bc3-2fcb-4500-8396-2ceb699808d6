<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <!-- <el-space>
        <el-button type="primary" @click="batchDelete()">批量删除</el-button>
      </el-space> -->
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border @selection-change="handleSelectionChange" style="height: calc(100vh - 245px)">
        <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" @click="toDeatil(scope.row)"> 详情 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="created_at" label="受理日期" align="center" min-width="100" />
        <el-table-column prop="park_name" label="车场名称" align="center" min-width="180" />
        <el-table-column prop="gateway_name" label="呼叫通道" align="center" min-width="120" />
        <el-table-column prop="call_type_desc" label="呼叫类型" align="center" min-width="100" />
        <el-table-column prop="user_name" label="受理人员" align="center" min-width="120" />
        <el-table-column prop="call_time_desc" label="处理时长" align="center" min-width="100" />
        <el-table-column prop="reason_type_desc" label="呼叫原因" align="center" min-width="120">
          <template v-slot="scope">
            <div>{{ scope.row.reason_type_desc == '其他' ? scope.row.remark : scope.row.reason_type_desc }}</div>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />

      <!-- 关联车场 -->
      <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
        <car-free-find-back
          :park_id="park_id"
          :park_name="park_name"
          @authCharge="authCharge(false, '')"
          :mode="flag"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
      <el-dialog v-model="dialogVisible" :title="title" width="40%">
        <img w-full style="max-width: 100%; height: auto; margin: auto; display: block" :src="dialogImageUrl" alt="Preview Image" />
      </el-dialog>
      <el-dialog v-model="detailDialogVisible" title="呼叫受理记录详情" width="80%">
        <detail ref="detailRef"></detail>
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="CarFreeTable" setup>
import carFreeService from '@/service/car/CarFreeService';
import commonService from '@/service/common/CommonService';
import UnattendedApi from '@/service/system/Unattended';
import { useUser } from '@/stores/user';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref, watch } from 'vue';
import CarFreeFindBack from './CarFreeFindBack.vue';
import detail from './detail.vue';
const user = useUser();
// import { useUser } from '@/stores/user';

// const user = useUser();

const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const spaceCodeList = ref([]);
const businessFormatList = ref([]);
commonService.findEnums('park', [{ enum_key: 'businessFormatType', enum_value: 'EnumBusinessFormatType' }]).then((response) => {
  businessFormatList.value = response.data.businessFormatType;
});

const total = ref(0);
const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const carFreeIds = ref([]);
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  uploadExcelFile: {
    space_upload_url: undefined
  },
  form: {
    park_id: '',
    park_name: '',
    space_id: undefined,
    space_code: undefined,
    plate_no: undefined,
    business_format: undefined,
    name: undefined,
    mobile: '',
    is_remind: '',
    validity_date: [],
    effective_start_time: undefined,
    effective_end_time: undefined,
    audit_data: undefined,
    audit_data_name: undefined
  },
  updateForm: {},
  reviewForm: {
    ids: []
  },
  deleteForm: {
    ids: []
  },
  cancelReviewForm: {
    id: undefined
  },
  rules: {}
});
onMounted(() => {
  getList(data.queryParams);
  status.value = true;
  window.addEventListener('message', handleMessage, false);
});
const handleMessage = () => {
  console.log('接收');
  getList(data.queryParams);
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  UnattendedApi.recordlist(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSelectionChange = (val) => {
  carFreeIds.value = val;
};

// 选择车场
const authCharge = (visible, mode) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
    // 清空车位编号，需重新选择
    data.form.space_id = '';
    carFreeService.listParkSpace(data.form.park_id).then((response) => {
      if (response.success === true) {
        spaceCodeList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
    // 清空车位编号，需重新选择
    data.updateForm.space_id = '';
    carFreeService.listParkSpace(data.updateForm.park_id).then((response) => {
      if (response.success === true) {
        spaceCodeList.value = response.data;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  }
};
watch(spaceCodeList, () => {
  if (spaceCodeList.value?.length > 0) {
    data.form.space_id = spaceCodeList.value[0].id;
    data.form.space_code = spaceCodeList.value[0].code;
  }
});
const detailDialogVisible = ref(false);
const detailRef = ref(null);
const toDeatil = (item) => {
  detailDialogVisible.value = true;
  UnattendedApi.recordgetDetailById(item.id).then((res) => {
    if (res.success && res.data) {
      detailRef.value.detailData = res.data;
      // detailData.value = res.data;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 80px;
  // width: 148px;
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;

  .upload-state {
    font-size: 12px;
    text-align: center;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-operations {
    display: none;
    width: 100%;
    height: 100%;
    font-size: 20px;
    background-color: var(--el-overlay-color-lighter);
    color: #fff;
    cursor: default;
    justify-content: center;
    align-items: center;
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
  }

  .upload-operations span {
    margin: 0 7px;
    cursor: pointer;
  }
}

:deep(.el-upload--picture-card) {
  position: relative;
}

:deep(.el-upload--picture-card:hover .upload-operations) {
  display: inline-flex;
  opacity: 1;
  transition: opacity var(--el-transition-duration);
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin: 0 20px 20px 0;
}
</style>
