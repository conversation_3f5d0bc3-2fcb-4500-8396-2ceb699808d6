/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 查询月汇总对账文件
export const monthYeeSettle = (data) => {
  return $({
    url: `/console/pay/order/park/monthYeeSettle/${data.parkId}/${data.month}`,
    method: 'get'
  });
};

// 查询日汇总对账文件
export const dayYeeSettle = (data) => {
  return $({
    url: `/console/pay/order/park/dayYeeSettle/${data.parkId}/${data.date}`,
    method: 'get'
  });
};

// 下载交易日对账文件到本地
export const settleFileDownload = (data) => {
  return $({
    // url: `/console/pay/order/park/settleFileDownload/${data.parkId}/${data.date}`,
    url: `/console/statistics/yop/stop/exportYopAccRecords`,
    method: 'post',
    data
  });
};
//下载账单
export const downloadOrderData = (data) => {
  return $.post('/console/pay/order/park/settleNewFileDownload', { park_id:data.park_id,settle_date:data.date })
}