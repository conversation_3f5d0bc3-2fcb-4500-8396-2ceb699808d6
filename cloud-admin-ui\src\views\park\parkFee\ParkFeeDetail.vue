<template>
  <div class="container">
    <el-card shadow="hover">
      <template #header>
        <div style="display: inline-block; line-height: 32px">基础设置</div>
      </template>
      <div>
        <el-form ref="addForm" :model="form" label-width="170px">
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="规则名称">
                {{ form.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="车辆类型">
                {{ form.car_type_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="关联子场">
                {{ form.park_region_name }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="同车重新起收限制"> {{ form.same_car_time }}&nbsp;时 </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="计费精度">{{ form.fee_precision_desc }} </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="费用精度"> {{ form.cost_precision_desc }} </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="进位方式">
                {{ form.trunc_mode_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跨分段计费方式">
                {{ form.cross_time_fee_type_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="跨分段起收计费方式">
                {{ form.cross_time_start_fee_type_desc }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="单笔封顶费用">
                <span v-if="form.bill_top_mode == 0">无</span>
                <span v-else>{{ form.bill_top_money }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="首日限额">
                <span v-if="form.first_day_limit_mode == 0">无</span>
                <span v-else>{{ form.first_day_limit_money }}</span>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="每日限额">
                <span v-if="form.each_day_limit_mode == 0">无</span>
                <span v-else>{{ form.each_day_limit_money }}</span>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="提前缴费出场时限"> {{ form.pre_payment_time_limit }}&nbsp;分 </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="二次免费时长"> {{ form.pre_payment_time_limit_two }}&nbsp;分 </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="生效日期">
                {{ form.valid_start_time }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="超过24小时计算免费时长">
                {{ form.is_free_first_time == 0 ? '是' : '否' }}
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
    </el-card>
    <el-card shadow="hover" style="margin-top: 10px">
      <template #header>
        <div style="display: inline-block; line-height: 32px">计费规则</div>
      </template>
      <div class="feeSetting">
        <div v-if="form.fee_rules === undefined || form.fee_rules.length > 0">
          <el-card class="box-card" v-for="item in form.fee_rules" :key="item.id" style="width: 18%">
            <template #header>
              <span>{{ item.name }}</span>
            </template>
            <div class="textItem">
              <div class="tag">时间段</div>
              <div class="right" style="color: rgb(214, 110, 110)">
                {{ item.start_time + '-' + item.end_time }}
              </div>
            </div>
            <div class="textItem">
              <div class="tag">计费方式</div>
              <div class="right">{{ item.type_desc }}</div>
            </div>
            <!-- 类型为1 -->
            <div v-if="item.type == 1">
              <div class="textItem">
                <div class="tag">收费金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.times_fee.money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.times_fee.free_time }}</span> 分钟
                </div>
              </div>
            </div>
            <!-- 类型为2 -->
            <div v-if="item.type == 3">
              <div class="textItem">
                <div class="tag">起收时长（分）</div>
                <div class="right">
                  <span style="color: rgb(147, 162, 251)"> {{ item.period_fee.start_fee_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">起收单位时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">起收单位金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.start_fee_unit_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续费单位时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续费单位金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.renew_fee_unit_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.period_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.period_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.period_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
            <!-- 类型为3 -->
            <div v-if="item.type == 2">
              <div class="textItem">
                <div class="tag">起收时长（分）</div>
                <div class="right" style="color: rgb(147, 162, 251)">{{ item.duration_fee.start_fee_time }}</div>
                分钟
              </div>
              <div class="textItem">
                <div class="tag">起收金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.start_fee_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续收时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">续收金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.renew_fee_money }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.duration_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.duration_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.duration_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
            <!-- 类型4 -->
            <div v-if="item.type == 4">
              <div class="textItem">
                <div class="tag">阶段时长（分）</div>
                <div class="right">{{ item.stair_fee.stair_time }}分钟</div>
              </div>
              <div class="textItem" v-for="(money, index) in item.stair_fee.stair_moneys" :key="index">
                <div class="tag">第{{ index + 1 }}阶段金额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ money.value }}</span> 元
                </div>
              </div>
              <div class="textItem">
                <div class="tag">免费时长（分）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.free_time }}</span> 分钟
                </div>
              </div>
              <div class="textItem">
                <div class="tag">满24小时是否从新计算</div>
                <div class="right">
                  <span v-if="item.stair_fee.beyond_day_calculate_again_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.stair_fee.beyond_day_calculate_again_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">不足一个时段是否计费</div>
                <div class="right">
                  <span v-if="item.stair_fee.not_enough_time_fee_type == 1" style="color: rgb(214, 110, 110)">是</span>
                  <span v-if="item.stair_fee.not_enough_time_fee_type == 0" style="color: rgb(214, 110, 110)">否</span>
                </div>
              </div>
              <div class="textItem">
                <div class="tag">时限额（元）</div>
                <div class="right">
                  <span style="color: rgb(214, 110, 110)">{{ item.stair_fee.time_limit_money }}</span
                  >元
                </div>
              </div>
            </div>
          </el-card>
        </div>
        <el-empty v-else description="暂无数据" style="height: 300px"></el-empty>
      </div>
    </el-card>
    <div class="fixed-bottom">
      <el-button @click="cancel">取 消</el-button>
    </div>
  </div>
</template>

<script name="ParkFeeDetail" setup>
import parkFeeService from '@/service/park/ParkFeeService';
import { reactive, onActivated, ref } from 'vue';
import { closeCurrentTab } from '@/utils/tabKit';
import { useRoute } from 'vue-router';

const route = useRoute();
const form = reactive({});
const parkId = ref('');
onActivated(() => {
  const feeId = route.query.feeId;
  parkId.value = route.query.parkId;
  getParkFee(feeId);
});

const getParkFee = (feeId) => {
  parkFeeService.getParkFeeById(feeId).then((response) => {
    form.name = response.name;
    form.car_type_desc = response.car_type_desc;
    form.park_region_name = response.park_region_name;
    form.same_car_time = response.same_car_time;
    form.fee_precision_desc = response.fee_precision_desc;
    form.cost_precision_desc = response.cost_precision_desc;
    form.trunc_mode_desc = response.trunc_mode_desc;
    form.cross_time_fee_type_desc = response.cross_time_fee_type_desc;
    form.cross_time_start_fee_type_desc = response.cross_time_start_fee_type_desc;
    form.bill_top_mode = response.bill_top_mode;
    form.bill_top_money = response.bill_top_money;
    form.first_day_limit_mode = response.first_day_limit_mode;
    form.first_day_limit_money = response.first_day_limit_money;
    form.each_day_limit_mode = response.each_day_limit_mode;
    form.each_day_limit_money = response.each_day_limit_money;
    form.valid_start_time = response.valid_start_time;
    form.is_free_first_time = response.is_free_first_time;
    form.pre_payment_time_limit = response.pre_payment_time_limit;
    form.pre_payment_time_limit_two = response.pre_payment_time_limit_two;
    form.fee_rules = response.fee_rules;
    const rules = form.fee_rules;
    rules.forEach(function (item) {
      if (item.type == 4) {
        const list = [];
        const arr = item.stair_fee.stair_moneys;
        arr.forEach(function (data) {
          const param = {
            value: data
          };
          list.push(param);
        });
        item.stair_fee.stair_moneys = list;
      }
    });
  });
};

const cancel = () => {
  closeCurrentTab({
    path: '/park/parkAdmin',
    query: {
      parkId: parkId.value,
      redirect_tab: 'parkFee'
    }
  });
};
</script>

<style lang="scss" scoped>
.feeSetting {
  width: 100%;
  height: 100%;
}

.box-card {
  position: relative;
  color: rgb(90, 90, 90);
  margin-right: 10px;
  margin-bottom: 10px;
  float: left;
}
.right {
  width: 200px;
  height: 30px;
  float: right;
  margin: -27px auto;
  text-align: right;
  line-height: 35px;
}

.textItem {
  margin-top: 10px;
}
</style>
