/*
 * @ProjectName: 惠达万安机电
 * @Author: huangquanya
 * @Date: 2024-05-27 19:36:21
 * @FilePath: \new-wanda-park\cloud-admin-ui\src\api\login\LoginApi.js
 * @Description: {}
 */
import $ from '@/utils/axios';

/**
 * 登录/退出登录
 */
/**
 * 获取验证码
 * @returns
 */
export const getCaptcha = () => {
  return $({
    url: '/console/auth/getCaptcha?t=' + new Date().getTime().toString(),
    method: 'get'
  });
};

export const getTokenByIamToken = (token, loginType) => {
  return $({
    url: `/console/auth/loginByIAM?t=${new Date().getTime().toString()}&loginType=${loginType}`,
    method: 'post',
    headers: {
      Authorization: `Bearer ${token}`
    }
  });
};

/**
 * 登录
 * @param {*} data
 * @returns
 */
export const login = (data) => {
  return $({
    url: '/console/auth/login',
    method: 'post',
    data
  });
};

/**
 * 退出登录
 * @param {*} data
 * @returns
 */
export const logout = () => {
  return $({
    url: '/console/auth/logout',
    method: 'post'
  });
};

/**
 * 强制修改密码
 * @param {*} data
 * @returns
 */
export const forceUpdatePassword = (data) => {
  return $({
    url: '/console/employee/forceUpdatePassword',
    method: 'post',
    data
  });
};
