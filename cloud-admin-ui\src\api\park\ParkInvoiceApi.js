/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找发票信息
export const pagingInvoice = (data) => {
  return $({
    url: '/console/park/invoice/pagingParkInvoice',
    method: 'post',
    data
  });
};

// 新建发票信息
export const createInvoice = (data) => {
  return $({
    url: '/console/park/invoice/createParkInvoice',
    method: 'post',
    data
  });
};

// 编辑发票信息
export const updateInvoice = (data) => {
  return $({
    url: '/console/park/invoice/updateParkInvoice',
    method: 'post',
    data
  });
};

// 发票信息删除
export const deleteInvoice = (id) => {
  return $({
    url: '/console/park/invoice/deleteInvoice/' + id,
    method: 'post'
  });
};

// 注册发票平台
export const registerInvoice = (id) => {
  return $({
    url: '/console/park/invoice/registerInvoice/' + id,
    method: 'post'
  });
};
