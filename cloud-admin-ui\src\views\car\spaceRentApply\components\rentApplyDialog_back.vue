<script setup>
import spaceRentApplyService from '@/service/car/SpaceRentApplyService';
import longRentRuleService from '@/service/park/LongRentRuleService';
import parkSpaceService from '@/service/park/ParkSpaceService';
import { useUser } from '@/stores/user';
import { getToken } from '@/utils/common';
import { ElMessage, dayjs } from 'element-plus';
import { cloneDeep } from 'lodash';
import { computed, reactive, ref, watch } from 'vue';

const emits = defineEmits(['closeForm', 'select', 'submit', 'cancel']);

// 弹窗显示控制
const dialogVisable = ref(false);

const loading = ref(false);
// 是否新建
const isAddRule = ref(true);
// 表单
const roleFormRef = ref();
// 表单数据
const formData = ref();
// 长租规则
const rentRules = ref([]);
// 产品列表
const products = ref([]);
// 车位列表
const parkSpaces = ref([]);
// 上传组件实体
const uploadRef1 = ref();
const uploadRef2 = ref();
const uploadRef3 = ref();
const uploadRef4 = ref();
// 默认上传列表
const defaultFileList1 = ref([]);
const defaultFileList2 = ref([]);
const defaultFileList3 = ref([]);
const defaultFileList4 = ref([]);
// 时间周期
const timeRange = ref([]);

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadAuditData');
const headers = reactive({
  Authorization: getToken()
});

// const checkPlateNos = (rule, val, callback) => {
//   console.log('returnreturn', val);
//   if (!val || val.length === 0) {
//     callback(new Error('请输入车牌号'));
//   } else {
//     const hasEmpty = val.find((item) => {
//       return !item.value;
//     });
//     if (hasEmpty) {
//       callback(new Error('请输入车牌号'));
//     } else {
//       callback();
//     }
//   }
// };

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[0123456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};

const validatePlateNo = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z][A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳]$/;
    const newReg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z](?:((\d{5}[A-HJK])|([A-HJK][A-HJ-NO-Z0-9][0-9]{4}))|[A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳])$/;
    if (!reg.test(value) && !newReg.test(value) && !value.includes('使') && !value.includes('领')) {
      console.log(reg.test(value), newReg.test(value), value);
      callback(new Error('请输入有效的车牌号'));
    }
  }
  callback();
};
const formateMaxLength = (value) => {
  let length = 8;
  // 省份简称正则
  const provinceRegex = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵青藏川宁琼]/;

  // 判断首位是否为省份简称
  if (!provinceRegex.test(value[0])) {
    // console.log('车牌号首位必须是省份简称（如京、沪等）');
    length = 8; // 默认最大长度
    return length;
  }

  // 判断新能源车或普通车
  if (value.length > 1) {
    const secondChar = value[1];
    if (/^[A-Z]$/.test(secondChar)) {
      // 普通车牌号：省份简称 + 字母 + 5位数字（如 京A12345）
      const normalPlateRegex = /^[A-Z]\d{5}$/;
      // 新能源车牌号：
      // 小型车：省简称 + 字母 + 6位数字（如 京AD12345）
      const smallNewEnergyRegex = /^[A-Z]{2}\d{5}$/;
      // 大型车：省简称 + 字母 + 5位数字 + 字母（如 京AD1234D）
      const largeNewEnergyRegex = /^[A-Z]{2}\d{4}[A-Z]$/;

      if (normalPlateRegex.test(value.slice(1))) {
        // console.log('普通车牌号，最大长度为7位');
        length = 7;
      } else if (smallNewEnergyRegex.test(value.slice(1))) {
        // console.log('小型新能源车牌，最大长度为8位');
        length = 8;
      } else if (largeNewEnergyRegex.test(value.slice(1))) {
        // console.log('大型新能源车牌，最大长度为8位');
        length = 8;
      } else {
        // console.log('请输入有效的车牌号');
        length = 8; // 默认最大长度
      }
    } else {
      // console.log('车牌号第二位必须是字母');
      length = 8;
    }
  } else {
    // console.log('请输入完整的车牌号');
    length = 8;
  }
  return length;
};

const rules = {
  park_id: [
    {
      required: true,
      message: '请选择车场',
      trigger: 'change'
    }
  ],
  prk_rent_rule_id: [
    {
      required: true,
      message: '请选择长租规则',
      trigger: 'change'
    }
  ],
  space_id: [
    {
      required: true,
      message: '请选择车位编号',
      trigger: 'change'
    }
  ],
  prk_rent_product_id: [
    {
      required: true,
      message: '请选择产品名称',
      trigger: 'change'
    }
  ],
  mbr_member_id: [
    {
      required: true,
      message: '请选择关联会员',
      trigger: 'change'
    }
  ],
  mbr_member_nickname: [
    {
      required: true,
      message: '请输入车主信息',
      trigger: 'blur'
    }
  ],
  valid_start_time: [
    {
      required: true,
      message: '请选择有效期',
      trigger: 'blur'
    }
  ],
  mbr_member_name: [
    {
      required: true,
      message: '请输入车主姓名',
      trigger: 'blur'
    }
  ],
  mbr_member_mobile: [
    {
      required: true,
      message: '请输入手机号',
      trigger: 'blur'
    },
    {
      trigger: 'blur',
      validator: validateMobilePhone
    }
  ],
  audit_url: [
    {
      required: false,
      message: '',
      trigger: ''
    }
  ],
  audit_url1: [
    {
      required: true,
      message: '请上传身份证正反面',
      trigger: 'blur'
    }
  ],
  audit_url2: [
    {
      required: true,
      message: '请上传驾驶证信息',
      trigger: 'blur'
    }
  ],
  audit_url3: [
    {
      required: true,
      message: '请上传行驶证信息',
      trigger: 'blur'
    }
  ],
  audit_url4: [
    {
      required: true,
      message: '请上传交强险或商业险',
      trigger: 'blur'
    }
  ],
  plate_nos: [
    {
      required: true,
      message: '请输入车牌号',
      trigger: 'blur'
    },
    {
      trigger: 'blur',
      validator: validatePlateNo
    }
  ]
};

// 表单字段是否展示
const showItem = computed(() => {
  const selectedProductType = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  })?.type;
  const commonSet = [5, 6, 7].includes(selectedProductType);
  return {
    product_range: commonSet,
    start_time: selectedProductType === 5,
    time_type: commonSet,
    week_day: selectedProductType === 7 && formData.value?.week_day?.length,
    days: selectedProductType === 7 && formData.value?.days?.length,
    month_range: selectedProductType === 7
  };
});

const showDialog = (data) => {
  initFormData(data);
  dialogVisable.value = true;
};

const closeDialog = () => {
  dialogVisable.value = false;
  emits('cancel');
};
/**
 * @description 初始化表单数据
 */
const initFormData = async (data) => {
  loading.value = false;
  isAddRule.value = !data;
  if (data) {
    fetchParkSpaceList(data.park_id, {
      code: data.space_code,
      id: data.space_id
    });
    fetchRentRuleList(data.park_id);
    fetchProductList(
      {
        rule_id: data.prk_rent_rule_id,
        park_id: data.park_id
      },
      true
    );
    const arr = data.plate_nos.split(',');
    arr.forEach((item, index) => {
      arr[index] = { value: item };
    });
    formData.value = {
      ...data,
      plate_nos: arr,
      prk_rent_product_money: data.order_money,
      valid_start_time: dayjs(data.valid_start_time).format('YYYY-MM-DD HH:mm:ss')
    };
    delete formData.value.order_money;
    defaultFileList1.value = [];
    defaultFileList2.value = [];
    defaultFileList3.value = [];
    defaultFileList4.value = [];
    formData.value.audit_urls.forEach((item) => {
      if (item.audit_data_name.indexOf('身份证') != -1) {
        defaultFileList1.value.push({ name: item.audit_data_name.split('身份证-')[1], url: item.audit_data_url });
      }
      if (item.audit_data_name.indexOf('驾驶证') != -1) {
        defaultFileList2.value.push({ name: item.audit_data_name.split('驾驶证-')[1], url: item.audit_data_url });
      }
      if (item.audit_data_name.indexOf('行驶证') != -1) {
        defaultFileList3.value.push({ name: item.audit_data_name.split('行驶证-')[1], url: item.audit_data_url });
      }
      if (item.audit_data_name.indexOf('保险单') != -1) {
        defaultFileList4.value.push({ name: item.audit_data_name.split('保险单-')[1], url: item.audit_data_url });
      }
    });
  } else {
    formData.value = {
      park_id: '',
      park_name: '',
      prk_rent_rule_id: '',
      prk_rent_product_id: '',
      prk_rent_product_money: '',
      space_id: '',
      valid_start_time: '',
      valid_end_time: '',
      mbr_member_id: '',
      mbr_member_name: '',
      mbr_member_mobile: '',
      mbr_member_nickname: '',
      audit_url: '',
      audit_url1: '',
      audit_url2: '',
      audit_url3: '',
      audit_url4: '',
      plate_nos: [{ value: '' }],
      channel: 1
    };
    defaultFileList1.value = [];
    defaultFileList2.value = [];
    defaultFileList3.value = [];
    defaultFileList4.value = [];
    const user = useUser();
    //判断user权限是否有授权车场，添加到筛选条件中直接进行查询
    if (user.park_ids !== undefined && user.park_ids.length > 0) {
      formData.value.park_id = user.park_ids[0];
      formData.value.park_name = user.park_names[0];
      fetchParkSpaceList(user.park_ids[0]);
      fetchRentRuleList(user.park_ids[0]);
      formData.value.valid_start_time = dayjs().format('YYYY-MM-DD HH:mm:ss');
      changeEndData(formData.value.valid_start_time);
    }
  }
};
watch(products, () => {
  if (products.value && products.value.length > 0) {
    formData.value.prk_rent_product_id = products.value[0].id;
    handleProductChange(formData.value.prk_rent_product_id);
  }
});
/**
 * @description 暴露给父组件 处理选中事件回调
 * @param {*} val 选中值
 * @param {*} type parking | user
 */
const dealSelect = (val, type) => {
  if (type === 'parking') {
    formData.value.park_id = val[0].park_id;
    formData.value.park_name = val[0].park_name;
    formData.value.prk_rent_rule_id = '';
    formData.value.prk_rent_product_id = '';
    formData.value.prk_rent_product_money = '';
    formData.value.space_id = '';
    formData.value.valid_start_time = '';
    formData.value.valid_end_time = '';
    fetchParkSpaceList(val[0].park_id);
    fetchRentRuleList(val[0].park_id);
  } else {
    formData.value.mbr_member_id = val[0].member_id;
    formData.value.mbr_member_nickname = val[0].member_name;
    formData.value.mbr_member_mobile = val[0].member_mobile;
  }
};
/**
 * @description 点击选择框的选择事件 触发父组件中的弹窗
 * @param {*} type 弹窗类型 parking | user
 */
const handleSelect = (type) => {
  emits('select', type, formData.value);
};
/**
 * @description 获取车位列表
 * @param {*} park_id 停车场id
 */
const fetchParkSpaceList = (park_id, defaultData) => {
  parkSpaces.value = [];
  parkSpaceService.listAvailableLongRentSpace(park_id).then((response) => {
    if (response.success === true) {
      parkSpaces.value = response.data;
      if (defaultData) {
        parkSpaces.value.push(defaultData);
      }
      if (!defaultData && parkSpaces.value && parkSpaces.value.length > 0) {
        formData.value.space_id = parkSpaces.value[0].id;
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 获取长租规则列表
 * @param {*} park_id 停车场id
 */
const fetchRentRuleList = (park_id) => {
  rentRules.value = [];
  const params = {
    parkId: park_id,
    userIdentity: formData.value.user_identity
  };
  longRentRuleService.listRentRule(params).then((response) => {
    if (response.success === true) {
      rentRules.value = response.data;
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 获取产品列表
 * @param {*} productparams
 */
const fetchProductList = (productparams, init = false) => {
  products.value = [];
  longRentRuleService.listProduct(productparams).then((response) => {
    if (response.success === true) {
      products.value = response.data;
      if (init) {
        handleProductChange(formData.value.prk_rent_product_id);
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 选择长租规则事件
 */
const handleRentRuleChange = (val) => {
  const productparams = {
    rule_id: val,
    park_id: formData.value.park_id
  };
  formData.value.prk_rent_product_id = '';
  fetchProductList(productparams);
};

/**
 * @description 选择产品事件
 * @param {*} val 选择产品id
 */
const handleProductChange = (val) => {
  const selectedProduct = products.value.find((item) => {
    return item.id == val;
  });
  if (selectedProduct) {
    formData.value.prk_rent_product_money = selectedProduct.money;
    formData.value.product_range = selectedProduct.product_range;
    formData.value.start_time = selectedProduct.start_time;
    formData.value.end_time = selectedProduct.end_time;
    formData.value.time_type = selectedProduct.time_type;
    formData.value.week_day = selectedProduct.week_day;
    formData.value.days = selectedProduct.days;
    formData.value.month_range = selectedProduct.month_range;
    formData.value.prk_rent_product_detail_id = formData.value.prk_rent_product_id;
    changeEndData(formData.value.valid_start_time);
  }
};
/**
 * @description 改变结束时间
 * @param {*} val 起始时间
 */
const changeEndData = (val) => {
  if (!val) return;
  const selectedProduct = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  });
  if (!selectedProduct) {
    formData.value.valid_end_time = formData.value.valid_start_time;
    return;
  }
  const timeRelaseBase = {
    1: 1,
    2: 3,
    3: 6,
    4: 12,
    0: 0
  };
  const flag = selectedProduct.type <= 4 ? selectedProduct.type : selectedProduct?.product_range || 0;
  formData.value.valid_end_time = dayjs(formData.value.valid_start_time)
    .add(timeRelaseBase[flag], 'month')
    .subtract(1, 'day')
    .format('YYYY-MM-DD HH:mm:ss');
};
/**
 * @description: 文件列表移除文件时的钩子
 */
const onRemove = (file, fileList) => {
  console.log(file, fileList);
};
/**
 * @description: 点击文件列表中已上传的文件时的钩子
 */
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const onPreview = (file) => {
  dialogVisible.value = true;
  title.value = '图片预览';
  dialogImageUrl.value = file.url || file.response.data.audit_data_url;
};
/**
 * @description: 上传文件前回调
 * @param {*} file
 * @return {*}
 */
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage.warning('上传文件大小不能超过 25MB!');
    return false;
  }
  if (file.type !== 'image/jpeg' && file.type !== 'image/png') {
    ElMessage.warning('上传文件格式错误!');
    return false;
  }
};

const handleExceed1 = (files) => {
  uploadRef1.value.clearFiles();
  uploadRef1.value.handleStart(files[0]);
  uploadRef1.value.submit();
};
const handleExceed2 = (files) => {
  uploadRef2.value.clearFiles();
  uploadRef2.value.handleStart(files[0]);
  uploadRef2.value.submit();
};
const handleExceed3 = (files) => {
  uploadRef3.value.clearFiles();
  uploadRef3.value.handleStart(files[0]);
  uploadRef3.value.submit();
};
const handleExceed4 = (files) => {
  uploadRef4.value.clearFiles();
  uploadRef4.value.handleStart(files[0]);
  uploadRef4.value.submit();
};

const onSuccessUpload = (response) => {
  if (response.success == true) {
    ElMessage.success(response.message);
  } else {
    ElMessage.error(response.message);
  }
};

// 动态增减表单项
const removePlateNo = (item) => {
  var index = formData.value.plate_nos.indexOf(item);
  if (index !== -1) {
    formData.value.plate_nos.splice(index, 1);
  }
};
const addPlateNo = () => {
  formData.value.plate_nos.push({
    value: ''
  });
};
/**
 * @description: 提交表单数据
 * @param {*}
 * @return {*}
 */
const handleSubmit = () => {
  // if (defaultFileList1.value.length < 3) {
  //   ElMessage.warning('请上传驾驶证信息、行驶证信息、车辆交强险记录等!');
  //   // return
  // }
  let defaultFileList1copy = JSON.parse(JSON.stringify(defaultFileList1.value));
  let defaultFileList2copy = JSON.parse(JSON.stringify(defaultFileList2.value));
  let defaultFileList3copy = JSON.parse(JSON.stringify(defaultFileList3.value));
  let defaultFileList4copy = JSON.parse(JSON.stringify(defaultFileList4.value));
  formData.value.audit_url1 = '';
  formData.value.audit_url2 = '';
  formData.value.audit_url3 = '';
  formData.value.audit_url4 = '';
  if (defaultFileList1copy.length > 0) {
    formData.value.audit_url1 = 'true';
    defaultFileList1copy.forEach((item, i) => {
      if (item.name.indexOf('身份证') != -1) {
        return;
      }
      item.name = '身份证-' + item.name;
    });
  }
  if (defaultFileList2copy.length > 0) {
    formData.value.audit_url2 = 'true';
    defaultFileList2copy.forEach((item, i) => {
      if (item.name.indexOf('驾驶证') != -1) {
        return;
      }
      item.name = '驾驶证-' + item.name;
    });
  }
  if (defaultFileList3copy.length > 0) {
    formData.value.audit_url3 = 'true';
    defaultFileList3copy.forEach((item, i) => {
      if (item.name.indexOf('行驶证') != -1) {
        return;
      }
      item.name = '行驶证-' + item.name;
    });
  }
  if (defaultFileList4copy.length > 0) {
    formData.value.audit_url4 = 'true';
    defaultFileList4copy.forEach((item, i) => {
      if (item.name.indexOf('保险单') != -1) {
        return;
      }
      item.name = '保险单-' + item.name;
    });
  }
  let filesList = defaultFileList1copy.concat(defaultFileList2copy).concat(defaultFileList3copy).concat(defaultFileList4copy);
  console.log(filesList, formData.value.audit_url1, 'filesList.value');
  const auditUrl = filesList.map((item) => {
    return {
      audit_data_name: item.name,
      audit_data_url: item.url || item.response.data.audit_data_url
    };
  });
  if (auditUrl.length > 0) {
    formData.value.audit_url = JSON.stringify(auditUrl);
  } else {
    formData.value.audit_url = undefined;
  }
  delete formData.value.audit_urls;

  console.log(formData.value, 'formData.value');
  // return
  roleFormRef.value.validate().then(async () => {
    loading.value = true;
    // 防止http请求错误时  数据回滚导致出错
    const initData = cloneDeep(formData.value.plate_nos);
    formData.value.plate_nos = formData.value.plate_nos.map((item) => {
      return item.value;
    });
    try {
      // console.log(formData.value, 'formData.value===');
      const submitFunc = isAddRule.value ? spaceRentApplyService.createRentSpaceApply : spaceRentApplyService.updateRentSpaceApply;
      const { success, message, detail_message } = await submitFunc(formData.value);
      if (success) {
        ElMessage.success(message);
        emits('submit');
        dialogVisable.value = false;
        uploadRef1.value.clearFiles();
        uploadRef2.value.clearFiles();
        uploadRef3.value.clearFiles();
        uploadRef4.value.clearFiles();
      } else {
        ElMessage.error(detail_message || message);
        formData.value.plate_nos = initData;
      }
    } catch (error) {
      formData.value.plate_nos = initData;
      ElMessage.error('保存失败');
    } finally {
      loading.value = false;
    }
  });
};

defineExpose({
  showDialog,
  dealSelect
});
</script>

<template>
  <div>
    <!-- 长租申请 -->
    <el-dialog
      :title="`${isAddRule ? '新建' : '修改'}长租申请`"
      v-model="dialogVisable"
      destroy-on-close
      :close-on-click-modal="false"
      width="1200px"
    >
      <el-form ref="roleFormRef" label-width="100px" :rules="rules" :model="formData" class="grid-form">
        <el-form-item label="选择车场" prop="park_id">
          <el-input v-model="formData.park_name" placeholder="请选择车场" readonly @click="handleSelect('parking')" />
        </el-form-item>
        <el-form-item label="长租规则" prop="prk_rent_rule_id">
          <el-select
            v-model="formData.prk_rent_rule_id"
            placeholder="长租规则"
            style="width: 100%"
            :disabled="!formData.park_id"
            @change="handleRentRuleChange"
          >
            <el-option v-for="item in rentRules" :key="item.id + 'prk_rent_rule_id'" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="车位编号" prop="space_id">
          <el-select v-model="formData.space_id" :disabled="!formData.park_id" placeholder="车位编号" style="width: 100%" clearable>
            <el-option v-for="item in parkSpaces" :key="item.id + 'space_id'" :label="item.code" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称" prop="prk_rent_product_id">
          <el-select
            v-model="formData.prk_rent_product_id"
            placeholder="产品名称"
            style="width: 100%"
            :disabled="!formData.prk_rent_rule_id"
            @change="handleProductChange"
          >
            <el-option v-for="item in products" :key="item.id + 'prk_rent_product_id'" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品金额" required>
          <el-input v-model="formData.prk_rent_product_money" :disabled="true" />
        </el-form-item>
        <el-form-item label="产品周期" v-if="showItem.product_range">
          <el-radio-group v-model="formData.product_range" disabled>
            <el-radio :label="1">1个月</el-radio>
            <el-radio :label="2">3个月</el-radio>
            <el-radio :label="3">6个月</el-radio>
            <el-radio :label="4">12个月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时段设定" v-if="showItem.start_time">
          <el-time-picker v-model="timeRange" is-range range-separator="至" value-format="HH:mm:ss" disabled />
        </el-form-item>
        <el-form-item label="时段类型" v-if="showItem.time_type">
          <el-radio-group v-model="formData.time_type" disabled>
            <el-radio :label="1">收费</el-radio>
            <el-radio :label="0">免费</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="星期设定" v-if="showItem.week_day">
          <el-checkbox-group v-model="formData.week_day" disabled>
            <el-checkbox v-for="(item, index) in ['周日', '周一', '周二', '周三', '周四', '周五', '周六']" :label="index + 1" :key="index + ''">
              {{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="日期设定" v-if="showItem.days">
          <el-checkbox-group v-model="formData.days" disabled>
            <el-checkbox v-for="item in 31" :label="item" :key="item + 'days'">
              {{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="月周期设定" v-if="showItem.month_range">
          <el-radio-group v-model="formData.month_range" disabled>
            <el-radio :label="2">30天</el-radio>
            <el-radio :label="1">31天</el-radio>
            <el-radio :label="0">自然月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="有效期" prop="valid_start_time">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-date-picker
                v-model="formData.valid_start_time"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择开始时间"
                @change="changeEndData"
              />
            </el-col>
            <el-col :span="12">
              <el-input v-model="formData.valid_end_time.split(' ')[0]" style="width: 100%" readonly placeholder="结束时间" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="车主姓名" prop="mbr_member_name">
          <el-input v-model="formData.mbr_member_name" maxlength="30" show-word-limit />
        </el-form-item>
        <el-form-item label="关联会员" prop="mbr_member_id">
          <el-input v-model="formData.mbr_member_nickname" placeholder="请选择关联会员" readonly @click="handleSelect('user')" />
        </el-form-item>
        <el-row class="row">
          <el-col :span="12">
            <div style="display: flex; flex-direction: column">
              <el-form-item label="手机号" prop="mbr_member_mobile">
                <el-input v-model="formData.mbr_member_mobile" />
              </el-form-item>
              <el-form-item
                v-for="(nos, index) in formData.plate_nos"
                :label="index == 0 ? '车牌号' : ''"
                :key="index"
                :prop="'plate_nos.' + index + '.value'"
                :rules="[
                  {
                    required: true,
                    message: '请输入车牌号',
                    trigger: 'blur'
                  },
                  {
                    trigger: 'blur',
                    validator: validatePlateNo
                  }
                ]"
              >
                <el-input v-model="nos.value" :maxlength="formateMaxLength(nos.value)" show-word-limit style="width: 75%" />&ensp;
                <div style="display: inline-block; height: 30px; margin-left: 5px; vertical-align: middle">
                  <el-icon :size="30" v-if="formData.plate_nos.length < 2" color="#409eff" @click="addPlateNo"> <CirclePlus /> </el-icon>&ensp;
                  <el-icon
                    :size="30"
                    v-if="formData.plate_nos.length > 1 || formData.plate_nos.length == 2"
                    color="#f56c6c"
                    @click="removePlateNo(nos)"
                  >
                    <CircleClose />
                  </el-icon>
                </div>
              </el-form-item>
            </div>
          </el-col>
          <el-col :span="12">
            <div style="display: flex; flex-direction: column">
              <el-form-item label="身份证" prop="audit_url1">
                <el-upload
                  :limit="15"
                  :action="uploadUrl"
                  :headers="headers"
                  ref="uploadRef1"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList1"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload"
                  :on-exceed="handleExceed1"
                  :multiple="true"
                >
                  <el-button type="primary">点击上传文件</el-button>
                  <span style="color: #f56c6c; font-size: 14px; margin-left: 10px">上传身份证正反面</span>
                </el-upload>
              </el-form-item>
              <el-form-item label="驾驶证" prop="audit_url2">
                <el-upload
                  :limit="15"
                  :action="uploadUrl"
                  :headers="headers"
                  ref="uploadRef2"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList2"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload"
                  :on-exceed="handleExceed2"
                  :multiple="true"
                >
                  <el-button type="primary">点击上传文件</el-button>
                  <span style="color: #f56c6c; font-size: 14px; margin-left: 10px">上传驾驶证信息</span>
                </el-upload>
              </el-form-item>
              <el-form-item label="行驶证" prop="audit_url3">
                <el-upload
                  :limit="15"
                  :action="uploadUrl"
                  :headers="headers"
                  ref="uploadRef3"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList3"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload"
                  :on-exceed="handleExceed3"
                  :multiple="true"
                >
                  <el-button type="primary">点击上传文件</el-button>
                  <span style="color: #f56c6c; font-size: 14px; margin-left: 10px">上传行驶证信息</span>
                </el-upload>
              </el-form-item>
              <el-form-item label="保险单" prop="audit_url4">
                <el-upload
                  :limit="15"
                  :action="uploadUrl"
                  :headers="headers"
                  ref="uploadRef4"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList4"
                  :on-preview="onPreview"
                  :on-remove="onRemove"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload"
                  :on-exceed="handleExceed4"
                  :multiple="true"
                >
                  <el-button type="primary">点击上传文件</el-button>
                  <span style="color: #f56c6c; font-size: 14px; margin-left: 10px">上传交强险或商业险</span>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row class="row"> </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible" :title="title" width="40%">
      <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.grid-form {
  display: grid;
  grid-template-columns: 1fr 1fr;

  .row {
    grid-column-start: 1;
    grid-column-end: 3;
  }
}

:deep(.el-form-item__content) {
  align-items: flex-start;
}

::v-deep .el-form-item__label {
  justify-content: unset !important;
  padding-left: 18px;
}
</style>
