import * as sentryBoxRecordApi from '@/api/charge/SentryBoxRecordApi';
import { ElMessage } from 'element-plus';

/**
 * 岗亭操作记录
 */
export default {
  /**
   * 获取入场图片
   */
  getByCarInRecordId(data) {
    if (!data) {
      ElMessage({
        message: '暂无图片可以查看',
        type: 'error'
      });
      return Promise.reject('暂无图片可以查看');
    }
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.getByCarInRecordId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取出场图片
   */
  getByCarOutRecordId(data) {
    if (!data) {
      ElMessage({
        message: '暂无图片可以查看',
        type: 'error'
      });
      return Promise.reject('暂无图片可以查看');
    }
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.getByCarOutRecordId(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 特殊放行 ----------------------------------------//
  /**
   * 分页查询特殊放行
   */
  pagingSpecialRelease(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingSpecialRelease(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出特殊放行
   */
  exportSpecitalRelease(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportSpecitalRelease(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 取消放行 ----------------------------------------//
  /**
   * 分页查询取消放行
   */
  pagingCancelRelease(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingCancelRelease(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出取消放行
   */
  exportCancelRelease(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportCancelRelease(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 车牌号矫正 ----------------------------------------//
  /**
   * 分页查询车牌号矫正
   */
  pagingPlateNoCorrect(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingPlateNoCorrect(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出车牌号矫正
   */
  exportPlateNoCorrect(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportPlateNoCorrect(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 入口抬竿 ----------------------------------------//
  /**
   * 分页查询入口抬竿
   */
  pagingEntranceRangePole(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingEntranceRangePole(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出入口抬竿
   */
  exportEntranceRangePole(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportEntranceRangePole(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 出口抬竿 ----------------------------------------//
  /**
   * 分页查询出口抬竿
   */
  pagingExitRangePole(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingExitRangePole(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出出口抬竿
   */
  exportExitRangePole(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportExitRangePole(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 手动匹配出场 ----------------------------------------//
  /**
   * 分页查询手动匹配出场
   */
  pagingManualMatchingExit(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingManualMatchingExit(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出手动匹配出场
   */
  exportManualMatchingExit(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportManualMatchingExit(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 重复入场 ----------------------------------------//
  /**
   * 分页查询重复入场
   */
  pagingRepeatEntrance(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingRepeatEntrance(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出重复入场
   */
  exportRepeatEntrance(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportRepeatEntrance(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 被冲车辆 ----------------------------------------//
  /**
   * 分页查询被冲车辆
   */
  pagingRushedCar(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingRushedCar(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出被冲车辆
   */
  exportRushedCar(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportRushedCar(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 切换费率 ----------------------------------------//
  /**
   * 分页查询被冲车辆
   */
  pagingChangeRate(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingChangeRate(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出被冲车辆
   */
  exportChangeRate(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportChangeRate(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 手动补录 ----------------------------------------//
  /**
   * 分页查询
   */
  pagingRepairInRecordRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingRepairInRecordRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportRepairInRecordRecords(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportRepairInRecordRecords(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  //---------------------------------------- 强制出场 ----------------------------------------//
  /**
   * 分页查询强制出场
   */
  pagingForceExit(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.pagingForceExit(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出强制出场
   */
  exportForceExit(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.exportForceExit(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出强制出场
   */
  countAgentEventLogByType(data) {
    return new Promise((resolve, reject) => {
      try {
        sentryBoxRecordApi.countAgentEventLogByType(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
