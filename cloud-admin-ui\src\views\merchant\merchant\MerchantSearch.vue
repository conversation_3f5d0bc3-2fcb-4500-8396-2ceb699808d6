<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly @click="authCharge(true, '')" placeholder="请选择停车场名称"
    /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.name" placeholder="商户名称" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.contact_mobile" placeholder="手机号" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.login_name" placeholder="商户账号" /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.states" placeholder="状态" multiple clearable>
        <el-option v-for="item in states" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
  </FormSearch>

  <!-- 车场查找带回 -->
  <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
    <park-find-back @authCharge="authCharge(false, '')" mode="search" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="MerchantSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '../ParkFindBack.vue';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search']);
const parkInfoDialogVisible = ref(false);
const form = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    contact_mobile: '',
    name: '',
    login_name: '',
    states: [],
    page: 1,
    limit: 30
  }
});
const states = ref([]);

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param = [{ enum_key: 'states', enum_value: 'EnumMerchantState' }];
  commonService.findEnums('merchant', param).then((response) => {
    states.value = response.data.states;
  });
};

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_id: '',
    contact_mobile: '',
    name: '',
    login_name: '',
    states: [],
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};

//车场查找带回;
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
