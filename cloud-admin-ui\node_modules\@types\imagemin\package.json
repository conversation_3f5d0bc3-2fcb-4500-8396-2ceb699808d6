{"name": "@types/imagemin", "version": "7.0.1", "description": "TypeScript definitions for imagemin", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "url": "https://github.com/romain-faust", "githubUsername": "romain-faust"}, {"name": "<PERSON>", "url": "https://github.com/hkjeffchan", "githubUsername": "hkjeffchan"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "typesPublisherContentHash": "65bb4a90b894c5431ff6926df9b8542f9de4abfaa9197c8fd44b827ff70fa192", "typeScriptVersion": "3.6"}