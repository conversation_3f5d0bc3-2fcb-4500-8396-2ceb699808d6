/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询临停应收未付记录
export const pagingParkCarNotPayRecord = (data) => {
  return $({
    url: '/console/statistics/receivable/unpaid/record/listReceivableUnpaidRecords',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/receivable/unpaid/record/exportReceivableUnpaidRecords',
    method: 'post',
    data
  });
};
