/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询临停应收未付记录
export const pagingParkCarNotPayRecord = (data) => {
  return $({
    url: '/console/statistics/receivable/unpaid/record/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/receivable/unpaid/record/exportByPeriod',
    method: 'post',
    data
  });
};
