<template>
  <div>
    <abnormal-record-search @form-search="searchAbnormalRecordList" @reset="resetParamsAndData" />
    <abnormal-record-table ref="table" />
  </div>
</template>

<script name="AbnormalRecord" setup>
import AbnormalRecordSearch from './abnormalRecord/AbnormalRecordSearch.vue';
import AbnormalRecordTable from './abnormalRecord/AbnormalRecordTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchAbnormalRecordList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
