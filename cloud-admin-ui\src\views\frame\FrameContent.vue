<!--
 * @Description: 
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-20 16:34:03
-->
<template>
  <div class="frame-content">
    <router-view v-slot="{ Component }">
      <keep-alive :include="includeKeepAlives" :exclude="excludeKeepAlives">
        <component :is="Component" />
      </keep-alive>
    </router-view>
  </div>
</template>

<script setup>
import { RouterView } from 'vue-router';
import { useMenu } from '@/stores/menu';
import { computed } from 'vue';

const menu = useMenu();

const includeKeepAlives = computed(() => menu.state.includeKeepAlives.join(','));
const excludeKeepAlives = computed(() => menu.state.excludeKeepAlives.join(','));
</script>

<style lang="scss" scoped>
.frame-content {
  padding: 10px;
  height: calc(100vh - 51px);
}
</style>
