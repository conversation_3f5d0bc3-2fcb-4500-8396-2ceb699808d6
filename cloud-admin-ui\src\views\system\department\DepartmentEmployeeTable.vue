<template>
  <div class="opers">
    <div></div>
    <div>
      <el-input v-model="data.queryParams.name_or_mobile" class="w-50 m-2" placeholder="姓名/手机号" @keyup.enter="handleDataSearch" clearable>
        <template #suffix
          ><el-icon @click="handleDataSearch"><Search /></el-icon>
        </template>
      </el-input>
    </div>
  </div>
  <el-table ref="multipleTable" :data="tableData" v-loading="loading" border tooltip-effect="dark" style="width: 100%; margin-top: 10px">
    <el-table-column type="selection" width="40" style="text-align: center" />
    <el-table-column prop="name" label="姓名" align="center" />
    <el-table-column prop="mobile" label="手机号" align="center" />
  </el-table>
  <el-pagination
    background
    :page-sizes="[10, 30, 50, 100]"
    :current-page="data.queryParams.page"
    :page-size="data.queryParams.limit"
    layout="total, sizes, prev, pager, next, jumper"
    :total="total"
    class="table-pagination"
    @size-change="handleSizeChange"
    @current-change="handleCurrentChange"
  />
</template>

<script name="DepartmentEmployeeTable" setup>
import departmentService from '@/service/system/DepartmentService';
import { reactive, onMounted, ref } from 'vue';
import { Search } from '@element-plus/icons-vue';

const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    name_or_mobile: '',
    mobile: '',
    department_id: '',
    page: 1,
    limit: 30
  }
});

onMounted(() => {
  getDataList(data.queryParams);
});

const getDataList = (params) => {
  loading.value = true;
  data.queryParams = params;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  departmentService.pagingEmployeesByDepartmentId(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getDataList(data.queryParams);
};

const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getDataList(data.queryParams);
};

const handleDataSearch = () => {
  getDataList(data.queryParams);
};
defineExpose({ getDataList });
</script>

<style lang="scss" scoped>
.page {
  background: #ebebeb;
  text-align: right;
  padding: 6px;
}
.opers {
  display: flex;
  justify-content: space-between;
  padding-bottom: 0px;
}
</style>
