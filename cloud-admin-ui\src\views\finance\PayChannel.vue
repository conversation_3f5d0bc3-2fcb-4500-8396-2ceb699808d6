<template>
  <el-tabs v-model="activeName" @tab-click="handleClick">
    <el-tab-pane label="微信支付" name="Wechat">
      <we-chat-pay-channel ref="wechatPay" />
    </el-tab-pane>
    <el-tab-pane label="支付宝支付" name="Alipay">
      <ali-pay-channel ref="alipay" />
    </el-tab-pane>
  </el-tabs>
</template>

<script name="PayChannel" setup>
import WeChatPayChannel from './WeChatPayChannel.vue';
import AliPayChannel from './AliPayChannel.vue';
import { ref, reactive, onActivated } from 'vue';

const activeName = ref('childPark');
const wechatPay = ref(null);
const alipay = ref(null);
const params = reactive({
  page: 1,
  limit: 30
});

onActivated(() => {
  wechatPay.value.searchWeChatPayChannelList(params);
});

const handleClick = (tab) => {
  if (tab.props.name === 'Wechat') {
    wechatPay.value.searchWeChatPayChannelList(params);
  }
  if (tab.props.name === 'Alipay') {
    alipay.value.searchAilPayChannelList(params);
  }
};
</script>
