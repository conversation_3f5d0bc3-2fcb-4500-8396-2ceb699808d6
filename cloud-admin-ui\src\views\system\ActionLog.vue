<template>
  <div class="container">
    <el-tabs v-model="activeName" type="border-card" @tab-click="handleClick">
      <el-tab-pane label="车场管理" name="parkAdmin">
        <park-admin ref="parkAdminRef" />
      </el-tab-pane>
      <el-tab-pane label="费率管理" name="parkFee">
        <park-fee ref="parkFeeRef" />
      </el-tab-pane>
      <el-tab-pane label="车辆管理" name="carAdmin">
        <car-admin ref="carAdminRef" />
      </el-tab-pane>
      <el-tab-pane label="缴费订单" name="payList">
        <pay-list ref="payListRef" />
      </el-tab-pane>
      <el-tab-pane label="入场记录" name="carInRecord">
        <car-in-record ref="carInRecordRef" />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script name="ActionLog" setup>
import parkAdmin from './actionLog/parkAdmin.vue';
import parkFee from './actionLog/parkFee.vue';
import carAdmin from './actionLog/carAdmin.vue';
import payList from './actionLog/payList.vue';
import carInRecord from './actionLog/carInRecord.vue';

import { ref, reactive, onActivated } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const activeName = ref('parkAdmin');
const parkAdminRef = ref(null);
const parkFeeRef = ref(null);
const carAdminRef = ref(null);
const payListRef = ref(null);
const carInRecordRef = ref(null);
const params = reactive({
  park_id: undefined,
  page: 1,
  limit: 30
});

onActivated(() => {
  // if (Object.keys(route.query).length !== 0 && undefined !== route.query.parkId) {
  //   params.park_id = route.query.parkId;
  //   if (undefined !== route.query.redirect_tab) {
  //     activeName.value = route.query.redirect_tab;
  //     parkFeeRef.value.searchParkFeeList(params);
  //   } else {
  //     parkRegionRef.value.getList(params);
  //   }
  // }

  // 重新加载打开的tab
  activeTab(activeName.value);
});

const handleClick = (tab) => {
  activeTab(tab.props.name);
};

const activeTab = (name) => {
  if (name === 'parkAdmin') {
    parkAdminRef.value.handleDataSearch(params);
  }
  if (name === 'parkFee') {
    parkFeeRef.value.handleDataSearch(params);
  }
  if (name === 'carAdmin') {
    carAdminRef.value.handleDataSearch(params);
  }
  if (name === 'payList') {
    payListRef.value.handleDataSearch(params);
  }
  if (name === 'carInRecord') {
    carInRecordRef.value.handleDataSearch(params);
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-tabs__content) {
  padding: 0px 10px;
  background-color: #f6f6f6;
}
</style>
