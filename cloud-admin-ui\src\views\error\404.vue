<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-01-05 11:40:41
 * @LastEditTime: 2024-03-21 11:04:55
 * @LastEditors: 达万安 段世煜
 * @Description: 
 * @FilePath: \cloud-admin-ui\src\views\error\404.vue
-->
<template>
  <el-result>
    <template #title>
      <span class="title">404</span>
    </template>
    <template #sub-title>
      <span class="sub-title">未找到对应页面或暂无访问权限</span>
    </template>
    <template #icon>
      <img src="../../assets/404.png" style="height: 50vh" />
    </template>
    <template #extra>
      <el-button type="primary" @click="backHome">返回首页</el-button>
    </template>
  </el-result>
</template>

<script setup>
import { activeRouteTab } from '@/utils/tabKit';

const backHome = () => {
  activeRouteTab({ path: '/' });
};
</script>

<style scoped>
.title {
  font-size: 48px;
}

.sub-title {
  font-size: 28px;
  color: rgba(86, 86, 86, 0.5);
}
</style>
