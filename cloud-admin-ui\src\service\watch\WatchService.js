import * as watch from '@/api/watch/WatchApi';

/**
 * 云值守
 */
export default {
  /**
   * 开始云端值守
   */
  beginWatch(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.beginWatch(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 停止云端值守
   */
  endWatch(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.endWatch(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改车牌号
   */
  updateCarInPlateNo(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.updateCarInPlateNo(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询通道中的车辆
   */
  queryCarInGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.queryCarInGateway(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 获取抓拍的快照
   */
  getCarSnapshot(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.getCarSnapshot(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 触发抓拍事件
   */
  triggerCamera(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.triggerCamera(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 分页查询停车缴费
   */
  pagingParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.pagingParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 抬杆请求
   */
  pushOpenStrobe(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.pushOpenStrobe(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 远程放闸
   */
  pushCloseStrobe(data) {
    return new Promise((resolve, reject) => {
      try {
        watch.pushCloseStrobe(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
