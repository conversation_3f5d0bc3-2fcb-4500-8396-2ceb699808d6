/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询分时段进出
export const pagingTimedAccess = (data) => {
  return $({
    url: '/console/statistics/time/period/in/out/pagingByPeriod',
    method: 'post',
    data
  });
};

// 导出
export const exportData = (data) => {
  return $({
    url: '/console/statistics/time/period/in/out/exportByPeriod',
    method: 'post',
    data
  });
};
//汇总导出
export const exportDataSum = (data) => {
  return $({
    url: '/console/statistics/time/period/in/out/exportSummaryByPeriod',
    method: 'post',
    data
  });
}
