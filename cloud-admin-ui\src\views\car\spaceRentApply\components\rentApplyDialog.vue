<script setup>
import spaceRentApplyService from '@/service/car/SpaceRentApplyService';
import longRentRuleService from '@/service/park/LongRentRuleService';
import parkSpaceService from '@/service/park/ParkSpaceService';
import commonService from '@/service/common/CommonService';
import { useUser } from '@/stores/user';
import { getToken } from '@/utils/common';
import { ElMessage, dayjs } from 'element-plus';
import { computed, reactive, ref, watch } from 'vue';
import { Delete, Plus, ZoomIn } from '@element-plus/icons-vue';
import { rentProductRanges } from '@/views/park/longRentRule/enums';
import { debounce } from 'lodash';
const PDFLogo = new URL('../../carVisitor/components/PDF.svg', import.meta.url).href;

const emits = defineEmits(['closeForm', 'select', 'submit', 'cancel', 'audit']);

const rejectForm = ref({
  id: '',
  reject_reason: ''
});

const rejectRules = reactive({
  reject_reason: [
    {
      required: true,
      message: '请输入驳回原因',
      trigger: 'blur'
    }
  ]
});

const rejectDialogVisible = ref(false);

const rejectLoading = ref(false);

const rejectFormRef = ref();

// 弹窗显示控制
const mainDialogVisible = ref(false);

const loading = ref(false);
// 是否新建
const isAddRule = ref(true);
// 表单
const roleFormRef = ref();
// 表单数据
const formData = ref({
  park_id: '',
  park_name: '',
  user_identity: '',
  prk_rent_rule_id: '',
  prk_rent_product_id: '',
  prk_rent_product_money: '',
  space_id: '',
  valid_start_time: '',
  valid_end_time: '',
  mbr_member_id: '',
  mbr_member_name: '',
  mbr_member_mobile: '',
  mbr_member_nickname: '',
  audit_url: '',
  audit_url1: '',
  audit_url2: '',
  audit_url3: '',
  audit_url4: '',
  audit_url5: '',
  audit_url6: '',
  audit_url7: '',
  payed_voucher_url: '',
  payed_voucher_url1: '',
  plate_nos: '',
  channel: 1,
  version: '1'
});
// 当前车场是否支持一车多位
const moreRentSwitch = ref(1);
// 监听付款方式变化，清空缴费凭证
watch(
  () => formData.value.pay_method,
  () => {
    if (typeof formData.value.pay_method === 'number' && formData.value.pay_method !== 4) {
      return;
    } else {
      defaultPayedVoucherFileList1.value = [];
    }
  }
);
// 用户身份
const userIdentities = ref();
// 规则名称
const rentRules = ref([]);
// 长租类型
const rentTypes = ref([]);
// 产品列表
const products = ref([]);
// 车位列表
const parkSpaces = ref([]);
// 付款方式
const paymentMethods = ref([]);
// 上传组件实体
const uploadRefNames = Array.from({ length: 5 }, (_, i) => `uploadRef${i + 1}`);
const uploadRefs = uploadRefNames.reduce((acc, refName) => {
  acc[refName] = ref(null);
  return acc;
}, {});
// 默认上传列表
const defaultFileList = ref([[], [], [], [], [], [], []]);
// 缴费凭证组件实体
const payedVoucherUploadRef1 = ref();
// 默认缴费凭证
const defaultPayedVoucherFileList1 = ref([]);
// 是否是开通审核
const isOpenReview = ref(false);

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadAuditData');
const headers = reactive({
  Authorization: getToken()
});

// const checkPlateNos = (rule, val, callback) => {
//   console.log('returnreturn', val);
//   if (!val || val.length === 0) {
//     callback(new Error('请输入车牌号'));
//   } else {
//     const hasEmpty = val.find((item) => {
//       return !item.value;
//     });
//     if (hasEmpty) {
//       callback(new Error('请输入车牌号'));
//     } else {
//       callback();
//     }
//   }
// };
const validatePayedMoney = (rule, value, callback) => {
  const reg = /^[0-9]+.?[0-9]*$/;
  if (!reg.test(value)) {
    callback(new Error('请输入数字'));
  } else if (value < 0 || value > formData.value.prk_rent_product_money) {
    callback(new Error('实收金额数不能大于套餐的产品金额数且不能小于0'));
  } else {
    callback();
  }
};
const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[0123456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};

const validateAuditUrl = (rule, value, callback) => {
  if (!value) {
    callback(new Error(`请上传${auditFileName.value}`));
  } else {
    callback();
  }
};

const validatePlateNo = (rule, value, callback) => {
  if (value) {
    const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]$/;
    const newReg =
      /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NP-Z](?:((\d{5}[A-HJK])|([A-HJK][A-HJ-NP-Z0-9][0-9]{4}))|[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳])$/;
    if (moreRentSwitch.value !== 1) {
      const arr = value.split(',');
      if (arr.length > 1) {
        callback(new Error('只能输入一张车牌号'));
        return;
      }
      if (!reg.test(value) && !newReg.test(value)) {
        callback(new Error(value + '为无效的车牌号'));
        return;
      }
    } else {
      const plateNosArr = value.split(',');
      // 判断车票号是否重复
      const uniquePlateNos = new Set(plateNosArr);
      if (uniquePlateNos.size !== plateNosArr.length) {
        callback(new Error('车牌号不能重复'));
        return;
      }
      for (let i = 0; i < plateNosArr.length; i++) {
        const item = plateNosArr[i];
        if (!reg.test(item) && !newReg.test(item)) {
          callback(new Error(item + '为无效的车牌号'));
          break;
        }
      }
    }
  }
  callback();
};

const mainRules = computed(() => {
  return {
    park_id: [
      {
        required: true,
        message: '请选择车场',
        trigger: 'change'
      }
    ],
    user_identity: [
      {
        required: true,
        message: '请选择用户申办身份',
        trigger: 'change'
      }
    ],
    prk_rent_rule_id: [
      {
        required: true,
        message: '请选择长租规则',
        trigger: 'change'
      }
    ],
    space_id: [
      {
        required: true,
        message: '请选择车位编号',
        trigger: 'change'
      }
    ],
    prk_rent_product_id: [
      {
        required: true,
        message: '请选择产品类型',
        trigger: 'change'
      }
    ],
    mbr_member_id: [
      {
        required: true,
        message: '请选择关联会员',
        trigger: 'change'
      }
    ],
    mbr_member_nickname: [
      {
        required: true,
        message: '请输入车主信息',
        trigger: 'blur'
      }
    ],
    valid_start_time: [
      {
        required: true,
        message: '请选择有效期',
        trigger: 'blur'
      }
    ],
    pay_method: [
      {
        required: formData.value?.data_source == 2 ? false : true,
        message: '请选择付款方式',
        trigger: 'blur'
      }
    ],
    payed_money: [
      {
        required: true,
        message: '请输入实际收费金额',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validatePayedMoney
      }
    ],
    // payed_memo: [
    //   {
    //     required: true,
    //     message: '请输入付款备注',
    //     trigger: 'blur'
    //   }
    // ],
    mbr_member_name: [
      {
        required: true,
        message: '请输入车主姓名',
        trigger: 'blur'
      }
    ],
    mbr_member_mobile: [
      {
        required: true,
        message: '请输入手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    audit_url: [
      {
        required: false,
        message: '',
        trigger: ''
      }
    ],
    audit_url1: [
      {
        required: true,
        message: '请上传身份证正面',
        trigger: 'blur'
      }
    ],
    audit_url2: [
      {
        required: true,
        message: '请上传身份证反面',
        trigger: 'blur'
      }
    ],
    audit_url3: [
      {
        required: true,
        message: '请上传驾驶证信息',
        trigger: 'blur'
      }
    ],
    audit_url4: [
      {
        required: true,
        message: '请上传行驶证信息',
        trigger: 'blur'
      }
    ],
    audit_url5: [
      {
        required: true,
        message: '请上传交强险或商业险',
        trigger: 'blur'
      }
    ],
    audit_url6: [
      {
        required: true,
        validator: validateAuditUrl,
        trigger: 'blur'
      }
    ],
    payed_voucher_url: [
      {
        required: false,
        message: '',
        trigger: ''
      }
    ],
    payed_voucher_url1: [
      {
        required: true,
        message: '请上传缴费凭证',
        trigger: 'blur'
      }
    ],
    plate_nos: [
      {
        required: true,
        message: '请输入车牌号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validatePlateNo
      }
    ]
  };
});

// 表单字段是否展示
const showItem = computed(() => {
  const selectedProductType = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  })?.type;
  const commonSet = [5, 6, 7, 8, 9].includes(selectedProductType);
  return {
    product_range: commonSet,
    start_time: selectedProductType === 5 || selectedProductType === 8 || selectedProductType === 9,
    time_type: commonSet,
    week_day: selectedProductType === 7 && formData.value?.week_day?.length,
    days: selectedProductType === 7 && formData.value?.days?.length,
    month_range: selectedProductType === 7
  };
});

const dialogTitle = computed(() => {
  if (isOpenReview.value) {
    return '开通审核';
  } else {
    return `${isAddRule.value ? '新建' : '修改'}长租申请`;
  }
});

const notSocialUser = computed(() => {
  return [1, 2, 4].includes(formData.value.user_identity);
});

const auditFileName = computed(() => {
  return formData.value.user_identity === 2 ? '车位服务协议' : '申请表';
});

const showDialog = (data, type) => {
  isOpenReview.value = type === 'openReview';
  initFormData(data);
  mainDialogVisible.value = true;
};

const closeDialog = () => {
  mainDialogVisible.value = false;
  emits('cancel');
};
/**
 * @description 初始化表单数据
 */
const initFormData = async (data) => {
  loading.value = false;
  isAddRule.value = !data;
  // 初始化获取付款方式字典
  commonService.findEnums('order', [{ enum_key: 'rentPayMethod', enum_value: 'EnumRentPayMethod' }]).then((response) => {
    paymentMethods.value = response.data.rentPayMethod;
  });
  commonService.findEnums('park', [{ enum_key: 'rentUserIdentityType', enum_value: 'EnumRentUserIdentityType' }]).then((response) => {
    userIdentities.value = response.data.rentUserIdentityType || [];
  });
  if (data) {
    formData.value = { ...data, prk_rent_product_money: data.order_money };
    delete formData.value.order_money;
    defaultFileList.value = [[], [], [], [], [], [], []];
    fetchParkSpaceList(
      data.park_id,
      formData.value.audit_state === 2
        ? {
            code: data.space_code,
            id: data.space_id
          }
        : undefined
    );
    fetchRentRuleList(
      data.park_id,
      formData.value.audit_state === 2
        ? {
            name: data.prk_rent_rule_name,
            id: data.prk_rent_rule_id,
            type: data.prk_rent_rule_type,
            type_desc: data.prk_rent_rule_type_desc
          }
        : undefined
    );
    fetchProductList(
      {
        rule_id: data.prk_rent_rule_id,
        park_id: data.park_id
      },
      true
    );
    if (formData.value.version > 0) {
      formData.value.audit_urls.forEach((item) => {
        if (item.audit_data_name.indexOf('身份证正面') != -1) {
          defaultFileList.value[0].push({ name: item.audit_data_name.split('身份证正面')[1], url: item.audit_data_url });
        } else if (item.audit_data_name.indexOf('身份证反面') != -1) {
          defaultFileList.value[1].push({ name: item.audit_data_name.split('身份证反面')[1], url: item.audit_data_url });
        } else if (item.audit_data_name.indexOf('驾驶证') != -1) {
          defaultFileList.value[2].push({ name: item.audit_data_name.split('驾驶证')[1], url: item.audit_data_url });
        } else if (item.audit_data_name.indexOf('行驶证') != -1) {
          defaultFileList.value[3].push({ name: item.audit_data_name.split('行驶证')[1], url: item.audit_data_url });
        } else if (item.audit_data_name.indexOf('保险单') != -1) {
          defaultFileList.value[4].push({ name: item.audit_data_name.split('保险单')[1], url: item.audit_data_url });
        } else if (notSocialUser.value && auditFileName.value.indexOf(item.audit_data_name.split('.')[0]) != -1) {
          defaultFileList.value[5].push({ name: item.audit_data_name.split(auditFileName.value)[1], url: item.audit_data_url });
        } else {
          defaultFileList.value[6].push({ name: item.audit_data_name, url: item.audit_data_url });
        }
      });
    } else {
      formData.value.audit_urls.forEach((item) => {
        defaultFileList.value[6].push({ name: item.audit_data_name, url: item.audit_data_url });
      });
    }
    // 如果审核过了，且修改了之前的用户身份
    if (formData.value.audit_state === 2) {
      const list = formData.value.audit_urls.filter(
        (item) => item.audit_data_name.includes('车位服务协议') || item.audit_data_name.includes('申请表')
      );
      if (list?.length > 0) {
        const name = list[0].audit_data_name;
        defaultFileList.value[5] = list
          ? list.map((item) => {
              return {
                name: item.audit_data_name,
                url: item.audit_data_url
              };
            })
          : [];
        defaultFileList.value[6] = defaultFileList.value[6].filter((item) => item.name !== name);
      }
    }
    defaultPayedVoucherFileList1.value = [];
    if (formData.value.payed_voucher_url) {
      JSON.parse(formData.value.payed_voucher_url).forEach((item) => {
        defaultPayedVoucherFileList1.value.push({ name: item.audit_data_name, url: item.audit_data_url });
      });
    }
  } else {
    formData.value = {
      park_id: '',
      park_name: '',
      user_identity: '',
      prk_rent_rule_id: '',
      prk_rent_product_id: '',
      prk_rent_product_money: '',
      space_id: '',
      valid_start_time: '',
      valid_end_time: '',
      mbr_member_id: '',
      mbr_member_name: '',
      mbr_member_mobile: '',
      mbr_member_nickname: '',
      audit_url: '',
      audit_url1: '',
      audit_url2: '',
      audit_url3: '',
      audit_url4: '',
      audit_url5: '',
      audit_url6: '',
      audit_url7: '',
      payed_voucher_url: '',
      payed_voucher_url1: '',
      plate_nos: '',
      channel: 1,
      version: '1'
    };
    defaultFileList.value = [[], [], [], [], [], [], []];
    defaultPayedVoucherFileList1.value = [];
    // 清除规则名称下拉选的列表数据
    rentRules.value = [];
    const user = useUser();
    //判断user权限是否有授权车场，添加到筛选条件中直接进行查询
    if (user.park_ids !== undefined && user.park_ids.length == 1) {
      formData.value.park_id = user.park_ids[0];
      formData.value.park_name = user.park_names[0];
      fetchParkSpaceList(user.park_ids[0]);
      fetchRentRuleList(user.park_ids[0]);
      formData.value.valid_start_time = dayjs().format('YYYY-MM-DD HH:mm:ss');
      changeEndData(formData.value.valid_start_time);
    }
  }
};
// watch(parkSpaces, () => {
//   if (parkSpaces.value && parkSpaces.value.length > 0) {
//     formData.value.space_id = parkSpaces.value[0].id;
//   }
// });
/**
 * @description 暴露给父组件 处理选中事件回调
 * @param {*} val 选中值
 * @param {*} type parking | user
 */
const dealSelect = (val, type) => {
  if (type === 'parking') {
    formData.value.prk_rent_rule_id = '';
    formData.value.prk_rent_product_id = '';
    formData.value.prk_rent_product_money = '';
    formData.value.space_id = '';
    if (!isOpenReview.value) {
      formData.value.valid_start_time = '';
      formData.value.valid_end_time = '';
    }
    if (val) {
      formData.value.park_id = val[0].park_id;
      formData.value.park_name = val[0].park_name;
      moreRentSwitch.value = val[0].more_rent_switch;
      fetchParkSpaceList(val[0].park_id);
      fetchRentRuleList(val[0].park_id);
    }
  } else {
    formData.value.mbr_member_id = val[0].member_id;
    formData.value.mbr_member_nickname = val[0].member_name;
    formData.value.mbr_member_mobile = val[0].member_mobile;
    roleFormRef.value.validateField('mbr_member_mobile');
  }
};
const clearFileList5 = () => {
  defaultFileList.value[5] = [];
};
/**
 * @description 点击选择框的选择事件 触发父组件中的弹窗
 * @param {*} type 弹窗类型 parking | user
 */
const handleSelect = (type) => {
  if (type === 'identity') {
    if (formData.value.audit_state !== 2) {
      dealSelect(undefined, 'parking');
      clearFileList5();
      fetchRentRuleList(formData.value.park_id);
      roleFormRef.value.validateField('audit_url6');
    }
  } else {
    emits('select', type, formData.value);
  }
};
/**
 * @description 获取车位列表
 * @param {*} park_id 停车场id
 */
const fetchParkSpaceList = (park_id, defaultData) => {
  parkSpaces.value = [];
  let fn = isAddRule.value ? parkSpaceService.listAvailableLongRentSpace : parkSpaceService.listRentSpaceById;
  fn(park_id, formData.value.id).then((response) => {
    if (response.success === true) {
      parkSpaces.value = response.data;
      if (defaultData) {
        // 去重
        const isExist = parkSpaces.value.some((item) => item.id === defaultData.id);
        if (!isExist) {
          parkSpaces.value.push(defaultData);
        }
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
  // if (isAddRule.value) {
  //   parkSpaceService.listAvailableLongRentSpace(park_id).then((response) => {
  //     if (response.success === true) {
  //       parkSpaces.value = response.data;
  //     } else {
  //       ElMessage.error(response.detail_message || response.message);
  //     }
  //   });
  // } else {

  // }
};
/**
 * @description 获取长租规则列表
 * @param {*} park_id 停车场id
 */
const fetchRentRuleList = (park_id, defaultData) => {
  if (!park_id || !formData.value.user_identity) return;
  const params = {
    parkId: park_id,
    userIdentity: formData.value.user_identity
  };
  rentRules.value = [];
  longRentRuleService.listRentRule(params).then((response) => {
    if (response.success === true) {
      rentRules.value = response.data;
      if (defaultData) {
        rentRules.value.push(defaultData);
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
// 编辑时监听规则变化，设置长租类型
watch(rentRules, () => {
  if (formData.value.prk_rent_rule_id) {
    const rentTypeObj = rentRules.value.find((item) => {
      return item.id == formData.value.prk_rent_rule_id;
    });
    formData.value.rentType = rentTypeObj?.type;
    rentTypes.value = [
      {
        value: rentTypeObj?.type,
        label: rentTypeObj?.type_desc
      }
    ];
  }
});
/**
 * @description 获取产品列表
 * @param {*} productparams
 */
const fetchProductList = (productparams, init = false) => {
  products.value = [];
  longRentRuleService.listProduct(productparams).then((response) => {
    if (response.success === true) {
      products.value = response.data;
      if (products.value && products.value.length > 0) {
        formData.value.prk_rent_product_id = products.value[0].id;
        formData.value.product_type = products.value[0].type;
        handleProductChange(formData.value.prk_rent_product_id, init);
      }
    } else {
      ElMessage.error(response.detail_message || response.message);
    }
  });
};
/**
 * @description 选择长租规则事件
 */
const handleRentRuleChange = (val) => {
  const productparams = {
    rule_id: val,
    park_id: formData.value.park_id
  };
  const rentTypeObj = rentRules.value.find((item) => {
    return item.id == val;
  });
  console.log('rentTypeObj', rentTypeObj);
  formData.value.rentType = rentTypeObj?.type;
  rentTypes.value = [
    {
      value: rentTypeObj?.type,
      label: rentTypeObj?.type_desc
    }
  ];
  formData.value.prk_rent_product_id = '';
  fetchProductList(productparams);
};

/**
 * @description 选择产品事件
 * @param {*} val 选择产品id
 */
const handleProductChange = (val, init) => {
  const selectedProduct = products.value.find((item) => {
    return item.id == val;
  });
  if (selectedProduct) {
    formData.value.prk_rent_product_money = selectedProduct.money;
    formData.value.product_range = selectedProduct.product_range;
    formData.value.start_time = selectedProduct.start_day;
    formData.value.end_time = selectedProduct.end_day;
    formData.value.time_type = selectedProduct.time_type;
    formData.value.week_day = selectedProduct.week_day;
    formData.value.days = selectedProduct.days;
    formData.value.month_range = selectedProduct.month_range;
    formData.value.prk_rent_product_detail_id = formData.value.prk_rent_product_id;
    if (!init) {
      changeEndData(formData.value.valid_start_time);
    }
  }
};
/**
 * @description 改变结束时间
 * @param {*} val 起始时间
 */
const changeEndData = (val) => {
  // if (!val || formData.value.audit_state === 2) return;、
  if (!val) return;
  const selectedProduct = products.value.find((item) => {
    return item.id == formData.value.prk_rent_product_id;
  });
  console.log('selectedProduct', selectedProduct, products.value);
  if (!selectedProduct) {
    formData.value.valid_end_time = formData.value.valid_start_time;
    return;
  }
  if (selectedProduct.type === 8) {
    // 判断日卡的情况
    formData.value.valid_end_time = dayjs(val)
      .add(selectedProduct?.product_range - 1, 'day')
      .format('YYYY-MM-DD HH:mm:ss');
  } else if (selectedProduct.type === 9) {
    // 判断周卡的情况
    formData.value.valid_end_time = dayjs(val).add(selectedProduct?.product_range, 'week').subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
  } else {
    const timeRelaseBase = {
      1: 1,
      2: 3,
      3: 6,
      4: 12,
      0: 0
    };
    const flag = selectedProduct.type <= 4 ? selectedProduct.type : selectedProduct?.product_range || 0;
    formData.value.valid_end_time = dayjs(formData.value.valid_start_time).add(timeRelaseBase[flag], 'month').format('YYYY-MM-DD HH:mm:ss');
    if (dayjs(formData.value.valid_start_time).date() <= dayjs(formData.value.valid_end_time).date()) {
      formData.value.valid_end_time = dayjs(formData.value.valid_end_time).subtract(1, 'day').format('YYYY-MM-DD HH:mm:ss');
    }
  }
};
/**
 * @description: 点击文件列表中已上传的文件时的钩子
 */
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const onPreview = (file) => {
  const fileUrl = file.url;
  const fileName = file.name;
  const fileExtension = fileName.split('.').pop().toLowerCase();
  if (fileExtension === 'pdf') {
    // 如果是 PDF 文件，则在新标签页中打开 URL 地址
    window.open(fileUrl, '_blank');
  } else {
    dialogVisible.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = file.response?.data.audit_data_url || file.url;
  }
};
/**
 * @description: 上传文件前回调
 * @param {*} file
 * @return {*}
 */
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage.warning('上传文件大小不能超过 25MB!');
    return false;
  }
  if (file.type !== 'image/jpeg' && file.type !== 'image/png' && file.type !== 'application/pdf') {
    ElMessage.warning('上传文件格式错误!');
    return false;
  }
};
const handleRemove = (file, fileList) => {
  const index = fileList.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.splice(index, 1);
  }
};

const handleExceed = (files, index) => {
  uploadRefs['uploadRef' + index].value.clearFiles();
  uploadRefs['uploadRef' + index].value.handleStart(files[0]);
  uploadRefs['uploadRef' + index].value.submit();
};
const handleExceed1 = (files) => {
  handleExceed(files, 1);
};
const handleExceed2 = (files) => {
  handleExceed(files, 2);
};
const handleExceed3 = (files) => {
  handleExceed(files, 3);
};
const handleExceed4 = (files) => {
  handleExceed(files, 4);
};
const handleExceed5 = (files) => {
  handleExceed(files, 5);
};
const handleExceed6 = (files) => {
  handleExceed(files, 6);
};
const handleExceed7 = (files) => {
  handleExceed(files, 7);
};
const handlePayedVoucherUploadExceed1 = (files) => {
  payedVoucherUploadRef1.value.clearFiles();
  payedVoucherUploadRef1.value.handleStart(files[0]);
  payedVoucherUploadRef1.value.submit();
};

const onSuccessUpload = (response, uploadFile, index) => {
  if (response.success == true) {
    formData.value['audit_url' + index] = 'true';
    uploadFile.url = response.data.audit_data_url;
    roleFormRef.value.validateField('audit_url' + index);
    ElMessage.success(response.message);
  } else {
    ElMessage.error(response.message);
  }
};

const onSuccessUpload1 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 1);
};
const onSuccessUpload2 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 2);
};
const onSuccessUpload3 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 3);
};
const onSuccessUpload4 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 4);
};
const onSuccessUpload5 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 5);
};
const onSuccessUpload6 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 6);
};
const onSuccessUpload7 = (response, uploadFile) => {
  onSuccessUpload(response, uploadFile, 7);
};
const onPayedVoucherUploadSuccessUpload1 = (response, uploadFile) => {
  if (response.success == true) {
    uploadFile.url = response.data.audit_data_url;
    formData.value.payed_voucher_url1 = 'true';
    roleFormRef.value.validateField('payed_voucher_url1');
    ElMessage.success(response.message);
  } else {
    ElMessage.error(response.message);
  }
};

const clearMember = () => {
  formData.value.mbr_member_mobile = '';
  formData.value.mbr_member_nickname = '';
  formData.value.mbr_member_id = '';
};
/**
 * @description: 提交表单数据
 * @param {*}
 * @return {*}
 */
const handleSubmit = debounce((type) => {
  const getFileExtension = (filename) => {
    console.log('getFileExtension', filename);
    const match = filename.match(/(\.[^.]+)$/);
    return match ? match[1] : '';
  };

  let defaultFileList1copy = JSON.parse(JSON.stringify(defaultFileList.value[0]));
  let defaultFileList2copy = JSON.parse(JSON.stringify(defaultFileList.value[1]));
  let defaultFileList3copy = JSON.parse(JSON.stringify(defaultFileList.value[2]));
  let defaultFileList4copy = JSON.parse(JSON.stringify(defaultFileList.value[3]));
  let defaultFileList5copy = JSON.parse(JSON.stringify(defaultFileList.value[4]));
  let defaultFileList6copy = JSON.parse(JSON.stringify(defaultFileList.value[5]));
  let defaultFileList7copy = JSON.parse(JSON.stringify(defaultFileList.value[6]));
  formData.value.audit_url1 = '';
  formData.value.audit_url2 = '';
  formData.value.audit_url3 = '';
  formData.value.audit_url4 = '';
  formData.value.audit_url5 = '';
  formData.value.audit_url6 = '';
  formData.value.audit_url7 = '';
  if (defaultFileList1copy.length > 0) {
    formData.value.audit_url1 = 'true';
    defaultFileList1copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('身份证正面') != -1) {
        return;
      }
      item.name = '身份证正面' + extension;
    });
  }
  if (defaultFileList2copy.length > 0) {
    formData.value.audit_url2 = 'true';
    defaultFileList2copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('身份证反面') != -1) {
        return;
      }
      item.name = '身份证反面' + extension;
    });
  }
  if (defaultFileList3copy.length > 0) {
    formData.value.audit_url3 = 'true';
    defaultFileList3copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('驾驶证') != -1) {
        return;
      }
      item.name = '驾驶证' + extension;
    });
  }
  if (defaultFileList4copy.length > 0) {
    formData.value.audit_url4 = 'true';
    defaultFileList4copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('行驶证') != -1) {
        return;
      }
      item.name = '行驶证' + extension;
    });
  }
  if (defaultFileList5copy.length > 0) {
    formData.value.audit_url5 = 'true';
    defaultFileList5copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('保险单') != -1) {
        return;
      }
      item.name = '保险单' + extension;
    });
  }
  if (defaultFileList6copy.length > 0) {
    formData.value.audit_url6 = 'true';
    defaultFileList6copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf(auditFileName.value) != -1) {
        return;
      }
      item.name = auditFileName.value + extension;
    });
  }
  if (defaultFileList7copy.length > 0) {
    defaultFileList7copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('其他') != -1) {
        return;
      }
      item.name = '其他' + extension;
    });
  }
  let filesList = defaultFileList1copy
    .concat(defaultFileList2copy)
    .concat(defaultFileList3copy)
    .concat(defaultFileList4copy)
    .concat(defaultFileList5copy)
    .concat(defaultFileList6copy)
    .concat(defaultFileList7copy);
  console.log('filesList===', filesList);
  const auditUrl = filesList.map((item) => {
    return {
      audit_data_name: item.name,
      audit_data_url: item.response?.data.audit_data_url || item.url
    };
  });
  if (auditUrl.length > 0) {
    formData.value.audit_url = JSON.stringify(auditUrl);
  } else {
    formData.value.audit_url = undefined;
  }
  delete formData.value.audit_urls;
  let defaultPayedVoucherFileList1copy = JSON.parse(JSON.stringify(defaultPayedVoucherFileList1.value));
  formData.value.payed_voucher_url1 = '';
  if (defaultPayedVoucherFileList1copy.length > 0) {
    formData.value.payed_voucher_url1 = 'true';
    defaultPayedVoucherFileList1copy.forEach((item) => {
      const extension = getFileExtension(item.name);
      if (item.name.indexOf('缴费凭证') != -1) {
        return;
      }
      item.name = '缴费凭证' + extension;
    });
    const auditPayedVoucherUrl = defaultPayedVoucherFileList1copy.map((item) => {
      return {
        audit_data_name: item.name,
        audit_data_url: item.response?.data.audit_data_url || item.url
      };
    });
    if (auditPayedVoucherUrl.length > 0) {
      formData.value.payed_voucher_url = JSON.stringify(auditPayedVoucherUrl);
    } else {
      formData.value.payed_voucher_url = undefined;
    }
  }
  formData.value.user_identity_desc = userIdentities.value.find((item) => item.value == formData.value.user_identity)?.key;
  roleFormRef.value.validate().then(async () => {
    loading.value = true;
    try {
      const submitFunc = isAddRule.value ? spaceRentApplyService.createRentSpaceApply : spaceRentApplyService.updateRentSpaceApply;
      const { message, data } = await submitFunc({ ...formData.value, plate_nos: formData.value.plate_nos?.split(',') });
      if (data?.detailMessage) {
        ElMessage.error(data.detailMessage);
      } else {
        mainDialogVisible.value = false;
        if (type === 'audit') {
          const { id, open_sign } = formData.value;
          emits('audit', { id, open_sign });
        } else {
          ElMessage.success(message);
          emits('submit');
        }
        uploadRefs['uploadRef1'].value.clearFiles();
        uploadRefs['uploadRef2'].value.clearFiles();
        uploadRefs['uploadRef3'].value.clearFiles();
        uploadRefs['uploadRef4'].value.clearFiles();
        uploadRefs['uploadRef5'].value.clearFiles();
        payedVoucherUploadRef1.value?.clearFiles();
      }
    } catch (error) {
      loading.value = false;
    } finally {
      loading.value = false;
    }
  });
}, 1000);

const handleReject = () => {
  rejectForm.value = {
    id: formData.value.id,
    reject_reason: ''
  };
  rejectDialogVisible.value = true;
  rejectFormRef.value?.resetFields();
};

const saveReject = (ref) => {
  ref.validate().then(async () => {
    rejectLoading.value = true;
    try {
      await spaceRentApplyService.rejectRentSpaceApplies(rejectForm.value);
      ElMessage.success('驳回长租车申请成功');
      emits('submit');
      rejectDialogVisible.value = false;
      mainDialogVisible.value = false;
    } catch (error) {
      rejectLoading.value = false;
    } finally {
      rejectLoading.value = false;
    }
  });
};

defineExpose({
  showDialog,
  dealSelect,
  mainDialogVisible
});
</script>

<template>
  <div>
    <!-- 长租申请 -->
    <el-dialog :title="dialogTitle" v-model="mainDialogVisible" destroy-on-close :close-on-click-modal="false" width="1080px">
      <el-form ref="roleFormRef" label-width="120px" :rules="mainRules" :model="formData" class="grid-form">
        <el-form-item label="停车场名称" prop="park_id">
          <el-input
            v-model="formData.park_name"
            placeholder="请选择车场"
            readonly
            @click="handleSelect('parking')"
            v-if="isAddRule || formData.audit_state !== 2"
          />
          <el-input v-model="formData.park_name" v-else disabled />
        </el-form-item>
        <el-form-item label="用户身份" prop="user_identity">
          <el-select
            v-model="formData.user_identity"
            placeholder="请选择用户申报身份"
            style="width: 100%"
            :disabled="formData.audit_state === 4"
            @change="handleSelect('identity')"
          >
            <el-option v-for="item in userIdentities" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="规则名称" prop="prk_rent_rule_id">
          <el-select
            v-model="formData.prk_rent_rule_id"
            placeholder="规则名称"
            style="width: 100%"
            :disabled="(!formData.park_id && !formData.user_identity) || formData.audit_state === 2"
            @change="handleRentRuleChange"
          >
            <el-option v-for="item in rentRules" :key="item.id + 'prk_rent_rule_id'" :label="item.name" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="车位编号" prop="space_id">
          <el-select v-model="formData.space_id" :disabled="!formData.park_id" placeholder="车位编号" style="width: 100%" clearable>
            <el-option v-for="item in parkSpaces" :key="item.id + 'space_id'" :label="item.code" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型" prop="prk_rent_product_id">
          <el-select v-model="formData.prk_rent_product_id" placeholder="产品类型" style="width: 100%" :disabled="true" @change="handleProductChange">
            <el-option v-for="item in products" :key="item.id + 'prk_rent_product_id'" :label="item.type_desc" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品金额" required>
          <el-input v-model="formData.prk_rent_product_money" :disabled="true" />
        </el-form-item>
        <el-form-item label="长租类型" required>
          <el-select v-model="formData.rentType" placeholder="长租类型" style="width: 100%" :disabled="true">
            <el-option v-for="item in rentTypes" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="产品周期" v-if="showItem.product_range">
          <el-radio-group v-if="[5, 6, 7].includes(formData.product_type)" v-model="formData.product_range" :disabled="true">
            <el-radio :label="item.label" v-for="item in rentProductRanges" :key="item.label">{{ item.text }}</el-radio>
          </el-radio-group>
          <el-input v-else-if="formData.product_type == 8" :model-value="formData.product_range + '天'" :disabled="true" />
          <el-input v-else-if="formData.product_type == 9" :model-value="formData.product_range + '周'" :disabled="true" />
        </el-form-item>
        <el-form-item label="长租时段" v-if="showItem.start_time">
          <!-- <el-time-picker v-model="timeRange" is-range range-separator="至" value-format="HH:mm:ss" disabled /> -->
          <div class="rime-range-picker" v-if="formData.product_type == 5">
            <el-time-picker v-model="formData.start_time" value-format="HH:mm:ss" :disabled="true" />
            <span>至</span>
            <el-time-picker v-model="formData.end_time" value-format="HH:mm:ss" :disabled="true" />
          </div>
          <div style="width: 100%" v-else-if="formData.product_type == 8 || formData.product_type == 9">
            <el-input :model-value="'全时段'" :disabled="true" />
          </div>
        </el-form-item>
        <!-- <el-form-item label="时段类型" v-if="showItem.time_type">
          <el-radio-group v-model="formData.time_type" disabled>
            <el-radio :label="1">收费</el-radio>
            <el-radio :label="0">免费</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item label="星期设定" v-if="showItem.week_day">
          <el-checkbox-group v-model="formData.week_day" disabled>
            <el-checkbox v-for="(item, index) in ['周日', '周一', '周二', '周三', '周四', '周五', '周六']" :label="index + 1" :key="index + ''">
              {{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="日期设定" v-if="showItem.days">
          <el-checkbox-group v-model="formData.days" disabled>
            <el-checkbox v-for="item in 31" :label="item" :key="item + 'days'">
              {{ item }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="月周期设定" v-if="showItem.month_range">
          <el-radio-group v-model="formData.month_range" disabled>
            <el-radio :label="2">30天</el-radio>
            <el-radio :label="1">31天</el-radio>
            <el-radio :label="0">自然月</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="车主姓名" prop="mbr_member_name">
          <el-input v-model="formData.mbr_member_name" maxlength="10" show-word-limit />
        </el-form-item>
        <el-form-item label="手机号" prop="mbr_member_mobile">
          <div style="display: flex; width: 100%">
            <el-input v-model="formData.mbr_member_mobile" :disabled="!!formData.mbr_member_id && !isOpenReview">
              <template #suffix>
                <el-icon @click="clearMember" class="closeBtn"><CircleClose /></el-icon>
              </template>
            </el-input>
            <el-button type="primary" @click="handleSelect('user')">调取会员</el-button>
          </div>
        </el-form-item>
        <el-form-item label="车牌号" prop="plate_nos">
          <el-input
            v-model="formData.plate_nos"
            :placeholder="moreRentSwitch == 1 ? '若多车牌以英文逗号隔开，例：粤XX,粤XX' : '请输入车牌号，仅支持添加一张'"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="有效期" prop="valid_start_time">
          <el-row :gutter="10">
            <el-col :span="12">
              <el-date-picker
                v-model="formData.valid_start_time"
                type="date"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="选择开始时间"
                :disabled="isOpenReview"
                v-if="isAddRule || formData.audit_state !== 2"
                @change="changeEndData"
              />
              <el-input v-model="formData.valid_start_time.split(' ')[0]" disabled placeholder="开始时间" v-else />
            </el-col>
            <el-col :span="12">
              <el-input v-model="formData.valid_end_time.split(' ')[0]" style="width: 100%" :disabled="true" placeholder="结束时间" />
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="付款方式" prop="pay_method" v-if="formData.channel !== 0">
          <el-select v-model="formData.pay_method" placeholder="付款方式" style="width: 100%" :disabled="formData.audit_state === 2">
            <el-option v-for="item in paymentMethods" :key="item.value" :label="item.key" :value="item.value" />
          </el-select>
        </el-form-item>
        <template v-if="typeof formData.pay_method === 'number' && formData.pay_method !== 4">
          <el-form-item label="实收金额" prop="payed_money">
            <el-input v-model="formData.payed_money" style="width: 100%" placeholder="请输入实际收费金额" :disabled="formData.audit_state === 2" />
          </el-form-item>
          <el-form-item label="付款备注" prop="payed_memo">
            <el-input
              v-model="formData.payed_memo"
              style="width: 100%"
              placeholder="请备注说明情况，限100字以内"
              :disabled="formData.audit_state === 2"
            />
          </el-form-item>
        </template>
        <el-row class="row">
          <el-col :span="24">
            <div class="el-form-item is-required asterisk-left">
              <label class="el-form-item__label">
                申请资料
                <span class="el-form-item__sublabel">（支持JPG、JPEG、PNG格式，单张限10M，请确保上传文件清晰，不要过度曝光、模糊）</span>
              </label>
            </div>
            <div style="display: flex" v-if="formData.version > 0">
              <el-form-item prop="audit_url1">
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef1']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[0]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload1"
                  :on-exceed="handleExceed1"
                >
                  <div v-if="!defaultFileList[0].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传身份证正面</div>
                  </div>
                  <div v-else class="upload-img">
                    <img :src="defaultFileList[0]?.[0].url" :alt="defaultFileList[0]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[0]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[0]?.[0], defaultFileList[0])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item prop="audit_url2" label-width="20">
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef2']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[1]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload2"
                  :on-exceed="handleExceed2"
                >
                  <div v-if="!defaultFileList[1].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传身份证反面</div>
                  </div>
                  <div v-else class="upload-img">
                    <img :src="defaultFileList[1]?.[0].url" :alt="defaultFileList[1]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[1]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[1]?.[0], defaultFileList[1])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item prop="audit_url3" label-width="20">
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef3']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[2]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload3"
                  :on-exceed="handleExceed3"
                >
                  <div v-if="!defaultFileList[2].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传驾驶证信息</div>
                  </div>
                  <div v-else class="upload-img">
                    <img :src="defaultFileList[2]?.[0].url" :alt="defaultFileList[2]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[2]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[2]?.[0], defaultFileList[2])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item prop="audit_url4" label-width="20">
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef4']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[3]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload4"
                  :on-exceed="handleExceed4"
                >
                  <div v-if="!defaultFileList[3].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传行驶证信息</div>
                  </div>
                  <div v-else class="upload-img">
                    <img :src="defaultFileList[3]?.[0].url" :alt="defaultFileList[3]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[3]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[3]?.[0], defaultFileList[3])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item prop="audit_url5" label-width="20">
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef5']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[4]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload5"
                  :on-exceed="handleExceed5"
                >
                  <div v-if="!defaultFileList[4].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传交强险或商业险</div>
                  </div>
                  <div v-else class="upload-img">
                    <img :src="defaultFileList[4]?.[0].url" :alt="defaultFileList[4]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[4]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[4]?.[0], defaultFileList[4])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row class="row">
          <el-col :span="24">
            <div></div>
            <div style="display: flex">
              <el-form-item
                v-if="(notSocialUser && formData.audit_state !== 2) || (formData.audit_state === 2 && defaultFileList[5].length > 0)"
                prop="audit_url6"
              >
                <el-upload
                  :limit="1"
                  :action="uploadUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :ref="uploadRefs['uploadRef6']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG, .pdf"
                  v-model:file-list="defaultFileList[5]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload6"
                  :on-exceed="handleExceed6"
                >
                  <div v-if="!defaultFileList[5].length" class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div class="upload-state">上传{{ auditFileName }}</div>
                  </div>
                  <div v-if="!defaultFileList[5].length" class="upload-state-extra">
                    {{ formData.user_identity === 2 ? '(盖章版)' : '(签批版)' }}
                  </div>
                  <div v-else class="upload-img">
                    <!-- <img :src="defaultFileList[5]?.[0].url" :alt="defaultFileList[5]?.[0].name" /> -->
                    <div
                      style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center"
                      v-if="defaultFileList[5]?.[0].name.split('.').pop().toLowerCase() === 'pdf'"
                    >
                      <img style="width: auto; height: auto" :src="PDFLogo" alt="" />
                    </div>
                    <img v-else :src="defaultFileList[5]?.[0].url" :alt="defaultFileList[5]?.[0].name" />
                    <div class="upload-operations">
                      <span @click.stop="onPreview(defaultFileList[5]?.[0])">
                        <el-icon><zoom-in /></el-icon>
                      </span>
                      <span @click.stop="handleRemove(defaultFileList[5]?.[0], defaultFileList[5])">
                        <el-icon><Delete /></el-icon>
                      </span>
                      <span>
                        <el-icon><Upload /></el-icon>
                      </span>
                    </div>
                  </div>
                </el-upload>
              </el-form-item>
              <el-form-item
                prop="audit_url7"
                :label-width="
                  (notSocialUser && formData.audit_state !== 2) || (formData.audit_state === 2 && defaultFileList[5].length > 0) ? 20 : 120
                "
              >
                <el-upload
                  :action="uploadUrl"
                  :headers="headers"
                  :ref="uploadRefs['uploadRef7']"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultFileList[6]"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onSuccessUpload7"
                  :on-exceed="handleExceed7"
                >
                  <div class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传其他文件</div>
                  </div>
                  <template #file="{ file }">
                    <div class="upload-img">
                      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="onPreview(file)">
                          <el-icon><zoom-in /></el-icon>
                        </span>
                        <span class="el-upload-list__item-delete" @click="handleRemove(file, defaultFileList[6])">
                          <el-icon><Delete /></el-icon>
                        </span>
                      </span>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
        <el-row class="row" v-if="typeof formData.pay_method === 'number' && formData.pay_method !== 4">
          <el-col :span="24">
            <div class="el-form-item is-required asterisk-left">
              <label class="el-form-item__label">
                缴费凭证
                <span class="el-form-item__sublabel">（上传支付或合同文件凭证，支持JPG、JPEG、PNG、PDF格式，单张限10M）</span>
              </label>
            </div>
            <div style="display: flex">
              <el-form-item prop="payed_voucher_url1">
                <el-upload
                  :action="uploadUrl"
                  :headers="headers"
                  ref="payedVoucherUploadRef1"
                  accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG"
                  v-model:file-list="defaultPayedVoucherFileList1"
                  :on-preview="onPreview"
                  list-type="picture-card"
                  :before-upload="beforeUpload"
                  :on-success="onPayedVoucherUploadSuccessUpload1"
                  :on-exceed="handlePayedVoucherUploadExceed1"
                >
                  <div class="upload-card">
                    <el-icon><Plus /></el-icon>
                    <div style="font-size: 12px">上传缴费凭证</div>
                  </div>
                  <template #file="{ file }">
                    <div class="upload-img">
                      <img class="el-upload-list__item-thumbnail" :src="file.url" alt="" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="onPreview(file)">
                          <el-icon><zoom-in /></el-icon>
                        </span>
                        <span class="el-upload-list__item-delete" @click="handleRemove(file, defaultPayedVoucherFileList1)">
                          <el-icon><Delete /></el-icon>
                        </span>
                      </span>
                    </div>
                  </template>
                </el-upload>
              </el-form-item>
            </div>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button v-if="!isOpenReview" type="primary" @click="handleSubmit" :loading="loading">确 定</el-button>
          <el-button v-if="isOpenReview" type="primary" @click="handleSubmit('audit')" :loading="loading">通过，提交审核</el-button>
          <el-button v-if="isOpenReview" type="danger" @click="handleReject">驳 回</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogVisible" :title="title" width="40%">
      <img w-full style="max-width: 100%; height: auto; margin: auto; display: block" :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
    <!-- 审核驳回-->
    <el-dialog title="审核驳回" v-model="rejectDialogVisible" :close-on-click-modal="false" width="500px">
      <el-form ref="rejectFormRef" label-width="100px" :rules="rejectRules" :model="rejectForm">
        <el-form-item label="驳回原因:" prop="reject_reason">
          <el-input v-model="rejectForm.reject_reason" type="textarea" placeholder="请输入驳回原因,限60字数以内" autosize :maxlength="60" />
          <el-text type="danger">提示: 请列明驳回原因,该原因将在小程序端对客展示</el-text>
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button @click="rejectDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="saveReject(rejectFormRef)" :loading="rejectLoading">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss" scoped>
.grid-form {
  display: grid;
  grid-template-columns: 1fr 1fr;

  .row {
    grid-column-start: 1;
    grid-column-end: 3;
  }
}

:deep(.el-form-item__content) {
  align-items: flex-start;
}

::v-deep .el-form-item__label {
  justify-content: unset !important;
  padding-left: 18px;
}
.el-form-item__sublabel {
  color: #f56c6c;
  font-size: 12px;
}
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 80px;
  // width: 148px;
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;

  .upload-state {
    font-size: 12px;
    text-align: center;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-operations {
    display: none;
    width: 100%;
    height: 100%;
    font-size: 20px;
    background-color: var(--el-overlay-color-lighter);
    color: #fff;
    cursor: default;
    justify-content: center;
    align-items: center;
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
  }

  .upload-operations span {
    margin: 0 7px;
    cursor: pointer;
  }
}

.upload-state-extra {
  font-size: 12px;
  color: #ea5757;
  position: absolute;
  bottom: 35px;
}

.closeBtn {
  visibility: hidden;
  cursor: pointer;
}
.el-input--suffix {
  &:hover {
    .closeBtn {
      visibility: visible;
    }
  }
}

:deep(.el-upload--picture-card) {
  position: relative;
}

:deep(.el-upload--picture-card:hover .upload-operations) {
  display: inline-flex;
  opacity: 1;
  transition: opacity var(--el-transition-duration);
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin: 0 20px 20px 0;
}
.rime-range-picker {
  width: 360px;
  display: flex;
  align-items: center;
  & > span {
    margin: 0 10px;
  }
}
</style>
