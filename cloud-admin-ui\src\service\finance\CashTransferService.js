import * as cashTransferApi from '@/api/finance/CashTransferApi';

/**
 * 停车找零明细
 */
export default {
  /**
   * 修改单据并撤销商户转账
   */
  cancelTransfer(data) {
    return cashTransferApi.cancelTransfer(data);
  },
  /**
   * 导出微信找零列表
   */
  exportWxTransfer(data) {
    return cashTransferApi.exportWxTransfer(data);
  },
  /**
   * 查询微信找零列表(分页)
   */ getWxTransferPage(data) {
    return cashTransferApi.getWxTransferPage(data);
  },
  /**
   * 根据找零状态统计找零
   */ countTransferByState(data) {
    return cashTransferApi.countTransferByState(data);
  },
  /**
   * 重新发起商家转账
   */ reTryTransfer(data) {
    return cashTransferApi.reTryTransfer(data);
  },
  /**
   * 商户单号查询转账单
   */ queryTransferByOutBillNo(data) {
    return cashTransferApi.queryTransferByOutBillNo(data);
  }
};
