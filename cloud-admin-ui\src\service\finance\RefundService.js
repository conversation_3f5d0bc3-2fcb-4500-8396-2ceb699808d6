import * as refundApi from '@/api/finance/RefundApi';

/**
 * 退款管理
 */
export default {
  /**
   * 分页查询退款管理
   */
  pagingRefundOrder(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.pagingRefundOrder(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询长租退款订单详情
   */
  getRefundLeaveDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.getRefundLeaveDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询临停退款订单详情
   */
  getRefundStopDetail(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.getRefundStopDetail(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租打款完成
   */
  leavingComplete(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.leavingComplete(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * ETC退款申请
   */
  etcRefund(data) {
    return refundApi.etcRefund(data);
  },
  /**
   * 临停打款完成
   */
  stopComplete(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.stopComplete(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租取消退款
   */
  leavingCancel(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.leavingCancel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 临停取消退款
   */
  stopCancel(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.stopCancel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 易宝退款-原路返回
   */
  yibaoCancel(data) {
    return new Promise((resolve, reject) => {
      try {
        refundApi.yibaoCancel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
