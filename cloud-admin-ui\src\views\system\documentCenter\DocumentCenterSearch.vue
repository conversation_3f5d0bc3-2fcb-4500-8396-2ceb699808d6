<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item> <el-input v-model="form.queryParams.title" placeholder="请输入文档标题" /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.updator_name" placeholder=" 请输入更新人" /></form-search-item>
  </FormSearch>
</template>

<script name="DocumentCenterSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import { reactive } from 'vue';
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    title: '',
    updator_name: '',
    page: 1,
    limit: 30
  }
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    title: '',
    updator_name: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
</script>
<style lang="scss" scoped></style>
