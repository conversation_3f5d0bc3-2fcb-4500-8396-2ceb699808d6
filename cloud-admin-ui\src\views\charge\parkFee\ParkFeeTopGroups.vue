<template>
  <div>
    <div class="search-btn-group" v-loading="loading">
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.count_order_money }}元</p>
        <span class="search-btn-group-total-label">应交金额</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.count_debate_money }}元</p>
        <span class="search-btn-group-total-label">优惠金额</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ data.countData.count_payed_money }}元</p>
        <span class="search-btn-group-total-label">实缴金额</span>
      </div>
    </div>
  </div>
</template>

<script name="ParkFeeTopGroups" setup>
import parkFeeService from '@/service/charge/ParkFeeService';
import '@/styles/searchBtnGroup.scss';
import { ElMessage } from 'element-plus';
import { onMounted, reactive, ref } from 'vue';

const loading = ref(false);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  countData: {
    count_order_money: 0,
    count_debate_money: 0,
    count_payed_money: 0
  }
});

onMounted(() => {
  data.queryParams.order_states = [1, 2];
  // countParkPayRecord(data.queryParams);
});

const countParkPayRecord = (queryParams) => {
  loading.value = true;
  data.queryParams = queryParams;
  const { park_name, ...newQueryParams } = queryParams;
  parkFeeService.countParkPayRecord(newQueryParams).then((response) => {
    if (response.success === true) {
      data.countData = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  countParkPayRecord
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
}
</style>
