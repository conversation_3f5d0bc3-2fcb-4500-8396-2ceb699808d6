<template>
  <el-card class="table" shadow="never">
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 241px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="300">
          <template v-slot="scope">
            <el-button link type="primary" @click="handlePayRecord(scope.row)"> 查看缴费记录 </el-button>
            <el-button link type="primary" @click="handleCarInfo(scope.row)"> 查看车辆信息 </el-button>
            <el-button link type="primary" @click="handleInvoice(scope.row)"> 查看发票 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="member_name" label="会员昵称" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="park_name" label="最近使用车场" align="center" />
        <el-table-column prop="count" label="投诉数量" align="center" />
        <el-table-column prop="pay_time" label="最近缴费时间" align="center" />
        <el-table-column prop="created_at" label="注册时间" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="缴费记录" v-model="payRecordDialogVisible" :close-on-click-modal="false" width="1000px">
        <el-table :data="payRecordData" border>
          <el-table-column prop="park_name" label="停车场名称" align="center" width="200px" />
          <el-table-column prop="plate_nos" label="车牌号" align="center" />
          <el-table-column prop="type_display" label="缴费类型" align="center" />
          <el-table-column prop="payed_time" label="缴费时间" align="center" width="180px" />
          <el-table-column prop="payed_money" label="缴费金额" align="center" />
          <el-table-column prop="pay_method_display" label="支付方式" align="center" />
          <el-table-column prop="refund_state_display" label="退款状态" align="center" />
        </el-table>
        <el-pagination
          background
          :current-page="data.payRecordParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.payRecordParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="count"
          class="table-pagination"
          @size-change="payRecordHandleSizeChange"
          @current-change="payRecordHandleCurrentChange"
        />
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="payRecordDialogVisible = false">关 闭</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="车辆信息" v-model="carInfoDialogVisible" :close-on-click-modal="false" width="1000px">
        <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
          <el-tab-pane label="免费车辆" name="carFree">
            <car-free ref="car_free" />
          </el-tab-pane>
          <el-tab-pane label="长租车辆" name="longRentCar">
            <long-rent-car ref="long_rent_car" />
          </el-tab-pane>
          <el-tab-pane label="临停车辆" name="parkCar">
            <park-car ref="park_car" />
          </el-tab-pane>
          <el-tab-pane label="已绑车辆" name="bindCar">
            <bind-car ref="bind_car" />
          </el-tab-pane>
        </el-tabs>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="carInfoDialogVisible = false">关 闭</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>
<script name="MemberInfoTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import memberInfoService from '@/service/member/MemberInfoService';
import CarFree from './CarFree.vue';
import LongRentCar from './LongRentCar.vue';
import ParkCar from './ParkCar.vue';
import BindCar from './BindCar.vue';
import { activeRouteTab } from '@/utils/tabKit';

const tableData = ref([]);
const loading = ref(false);
const payRecordData = ref([]);
const activeName = ref('carFree');
const car_free = ref([]);
const long_rent_car = ref([]);
const park_car = ref([]);
const bind_car = ref([]);
const total = ref(0);
const count = ref(0);
const payRecordDialogVisible = ref(false);
const carInfoDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  payRecordParams: {
    id: undefined,
    mobile: undefined,
    page: 1,
    limit: 30
  }
});

const param = reactive({
  id: undefined,
  mobile: undefined,
  page: 1,
  limit: 30
});

onMounted(() => {
  getList(data.queryParams);
});

// 分页查询设备列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  memberInfoService.pagingMemberMessage(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 查看缴费记录
const handlePayRecord = (params) => {
  payRecordDialogVisible.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.payRecordParams = params;
  data.payRecordParams.id = params.id;
  data.payRecordParams.page = params.page;
  data.payRecordParams.limit = params.limit;
  memberInfoService.pagingMemberPayRecords(data.payRecordParams).then((response) => {
    payRecordData.value = response.data.rows;
    count.value = parseInt(response.data.total);
  });
};

// 查看车辆信息
const handleCarInfo = (row) => {
  carInfoDialogVisible.value = true;
  data.payRecordParams.id = row.id;
  data.payRecordParams.mobile = row.mobile;
  activeName.value = 'carFree';
  setTimeout(function () {
    car_free.value.getList(data.payRecordParams);
  }, 100);
};

// 查看发票
const handleInvoice = (row) => {
  activeRouteTab({
    path: '/invoice/electronicInvoiceRecord',
    query: {
      memberId: row.id,
      memberName: row.member_name
    }
  });
};

const handleClick = (tab) => {
  if (tab.props.name === 'carFree') {
    param.mobile = data.payRecordParams.mobile;
    car_free.value.getList(param);
  }
  if (tab.props.name === 'longRentCar') {
    param.id = data.payRecordParams.id;
    long_rent_car.value.getList(param);
  }
  if (tab.props.name === 'parkCar') {
    param.id = data.payRecordParams.id;
    park_car.value.getList(param);
  }
  if (tab.props.name === 'bindCar') {
    param.id = data.payRecordParams.id;
    bind_car.value.getList(param);
  }
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
