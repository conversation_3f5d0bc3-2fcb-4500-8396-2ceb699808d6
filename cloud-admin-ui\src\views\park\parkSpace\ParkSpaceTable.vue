<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate(addForm)">添加车位</el-button>
      </el-space>
      <div>
        <el-space>
          <el-button type="plain" @click="templateDownload()">模板下载</el-button>
          <el-upload
            :limit="1"
            :action="uploadExcelUrl"
            :headers="headers"
            :show-file-list="false"
            :before-upload="beforeUpload"
            :on-success="onSuccessUpload"
          >
            <el-button type="plain">上传excel</el-button>
          </el-upload>
        </el-space>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 325px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="180">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" min-width="120" />
        <el-table-column prop="code" label="车位编号" align="center" min-width="100" />
        <el-table-column prop="type_desc" label="车位类型" align="center" min-width="100" />
        <el-table-column prop="property_desc" label="车位属性" align="center" min-width="120" />
        <el-table-column prop="plate_no" label="关联车牌号" align="center" min-width="100" />
        <el-table-column prop="picture_url" label="车位图" align="center" min-width="120">
          <template v-slot="scope">
            <el-image
              v-if="scope.row.picture_url !== '' && scope.row.picture_url !== null && scope.row.picture_url !== undefined"
              style="width: 100px; height: 100px"
              :src="scope.row.picture_url"
              :fit="fit"
            />
          </template>
        </el-table-column>
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加车位" v-model="createDialogVisible" :close-on-click-modal="false" @close="closeAddDialog(addForm)" width="500px">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item prop="park_id" label="关联停车场">
            <el-input v-model="data.form.park_name" readonly="true" @click="authCharge(true, 'add')" placeholder="选择车场" />
          </el-form-item>
          <el-form-item prop="park_region_id" label="关联子场">
            <el-select v-model="data.form.park_region_id" style="width: 100%" @click="findParkRegion(data.form.park_id)">
              <el-option v-for="item in parkRegionList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="code" label="车位编号"> <el-input v-model="data.form.code" placeholder="车位编号" /> </el-form-item>
          <el-form-item prop="type" label="车位类型">
            <el-select v-model="data.form.type" style="width: 100%">
              <el-option v-for="item in typeList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="property" label="车位属性">
            <el-select v-model="data.form.property" style="width: 100%">
              <el-option v-for="item in propertyList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="picture" label="车位图">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="data.form.picUrl" :src="data.form.picUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createParkSpace(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改车位" v-model="updateDialogVisible" :close-on-click-modal="false" @close="closeEditDialog(editForm)" width="500px">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="park_id" label="关联停车场">
            <el-input v-model="data.updateForm.park_name" readonly="true" @click="authCharge(true, 'edit')" placeholder="选择车场" />
          </el-form-item>
          <el-form-item prop="park_region_id" label="关联子场">
            <el-select v-model="data.updateForm.park_region_id" style="width: 100%" @click="findParkRegion(data.updateForm.park_id)">
              <el-option v-for="item in parkRegionList" :key="item.id" :label="item.name" :value="item.id" />
            </el-select>
          </el-form-item>
          <el-form-item prop="code" label="车位编号"> <el-input v-model="data.updateForm.code" placeholder="车位编号" /> </el-form-item>
          <el-form-item prop="type" label="车位类型">
            <el-select v-model="data.updateForm.type" style="width: 100%">
              <el-option v-for="item in typeList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="property" label="车位属性">
            <el-select v-model="data.updateForm.property" style="width: 100%">
              <el-option v-for="item in propertyList" :key="item.value" :label="item.key" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="picture" label="车位图">
            <el-upload
              class="avatar-uploader"
              :action="uploadUrl"
              :headers="headers"
              :show-file-list="false"
              :on-success="handleAvatarSuccess"
              :before-upload="beforeAvatarUpload"
            >
              <img v-if="data.updateForm.picUrl" :src="data.updateForm.picUrl" class="avatar" />
              <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
            </el-upload>
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateParkSpace(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 关联车场 -->
      <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
        <park-space-find-back
          :park_id="park_id"
          :park_name="park_name"
          @authCharge="authCharge(false, '')"
          :mode="flag"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="ParkSpaceTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import parkSpaceService from '@/service/park/ParkSpaceService';
import commonService from '@/service/common/CommonService';
import ParkSpaceFindBack from './ParkSpaceFindBack.vue';
import { getToken } from '@/utils/common';
import { saveToFile } from '@/utils/utils.js';

const validateNumAndEng = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^[A-Za-z0-9]+$/;
    if (!reg.test(value)) {
      callback(new Error('只能输入英文和数字'));
    }
  }
  callback();
};

const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/space/uploadPicture');
const uploadExcelUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/space/uploadImportExcel');
const url = ref(import.meta.env.VITE_FDFS_URL);
const urlDownLoad = ref(import.meta.env.VITE_BASE_URL);
const headers = reactive({
  Authorization: getToken()
});
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const total = ref(0);
const relatedParkDialogVisible = ref(false);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const propertyList = ref([]);
const typeList = ref([]);
const parkRegionList = ref([]);
const data = reactive({
  updatePicUrl: undefined,
  picUrl: undefined,
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: undefined,
    park_region_id: undefined,
    code: undefined,
    type: undefined,
    property: undefined
  },
  updateForm: {},
  uploadExcelFile: {
    space_upload_url: undefined
  },
  rules: {
    park_id: [
      {
        required: true,
        message: '请选择关联停车场',
        trigger: 'blur'
      }
    ],
    park_region_id: [
      {
        required: true,
        message: '请选择关联子场',
        trigger: 'change'
      }
    ],
    code: [
      {
        required: true,
        message: '请输入车位编号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateNumAndEng
      }
    ],
    type: [
      {
        required: true,
        message: '请选择车位类型',
        trigger: 'blur'
      }
    ],
    property: [
      {
        required: true,
        message: '请选择车位属性',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  getList(data.queryParams);
  initSelects();
  status.value = true;
});

const initSelects = () => {
  const param = [
    { enum_key: 'propertyList', enum_value: 'EnumParkSpaceProperty' },
    { enum_key: 'typeList', enum_value: 'EnumParkSpaceType' }
  ];
  commonService.findEnums('park', param).then((response) => {
    propertyList.value = response.data.propertyList;
    typeList.value = response.data.typeList;
  });
};
// 子场列表
const findParkRegion = (parkId) => {
  if (parkId !== undefined && parkId !== '' && parkId !== null) {
    parkSpaceService.listParkRegion(parkId).then((response) => {
      parkRegionList.value = response;
    });
  }
};
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  parkSpaceService.pagingParkSpace(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleAvatarSuccess = (res) => {
  if (createDialogVisible.value === true) {
    data.form.picture = res.data.picture;
    data.form.picUrl = res.data.picture_url;
  }
  if (updateDialogVisible.value === true) {
    data.updateForm.picture = res.data.picture;
    data.updateForm.picUrl = res.data.picture_url;
  }
};

const beforeAvatarUpload = (file) => {
  const isLt100M = file.size / 1024 / 1024 < 100;
  if (!isLt100M) {
    ElMessage({
      message: '上传文件大小不能超过 100MB!',
      type: 'error'
    });
  }
};
const handleCreate = () => {
  data.form = {
    park_id: undefined,
    park_region_id: undefined,
    code: undefined,
    type: undefined,
    property: undefined
  };
  data.form.picUrl = undefined;
  createDialogVisible.value = true;
  status.value = false;
};

const createParkSpace = (addForm) => {
  addForm.validate().then(() => {
    parkSpaceService
      .createParkSpace(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          createDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
// 编辑
const handleEdit = (row) => {
  data.updateForm = {
    id: row.id,
    park_id: row.park_id,
    park_name: row.park_name,
    park_region_id: row.park_region_id,
    code: row.code,
    type: row.type,
    property: row.property,
    picture: row.picture,
    picUrl: row.picture_url
  };
  if (row.picture !== null && row.picture !== undefined && row.picture !== '') {
    data.updateForm.updatePicUrl = url.value + row.picture;
  }
  // 子场列表
  parkSpaceService.listParkRegion(data.updateForm.park_id).then((response) => {
    parkRegionList.value = response;
  });
  updateDialogVisible.value = true;
};

const updateParkSpace = (editForm) => {
  editForm.validate().then(() => {
    parkSpaceService
      .updateParkSpace(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          updateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
// 下载
const templateDownload = () => {
  commonService.fileDownload('template/1_parkSpace.xlsx').then((res) => {
    let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
    saveToFile(res.data, decodeURIComponent(fileName));
  });
};

const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    ElMessage({
      message: '上传文件大小不能超过 25MB!',
      type: 'error'
    });
  }
};
// 上传
const onSuccessUpload = (response) => {
  if (response.success == true) {
    ElMessage({
      message: response.message,
      type: 'success'
    });
    getList(data.queryParams);
  } else {
    ElMessage({
      message: response.detail_message != '' ? response.detail_message : response.message,
      type: 'error'
    });
  }
};
// 删除长租规则
const handleDelete = (val) => {
  batchDelete(val);
};
const batchDelete = (id) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    parkSpaceService
      .deleteParkSpace(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 选择车场
const authCharge = (visible, mode) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    relatedParkDialogVisible.value = true;
  }
};

const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
    data.form.park_region_id = '';
    parkSpaceService.listParkRegion(data.form.park_id).then((response) => {
      parkRegionList.value = response;
    });
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
    data.updateForm.park_region_id = '';
    parkSpaceService.listParkRegion(data.updateForm.park_id).then((response) => {
      parkRegionList.value = response;
    });
  }
};
// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  createDialogVisible.value = false;
};

// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.avatar-uploader .avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.avatar-uploader .el-upload {
  border: 1px dashed var(--el-border-color);
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: var(--el-transition-duration-fast);
}

.avatar-uploader .el-upload:hover {
  border-color: var(--el-color-primary);
}

.el-icon.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  border: 1px dashed rgb(196, 196, 204);
}
</style>
