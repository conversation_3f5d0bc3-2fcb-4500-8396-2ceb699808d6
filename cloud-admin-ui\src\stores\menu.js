import { reactive } from 'vue';
import { defineStore } from 'pinia';

export const useMenu = defineStore('menu', () => {
  const state = reactive({
    // 激活的菜单
    activedMenuIndex: '',
    activedMenuPath: '',
    // 菜单是否已经加载
    menuLoaded: false,
    // 菜单列表
    menuList: [],
    // 页面列表
    pageList: [],
    // 权限列表
    permissionList: [],
    // 组件缓存
    includeKeepAlives: [],
    excludeKeepAlives: [],
    menuVisable: true
  });

  function unloadMenu() {
    state.activedMenuIndex = '';
    state.menuLoaded = false;
    state.menuList = [];
    state.pageList = [];
    state.permissionList = [];
    state.includeKeepAlives = [];
    state.excludeKeepAlives = [];
  }

  return { state, unloadMenu };
});
