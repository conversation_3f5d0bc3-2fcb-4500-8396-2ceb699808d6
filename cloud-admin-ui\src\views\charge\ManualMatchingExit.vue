<template>
  <div class="container">
    <manual-matching-exit-search @form-search="searchManualMatchingExitList" @reset="resetParamsAndData" />
    <manual-matching-exit-table ref="table" />
  </div>
</template>

<script setup name="ManualMatchingExit">
import ManualMatchingExitSearch from './manualMatchingExit/ManualMatchingExitSearch.vue';
import ManualMatchingExitTable from './manualMatchingExit/ManualMatchingExitTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pageManualMatchingExit = (queryParams) => {
  table.value.getList(queryParams);
};

const searchManualMatchingExitList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pageManualMatchingExit
});
</script>
