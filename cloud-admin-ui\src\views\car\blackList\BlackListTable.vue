<template>
  <el-card style="margin-top: 10px; margin-bottom: -8px" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">添加黑名单</el-button>
      </el-space>
      <div>
        <DownloadButton
          btnType="default"
          :exportFunc="blackListService.exportBlackLists"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </div>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 385px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="120">
          <template v-slot="scope">
            <el-button link type="primary" @click="handleEdit(scope.row)"> 编辑 </el-button>
            <el-button link type="danger" @click="handleDelete(scope.row.id)"> 删除 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="black_list_type_desc" label="黑名单类型" align="center" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="name" label="人员姓名" align="center" />
        <el-table-column prop="mobile" label="手机号" align="center" />
        <el-table-column prop="effective_start_time" label="开始时间" align="center" />
        <el-table-column prop="effective_end_time" label="结束时间" align="center" />
        <el-table-column prop="state_desc" label="状态" align="center" />
        <el-table-column prop="updator_name" label="最后更新人" align="center" />
        <el-table-column prop="updated_at" label="更新时间" align="center" min-width="120" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="添加黑名单" v-model="blackCreateDialogVisible" :close-on-click-modal="false" width="600px" @close="closeAddDialog(addForm)">
        <el-form ref="addForm" label-width="100px" :rules="data.rules" :model="data.form">
          <el-form-item label="选择车场" prop="park_id">
            <el-input v-model="data.form.park_name" placeholder="请选择车场" readonly @click="authCharge(true, 'add')" />
          </el-form-item>
          <el-form-item prop="black_list_type" label="黑名单类型">
            <el-select v-model="data.form.black_list_type" placeholder="黑名单类型" clearable style="width: 100%">
              <el-option v-for="item in blackListType" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="plate_no" label="车牌号">
            <template #label>
              <div class="label-flex">
                <span>车牌号</span>
                <el-tooltip placement="top">
                  <template #content>
                    <span>特殊车牌须知<br /></span>
                    <span> 1) 若输入港澳本土牌、其他特殊类型车牌请切换"特殊车牌"模式；<br /></span>
                    <span> 2) 若输入大陆、粤港/粤澳车牌(如粤Z开头)请切回"大陆车牌"模式；<br /></span>
                    <span>
                      3) 若悬挂粤港/澳、港澳本土、特殊等多张号牌，请按粤港/澳(粤Z开头)->香港本土->澳门本土->特殊车牌，由此顺序择其一张号牌绑定。<br
                    /></span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <div style="display: flex; width: 100%" v-show="data.form.plate_no_type == 0">
              <el-input v-model="data.form.plate_no" placeholder="请选择车牌号" />
              <el-button type="primary" @click="changePlateNoType">特殊车牌</el-button>
            </div>
            <div style="display: flex; width: 100%" v-show="data.form.plate_no_type != 0">
              <el-input v-model="data.form.plate_no" placeholder="请输入港澳本土、其他车牌，仅支持添加一张" />
              <el-button type="primary" @click="changePlateNoType">大陆车牌</el-button>
            </div>
          </el-form-item>
          <el-form-item label="人员姓名">
            <el-input v-model="data.form.name" maxlength="10" show-word-limit />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号">
            <el-input v-model="data.form.mobile" maxlength="11" show-word-limit />
          </el-form-item>
          <el-form-item label="开始时间" class="required">
            <el-date-picker
              v-model="data.form.effective_start_time"
              type="date"
              format="YYYY-MM-DD"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <el-form-item label="结束时间" class="required">
            <el-date-picker
              v-model="data.form.effective_end_time"
              type="date"
              format="YYYY-MM-DD"
              style="width: 100%"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createCancel(addForm)">取 消</el-button>
            <el-button type="primary" @click="createBlackList(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="修改黑名单" v-model="blackUpdateDialogVisible" :close-on-click-modal="false" width="600px" @close="closeEditDialog(editForm)">
        <el-form ref="editForm" label-width="100px" :rules="data.rules" :model="data.updateForm">
          <el-form-item label="选择车场" prop="park_id">
            <el-input v-model="data.updateForm.park_name" placeholder="请选择车场" readonly @click="authCharge(true, 'edit')" />
          </el-form-item>
          <el-form-item prop="black_list_type" label="黑名单类型">
            <el-select v-model="data.updateForm.black_list_type" placeholder="黑名单类型" clearable style="width: 100%">
              <el-option v-for="item in blackListType" :key="item.value" :label="item.name" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item prop="plate_no" label="车牌号">
            <template #label>
              <div class="label-flex">
                <span>车牌号</span>
                <el-tooltip placement="top">
                  <template #content>
                    <span>特殊车牌须知<br /></span>
                    <span> 1) 若输入港澳本土牌、其他特殊类型车牌请切换"特殊车牌"模式；<br /></span>
                    <span> 2) 若输入大陆、粤港/粤澳车牌(如粤Z开头)请切回"大陆车牌"模式；<br /></span>
                    <span>
                      3) 若悬挂粤港/澳、港澳本土、特殊等多张号牌，请按粤港/澳(粤Z开头)->香港本土->澳门本土->特殊车牌，由此顺序择其一张号牌绑定。<br
                    /></span>
                  </template>
                  <el-icon>
                    <QuestionFilled />
                  </el-icon>
                </el-tooltip>
              </div>
            </template>
            <div style="display: flex; width: 100%" v-show="data.updateForm.plate_no_type == 0">
              <el-input v-model="data.updateForm.plate_no" placeholder="请选择车牌号" />
              <el-button type="primary" @click="changeUpdatePlateNoType">特殊车牌</el-button>
            </div>
            <div style="display: flex; width: 100%" v-show="data.updateForm.plate_no_type != 0">
              <el-input v-model="data.updateForm.plate_no" placeholder="请输入港澳本土、其他车牌，仅支持添加一张" />
              <el-button type="primary" @click="changeUpdatePlateNoType">大陆车牌</el-button>
            </div>
          </el-form-item>
          <el-form-item label="人员姓名">
            <el-input v-model="data.updateForm.name" maxlength="10" show-word-limit />
          </el-form-item>
          <el-form-item prop="mobile" label="手机号">
            <el-input v-model="data.updateForm.mobile" maxlength="11" show-word-limit />
          </el-form-item>
          <el-form-item label="开始时间" class="required">
            <el-date-picker
              v-model="data.updateForm.effective_start_time"
              type="date"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="选择开始时间"
            />
          </el-form-item>
          <el-form-item label="结束时间" class="required">
            <el-date-picker
              v-model="data.updateForm.effective_end_time"
              type="date"
              style="width: 100%"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              placeholder="选择结束时间"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateBlackList(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 车场查找带回 -->
      <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          :mode="flag"
          @authCharge="authCharge(false, '')"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
    </div>
  </el-card>
</template>

<script name="BlackListTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import blackListService from '@/service/car/BlackListService';
import ParkFindBack from './ParkFindBack.vue';
import dictService from '@/service/system/DictService';
import DownloadButton from '@/components/DownloadButton.vue';

const validateMobilePhone = (rule, value, callback) => {
  if (value !== '') {
    const reg = /^1[3456789]\d{9}$/;
    if (!reg.test(value)) {
      callback(new Error('请输入有效的手机号码'));
    }
  }
  callback();
};

const validatePlateNo = (rule, value, callback) => {
  if (value !== '') {
    if (
      (blackCreateDialogVisible.value && data.form.plate_no_type === 0) ||
      (blackUpdateDialogVisible.value && data.updateForm.plate_no_type === 0)
    ) {
      const reg = /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z][A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳]$/;
      const newReg =
        /^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领][A-HJ-NO-Z](?:((\d{5}[A-HJK])|([A-HJK][A-HJ-NO-Z0-9][0-9]{4}))|[A-HJ-NO-Z0-9]{4}[A-HJ-NO-Z0-9挂学警港澳])$/;
      if (!reg.test(value) && !newReg.test(value) && !value.includes('使') && !value.includes('领')) {
        callback(new Error('请输入有效的车牌号'));
      }
    } else {
      const reg = /^[a-zA-Z0-9\u4e00-\u9fa5]{1,15}$/;
      if (!reg.test(value)) {
        callback(new Error('请输入15位以内且为数字/中英文组合'));
      }
    }
  }
  callback();
};
const addForm = ref();
const editForm = ref();
const tableData = ref([]);
const blackListType = ref([]);
const total = ref(0);
const loading = ref(false);
const blackCreateDialogVisible = ref(false);
const blackUpdateDialogVisible = ref(false);
const parkInfoDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');

const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  form: {
    park_id: '',
    park_name: '',
    black_list_type: '',
    plate_no: undefined,
    plate_no_type: 0,
    name: undefined,
    mobile: undefined,
    effective_start_time: undefined,
    effective_end_time: undefined
  },
  updateForm: {},
  rules: {
    mobile: [
      {
        required: false,
        message: '请输入员工手机号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validateMobilePhone
      }
    ],
    park_id: [
      {
        required: true,
        message: '请选择停车场',
        trigger: 'blur'
      }
    ],
    black_list_type: [
      {
        required: true,
        message: '黑名单类型',
        trigger: 'change'
      }
    ],
    plate_no: [
      {
        required: true,
        message: '请输入车牌号',
        trigger: 'blur'
      },
      {
        trigger: 'blur',
        validator: validatePlateNo
      }
    ]
  }
});

onMounted(() => {
  initSelects();
});
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  blackListService.pagingBlackLists(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
const handleCreate = () => {
  data.form = {
    park_id: '',
    park_name: '',
    black_list_type: '',
    plate_no: undefined,
    plate_no_type: 0,
    name: undefined,
    mobile: '',
    effective_start_time: undefined,
    effective_end_time: undefined
  };
  blackCreateDialogVisible.value = true;
};

const initSelects = () => {
  dictService.getDictsList('BLACK_LIST_TYPE').then((response) => {
    blackListType.value = response;
  });
};
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};
const createBlackList = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.effective_start_time === '' || data.form.effective_start_time === undefined) {
      ElMessage({
        message: '请选择开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.form.effective_end_time === '' || data.form.effective_end_time === undefined) {
      ElMessage({
        message: '请选择结束时间',
        type: 'warning'
      });
      return false;
    }
    delete data.form.park_name;
    blackListService
      .createBlackList(data.form)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          addForm.resetFields();
          blackCreateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleDelete = (id) => {
  ElMessageBox.confirm('请确认是否删除？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    blackListService
      .deleteBlackList(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
const handleEdit = (val) => {
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    park_name: val.park_name,
    black_list_type: val.black_list_type,
    plate_no: val.plate_no,
    plate_no_type: val.plate_no_type,
    name: val.name,
    mobile: val.mobile,
    effective_start_time: val.effective_start_time,
    effective_end_time: val.effective_end_time
  };
  blackUpdateDialogVisible.value = true;
};
const updateBlackList = (editForm) => {
  editForm.validate().then(() => {
    if (data.updateForm.effective_start_time === '' || data.updateForm.effective_start_time === undefined) {
      ElMessage({
        message: '请选择开始时间',
        type: 'warning'
      });
      return false;
    }
    if (data.updateForm.effective_end_time === '' || data.updateForm.effective_end_time === undefined) {
      ElMessage({
        message: '请选择结束时间',
        type: 'warning'
      });
      return false;
    }
    delete data.updateForm.park_name;
    blackListService
      .updateBlackList(data.updateForm)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          editForm.resetFields();
          blackUpdateDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};
//车场查找带回
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  if (val[0].mode == 'add') {
    data.form.park_id = val[0].park_id;
    data.form.park_name = val[0].park_name;
  } else {
    data.updateForm.park_id = val[0].park_id;
    data.updateForm.park_name = val[0].park_name;
  }
};

// 取消
const createCancel = (addForm) => {
  addForm.resetFields();
  blackCreateDialogVisible.value = false;
};
// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  blackUpdateDialogVisible.value = false;
};
const closeAddDialog = (addForm) => {
  addForm.resetFields();
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const changePlateNoType = () => {
  data.form.plate_no_type = data.form.plate_no_type === 0 ? 3 : 0;
  data.form.plate_no = '';
  addForm.value.clearValidate('plate_no');
};
const changeUpdatePlateNoType = () => {
  data.updateForm.plate_no_type = data.updateForm.plate_no_type === 0 ? 3 : 0;
  data.updateForm.plate_no = '';
  editForm.value.clearValidate('plate_no');
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
