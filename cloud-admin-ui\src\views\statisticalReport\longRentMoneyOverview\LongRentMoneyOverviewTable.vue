<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <DownloadButton
          btnType="default"
          :exportFunc="longRentService.exportData"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]"
          :params="data.queryParams"
        ></DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="开通&续费(车场后台)" align="center">
          <el-table-column label="现金收取" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.park_admin_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_admin_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="退费(车场后台)" align="center">
          <el-table-column label="退费金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.refund_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.refund_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="移动端开通&续费" align="center">
          <el-table-column label="电子支付" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.mobile_terminal_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.mobile_terminal_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="长租汇总" align="center">
          <el-table-column label="收入合计" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.total_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="有效车位总数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.total_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
  </el-card>
</template>

<script name="LongRentMoneyOverviewTable" setup>
import { reactive, ref } from 'vue';
import { ElMessage } from 'element-plus';
import longRentService from '@/service/statisticalReport/LongRentService';
import DownloadButton from '@/components/DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

const getList = (params) => {
  loading.value = true;
  data.queryParams = params;
  longRentService.pagingLongRent(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
