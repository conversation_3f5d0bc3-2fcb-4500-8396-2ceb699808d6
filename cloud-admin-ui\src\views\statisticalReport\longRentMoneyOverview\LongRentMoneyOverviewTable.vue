<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space><div></div></el-space>
      <el-space>
        <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 300px)">
        <el-table-column label="车场基础信息" align="center">
          <el-table-column label="统计日期" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.statistics_date }}</span>
            </template>
          </el-table-column>
          <el-table-column label="时间周期" align="center" min-width="130">
            <template #default="scope">
              <span>{{ getShowTime(scope.row) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车场名称" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_name }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="开通&续费(车场后台)" align="center">
          <el-table-column label="现金收取" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.park_admin_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.park_admin_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="退费(车场后台)" align="center">
          <el-table-column label="退费金额" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.refund_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.refund_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="移动端开通&续费" align="center">
          <el-table-column label="电子支付" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.mobile_terminal_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="车位数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.mobile_terminal_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="长租汇总" align="center">
          <el-table-column label="收入合计" align="center" min-width="110">
            <template #default="scope">
              <span>{{ scope.row.total_money }}</span>
            </template>
          </el-table-column>
          <el-table-column label="有效车位总数" align="center" min-width="90">
            <template #default="scope">
              <span>{{ scope.row.total_space }}</span>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
           <div class="pagination">
      <el-pagination
        v-model:current-page="data.queryParams.page"
        v-model:page-size="data.queryParams.limit"
        :page-sizes="[30,100, 200, 300, 400]"
        :background="true"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
       />
     </div>
  </el-card>
</template>

<script name="LongRentMoneyOverviewTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import longRentService from '@/service/statisticalReport/LongRentService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
const total=ref(0)
const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {
      page: 1,
       limit:30
  }
});
const newdata = ref();
onMounted(() => {
  getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
   getList({})
}
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({})
}
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(12);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
const getList = (params) => {
  loading.value = true;
   data.queryParams =  {...data.queryParams,...params};
  longRentService.pagingLongRent( data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value= Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end
}
</style>
