{"name": "@types/imagemin-jpegtran", "version": "5.0.4", "description": "TypeScript definitions for imagemin-jpegtran", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin-jpegtran", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "romain-faust", "url": "https://github.com/romain-faust"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-jpegtran"}, "scripts": {}, "dependencies": {"@types/imagemin": "*"}, "typesPublisherContentHash": "695382ff5858551823ec250cae0546b28e67e88757d5cefd59156bed8fdc8f7b", "typeScriptVersion": "4.5"}