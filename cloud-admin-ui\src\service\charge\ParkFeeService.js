import * as park<PERSON>ee<PERSON><PERSON> from '@/api/charge/ParkFeeApi';

/**
 * 停车缴费
 */
export default {
  /**
   * 分页查询停车缴费
   */
  pagingParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.pagingParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询各项金额统计
   */
  countParkPayRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.countParkPayRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 申请退款
   */
  applyRefund(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.applyRefund(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出停车缴费
   */
  exportParkFee(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.exportParkFee(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询当前车场通道列表
   */
  listParkGateway(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.listParkGateway(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  listParkAccount(data) {
    return new Promise((resolve, reject) => {
      try {
        parkFeeApi.listParkAccount(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  gatewayListGateway(data) {
    return parkFeeApi.gatewayListGateway(data);
  }
};
