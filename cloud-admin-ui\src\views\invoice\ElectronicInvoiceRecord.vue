<template>
  <div class="container">
    <electronic-invoice-record-search @form-search="searchInvoiceList" @reset="resetParamsAndData" ref="search" />
    <electronic-invoice-record-table ref="table" />
  </div>
</template>

<script name="ElectronicInvoiceRecord" setup>
import ElectronicInvoiceRecordSearch from './electronicInvoiceRecord/ElectronicInvoiceRecordSearch.vue';
import ElectronicInvoiceRecordTable from './electronicInvoiceRecord/ElectronicInvoiceRecordTable.vue';
import { ref, reactive, onActivated } from 'vue';
import { useRoute } from 'vue-router';

const route = useRoute();
const table = ref(null);
const search = ref(null);
const params = reactive({
  memberId: undefined,
  memberName: undefined
});

onActivated(() => {
  if ({} !== route.query && undefined !== route.query.memberId && undefined !== route.query.memberName) {
    params.memberId = route.query.memberId;
    params.name_or_mobile = route.query.memberName;
    // table.value.getList(params);
    search.value.getParam(params);
  }
});

const searchInvoiceList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
