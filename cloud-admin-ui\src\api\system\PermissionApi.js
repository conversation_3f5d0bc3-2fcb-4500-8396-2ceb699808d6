import $ from '@/utils/axios';

/**
 * 权限接口层
 */

/**
 * 分页查询权限
 * @returns {*}
 */
export const pagingPermissions = (data) => {
  return $({
    url: '/console/permission/pagingPermissions',
    method: 'post',
    data
  });
};

/**
 * 创建权限
 * @returns {*}
 */
export const createPermission = (data) => {
  return $({
    url: '/console/permission/createPermission',
    method: 'post',
    data
  });
};

/**
 * 修改权限
 * @returns {*}
 */
export const updatePermission = (data) => {
  return $({
    url: '/console/permission/updatePermission',
    method: 'post',
    data
  });
};

/**
 * 删除权限
 * @returns {*}
 */
export const deletePermission = (data) => {
  return $({
    url: '/console/permission/deletePermission',
    method: 'post',
    data
  });
};
