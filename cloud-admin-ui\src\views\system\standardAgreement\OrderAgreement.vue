<template>
  <el-card shadow="never" style="margin: 10px 0px">
    <div class="opers">
      <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
        <el-form-item prop="content">
          <div style="border: 1px solid #ccc">
            <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
            <Editor
              style="height: 500px; overflow-y: hidden"
              v-model="data.form.content"
              :defaultConfig="editorConfig"
              :mode="mode"
              @onCreated="handleCreated"
            />
          </div>
        </el-form-item>
      </el-form>
    </div>
    <div class="footer">
      <el-button type="primary" v-if="buttonStatus === false" @click="edit()">编 辑</el-button>
      <el-button type="primary" v-if="buttonStatus === true" @click="saveStandardLicense(addForm)">保 存</el-button>
    </div>
  </el-card>
</template>

<script name="OrderAgreement" setup>
import '@wangeditor/editor/dist/css/style.css';
import agreementService from '@/service/system/AgreementService';
import { onBeforeUnmount, ref, shallowRef, reactive } from 'vue';
import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
import { ElMessage } from 'element-plus';
{
  Editor, Toolbar;
}
// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef();
// 内容 HTML
const mode = ref('default');
const buttonStatus = ref(false);
const addForm = ref();
const data = reactive({
  queryParams: {
    licenseType: undefined
  },
  form: {
    type: undefined,
    type_desc: undefined,
    content: undefined
  },
  rules: {
    type: [
      {
        required: true,
        message: '请选择协议类型',
        trigger: 'blur'
      }
    ]
  }
});

const initSelects = () => {
  const editor = editorRef.value;
  editor.disable();
};

// 只读变为可编辑状态
const edit = () => {
  const editor = editorRef.value;
  editor.enable();
  buttonStatus.value = true;
};

// 获取标准协议
const getStandardLicense = (params) => {
  data.queryParams = params;
  agreementService.getStandardLicense(params).then((response) => {
    data.form = response.data;
    if (response.data.content === null || response.data.content === '') {
      data.form.content = '';
    }
  });
};
const toolbarConfig = {};
const editorConfig = { placeholder: '请输入内容...' };
// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value;
  if (editor == null) return;
  editor.destroy();
});

const handleCreated = (editor) => {
  editorRef.value = editor; // 记录 editor 实例，重要！
};

// 保存
const saveStandardLicense = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.content === undefined || data.form.content === '' || data.form.content === null) {
      ElMessage({
        message: '协议内容不能为空！',
        type: 'error'
      });
      return false;
    }
    data.form.type = '2';
    agreementService.saveStandardLicense(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        const editor = editorRef.value;
        editor.disable();
        buttonStatus.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

defineExpose({
  getStandardLicense,
  initSelects
});
</script>
<style lang="scss" scoped>
.footer {
  padding-top: 50px;
  padding-bottom: 50px;
  text-align: center;
}
</style>
