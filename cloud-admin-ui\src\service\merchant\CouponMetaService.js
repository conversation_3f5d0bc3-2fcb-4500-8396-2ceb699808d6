import * as coupon from '@/api/merchant/CouponMetaApi';

/**
 * 优免模板
 */
export default {
  /**
   *优惠券表格数据查询
   */
  pagingCouponMetas(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.pagingCouponMetas(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增优惠券
   */
  createCouponMeta(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.createCouponMeta(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改优惠券
   */
  updateCouponMeta(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.updateCouponMeta(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除优惠券
   */
  deleteCouponMeta(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.deleteCouponMeta(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 启用
   */
  enableCouponMeta(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.enableCouponMeta(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 停用
   */
  disableCouponMeta(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.disableCouponMeta(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 优惠券表格数据查询(查找带回)
   */
  findBackCouponMetas(data) {
    return new Promise((resolve, reject) => {
      try {
        coupon.findBackCouponMetas(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
