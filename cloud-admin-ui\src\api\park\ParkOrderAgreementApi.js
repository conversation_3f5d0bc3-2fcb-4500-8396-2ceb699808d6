/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 获取预约车位协议
export const getSpaceReserveLicense = (parkId) => {
  return $({
    url: '/console/park/license/reserve/getSpaceReserveLicense/' + parkId,
    method: 'get'
  });
};

// 新建预约车位协议
export const createParkOrderAgreement = (data) => {
  return $({
    url: '/console/park/license/reserve/saveSpaceReserveLicense',
    method: 'post',
    data
  });
};
