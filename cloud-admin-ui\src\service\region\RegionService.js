import * as region from '@/api/region/RegionApi';

/**
 * 行政区域
 */
export default {
  /**
   * 省列表查询查询
   */
  listProvince() {
    return new Promise((resolve, reject) => {
      try {
        region.listProvince().then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 市列表查询查询
   */
  listCity(data) {
    return new Promise((resolve, reject) => {
      try {
        region.listCity(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 区列表查询查询
   */
  listDistrict(data) {
    return new Promise((resolve, reject) => {
      try {
        region.listDistrict(data).then(function (res) {
          resolve(res.data);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
