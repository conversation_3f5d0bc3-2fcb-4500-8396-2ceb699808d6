<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset">
    <template v-if="radioType === '1'">
      <form-search-item>
        <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
      </form-search-item>
      <form-search-item>
        <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
      </form-search-item>
      <form-search-item>
        <time-range
          v-model:date="form.dateRange"
          v-model:unit="form.dateType"
          style="width: 100%"
          :disabled-date="disabledDateFn"
          :show-type="['date', 'week', 'month', 'fiscalMonth']"
          type-value="type"
        />
        <!-- <el-date-picker
          v-model="form.dateRange"
          type="daterange"
          style="width: 100%"
          range-separator="至"
          start-placeholder="统计日期开始日期"
          end-placeholder="统计日期结束日期"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDateFn"
        /> -->
      </form-search-item>
    </template>
    <template v-else-if="radioType === '2'">
      <form-search-item :spanValue="12">
        <el-date-picker
          v-model="form.datetimerange"
          type="daterange"
          format="YYYY-MM-DD"
          range-separator="-"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
          :shortcuts="shortcuts"
        />
      </form-search-item>
    </template>
    <template v-else-if="radioType === '3'">
      <form-search-item>
        <el-date-picker
          v-model="form.datetimerange"
          type="daterange"
          style="width: 100%"
          format="YYYY-MM-DD"
          range-separator="-"
          start-placeholder="起始时间"
          end-placeholder="结束时间"
          :shortcuts="shortcuts"
        />
      </form-search-item>
      <form-search-item>
        <el-select v-model="form.startHourPeriods" placeholder="统计开始时段" style="width: 120px" @change="handleStartHourPeriodsChange">
          <el-option v-for="item in startHourPeriodsOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
        -
        <el-select v-model="form.endHourPeriods" placeholder="统计结束时段" style="width: 120px">
          <el-option
            v-for="item in endHourPeriodsOptions"
            :key="item.value"
            :disabled="item.value < form.startHourPeriods"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- <el-date-picker
          v-model="form.datetimerange"
          type="daterange"
          format="YYYY-MM-DD"
          range-separator="-"
          start-placeholder="统计开始时段"
          end-placeholder="统计结束时段"
          :shortcuts="shortcuts"
        /> -->
      </form-search-item>
    </template>
    <template v-else-if="radioType === '4'">
      <form-search-item :spanValue="12">
        <el-date-picker v-model="form.date" type="date" format="YYYY-MM-DD" />
      </form-search-item>
    </template>
    <template #button>
      <el-button type="success" @click="exportDataByDay()" :loading="dayDownLoading" v-if="radioType == '1'">导出</el-button>
      <el-button type="warning" @click="exportAll()" v-if="radioType == '1'" v-show="form.dateType != 'fiscalMonth'">汇总导出</el-button>
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>

  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="organization_ids"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="ParkingCarFlowSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import parkingCarFlowService from '@/service/statisticalReport/ParkingCarFlowService';
import { exportDataBySum } from '@/api/statisticalReport/ParkingCarFlowApi';
import commonService from '@/service/common/CommonService';
import { saveToFile } from '@/utils/utils.js';

import timeRange from '@/components/timeRange.vue';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';
import { reactive, ref, onMounted, watch } from 'vue';
import { ElMessage, dayjs } from 'element-plus';
import { useUser } from '@/stores/user';
import { useRouter } from 'vue-router';

const user = useUser();
const router = useRouter();
onMounted(() => {
  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  // if (user.role_id == 1) {
  //   return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const emits = defineEmits(['form-search']);
const props = defineProps({ radioType: String });
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const organization_ids = ref('');
const department_name = ref('');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: dayjs().format('YYYY-MM-DD'),
    end_time: dayjs().format('YYYY-MM-DD'),
    time_type: '3',
    hourPeriods: undefined
  },
  date: dayjs().format('YYYY-MM-DD'),
  dateRange: [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')],
  datetimerange: [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')],
  startHourPeriods: 0,
  endHourPeriods: 23,
  dateType: 'date'
});

const shortcuts = [
  {
    text: '昨天',
    value: () => {
      const end = dayjs(dayjs().format('YYYY-MM-DD 23:59:59')).subtract(1, 'day');
      const start = dayjs(dayjs().format('YYYY-MM-DD 00:00:00')).subtract(1, 'day');
      return [start, end];
    }
  },
  {
    text: '近一周',
    value: () => {
      const end = dayjs(dayjs().format('YYYY-MM-DD 23:59:59'));
      const start = dayjs(dayjs().format('YYYY-MM-DD 00:00:00')).subtract(7, 'day');
      return [start, end];
    }
  },
  {
    text: '近一月',
    value: () => {
      const end = dayjs(dayjs().format('YYYY-MM-DD 23:59:59'));
      const start = dayjs(dayjs().format('YYYY-MM-DD 00:00:00')).subtract(1, 'month');
      return [start, end];
    }
  }
];

const startHourPeriodsOptions = new Array(24).fill(0).map((item, index) => ({ label: `${index}:00`, value: index }));
const endHourPeriodsOptions = new Array(24).fill(0).map((item, index) => ({ label: `${index}:59`, value: index }));
const handleStartHourPeriodsChange = (value) => {
  if (value > form.endHourPeriods) {
    form.endHourPeriods = value;
  }
};
const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.department_name = undefined;
};

const exportDataByDay = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  // if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
  //   // 选择的时间最长只能是31天
  //   var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
  //   var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
  //   var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
  //   if (days + 1 > 31) {
  //     ElMessage({
  //       message: '查询日期最长只能选择31天！',
  //       type: 'warning'
  //     });
  //     return false;
  //   }
  // }
  parkingCarFlowService
    .exportData(form.queryParams)
    .then((response) => {
      if (response.success == true) {
        commonService.fileDownload(response.data).then((res) => {
          let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
          saveToFile(res.data, decodeURIComponent(fileName));
        });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    })
    .catch(() => {});
};
watch(
  () => props.radioType,
  () => {
    if (props.radioType !== '1') {
      handleDataSearch();
      // getData(data.params);
      // chart.value && chart.value.setOption(barChartOption.value);
    } else {
      // chart.value && chart.value.setOption(lineChartOption.value);
    }
  }
);
//汇总导出
const exportAll = async () => {
  try {
    const rudata = await exportDataBySum(form.queryParams);
    if (rudata.code == 200) {
      commonService.fileDownload(rudata.data).then((res) => {
        let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
        saveToFile(res.data, decodeURIComponent(fileName));
      });
    }
    console.log('汇总导出', rudata);
  } catch (error) {
    console.error('汇总导出失败', error);
  }

  console.log('汇总导出');
};
const disabledDateFn = (time) => {
  return time.getTime() > Date.now();
};

const handleDataSearch = () => {
  if (props.radioType !== '1') {
    if (props.radioType === '4') {
      const query = Object.assign(form, {});
      console.log('handleDataSearch', query);
      emits('form-search', { date: form.date });
      return;
    } else if (props.radioType === '2') {
      const query = Object.assign(form, {});
      console.log('handleDataSearch', query);
      emits('form-search', {
        dateRange: form.dateRange,
        datetimerange: form.datetimerange
      });
      return;
    } else if (props.radioType === '3') {
      const query = Object.assign(form, {});
      console.log('handleDataSearch', query);
      emits('form-search', {
        dateRange: form.dateRange,
        datetimerange: form.datetimerange,
        hourPeriods: Array.from({ length: form.endHourPeriods - form.startHourPeriods + 1 }, (item, index) => form.startHourPeriods + index)
      });
      return;
    }
  }
  if (undefined !== form.dateRange && null !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
    // 选择的时间最长只能是31天
    // var d = new Date(Date.parse(form.queryParams.start_time.replace(/-/g, '/')));
    // var d2 = new Date(Date.parse(form.queryParams.end_time.replace(/-/g, '/')));
    // var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    // if (days + 1 > 31) {
    //   ElMessage({
    //     message: '查询日期最长只能选择31天！',
    //     type: 'warning'
    //   });
    //   return false;
    // }
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }
  let type;
  if (props.radioType === '1') {
    switch (form.dateType) {
      case 'date':
        type = '3';
        break;
      case 'week':
        type = '5';
        break;
      case 'month':
        type = '2';
        break;
      case 'fiscalMonth':
        type = '6';
        break;
      case 'year':
        type = '1';
        break;
      default: {
        type = '4';
      }
    }
  }
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, { time_type: type });
    emits('form-search', query);
  } else {
    //判断组织架构是否选择
    if (
      typeof form.queryParams.organization_ids !== 'undefined' &&
      form.queryParams.organization_ids != null &&
      form.queryParams.organization_ids !== ''
    ) {
      const query = Object.assign(form.queryParams, { time_type: type });
      emits('form-search', query);
    } else {
      const query = Object.assign(form.queryParams, { time_type: type });
      emits('form-search', query);
      // ElMessage({
      //   message: '请选择停车场或组织进行统计',
      //   type: 'warning'
      // });
      // return false;
    }
  }
};
const handleAllReset = () => {
  if (props.radioType === '1') {
    form.dateRange = [dayjs().format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')];
    form.queryParams = {
      park_id: undefined,
      park_name: undefined,
      organization_ids: undefined,
      department_name: undefined,
      time_type: '3',
      start_time: dayjs().format('YYYY-MM-DD'),
      end_time: dayjs().format('YYYY-MM-DD')
    };
  } else if (props.radioType === '2') {
    form.datetimerange = [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')];
  } else if (props.radioType === '3') {
    form.datetimerange = [dayjs().format('YYYY-MM-DD 00:00:00'), dayjs().format('YYYY-MM-DD 23:59:59')];
    form.startHourPeriods = 0;
    form.endHourPeriods = 23;
  } else if (props.radioType === '4') {
    form.date = dayjs().format('YYYY-MM-DD');
  }
  form.dateType = 'date';
  emits('reset', form.queryParams);
  // handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
