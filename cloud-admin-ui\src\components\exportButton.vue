<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-19 14:12:39
 * @LastEditTime: 2024-03-20 17:16:09
 * @LastEditors: 达万安 段世煜
 * @Description: 导出
 * @FilePath: \cloud-admin-ui\src\components\exportButton.vue
-->
<template>
  <el-button type="success" @click="handleExport" :loading="downLoading">
    <slot>导出</slot>
  </el-button>
</template>

<script setup>
import { ElMessage } from 'element-plus';
import commonService from '@/service/common/CommonService';
import { saveToFile } from '@/utils/utils.js';
import { ref } from 'vue';

const props = defineProps({
  exportFunc: {
    type: Function,
    default: () => {}
  },
  params: {
    type: Object,
    default: () => {}
  },
  require: {
    type: String,
    default: ''
  }
});

const downLoading = ref(false);
const handleExport = async () => {
  if (props.require) {
    if (!props.params[props.require]) {
      ElMessage.warning('请选择导出必要条件');
      return false;
    }
  }
  if (!props.exportFunc) return;
  const { data, success, detail_message, message } = await props.exportFunc(props.params);
  if (success == true) {
    downLoading.value = true;
    commonService
      .fileDownload(data)
      .then((res) => {
        let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
        saveToFile(res.data, decodeURIComponent(fileName));
        downLoading.value = false;
      })
      .catch(() => {
        downLoading.value = false;
      });
  } else {
    ElMessage({
      message: detail_message != '' ? detail_message : message,
      type: 'error'
    });
    downLoading.value = false;
  }
};
</script>
