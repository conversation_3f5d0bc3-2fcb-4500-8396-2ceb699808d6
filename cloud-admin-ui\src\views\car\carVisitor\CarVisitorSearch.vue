<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" placeholder="请选择停车场" :readonly="true" @click="authCharge(true)"
    /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.visitor_name" placeholder="请输入访客姓名" clearable /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.visitor_plate_no" placeholder="请输入车牌号" clearable /></form-search-item>
    <form-search-item>
      <el-select v-model="form.queryParams.visitor_type" placeholder="访客类型" clearable>
        <el-option v-for="item in visitors" :key="item.value" :label="item.key" :value="item.value" />
      </el-select>
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.dateRange"
        type="datetimerange"
        style="width: 100%"
        range-separator="至"
        start-placeholder="到访开始时间"
        end-placeholder="到访结束时间"
        format="YYYY-MM-DD HH:mm:ss"
        value-format="YYYY-MM-DD HH:mm:ss"
        :default-time="[dayjs().startOf('day').toDate(), dayjs().endOf('day').toDate()]"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_name="park_name" :park_id="park_id" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
</template>

<script name="CarVisitorSearch" setup>
import { dayjs } from 'element-plus';
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import commonService from '@/service/common/CommonService';
import ParkFindBack from '@/views/car/ParkFindBack.vue';
import { reactive, ref, onActivated } from 'vue';

const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    visitor_name: '',
    visitor_plate_no: '',
    visitor_type: '',
    visit_start_time: '',
    visit_end_time: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});
const visitors = ref([]);

const relatedParkDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');

onActivated(() => {
  // 数据初始化
  initSelects();
});

const initSelects = () => {
  const param1 = [{ enum_key: 'EnumVisitorType', enum_value: 'EnumVisitorType' }];
  commonService.findEnums('park', param1).then((response) => {
    visitors.value = response.data.EnumVisitorType;
  });
};
const handleDataSearch = () => {
  if (form.dateRange?.length > 0) {
    form.queryParams.visit_start_time = form.dateRange[0];
    form.queryParams.visit_end_time = form.dateRange[1];
  } else {
    form.queryParams.visit_start_time = undefined;
    form.queryParams.visit_end_time = undefined;
  }
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: '',
    park_name: '',
    visitor_name: '',
    visitor_plate_no: '',
    visitor_type: '',
    visit_start_time: '',
    visit_end_time: '',
    page: 1,
    limit: 30
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  console.log(val[0].park_name);
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
