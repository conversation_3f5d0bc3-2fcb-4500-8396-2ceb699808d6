<template>
  <div class="container">
    <park-car-daily-payment-search @form-search="searchParkCarDailyPaymentList" @reset="resetParamsAndData" />
    <park-car-daily-payment-table ref="table" />
  </div>
</template>

<script name="ParkCarDailyPayment" setup>
import ParkCarDailyPaymentSearch from './parkCarDailyPayment/ParkCarDailyPaymentSearch.vue';
import ParkCarDailyPaymentTable from './parkCarDailyPayment/ParkCarDailyPaymentTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchParkCarDailyPaymentList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
