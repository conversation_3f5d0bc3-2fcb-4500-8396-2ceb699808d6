<template>
  <el-dialog title="一户多车查询" v-model="dialogVisible" :close-on-click-modal="false" width="650px">
    <FormSearch @search="getList" @reset="handleAllReset">
      <form-search-item>
        <el-input placeholder="请输入车牌号码" v-model="carno"></el-input>
      </form-search-item>
    </FormSearch>
    <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
      <el-tab-pane
        v-for="(item, i) in tabList"
        :key="i"
        :label="item.label + (item.num > 0 ? '(' + item.num + ')' : '')"
        :name="item.label"
      ></el-tab-pane>
    </el-tabs>
    <el-card class="table" shadow="never">
      <div v-if="activeName == '已在场车辆'" ref="table">
        <el-table :data="not_drive_outs" v-loading="loading" border>
          <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
          <!-- <el-table-column prop="car_in_biz_no" label="入场记录流水号" align="center" min-width="140" />
          <el-table-column prop="park_name" label="车场名" align="center" min-width="160" /> -->
          <el-table-column prop="plate_no" label="车牌号" align="center" />
          <el-table-column prop="park_region_name" label="区域名" align="center" />
          <!-- <el-table-column prop="gateway_name" label="通道名" align="center" min-width="140" /> -->
          <el-table-column prop="in_time" label="入场时间" align="center" />
          <!-- <el-table-column prop="car_type_desc" label="车辆类型" align="center" min-width="100" /> -->
          <!-- <el-table-column prop="car_color" label="颜色" align="center" min-width="100" /> -->
          <!-- <el-table-column label="入场图片" align="center" min-width="100">
            <template v-slot="scope">
              <el-image
                v-if="scope.row.car_photo_url !== '' && scope.row.car_photo_url !== null && scope.row.car_photo_url !== undefined"
                style="width: 100px; height: 100px"
                :src="scope.row.car_photo_url"
                :fit="fit"
                @click="showImage(scope.row.car_photo_url)"
              />
            </template>
          </el-table-column>
          <el-table-column prop="out_state_desc" label="出场状态" align="center" min-width="100" />
          <el-table-column prop="in_type_desc" label="入场类型" align="center" min-width="100" /> -->
        </el-table>
        <!-- <el-pagination
          background
          :current-page="data.queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        /> -->
      </div>
      <div v-if="activeName == '已注册车辆'" ref="table">
        <el-table :data="related_cars" v-loading="loading" border>
          <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
          <el-table-column prop="plate_no" label="车牌号" align="center" />
          <el-table-column prop="car_owner" label="车主" align="center" />
          <el-table-column prop="mobile" label="手机号" align="center" />
        </el-table>
        <!-- <el-pagination
          background
          :current-page="data.queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        /> -->
      </div>
      <div v-if="activeName == '已开通车位'" ref="table">
        <el-table :data="parking_spaces" v-loading="loading" border>
          <!-- <el-table-column type="selection" style="text-align: center" width="40" /> -->
          <el-table-column prop="park_name" label="车场名" align="center" />
          <el-table-column prop="region" label="区域名" align="center" />
          <el-table-column prop="valid_time" label="有效期" align="center" />
        </el-table>
        <!-- <el-pagination
          background
          :current-page="data.queryParams.page"
          :page-sizes="[10, 30, 50, 100]"
          :page-size="data.queryParams.limit"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          class="table-pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        /> -->
      </div>
    </el-card>
  </el-dialog>
</template>
<script name="onTocars" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import UnattendedApi from '@/service/system/Unattended';
import { useDuty } from '@/stores/duty';
import { ElMessage, ElTabPane } from 'element-plus';
import { onMounted, ref } from 'vue';
const duty = useDuty();
const activeName = ref('已在场车辆');
const tabList = ref([
  { label: '已在场车辆', num: '' },
  { label: '已注册车辆', num: '' },
  { label: '已开通车位', num: '' }
]);
const handleClick = (tab, event) => {
  console.log(tab, event);
};
const carno = ref('');
const dialogVisible = ref(false);
const loading = ref(false);
const handleAllReset = () => {
  carno.value = '';
};
onMounted(() => {
  // getList();
});
const parking_spaces = ref([]);
const related_cars = ref([]);
const not_drive_outs = ref([]);
const last_day_park_in = ref([]);
// 分页查询设备列表数据
const getList = (params) => {
  loading.value = true;
  UnattendedApi.getCarBindInfo({
    plate_no: carno.value,
    park_id: duty.callInfo.park_id
  }).then((response) => {
    if (response.success === true) {
      parking_spaces.value = response.data.parking_spaces;
      related_cars.value = response.data.related_cars;
      not_drive_outs.value = response.data.not_drive_outs;
      last_day_park_in.value = response.data.last_day_park_in;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

defineExpose({
  getList,
  carno,
  dialogVisible
});
</script>
<style lang="scss" scoped></style>
