<template>
  <div class="container" style="margin: 10px 0px">
    <device-search ref="searchRef" @form-search="searchDeviceList" /> <!-- @reset="resetParamsAndData" -->
    <device-table ref="table" />
  </div>
</template>

<script setup name="Device">
import DeviceSearch from './device/DeviceSearch.vue';
import DeviceTable from './device/DeviceTable.vue';
import { ref } from 'vue';

const searchRef = ref(null);
const table = ref(null);

const resetDataAndGetList = () => {
  searchRef.value.handleAllReset();
};

const searchDeviceList = (queryParams) => {
  table.value.getList(queryParams);
};
// const resetParamsAndData = () => {
//   params.park_id = park_id.value;
//   table.value.getList(params);
// };

defineExpose({
  resetDataAndGetList
});
</script>
