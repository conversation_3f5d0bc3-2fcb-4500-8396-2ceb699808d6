<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
    </form-search-item>
    <form-search-item>
      <time-range v-model:date="form.dateRange" :type="type" style="width: 100%" :showSelect="false" />
    </form-search-item>
    <template #button>
      <export-button :export-func="exportAll" :params="{ ...form.queryParams, time_type: type == 'date' ? '3' : '2' }"></export-button>
      <export-button type="warning" :export-func="exportHz" :params="{ ...form.queryParams, time_type: type == 'date' ? '3' : '2' }"
        >汇总导出</export-button
      >
    </template>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>
  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="organization_ids"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="ParkTurnoverSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';
import { reactive, ref, onMounted, computed } from 'vue';
import { dayjs } from 'element-plus';
import { useUser } from '@/stores/user';
import exportButton from '@/components/exportButton.vue';
import timeRange from '@/components/timeRange.vue';
import { exportAll, exportHz } from '@/api/statisticalReport/ParkTurnoverApi';

const props = defineProps({
  type: String
});
const defaultDateRange = computed(() => {
  let startDate = dayjs().format('YYYY-MM-DD');
  if (props.type === 'month') startDate = dayjs().startOf('month').format('YYYY-MM-DD');
  return [startDate, dayjs().format('YYYY-MM-DD')];
});
const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const organization_ids = ref('');
const department_name = ref('');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: undefined,
    end_time: undefined
  },
  dateRange: defaultDateRange.value
});

onMounted(() => {
  form.queryParams.dateRange = defaultDateRange.value;
  //判断是否是超级管理员
  const user = useUser();
  if (user.role_id == 1) {
    handleDataSearch();
    return false;
  }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
    handleDataSearch();
  }
});
const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.queryParams.department_name = undefined;
};
const handleDataSearch = () => {
  if (undefined !== form.dateRange && null !== form.dateRange && form.dateRange.length > 0) {
    form.queryParams.start_time = form.dateRange[0];
    form.queryParams.end_time = form.dateRange[1];
  }
  if (form.dateRange === null) {
    form.queryParams.start_time = undefined;
    form.queryParams.end_time = undefined;
  }

  const query = Object.assign(form.queryParams, {page:1,limit:30});
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams.dateRange = defaultDateRange.value;
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    organization_ids: undefined,
    department_name: undefined,
    start_time: undefined,
    end_time: undefined
  };
  emits('reset', form.queryParams);
  handleDataSearch();
};
//汇总导出
const exportAlldata = () => {
  console.log('汇总导出');
};
// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
