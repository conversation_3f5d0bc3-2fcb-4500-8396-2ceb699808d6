import $ from '@/utils/axios';

/**
 * 文档接口层
 */

/**
 * 查询文档分类树形
 * @returns {*}
 */
export const docTypeTree = () => {
  return $({
    url: '/console/document/docTypeTree',
    method: 'get'
  });
};
// 添加文档分类
export const createDocType = (data) => {
  return $({
    url: '/console/document/createDocType',
    method: 'post',
    data
  });
};
// 删除文档分类
export const deleteDocType = (id) => {
  return $({
    url: '/console/document/deleteDocType/' + id,
    method: 'post'
  });
};
/**
 * 分页查询文档(按照文档分类)
 * @returns {*}
 */
export const pagingDocument = (data) => {
  return $({
    url: '/console/document/pagingDocument',
    method: 'post',
    data
  });
};
// 添加文档
export const createDocument = (data) => {
  return $({
    url: '/console/document/createDocument',
    method: 'post',
    data
  });
};

// 修改文档
export const updateDocument = (data) => {
  return $({
    url: '/console/document/updateDocument',
    method: 'post',
    data
  });
};

// 删除文档
export const deleteDocument = (id) => {
  return $({
    url: '/console/document/deleteDocument/' + id,
    method: 'post'
  });
};

// 通过文档ID获取文档信息
export const getDocumentById = (id) => {
  return $({
    url: '/console/document/getDocumentById/' + id,
    method: 'post'
  });
};

// 获取文档分类名称
export const getDocTypeName = (data) => {
  return $({
    url: '/console/document/getDocTypeName',
    method: 'post',
    data
  });
};
