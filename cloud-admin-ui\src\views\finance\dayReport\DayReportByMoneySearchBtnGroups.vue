<template>
  <div>
    <div class="search-btn-group" v-loading="loading">
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ total_should_pay_money }}元</p>
        <span class="search-btn-group-total-label">应收</span>
      </div>
      &ensp;
      <div class="search-btn-group-total">
        <p class="search-btn-group-total-num">{{ total_payed_money }}元</p>
        <span class="search-btn-group-total-label">实收</span>
      </div>
      <div class="search-btn-group-con">
        <div class="search-btn-group-con-title" @click="toggleContainer(index)" style="transition: all 0.8s ease">
          <span>渠道</span>
          <span v-if="index == active ? false : true">
            <i style="padding-top: 2px" class="el-icon-arrow-right"></i>
          </span>
        </div>
        <el-scrollbar>
          <div class="search-btn-group-container" :class="index == active ? 'group-active' : ''">
            <div class="search-btn-group-type">
              <div v-for="(searchTypeItem, key) in data.searchBtnData" :key="key" class="search-btn-group-type-block">
                <div class="search-btn-group-type-content">
                  <span class="search-btn-group-type-num">{{ searchTypeItem.total_money }}元</span>
                  <br />
                  <span class="search-btn-group-type-label">{{ searchTypeItem.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script name="DayReportByMoneySearchBtnGroups" setup>
import dayReportService from '@/service/finance/DayReportService';
import { reactive, ref , onMounted} from 'vue';
import '@/styles/searchBtnGroup.scss';

const loading = ref(false);
const active = ref(0);
const total_should_pay_money = ref(0);
const total_payed_money = ref(0);
const data = reactive({
  queryParams: {
    park_name: undefined,
    park_id: undefined,
    page: 1,
    limit: 30
  },
  searchBtnData: []
});

onMounted(() => {
  // findDayReportByMoney(data.queryParams);
  toggleContainer();
});

const toggleContainer = (index) => {
  active.value = index;
};

const findDayReportByMoney = (queryParams) => {
  loading.value = true;
  data.queryParams = queryParams;
  dayReportService.searchBtnData(queryParams).then((response) => {
    loading.value = false;
    total_should_pay_money.value = response.total_should_pay_money;
    total_payed_money.value = response.total_payed_money;
    data.searchBtnData = response.vos;
  });
};

defineExpose({
  findDayReportByMoney,
  toggleContainer
});
</script>
<style lang="scss" scoped>
.search-btn-group-total {
  box-shadow: 0 0 2px 1px #eeeeee;
  border-bottom: 2px solid #409eff;
  // .search-btn-group-type {
  //   overflow-y: scroll;
  //   ::-webkit-scrollbar {
  //     width: 4px;
  //   }
  //   ::-webkit-scrollbar-thumb {
  //     border-radius: 2px;
  //     background-color: #afafaf;
  //   }
  // }
}
</style>
