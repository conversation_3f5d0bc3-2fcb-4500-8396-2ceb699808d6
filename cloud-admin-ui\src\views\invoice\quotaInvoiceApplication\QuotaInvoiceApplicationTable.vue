<template>
  <el-card class="table" shadow="never">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="handleCreate()">发票申请</el-button>
      </el-space>
      <el-space>
        <el-button type="default" @click="exportData()">导 出</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 281px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="200">
          <template v-slot="scope">
            <el-button link type="primary" v-if="scope.row.audit_state === 0 || scope.row.audit_state === 3" @click="handleAudit(scope.row.id)">
              提交审核
            </el-button>
            <el-button link type="primary" v-if="scope.row.audit_state !== 1 && scope.row.audit_state !== 2" @click="handleEdit(scope.row)">
              编辑
            </el-button>
            <el-button link type="danger" v-if="scope.row.audit_state !== 1 && scope.row.audit_state !== 2" @click="handleDelete(scope.row.id)">
              删除
            </el-button>
            <el-button link type="danger" v-if="scope.row.audit_state === 1" @click="cancelReview(scope.row.id)"> 撤回 </el-button>
            <el-button link type="primary" v-if="scope.row.audit_state === 2 && scope.row.draw_state === 0" @click="invoiceReceive(scope.row.id)">
              发票领取
            </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="停车场名称" align="center" />
        <el-table-column prop="invoice_title" label="开票公司" align="center" />
        <el-table-column prop="unit_money" label="单张金额" align="center" />
        <el-table-column prop="apply_count" label="申请数量" align="center" />
        <el-table-column prop="plan_draw_time" label="预计领取时间" align="center" />
        <el-table-column prop="audit_state_desc" label="审核状态" align="center" />
        <el-table-column prop="draw_state_desc" label="领取状态" align="center" />
        <el-table-column prop="draw_count" label="领取数量" align="center" />
        <el-table-column prop="draw_memo" label="发票详情" align="center" />
        <el-table-column prop="draw_time" label="领取时间" align="center" />
        <el-table-column prop="audit_operator_name" label="审核人" align="center" />
        <el-table-column prop="audit_time" label="审核时间" align="center" />
        <el-table-column prop="operator_name" label="申请人" align="center" />
      </el-table>
      <el-pagination
        background
        :current-page="data.queryParams.page"
        :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        class="table-pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
      <el-dialog title="发票申请" v-model="createDialogVisible" :close-on-click-modal="false" width="600px" @close="closeAddDialog(addForm)">
        <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
          <el-form-item prop="park_name" label="停车场名称">
            <el-input v-model="data.form.park_name" readonly="true" @click="authCharge(true, 'add')" placeholder="请选择车场" />
          </el-form-item>
          <el-form-item class="required" prop="invoice_title" label="开票公司">
            <span>{{ data.form.invoice_title }}</span>
          </el-form-item>
          <el-form-item prop="unit_money" label="单张金额">
            <el-input
              type="number"
              v-model="data.form.unit_money"
              oninput="if(value.length > 8) value=value.slice(0, 8)"
              placeholder="请输入单张金额"
            />
          </el-form-item>
          <el-form-item prop="apply_count" label="申请数量">
            <el-input
              type="number"
              v-model="data.form.apply_count"
              oninput="if(value.length > 8) value=value.slice(0, 8)"
              placeholder="请输入申请数量"
            />
          </el-form-item>
          <el-form-item prop="plan_draw_time" label="预计领取时间">
            <el-date-picker
              v-model="data.form.plan_draw_time"
              type="date"
              placeholder="预计领取时间"
              :size="size"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="createDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="createInvoice(addForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <el-dialog title="编辑发票抬头" v-model="updateDialogVisible" :close-on-click-modal="false" width="600px" @close="closeEditDialog(editForm)">
        <el-form ref="editForm" label-width="120px" :rules="data.rules" :model="data.updateForm">
          <el-form-item prop="park_name" label="停车场名称">
            <el-input v-model="data.updateForm.park_name" readonly="true" @click="authCharge(true, 'edit')" placeholder="请选择车场" />
          </el-form-item>
          <el-form-item class="required" prop="invoice_title" label="开票公司">
            <span>{{ data.updateForm.invoice_title }}</span>
          </el-form-item>
          <el-form-item prop="unit_money" label="单张金额">
            <el-input v-model="data.updateForm.unit_money" maxlength="50" placeholder="请输入单张金额" />
          </el-form-item>
          <el-form-item prop="apply_count" label="申请数量">
            <el-input v-model="data.updateForm.apply_count" maxlength="11" placeholder="请输入申请数量" />
          </el-form-item>
          <el-form-item prop="plan_draw_time" label="预计领取时间">
            <el-date-picker
              v-model="data.updateForm.plan_draw_time"
              type="date"
              placeholder="预计领取时间"
              :size="size"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="updateCancel(editForm)">取 消</el-button>
            <el-button type="primary" @click="updateInvoice(editForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
      <!-- 关联车场 -->
      <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible" :before-close="handleClose">
        <park-find-back
          :park_id="park_id"
          :park_name="park_name"
          @authCharge="authCharge(false, '')"
          :mode="flag"
          @renderTableInput="renderTableInput"
        />
      </el-dialog>
      <el-dialog title="发票领取" v-model="invoiceDialogVisible" :close-on-click-modal="false" width="600px" @close="closeInvoiceDialog(invoiceForm)">
        <el-form ref="invoiceForm" label-width="120px" :rules="data.invoiceRules" :model="data.invoiceForm">
          <el-form-item prop="draw_count" label="领取数量">
            <el-input v-model="data.invoiceForm.draw_count" maxlength="50" placeholder="请输入领取数量" />
          </el-form-item>
          <el-form-item prop="draw_memo" label="发票详情">
            <el-input v-model="data.invoiceForm.draw_memo" maxlength="11" placeholder="请输入发票详情" />
          </el-form-item>
        </el-form>
        <template #footer>
          <span class="dialog-footer">
            <el-button @click="invoiceDialogVisible = false">取 消</el-button>
            <el-button type="primary" @click="getInvoice(invoiceForm)">确 定</el-button>
          </span>
        </template>
      </el-dialog>
    </div>
  </el-card>
</template>
<script name="QuotaInvoiceApplicationTable" setup>
import { reactive, ref, onMounted } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import commonService from '@/service/common/CommonService';
import invoiceService from '@/service/invoice/InvoiceService';
import ParkFindBack from './ParkFindBack.vue';

const tableData = ref([]);
const loading = ref(false);
const status = ref(false);
const addForm = ref();
const editForm = ref();
const invoiceForm = ref();
const total = ref(0);
const invoiceDialogVisible = ref(false);
const createDialogVisible = ref(false);
const updateDialogVisible = ref(false);
const relatedParkDialogVisible = ref(false);
const statesList = ref([]);
const park_id = ref('');
const park_name = ref('');
const flag = ref('');
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  },
  orderParams: {
    id: undefined,
    page: 1,
    limit: 30
  },
  form: {
    park_id: undefined,
    invoice_title_id: undefined,
    invoice_title: undefined,
    unit_money: undefined,
    apply_count: undefined,
    plan_draw_time: undefined
  },
  updateForm: {},
  invoiceForm: {
    id: undefined,
    count: undefined,
    detail: undefined
  },
  rules: {
    park_name: [
      {
        required: true,
        message: '请选择停车场名称',
        trigger: 'blur'
      }
    ],
    unit_money: [
      {
        required: true,
        message: '请输入单张金额',
        trigger: 'blur'
      }
    ],
    apply_count: [
      {
        required: true,
        message: '请输入申请数量',
        trigger: 'blur'
      }
    ],
    plan_draw_time: [
      {
        required: true,
        message: '请选择预计领取时间',
        trigger: 'blur'
      }
    ]
  },
  invoiceRules: {
    draw_count: [
      {
        required: true,
        message: '请输入领取数量',
        trigger: 'blur'
      }
    ],
    draw_memo: [
      {
        required: true,
        message: '请输入发票详情',
        trigger: 'blur'
      }
    ]
  }
});

onMounted(() => {
  initSelects();
  // getList(data.queryParams);
  status.value = true;
});

const initSelects = () => {
  const param = [
    {
      enum_key: 'statesList',
      enum_value: 'EnumQuestionState'
    }
  ];
  commonService.findEnums('park', param).then((response) => {
    const list = response.data.statesList.filter((item) => item.value !== 0);
    statesList.value = list;
  });
};

// 分页查询电子发票列表数据
const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  invoiceService.pagingQuotaInvoices(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

// 获取开票公司名称
const getTitleByParkId = (id) => {
  invoiceService.getTitleByParkId(id).then((response) => {
    if (response.success === true) {
      data.form.invoice_title_id = response.data.id;
      data.form.invoice_title = response.data.company_name;
      data.updateForm.invoice_title_id = response.data.id;
      data.updateForm.invoice_title = response.data.company_name;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const renderTableInput = (val) => {
  data.form.park_name = val[0].park_name;
  data.form.park_id = val[0].park_id;
  getTitleByParkId(data.form.park_id);
};

// 选择车场
const authCharge = (visible, mode) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    if (mode == 'add') {
      park_id.value = data.form.park_id;
      park_name.value = data.form.park_name;
      flag.value = mode;
    } else if (mode == 'edit') {
      park_id.value = data.updateForm.park_id;
      park_name.value = data.updateForm.park_name;
      flag.value = mode;
    }
    relatedParkDialogVisible.value = true;
  }
};

// 发票申请
const createInvoice = (addForm) => {
  addForm.validate().then(() => {
    if (data.form.invoice_title_id === '' || data.form.invoice_title_id === undefined || data.updateForm.invoice_title_id === null) {
      ElMessage({
        message: '开票公司不能为空！',
        type: 'error'
      });
      return false;
    }
    invoiceService.createQuotaInvoiceApply(data.form).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
        addForm.resetFields();
        createDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

// 编辑发票抬头
const updateInvoice = (editForm) => {
  editForm.validate().then(() => {
    if (data.updateForm.invoice_title_id === '' || data.updateForm.invoice_title_id === undefined || data.updateForm.invoice_title_id === null) {
      ElMessage({
        message: '开票公司不能为空！',
        type: 'error'
      });
      return false;
    }
    invoiceService.updateQuotaInvoiceApply(data.updateForm).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
        updateDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

const handleAudit = (id) => {
  ElMessageBox.confirm('请确认是否提交审核？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    invoiceService
      .submitAuditQuotaInvoiceApply(id)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 发票领取
const invoiceReceive = (id) => {
  data.invoiceForm.id = id;
  invoiceDialogVisible.value = true;
};

const getInvoice = (invoiceForm) => {
  invoiceForm.validate().then(() => {
    invoiceService.drawQuotaInvoice(data.invoiceForm).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
        invoiceDialogVisible.value = false;
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
      }
    });
  });
};

// 发票申请
const handleCreate = () => {
  (data.form = {
    park_id: '',
    invoice_title_id: '',
    unit_money: undefined,
    apply_count: undefined,
    plan_draw_time: undefined
  }),
    (createDialogVisible.value = true);
  status.value = false;
};

// 编辑
const handleEdit = (val) => {
  data.updateForm = {
    id: val.id,
    park_id: val.park_id,
    park_name: val.park_name,
    invoice_title: val.invoice_title,
    invoice_title_id: val.invoice_title_id,
    unit_money: val.unit_money,
    apply_count: val.apply_count,
    plan_draw_time: val.plan_draw_time
  };
  updateDialogVisible.value = true;
};

// 删除
const handleDelete = (row) => {
  ElMessageBox.confirm('确定要删除吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    invoiceService
      .deleteQuotaInvoiceApply(row)
      .then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      })
      .catch(() => {
        getList(data.queryParams);
      });
  });
};

// 撤回
const cancelReview = (id) => {
  ElMessageBox.confirm('请确认是否撤回审核？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    invoiceService.revokeQuotaInvoiceApply(id).then((response) => {
      if (response.success === true) {
        ElMessage({
          message: response.message,
          type: 'success'
        });
        getList(data.queryParams);
      } else {
        ElMessage({
          message: response.message,
          type: 'error'
        });
      }
    });
  });
};

// 获取分页参数
const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
// 获取分页参数
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

// 导出
const exportData = () => {
  invoiceService.exportQuotaInvoices(data.queryParams).then((response) => {
    if (response.success == true) {
      ElMessage({
        message: response.message,
        type: 'success'
      });
      window.open(response.data, '_blank');
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
    }
  });
};

const closeAddDialog = (addForm) => {
  addForm.resetFields();
};

// 取消
const updateCancel = (editForm) => {
  editForm.resetFields();
  updateDialogVisible.value = false;
};
const closeEditDialog = (editForm) => {
  editForm.resetFields();
};

const closeInvoiceDialog = (invoiceForm) => {
  invoiceForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
