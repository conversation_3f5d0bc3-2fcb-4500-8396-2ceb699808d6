<template>
  <div class="container">
    <alarm-history-search @form-search="searchDeviceStatusList" @reset="resetParamsAndData" />
    <alarm-history-table ref="table" />
  </div>
</template>

<script name="AlarmHistory" setup>
import AlarmHistorySearch from './alarmHistory/AlarmHistorySearch.vue';
import AlarmHistoryTable from './alarmHistory/AlarmHistoryTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchDeviceStatusList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  table.value.getList(params);
};
</script>
