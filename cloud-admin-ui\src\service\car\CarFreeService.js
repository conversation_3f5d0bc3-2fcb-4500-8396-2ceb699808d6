import * as car<PERSON>ree<PERSON>pi from '@/api/car/CarFreeApi';

/**
 * 免费车
 */
export default {
  /**
   * 免费车分页数据
   */
  pagingFreeCar(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.pagingFreeCar(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 通过车场id查询车位信息查询车位编号
   */
  listParkSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.listParkSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 新增白名单
   */
  createWhiteList(data) {
    return carFreeApi.createWhiteList(data);
  },
  /**
   * 修改白名单
   */
  updateWhiteList(data) {
    return carFreeApi.updateWhiteList(data);
  },
  /**
   * 删除白名单
   */
  deleteWhiteList(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.deleteWhiteList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 批量删除白名单
   */
  batchDeleteWhiteList(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.batchDeleteWhiteList(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出白名单
   */
  exportWhiteLists(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.exportWhiteLists(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导入
   */
  importExcel(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.importExcel(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 提交审核
   */
  submitAuditWhiteListsApply(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.submitAuditWhiteListsApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 撤回审核
   */
  cancelAuditWhiteListsApply(data) {
    return new Promise((resolve, reject) => {
      try {
        carFreeApi.cancelAuditWhiteListsApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
