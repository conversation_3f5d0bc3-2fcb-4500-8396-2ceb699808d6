import * as longRentRuleApi from '@/api/park/LongRentRuleApi';

/**
 * 长租规则
 */
export default {
  /**
   * 分页查询
   */
  pagingRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.pagingRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新建长租规则
   */
  createRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.createRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 修改长租规则
   */
  updateRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.updateRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除长租规则
   */
  deleteRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.deleteRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 启用长租规则
   */
  enableRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.enableRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 禁用长租规则
   */
  disableRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.disableRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 查询当前车场下长租规则
   */
  listRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.listRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 查询车场，当前长租规则下的产品信息
   */
  listProduct(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.listProduct(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 提交审核
   */
  submitAuditRentRuleApply(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.submitAuditRentRuleApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 撤回
   */
  cancelAuditRentRuleApply(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.cancelAuditRentRuleApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租规则校验
   */
  checkRentRule(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.checkRentRule(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租规则名称校验
   */
  checkRepeatRentRuleName(data) {
    return new Promise((resolve, reject) => {
      try {
        longRentRuleApi.checkRepeatRentRuleName(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
