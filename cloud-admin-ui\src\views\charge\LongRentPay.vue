<template>
  <div class="container">
    <long-rent-pay-search @form-search="searchLongRentPayList" @reset="resetParamsAndData" />
    <long-rent-pay-table ref="table" />
  </div>
</template>

<script setup name="LongRentPay">
import LongRentPaySearch from './longRentPay/LongRentPaySearch.vue';
import LongRentPayTable from './longRentPay/LongRentPayTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchLongRentPayList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
