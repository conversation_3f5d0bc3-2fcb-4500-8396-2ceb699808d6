<template>
  <div class="box">
    <div class="conbox">
      <div class="conboxTop">
        <div class="conboxTopLeft">
          <div class="conboxTopLeftTop alltitle">车场服务管理权</div>
          <div class="conboxTopLeftBot">
            <div class="conTLB-item" @click="toMonitor(0)">
              <div class="item-l">
                <el-icon :size="40" color="#777777">
                  <Avatar />
                </el-icon>
              </div>
              <div class="item-r">
                <div>云客服管理</div>
                <div>
                  <div>{{ leftCount1 }}/{{ leftAllCount1 }}</div>
                </div>
              </div>
            </div>
            <div class="conTLB-item" @click="toMonitor(1)">
              <div class="item-l">
                <el-icon :size="40" color="#777777">
                  <SetUp />
                </el-icon>
              </div>
              <div class="item-r">
                <div>车场本地管理</div>
                <div>
                  <div>{{ leftCount2 }}/{{ leftAllCount2 }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="conboxTopCenter">
          <div class="conboxTopCenterTop alltitle">最近一次呼叫记录</div>
          <div class="conboxTopCenterBot">
            <div class="contcb-item">
              <div class="l">
                <div>呼叫时间:{{ lastCallrecford.created_at }}</div>
                <div>呼叫道口:{{ lastCallrecford.gateway_name }}</div>
              </div>
              <div class="r">
                <el-button type="primary" size="small" @click="toLast">查看</el-button>
              </div>
            </div>
          </div>
        </div>
        <div class="conboxTopRight">
          <div class="conboxTopRightTop">
            <div class="conboxTopRightTopTitle alltitle">
              待处理托管请求
              <div v-if="tableDate.length > 0" class="tip" :class="{ moreCount: tableDate.length > 99 }">
                {{ tableDate.length > 99 ? '99+' : tableDate.length }}
              </div>
            </div>
          </div>
          <div class="conboxTopRightBot">
            <div class="contrb-item">
              <el-table border :data="tableDate">
                <el-table-column label="时间" prop="apply_begin_time" align="center" width="200">
                  <template v-slot="{ row }">
                    <span>{{ row.hosting_type == 2 ? row.apply_begin_time : '长期托管' }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="车场名称" prop="park_name" align="center"></el-table-column>
                <el-table-column label="类型" prop="apply_type_desc" align="center"></el-table-column>
                <el-table-column label="操作">
                  <template v-slot="{ row }">
                    <div class="flexCenter">
                      <el-button link style="font-size: 14px" type="primary" @click="confirm(row)">同意</el-button>
                    </div>
                  </template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </div>
      </div>
      <div class="conboxBot">
        <div class="conboxBotTop">
          <div class="conboxBotTopTitle alltitle" @click="tabindex = 0">
            <span :style="{ color: tabindex == 0 ? '#40a9ff' : '' }">待处理异常事件</span>
            <div v-if="total0 > 0" class="tip" :class="{ moreCount: total0 > 99 }">{{ total0 > 99 ? '99+' : total0 }}</div>
            <div v-if="tabindex == 0" class="tabline"></div>
          </div>
          <div class="conboxBotTopTitle alltitle" @click="tabindex = 1">
            <span :style="{ color: tabindex == 1 ? '#40a9ff' : '' }">待处理设备报警</span>
            <div v-if="total1 > 0" class="tip" :class="{ moreCount: total1 > 99 }">{{ total1 > 99 ? '99+' : total1 }}</div>
            <div v-if="tabindex == 1" class="tabline"></div>
          </div>
        </div>
        <div v-show="tabindex == 0" class="conboxBotBot">
          <dclycsj @total="getTotal0"></dclycsj>
        </div>
        <div v-show="tabindex == 1" class="conboxBotBot">
          <dclsbbj @total="getTotal1"></dclsbbj>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import UnattendedApi from '@/service/system/Unattended';
import { useUser } from '@/stores/user';
import { activeRouteTab } from '@/utils/tabKit';
import { ElMessage } from 'element-plus';
import { onBeforeUnmount, onMounted, ref } from 'vue';
import dclsbbj from './components/dclsbbj.vue';
import dclycsj from './components/dclycsj.vue';
import { useDuty } from '@/stores/duty';
const user = useUser();
const duty = useDuty();

const tableDate = ref([]);
const count = ref(601);
const tabindex = ref(0);
const park_id = user.park_ids[0];
// 定时器引用
const pendingInterval = ref(null);
// 组件挂载时
onMounted(() => {
  // 初始化数据
  getStatics();
  getPending(); // 立即调用一次

  // 设置10秒定时刷新
  pendingInterval.value = setInterval(() => {
    getPending();
  }, 10000);

  // 获取最新呼叫记录
  getLastRecord();
});

// 组件卸载前
onBeforeUnmount(() => {
  // 清除定时器
  if (pendingInterval.value) {
    clearInterval(pendingInterval.value);
    pendingInterval.value = null;
  }
});
// 待接受托管列表
const getPending = () => {
  UnattendedApi.hostingapplyList().then((res) => {
    if (res.success) {
      tableDate.value = res.data;
    }
  });
};
// 待接受托管列表-同意
const confirm = (row) => {
  let api = 'hostingbegin';
  if (row.apply_type == 2) {
    api = 'hostingend';
  }
  UnattendedApi[api]({
    hosting_id: row.id,
    user_id: user.user_id
  }).then((res) => {
    if (res.success) {
      getPending();
      getStatics();
    }
  });
};

// 车场服务管理权
const leftCount1 = ref(0);
const leftAllCount1 = ref(0);
const leftCount2 = ref(0);
const leftAllCount2 = ref(0);
const getStatics = () => {
  UnattendedApi.statisticsparkId().then((res) => {
    if (res.success) {
      leftCount1.value = res.data.cloud_count;
      leftAllCount1.value = res.data.total_count;
      leftCount2.value = res.data.agent_count;
      leftAllCount2.value = res.data.total_count;
      duty.cloud_park_ids = res.data.cloud_park_ids
    }
  });
};

// 跳转监控中心
const toMonitor = (type) => {
  activeRouteTab({
    path: '/monitoringMan/viewMonitoring/viewMonitoring',
    query: {
      parkId: park_id,
      type: type
    }
  });
};

// 最新呼叫记录
const lastCallrecford = ref({
  created_at: '',
  gateway_id: '',
  gateway_name: '',
  id: ''
});
const getLastRecord = () => {
  UnattendedApi.recordgetResentRecord().then((res) => {
    if (res.success && res.data) {
      lastCallrecford.value = res.data;
    }
  });
};
const toLast = () => {
  if (!lastCallrecford.value.id || lastCallrecford.value.id == '') {
    ElMessage({
      message: '暂无记录',
      type: 'warning'
    });
    return;
  }
  activeRouteTab({
    path: '/monitoringMan/LastCall/LastCall',
    query: {
      detId: lastCallrecford.value.id
    }
  });
};
const total0 = ref(0);
const total1 = ref(0);
const getTotal0 = (v) => {
  total0.value = v;
};
const getTotal1 = (v) => {
  total1.value = v;
};
</script>

<style scoped lang="scss">
* {
  user-select: none;
}

.moreCount {
  right: -30px !important;
}

.flexCenter {
  height: 100%;
  width: 100%;
  text-align: center;
}

.box {
  height: 100%;
  width: 100%;
  padding: 5px;
}

.conbox {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
  background-color: #fff;
  border-radius: 15px;
  padding: 15px;

  .alltitle {
    font-size: 14px;
    font-weight: 700;
    color: #383838;
  }

  .conboxTop {
    width: 100%;
    height: 35%;
    display: flex;
    justify-content: space-between;
    gap: 10px;

    > div {
      flex: 1;
      display: flex;
      flex-direction: column;
      box-shadow: 0 0 10px rgb(0, 0, 0, 0.12);
      border-radius: 10px;
      padding: 10px 15px;
    }

    .conboxTopLeft {
      .conboxTopLeftTop {
      }

      .conboxTopLeftBot {
        flex: 1;
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 80px;

        .conTLB-item {
          display: flex;
          align-items: center;
          gap: 5px;
          cursor: pointer;
          .item-l {
            display: flex;
            align-items: center;
            justify-content: center;
          }

          .item-r {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            gap: 2px;

            div:nth-child(1) {
              font-size: 14px;
              font-weight: 700;
              color: #777777;
            }

            div:nth-child(2) {
              display: flex;
              align-items: center;
              justify-content: center;

              > div {
                width: fit-content;
                padding: 0px 5px;
                border-radius: 5px;
                background-color: #ebf1fc;
                font-size: 14px;
                color: #383838;
              }
            }
          }
        }
      }
    }

    .conboxTopCenter {
      border-left: 1px solid rgba(0, 0, 0, 0.12);
      border-right: 1px solid rgba(0, 0, 0, 0.12);

      .conboxTopCenterTop {
        display: flex;
        justify-content: center;
        align-content: center;
      }

      .conboxTopCenterBot {
        flex: 1;
        display: flex;
        align-items: center;

        .contcb-item {
          height: 60%;
          width: 100%;
          background-color: #fafafa;
          border-radius: 5px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          padding: 0 20px;

          .l {
            display: flex;
            flex-direction: column;
            gap: 15px;
            font-size: 14px;
            font-weight: 700;
            color: #777777;
          }

          .r {
            .el-button {
              font-size: 14px !important;
            }
          }
        }
      }
    }

    .conboxTopRight {
      .conboxTopRightTop {
        width: fit-content;
        height: fit-content;

        .conboxTopRightTopTitle {
          width: fit-content;
          height: fit-content;
          position: relative;

          .tip {
            position: absolute;
            top: -5px;
            right: -25px;
            padding: 0px 5px;
            border-radius: 10px;
            font-size: 14px;
            color: #fff;
            background-color: red;
          }
        }
      }

      .conboxTopRightBot {
        height: 95%;

        .contrb-item {
          height: 100%;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: center;

          .el-table {
            width: 100%;
            height: 90%;
            border-radius: 5px;
            font-size: 14px;
            color: #777777;

            :deep(.cell) {
              font-size: 14px;
            }
          }
        }
      }
    }
  }

  .conboxBot {
    height: 65%;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0 0 10px rgb(0, 0, 0, 0.12);
    border-radius: 10px;
    overflow: hidden;

    .conboxBotTop {
      padding: 10px 15px;
      display: flex;

      .conboxBotTopTitle {
        width: fit-content;
        height: fit-content;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-right: 80px;
        cursor: pointer;

        .tip {
          position: absolute;
          top: -5px;
          right: -25px;
          padding: 0px 5px;
          border-radius: 10px;
          font-size: 14px;
          color: #fff;
          background-color: red;
        }

        .tabline {
          width: 80px;
          height: 2px;
          background-color: #40a9ff;
        }
      }
    }

    .conboxBotBot {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      // align-items: center;
      overflow: hidden;
      justify-content: center;
      box-sizing: border-box;

      .el-table {
        width: 99%;
        height: 100%;
        border-radius: 5px;
        font-size: 14px;
        color: #777777;

        :deep(.cell) {
          font-size: 14px;
        }
      }

      .pagination {
        height: 15%;
        width: 100%;
        display: flex;
        justify-content: flex-end;
        align-items: center;
        padding-right: 20px;
      }
    }
  }
}
</style>
