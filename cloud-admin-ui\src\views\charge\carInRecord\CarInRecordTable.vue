<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="batchDelete()">批量删除</el-button>
      </el-space>
      <el-space>
        <DownloadButton btnType="default" :exportFunc="carInRecordService.exportCarInRecord"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
        </DownloadButton>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 384px)"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button link type="danger" @click="deleteRecord(scope.row.id)"> 删除记录 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_code" label="停车场编号" align="center" />
        <el-table-column prop="park_name" label="停车场名称" align="center" width="180" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="180" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入场通道" align="center" />
        <el-table-column prop="main_brand" label="车辆品牌" align="center" />
        <el-table-column prop="sub_brand" label="车辆型号" align="center" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
        <el-table-column prop="car_photo" label="入场图片" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="next_in_desc" label="次入记录" align="center" />
        <el-table-column prop="out_state_desc" label="是否出场" align="center" />
        <el-table-column prop="days" label="滞留天数" align="center" />
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <!-- 删除记录 -->
    <el-dialog v-if="deleteDialogVisible" width="500px" title="删除记录" v-model="deleteDialogVisible"
      @close="closeDialog(deleteForm)">
      <el-form ref="deleteForm" label-width="90px" :rules="data.rules" :model="data.form">
        <el-form-item prop="delete_reason" label="删除原因">
          <el-input v-model="data.form.delete_reason" type="textarea" placeholder="删除原因" :rows="3" />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteCarInRecord(deleteForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>
</template>

<script name="CarInRecordTable" setup>
import carInRecordService from '@/service/charge/CarInRecordService';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import DownloadButton from './DownloadButton.vue';

const tableData = ref([]);
const loading = ref(false);
const deleteForm = ref();
const total = ref(0);
const ids = ref([]);
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const deleteDialogVisible = ref(false);
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  form: {
    id: [],
    delete_reason: undefined
  },
  rules: {
    delete_reason: [
      {
        required: true,
        message: '请输入删除原因',
        trigger: 'blur'
      }
    ]
  }
});

// 查看入场图片
const checkInPicture = (row) => {
  if (row.car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '入场图片';
    dialogImageUrl.value = row.car_photo_url;
  }
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  const { park_name, ...newParams } = params;
  carInRecordService.pagingCarInRecord(newParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSelectionChange = (val) => {
  ids.value = val;
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const deleteRecord = (val) => {
  ids.value[0] = val;
  batchDelete();
};
const batchDelete = () => {
  if (ids.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的入场记录',
      type: 'warning'
    });
    return false;
  }
  deleteDialogVisible.value = true;
};

const deleteCarInRecord = (deleteForm) => {
  deleteForm
    .validate()
    .then(() => {
      const pushIds = [];
      for (let i = 0; i < ids.value.length; i++) {
        pushIds.push(ids.value[i].id);
      }
      data.form.id = pushIds;
      if (pushIds[0] === undefined) {
        pushIds[0] = ids.value[0];
        data.form.id = pushIds;
      }
      carInRecordService.deleteCarInRecord(data.form).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          deleteDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    })
    .catch(() => {
      getList(data.queryParams);
    });
};

const closeDialog = (deleteForm) => {
  deleteForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
