<template>
  <el-card class="table" shadow="never" style="margin-bottom: 10px">
    <div class="opers">
      <el-space>
        <el-button type="primary" @click="batchDelete()">批量删除</el-button>
      </el-space>
      <el-space>
        <DownloadButton btnType="default" :exportFunc="carInRecordService.exportCarInRecord"
          :rules="[{ name: 'park_id', required: true, message: '请选择停车场进行统计' }]" :params="data.queryParams">
        </DownloadButton>
      </el-space>
    </div>
    <div ref="table">

      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 384px)"
        @selection-change="handleSelectionChange">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="action" label="操作" align="center" width="100">
          <template v-slot="scope">
            <el-button v-if='scope.row.stay_del_audit_state == 1' link type="primary"
              @click="deleteRecord(scope.row.id, false)">
              撤回记录 </el-button>
            <el-button
              v-if='scope.row.stay_del_audit_state == 0 || scope.row.stay_del_audit_state == 3 || !scope.row.stay_del_audit_state'
              link type="danger" @click="deleteRecord(scope.row.id, true)"> 删除记录 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="park_code" label="停车场编号" align="center" />
        <el-table-column prop="park_name" label="停车场名称" align="center" width="180" />
        <el-table-column prop="park_region_name" label="子场名称" align="center" width="180" />
        <el-table-column prop="plate_no" label="车牌号" align="center" />
        <el-table-column prop="in_time" label="入场时间" align="center" />
        <el-table-column prop="gateway_name" label="入场通道" align="center" />
        <el-table-column prop="main_brand" label="车辆品牌" align="center" />
        <el-table-column prop="sub_brand" label="车辆型号" align="center" />
        <el-table-column prop="car_type_desc" label="车辆类型" align="center" />
        <el-table-column prop="car_photo" label="入场图片" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="checkInPicture(scope.row)"> 查看图片 </el-button>
          </template>
        </el-table-column>
        <el-table-column prop="next_in_desc" label="次入记录" align="center" />
        <el-table-column prop="out_state_desc" label="是否出场" align="center" />
        <el-table-column prop="days" label="滞留天数" align="center" />
        <el-table-column prop="in_type_desc" label="入场类型" align="center" />
        <el-table-column prop="days" label="审核状态" align="center">
          <template #default="scope">
            <span v-if='scope.row.stay_del_audit_state == 0'>待审核</span>
            <span v-if='scope.row.stay_del_audit_state == 1'>审核中</span>
            <span v-if='scope.row.stay_del_audit_state == 2'>已通过</span>
            <el-button v-if='scope.row.stay_del_audit_state == 3' link type="primary" @click="reject(scope.row)">
              已驳回 </el-button>
          </template>
        </el-table-column>
      </el-table>
      <el-pagination background :current-page="data.queryParams.page" :page-sizes="[10, 30, 50, 100]"
        :page-size="data.queryParams.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"
        class="table-pagination" @size-change="handleSizeChange" @current-change="handleCurrentChange" />
    </div>
    <!-- 删除记录 -->
    <el-dialog v-if="deleteDialogVisible" width="500px" :title="isDelType ? '删除记录' : '恢复记录'"
      v-model="deleteDialogVisible" @close="closeDialog(deleteForm)">
      <el-form ref="deleteForm" label-width="90px" :rules="data.rules" :model="data.form">
        <el-form-item v-if="isDelType" prop="del_reason" :label="isDelType ? '删除原因' : '恢复原因'">
          <el-input v-model="data.form.del_reason" type="textarea" :placeholder="isDelType ? '删除原因' : '恢复原因'"
            :rows="3" />
        </el-form-item>
        <el-form-item v-if="!isDelType" prop="recover_reason" :label="isDelType ? '删除原因' : '恢复原因'">
          <el-input v-model="data.form.recover_reason" type="textarea" :placeholder="isDelType ? '删除原因' : '恢复原因'"
            :rows="3" />
        </el-form-item>
        <el-form-item v-if="isDelType" prop="audit_url" label="审核材料">
          <el-upload ref="uploadRef" accept=".jpg, .jpeg, .png, .JPG, .JPEG, .PNG" v-model:file-list="fileList"
            :on-preview="onPreview" list-type="picture-card" :show-file-list="false" :limit="1" :action="uploadUrl"
            :headers="headers" :before-upload="beforeUpload" :on-success="onSuccessUpload" :on-exceed="handleExceed">
            <div v-if="!fileList.length" class="upload-card">
              <el-icon>
                <Plus />
              </el-icon>
            </div>
            <div v-else class="upload-img">
              <img :src="fileList?.[0].url" :alt="fileList?.[0].name" />
              <div class="upload-operations">
                <span @click.stop="onPreview(fileList?.[0])">
                  <el-icon><zoom-in /></el-icon>
                </span>
                <span @click.stop="handleRemove(fileList?.[0], fileList)">
                  <el-icon>
                    <Delete />
                  </el-icon>
                </span>
                <span>
                  <el-icon>
                    <Upload />
                  </el-icon>
                </span>
              </div>
            </div>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deleteDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="deleteCarInRecord(deleteForm)">确 定</el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 已驳回 -->
    <el-dialog v-if="rejectDialogVisible" width="500px" title="已驳回" v-model="rejectDialogVisible"
      @close="closeDialog(rejectForm)">
      <el-form ref="rejectForm" label-width="90px" :model="rejectform">
        <el-form-item label="驳回原因">
          <el-input v-model="rejectform.audit_comment" type="textarea" :rows="3" readOnly />
        </el-form-item>
        <el-form-item label="附件">
          <img :src="rejectform.del_apply_url" style="width: 200px;height: auto;cursor: pointer;"
            @click="checkOutPicture2(rejectform.del_apply_url)" alt="">
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="rejectDialogVisible = false">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </el-card>

  <el-dialog v-model="dialogVisible" :title="title" width="40%">
    <img w-full style="max-width: 100%; height: auto" :src="dialogImageUrl" alt="Preview Image" />
  </el-dialog>


</template>

<script name="CarInRecordTable" setup>
import carInRecordService from '@/service/charge/CarInRecordService';
import { getToken } from '@/utils/common';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';
import DownloadButton from './DownloadButton.vue';
const tableData = ref([]);
const loading = ref(false);
const deleteForm = ref();
const rejectForm = ref();
const total = ref(0);
const ids = ref([]);
const title = ref('');
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const deleteDialogVisible = ref(false);
const rejectDialogVisible = ref(false);
const isDelType = ref(true)
const data = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    page: 1,
    limit: 30
  },
  form: {
    park_ids: [],
    del_reason: undefined,
    recover_reason: undefined,
    audit_url: undefined
  },
  rules: {
    del_reason: [
      {
        required: true,
        message: '请输入删除原因',
        trigger: 'blur'
      }
    ],
    recover_reason: [
      {
        required: true,
        message: '请输入恢复原因',
        trigger: 'blur'
      }
    ],
    audit_url: [
      {
        required: true,
        message: '请上传审核材料',
        trigger: 'blur'
      }
    ],
  }
});
const rejectform = ref({})
const reject = (row) => {
  rejectform.value = row
  rejectDialogVisible.value = true
}
const headers = reactive({
  Authorization: getToken()
});
const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/rent/space/apply/uploadAuditData');
const fileList = ref([]);
// 查看出场图片
const checkOutPicture = (row) => {
  if (row.out_car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '出场图片';
    dialogImageUrl.value = row.out_car_photo_url;
  }
};
const checkOutPicture2 = (url) => {
  if (url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = url;
  }
};
const onPreview = (file) => {
  const fileUrl = file.url;
  const fileName = file.name;
  const fileExtension = fileName.split('.').pop().toLowerCase();
  if (fileExtension === 'pdf') {
    // 如果是 PDF 文件，则在新标签页中打开 URL 地址
    window.open(fileUrl, '_blank');
  } else {
    dialogVisible.value = true;
    title.value = '图片预览';
    dialogImageUrl.value = file.response?.data.audit_data_url || file.url;
  }
};
const beforeUpload = (file) => {
  const isLt25M = file.size / 1024 / 1024 < 25;
  if (!isLt25M) {
    this.$message.error('上传文件大小不能超过 25MB!');
  }
};
const onSuccessUpload = (response) => {
  if (response.success == true) {
    data.form.audit_url = response.data.audit_data;
    // data.form.audit_data_name = response.data.audit_data_name;
    // data.updateForm.audit_data = response.data.audit_data;
    // data.updateForm.audit_data_name = response.data.audit_data_name;
    ElMessage({
      message: response.message,
      type: 'success'
    });
  } else {
    ElMessage({
      message: response.message,
      type: 'error'
    });
  }
};
const uploadRef = ref(null)
const handleExceed = (files, fileList) => {
  uploadRef.value.clearFiles();
  uploadRef.value.handleStart(files[0]);
  uploadRef.value.submit();
};
const handleRemove = (file, fileList) => {
  const index = fileList.findIndex((item) => item.uid === file.uid);
  if (index !== -1) {
    fileList.splice(index, 1);
  }
};


// 查看入场图片
const checkInPicture = (row) => {
  if (row.car_photo_url === '') {
    ElMessage({
      message: '暂无图片可以查看',
      type: 'error'
    });
  } else {
    dialogVisible.value = true;
    title.value = '入场图片';
    dialogImageUrl.value = row.car_photo_url;
  }
};

const getList = (params) => {
  loading.value = true;
  params.page === undefined ? (params.page = 1) : (params.page = data.queryParams.page);
  params.limit === undefined ? (params.limit = 30) : (params.limit = data.queryParams.limit);
  data.queryParams = params;
  const { park_name, ...newParams } = params;
  carInRecordService.pagingCarInRecord(newParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = parseInt(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const handleSelectionChange = (val) => {
  ids.value = val;
};

const handleSizeChange = (val) => {
  data.queryParams.limit = val;
  getList(data.queryParams);
};
const handleCurrentChange = (val) => {
  data.queryParams.page = val;
  getList(data.queryParams);
};

const deleteRecord = (val, flag) => {
  isDelType.value = flag
  ids.value[0] = val;
  batchDelete();
};
const batchDelete = () => {
  if (ids.value.length === 0) {
    ElMessage({
      message: '请勾选要删除的入场记录',
      type: 'warning'
    });
    return false;
  }
  fileList.value = []
  data.form = {
    park_ids: [],
    del_reason: undefined,
    audit_url: undefined
  }
  deleteDialogVisible.value = true;
};

const deleteCarInRecord = (deleteForm) => {
  deleteForm
    .validate()
    .then(() => {
      const pushIds = [];
      for (let i = 0; i < ids.value.length; i++) {
        pushIds.push(ids.value[i].id);
      }
      data.form.park_ids = pushIds;
      data.form.in_record_ids = pushIds;
      if (pushIds[0] === undefined) {
        pushIds[0] = ids.value[0];
        data.form.park_ids = pushIds;
        data.form.in_record_ids = pushIds;
      }
      let api = 'submitAuditStayCarDelApply'
      if (!isDelType.value) { // 撤回
        api = 'recoverDelStayCar'
        data.form.park_id = data.queryParams.park_id
        delete data.form.park_ids
        delete data.form.del_reason
      } else {
        delete data.form.in_record_ids
        delete data.form.recover_reason
      }
      carInRecordService[api](data.form).then((response) => {
        if (response.success === true) {
          ElMessage({
            message: response.message,
            type: 'success'
          });
          getList(data.queryParams);
          deleteDialogVisible.value = false;
        } else {
          ElMessage({
            message: response.detail_message != '' ? response.detail_message : response.message,
            type: 'error'
          });
        }
      });
    })
    .catch(() => {
      getList(data.queryParams);
    });
};

const closeDialog = (deleteForm) => {
  deleteForm.resetFields();
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.upload-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  // height: 80px;
  // width: 148px;
  // border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  transition: border-color 0.3s ease;
  color: #999;
  font-size: 14px;

  .upload-state {
    font-size: 12px;
    text-align: center;
  }
}

.upload-img {
  width: 100%;
  height: 100%;
  text-align: center;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  .upload-operations {
    display: none;
    width: 100%;
    height: 100%;
    font-size: 20px;
    background-color: var(--el-overlay-color-lighter);
    color: #fff;
    cursor: default;
    justify-content: center;
    align-items: center;
    position: absolute;
    opacity: 0;
    top: 0;
    left: 0;
  }

  .upload-operations span {
    margin: 0 7px;
    cursor: pointer;
  }
}

:deep(.el-upload--picture-card) {
  position: relative;
}

:deep(.el-upload--picture-card:hover .upload-operations) {
  display: inline-flex;
  opacity: 1;
  transition: opacity var(--el-transition-duration);
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  margin: 0 20px 20px 0;
}
</style>