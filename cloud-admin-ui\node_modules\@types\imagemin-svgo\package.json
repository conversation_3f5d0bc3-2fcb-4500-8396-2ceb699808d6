{"name": "@types/imagemin-svgo", "version": "10.0.5", "description": "TypeScript definitions for imagemin-svgo", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/imagemin-svgo", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "romain-faust", "url": "https://github.com/romain-faust"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/imagemin-svgo"}, "scripts": {}, "dependencies": {"@types/imagemin": "*", "@types/svgo": "2"}, "typesPublisherContentHash": "b34a646c00bb57faca940c4a9d723ef36333b08c754d38b21c176edefff672a2", "typeScriptVersion": "4.5"}