/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查找电子发票记录
export const pagingInvoice = (data) => {
  return $({
    url: '/console/invoice/pagingInvoiceRecord',
    method: 'post',
    data
  });
};

// 导出电子发票申领
export const exportInvoices = (data) => {
  return $({
    url: '/console/invoice/exportInvoices',
    method: 'post',
    data
  });
};
// 发送电子邮件
export const sendingEInvoiceMail = (id) => {
  return $({
    url: '/console/invoice/sendingEInvoiceMail/' + id,
    method: 'post'
  });
};
// 手动查询发票结果
export const queryEInvoice = (id) => {
  return $({
    url: '/console/invoice/queryEInvoice/' + id,
    method: 'post'
  });
};
// 红冲-作废发票
export const cancelInvoice = (id) => {
  return $({
    url: '/console/invoice/cancelInvoice/' + id,
    method: 'post'
  });
};
// 保存发票
export const saveFile = (data) => {
  return $({
    url: '/console/invoice/saveFile',
    method: 'post',
    data
  });
};

// 分页查询定额发票申领
export const pagingQuotaInvoices = (data) => {
  return $({
    url: '/console/quotaInvoice/pagingQuotaInvoices',
    method: 'post',
    data
  });
};

// 新增定额发票申请
export const createQuotaInvoiceApply = (data) => {
  return $({
    url: '/console/quotaInvoice/createQuotaInvoiceApply',
    method: 'post',
    data
  });
};

// 修改定额发票申请
export const updateQuotaInvoiceApply = (data) => {
  return $({
    url: '/console/quotaInvoice/updateQuotaInvoiceApply',
    method: 'post',
    data
  });
};

// 删除定额发票申请
export const deleteQuotaInvoiceApply = (id) => {
  return $({
    url: '/console/quotaInvoice/deleteQuotaInvoiceApply/' + id,
    method: 'post'
  });
};

// 导出定额发票申领
export const exportQuotaInvoices = (data) => {
  return $({
    url: '/console/quotaInvoice/exportQuotaInvoices',
    method: 'post',
    data
  });
};

// 领取定额发票
export const drawQuotaInvoice = (data) => {
  return $({
    url: '/console/quotaInvoice/drawQuotaInvoice',
    method: 'post',
    data
  });
};

// 提交审核定额发票申请
export const submitAuditQuotaInvoiceApply = (id) => {
  return $({
    url: '/console/quotaInvoice/submitAuditQuotaInvoiceApply/' + id,
    method: 'post'
  });
};

// 撤回定额发票申请
export const revokeQuotaInvoiceApply = (id) => {
  return $({
    url: '/console/quotaInvoice/revokeQuotaInvoiceApply/' + id,
    method: 'post'
  });
};

// 通过车场ID获取开票公司信息
export const getTitleByParkId = (id) => {
  return $({
    url: '/console/quotaInvoice/getTitleByParkId/' + id,
    method: 'post'
  });
};

// 关联临停费用订单的分页数据
export const relativeParkOrderPaging = (data) => {
  return $({
    url: '/console/invoice/relativeParkOrderPaging',
    method: 'post',
    data
  });
};

// 关联预约车位订单的分页数据
export const relativeReserveOrderPaging = (data) => {
  return $({
    url: '/console/invoice/relativeReserveOrderPaging',
    method: 'post',
    data
  });
};

// 关联长租费用订单的分页数据
export const relativeRentOrderPaging = (data) => {
  return $({
    url: '/console/invoice/relativeRentOrderPaging',
    method: 'post',
    data
  });
};
