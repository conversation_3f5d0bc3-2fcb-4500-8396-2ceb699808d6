<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-28 15:34:16
 * @LastEditors: 达万安 段世煜
 * @Description: 停车统计
 * @FilePath: \cloud-admin-ui\src\views\home\single\notice.vue
-->
<template>
  <warp-card height="27%" title="全国公告栏">
    <template v-if="noticeData.length > 0">
      <div class="notice-container">
        <div class="notice-item" v-for="notice in noticeData" :key="notice.content">
          <div class="dot" />
          <div class="title">{{ notice.title }}</div>
          <div class="hot" v-if="notice.hot">热</div>
        </div>
      </div>
    </template>
    <div>暂无公告</div>
  </warp-card>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import warpCard from './components/warpCard.vue';

const noticeData = ref([]);

const fetchData = () => {
  noticeData.value = [
    // {
    //   title: '2024年(中国国庆)假期结算通知',
    //   content: '2024年(中国国庆)假期结算通知',
    //   hot: false
    // },
    // {
    //   title: '[系统升级]移动端查询车辆支付记录',
    //   content: '[系统升级]移动端查询车辆支付记录',
    //   hot: false
    // },
    // {
    //   title: '[新功能]移动值守免费试用30天，降本增效',
    //   content: '[新功能]移动值守免费试用30天，降本增效',
    //   hot: true
    // },
    // {
    //   title: '2024[春节]假期结算通知',
    //   content: '2024[春节]假期结算通知',
    //   hot: false
    // }
  ];
};

onMounted(() => {
  fetchData();
});
</script>

<style scoped lang="scss">
.notice-container {
  width: 100%;
  height: 100%;
  .notice-item {
    display: flex;
    align-items: center;
    height: 35px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: pointer;
    .dot {
      width: 8px;
      height: 8px;
      background-color: #3b8fff;
      margin-right: 6px;
    }
    .title {
      font-size: 16px;
      color: #2d2d2d;
    }
    .hot {
      width: 18px;
      height: 18px;
      line-height: 18px;
      text-align: center;
      background-color: #ff6600;
      border-radius: 2px;
      font-size: 12px;
      color: #fff;
      margin-left: 5px;
    }
  }
}
</style>
