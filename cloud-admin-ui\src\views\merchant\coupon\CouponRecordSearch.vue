<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly @click="authCharge(true, '')" placeholder="请选择停车场"
    /></form-search-item>
    <form-search-item> <el-input v-model="form.queryParams.merchant_name" placeholder="商户名称" /></form-search-item>
    <!-- 车场查找带回 -->
    <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
      <park-find-back @authCharge="authCharge(false, '')" mode="search" @renderTableInput="renderTableInput" />
    </el-dialog>
  </FormSearch>
</template>

<script name="CouponMetaSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ParkFindBack from '../ParkFindBack.vue';
import { reactive, ref } from 'vue';

const parkInfoDialogVisible = ref(false);
const emits = defineEmits(['form-search']);
const form = reactive({
  queryParams: {
    park_id: '',
    park_name: '',
    merchant_name: '',
    page: 1,
    limit: 30
  },
  dateRange: []
});

const handleDataSearch = () => {
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
const handleAllReset = () => {
  form.queryParams = {
    park_id: '',
    merchant_name: '',
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  emits('reset', form.queryParams);
  handleDataSearch();
};
//车场查找带回;
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
</script>
<style lang="scss" scoped></style>
