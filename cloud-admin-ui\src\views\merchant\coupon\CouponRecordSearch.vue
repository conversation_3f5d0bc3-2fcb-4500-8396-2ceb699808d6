<template>
  <FormSearch canFold @search="handleDataSearch" @reset="handleAllReset">
    <!-- 选择停车场 -->
    <form-search-item>
      <el-input v-model="form.queryParams.park_name" readonly @click="authCharge(true, '')" placeholder="请选择停车场"
    /></form-search-item>
    <!-- 组织架构 -->
    <form-search-item>
      <ClearableChargeInput v-model="form.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构"
    /></form-search-item>
    <!-- 日期选择器 -->
    <form-search-item>
      <el-date-picker
        style="width: 100%;"
        v-model="form.datavlue"
        type="daterange"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        @clear="timeclear"
      />
    </form-search-item>
    <form-search-item v-if="type">
    <el-input v-model="form.queryParams.merchant_name" placeholder="商家名称"></el-input>
    </form-search-item>
    <!-- 车场查找带回 -->
    <el-dialog v-if="parkInfoDialogVisible" width="80%" title="选择停车场" v-model="parkInfoDialogVisible" :before-close="handleClose">
      <park-find-back @authCharge="authCharge(false, '')" mode="search" @renderTableInput="renderTableInput" />
    </el-dialog>
  </FormSearch>

  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <OrgFindBack
      :organization_id="organization_ids"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="CouponMetaSearch" setup>
import FormSearch from '@/components/FormSearch.vue'; //form组件
import FormSearchItem from '@/components/FormSearchItem.vue'; //formitem组件
import ClearableChargeInput from '@/components/ClearableChargeInput.vue'; //组织架构
import OrgFindBack from './OrgFindBack.vue'; //组织架构组件
import ParkFindBack from '../ParkFindBack.vue'; //停车场组件
import { reactive, ref, watch } from 'vue'; //引入vue
const props = defineProps(['isreset',"type"]); //接收父组件传值
const organization_ids = ref();
const department_name = ref();
const parkInfoDialogVisible = ref(false); //控制停车场查找带回弹窗
const relatedOrgDialogVisible = ref(false); //控制组织架构查找带回弹窗
const emits = defineEmits(['form-search']); //引入父组件方法
const form = reactive({
  //发送的数据
  queryParams: {
    park_id: '',
    park_name: '',
    organization_ids: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30,
    merchant_name:''
  },
  dateRange: [],
  department_name: '',
  datavlue: ''
});
//监听navbar切换，切换则重置数据
watch(
  () => props.isreset,
  () => {
    handleAllReset();
  }
);
//时间格式化${y}-${m}-${d}形式
const gettime = (time) => {
  const date = new Date(time);
  const y = date.getFullYear();
  const m = String(date.getMonth() + 1).padStart(2, '0');
  const d = String(date.getDate()).padStart(2, '0');
  return `${y}-${m}-${d}`;
};
//点击"搜索"按钮
const handleDataSearch = () => {
  //判断日期选择器是否有值
  if (form.datavlue) {
    //把选择的如期格式化存储
    form.queryParams.start_time = gettime(form.datavlue[0]);
    form.queryParams.end_time = gettime(form.datavlue[1]);
  }
  //不知道为什么要这样
  const query = Object.assign(form.queryParams, { page: 1, limit: 30 });
  emits('form-search', query);
};
//点击事件“清除”按钮
const timeclear = () => {
  form.queryParams.start_time = '';
  form.queryParams.end_time = '';
};
//当navbar改变进行提示父组件 获取响应table的数据
const handleAllReset = () => {
  form.queryParams = {
    park_id: '',
    park_name: '',
    organization_ids: '',
    start_time: '',
    end_time: '',
    page: 1,
    limit: 30
  };
  form.dateRange = [];
  form.datavlue = '';
  form.department_name = '';
  emits('reset', form.queryParams);
  // handleDataSearch();
};
//车场查找带回;
const handleClose = () => {
  parkInfoDialogVisible.value = false;
};
const authCharge = (visible, mode) => {
  if (visible === false) {
    parkInfoDialogVisible.value = false;
  } else {
    parkInfoDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};
//选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_ids.value = form.queryParams.organization_ids;
    department_name.value = form.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const clearDepartment = () => {
  form.queryParams.organization_ids = undefined;
  form.department_name = undefined;
};
//搜集组织架数据
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_ids = arrId.toString();
  form.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
