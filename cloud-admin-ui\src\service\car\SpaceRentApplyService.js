import * as spaceRentApply from '@/api/car/SpaceRentApplyApi';

/**
 * 长租车
 */
export default {
  /**
   * 分页查询
   */
  pagingRentSpaceApplies(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.pagingRentSpaceApplies(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 新增长租车申请
   */
  createRentSpaceApply(data) {
    return spaceRentApply.createRentSpaceApply(data);
  },
  /**
   * 长租车申请续期前置判断
   */
  whetherOrNotRenew(rentApplyId, parkId) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.whetherOrNotRenew(rentApplyId, parkId).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 长租车申请续期
   */
  webRenewRentSpace(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.webRenewRentSpace(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 提交长租车申请续期申请
   */
  submitAuditRenewRentSpaceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.submitAuditRenewRentSpaceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 撤回长租车申请续期申请
   */
  revokeAuditRenewRentSpaceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.revokeAuditRenewRentSpaceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 修改长租车申请
   */
  updateRentSpaceApply(data) {
    return spaceRentApply.updateRentSpaceApply(data);
  },
  /**
   * 获取用户当前套餐
   */
  getNewRentProductDetails(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.getNewRentProductDetails(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 删除长租车申请
   */
  deleteRentSpaceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.deleteRentSpaceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 编辑车主信息
   */
  updateRentSpaceMember(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.updateRentSpaceMember(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 编辑有效期
   */
  updateRentSpaceValidityTime(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.updateRentSpaceValidityTime(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 提交审核长租车申请
   */
  submitAuditRentSpaceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.submitAuditRentSpaceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 撤回长租车申请
   */
  revokeAuditRentSpaceApply(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.revokeAuditRentSpaceApply(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 导出长租车申请
   */
  exportRentSpaceApplies(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.exportRentSpaceApplies(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 驳回长租车申请
   */
  rejectRentSpaceApplies(data) {
    return new Promise((resolve, reject) => {
      try {
        spaceRentApply.rejectRentSpaceApplies(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
