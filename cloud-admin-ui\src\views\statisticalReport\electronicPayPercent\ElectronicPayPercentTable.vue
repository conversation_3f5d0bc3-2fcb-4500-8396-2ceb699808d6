<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <el-button type="default" @click="exportDataByDay()" :loading="dayDownLoading">按日导出</el-button>
        <el-button type="default" @click="exportDataGather()" :loading="summaryDownLoading">汇总导出</el-button>
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 240px)">
        <el-table-column type="selection" style="text-align: center" width="40" />
        <el-table-column prop="statistics_date" label="日期" align="center" />
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" width="100" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <el-table-column prop="parking_balance" label="临停余额" align="center" />
        <el-table-column prop="rent_balance" label="长租余额" align="center" />
        <el-table-column prop="receivable_money" label="应收金额" align="center" />
        <el-table-column prop="receivable_number" label="应收笔数" align="center" />
        <el-table-column prop="electronic_payment_money" label="电子支付金额" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_money" label="电子支付金额（含优免)" align="center" />
        <el-table-column prop="electronic_payment_number" label="电子支付笔数" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_number" label="电子支付笔数（含优免）" align="center" />
        <el-table-column prop="electronic_payment_money_proportion" label="电子支付金额占比" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_money_proportion" label="电子支付金额占比（含优免）" align="center" />
        <el-table-column prop="electronic_payment_number_proportion" label="电子支付笔数占比" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_number_proportion" label="电子支付笔数占比（含优免）" align="center" />
      </el-table>
    </div>
  </el-card>
</template>

<script name="ElectronicPayPercentTable" setup>
import commonService from '@/service/common/CommonService';
import electronicPayPercentService from '@/service/statisticalReport/ElectronicPayPercentService';
import { saveToFile } from '@/utils/utils.js';
import { ElMessage } from 'element-plus';
import { reactive, ref } from 'vue';

const tableData = ref([]);
const loading = ref(false);
const data = reactive({
  queryParams: {}
});

// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;

  data.queryParams = params;
  electronicPayPercentService.pagingElectronicPay(params).then((response) => {
    if (response.success === true) {
      tableData.value = response.data;
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};

const dayDownLoading = ref(false);
const summaryDownLoading = ref(false);
// 按日导出
const exportDataByDay = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
    // 选择的时间最长只能是31天
    var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
    var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
    var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    if (days + 1 > 31) {
      ElMessage({
        message: '查询日期最长只能选择31天！',
        type: 'warning'
      });
      return false;
    }
  }
  electronicPayPercentService
    .exportDataByDay(data.queryParams)
    .then((response) => {
      if (response.success == true) {
        dayDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            dayDownLoading.value = false;
          })
          .catch(() => {
            dayDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        dayDownLoading.value = false;
      }
    })
    .catch(() => {
      dayDownLoading.value = false;
    });
};

// 汇总导出
const exportDataGather = () => {
  // if (typeof data.queryParams.park_id == 'undefined' || data.queryParams.park_id == null || data.queryParams.park_id == '') {
  //   ElMessage({
  //     message: '请选择停车场进行统计',
  //     type: 'warning'
  //   });
  //   return false;
  // }
  if (
    data.queryParams.start_time === undefined ||
    data.queryParams.start_time === '' ||
    data.queryParams.end_time === undefined ||
    data.queryParams.end_time === ''
  ) {
    ElMessage({
      message: '请选择统计日期！',
      type: 'warning'
    });
    return false;
  }
  if (data.queryParams.start_time !== undefined && data.queryParams.start_time !== '' && data.queryParams.start_time !== null) {
    // 选择的时间最长只能是31天
    var d = new Date(Date.parse(data.queryParams.start_time.replace(/-/g, '/')));
    var d2 = new Date(Date.parse(data.queryParams.end_time.replace(/-/g, '/')));
    var days = parseInt(d2.getTime() - d.getTime()) / (1000 * 60 * 60 * 24);
    if (days + 1 > 31) {
      ElMessage({
        message: '查询日期最长只能选择31天！',
        type: 'warning'
      });
      return false;
    }
  }
  electronicPayPercentService
    .exportDataGather(data.queryParams)
    .then((response) => {
      if (response.success == true) {
        summaryDownLoading.value = true;
        commonService
          .fileDownload(response.data)
          .then((res) => {
            let fileName = res.headers['content-disposition'].split(';')[2].split('filename=')[1].replace(/\"/g, '');
            saveToFile(res.data, decodeURIComponent(fileName));
            summaryDownLoading.value = false;
          })
          .catch(() => {
            summaryDownLoading.value = false;
          });
      } else {
        ElMessage({
          message: response.detail_message != '' ? response.detail_message : response.message,
          type: 'error'
        });
        summaryDownLoading.value = false;
      }
    })
    .catch(() => {
      summaryDownLoading.value = false;
    });
};

defineExpose({
  getList
});
</script>
<style lang="scss" scoped></style>
