<template>
  <el-card style="margin-top: 10px" shadow="never">
    <div class="opers">
      <el-space>
        <div></div>
      </el-space>
      <el-space>
        <!-- <div class="uodataClass">
          <el-tooltip>
            <template #content> 目前多数统计报表基本做到了定时任务按时点整点更新数据，<br />加此便于用户识别当前最新数据更新时间点 </template>
            <el-icon><QuestionFilled style="cursor: pointer" /></el-icon>
          </el-tooltip>
          <div>数据最近更新：{{ newdata ? newdata : '暂无数据' }}</div>
        </div> -->
      </el-space>
    </div>
    <div ref="table">
      <el-table :data="tableData" v-loading="loading" border style="height: calc(100vh - 280px)">
        <el-table-column prop="statistics_date" label="统计日期" align="center" />
        <el-table-column label="时间周期" align="center" min-width="130">
          <template #default="scope">
            <span>{{ getShowTime(scope.row) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="park_name" label="车场名称" align="center" width="180" />
        <el-table-column prop="park_id" label="车场ID" align="center" width="100" />
        <el-table-column prop="region_name" label="大区" align="center" />
        <el-table-column prop="organizational_structure" label="城市公司" align="center" />
        <el-table-column prop="province_name" label="所在省份" align="center" />
        <el-table-column prop="city_name" label="所在城市" align="center" />
        <el-table-column prop="district_name" label="所在区域" align="center" />
        <el-table-column prop="parking_balance" label="临停余额" align="center" />
        <el-table-column prop="rent_balance" label="长租余额" align="center" />
        <el-table-column prop="receivable_money" label="应收金额" align="center" />
        <el-table-column prop="receivable_number" label="应收笔数" align="center" />
        <el-table-column prop="electronic_payment_money" label="电子支付金额" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_money" label="电子支付金额（含优免)" align="center" />
        <el-table-column prop="electronic_payment_number" label="电子支付笔数" align="center" />
        <el-table-column prop="electronic_payment_add_coupon_number" label="电子支付笔数（含优免）" align="center" />
        <el-table-column prop="electronic_payment_money_proportion" label="电子支付金额占比" align="center">
          <template #="{ row }"> {{ multiply(row.electronic_payment_money_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>
        <el-table-column prop="electronic_payment_add_coupon_money_proportion" label="电子支付金额占比（含优免）" align="center">
          <template #="{ row }"> {{ multiply(row.electronic_payment_add_coupon_money_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>
        <el-table-column prop="electronic_payment_number_proportion" label="电子支付笔数占比" align="center">
          <template #="{ row }"> {{ multiply(row.electronic_payment_number_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>
        <el-table-column prop="electronic_payment_add_coupon_number_proportion" label="电子支付笔数占比（含优免）" align="center">
          <template #="{ row }"> {{ multiply(row.electronic_payment_add_coupon_number_proportion, 100).toFixed(2) }}% </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <el-pagination
          v-model:current-page="data.queryParams.page"
          v-model:page-size="data.queryParams.limit"
          :page-sizes="[30, 100, 200, 300, 400]"
          :background="true"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </el-card>
</template>

<script name="ElectronicPayPercentTable" setup>
import electronicPayPercentService from '@/service/statisticalReport/ElectronicPayPercentService';
import { getNewUpdateTmie } from '@/api/statisticalReport/common.js';
import { ElMessage } from 'element-plus';
import { reactive, ref, onMounted } from 'vue';
import { multiply } from '@/utils/computeData';
const tableData = ref([]);
const loading = ref(false);
const total = ref(0);
const data = reactive({
  queryParams: {
    page: 1,
    limit: 30
  }
});
const newdata = ref();
onMounted(() => {
  // getNewUpdateTmieData();
});
// 分页
const handleSizeChange = (value) => {
  data.queryParams.limit = value;
  getList({});
};
const handleCurrentChange = (value) => {
  data.queryParams.page = value;
  getList({});
};
// 获取最新更新时间
const getNewUpdateTmieData = async () => {
  try {
    const rudata = await getNewUpdateTmie(4);
    if (rudata.code == 200) {
      newdata.value = rudata.data.last_job_time;
      console.log(rudata, 'rudata');
    }
  } catch (error) {
    console.log('获取最新更新时间失败', error);
  }
};
// onMounted(() => {
//   getList(data.queryParams);
// });

const getList = (params) => {
  loading.value = true;

  data.queryParams = { ...data.queryParams, ...params };
  electronicPayPercentService.pagingElectronicPay(data.queryParams).then((response) => {
    if (response.success === true) {
      tableData.value = response.data.rows;
      total.value = Number(response.data.total);
      loading.value = false;
    } else {
      ElMessage({
        message: response.detail_message != '' ? response.detail_message : response.message,
        type: 'error'
      });
      loading.value = false;
    }
  });
};
//根据年月日返回相应显示的时间周期
const getShowTime = (row) => {
  const week = ['日', '一', '二', '三', '四', '五', '六'];
  switch (data.queryParams.time_type) {
    case '1':
      break;
    case '2':
    case '6':
      return row.statistics_date.split('-')[1] + '月';
    case '3':
      return '星期' + week[new Date(row.statistics_date).getDay()];
    case '4':
      break;
    case '5':
      return row.statistics_date.split('-')[1] + '周';
    default:
      break;
  }
};
defineExpose({
  getList
});
</script>
<style lang="scss" scoped>
.uodataClass {
  // height: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20px;
  color: rgba(0, 0, 0, 0.5);
  font-size: 12px;
}
.pagination {
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
