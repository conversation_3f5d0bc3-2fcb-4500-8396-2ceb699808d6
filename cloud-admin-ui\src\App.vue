<template>
  <el-config-provider :locale="locale">
    <div id="app">
      <RouterView />
    </div>
  </el-config-provider>
</template>

<script setup>
import { RouterView } from 'vue-router';
import zhCn from 'element-plus/dist/locale/zh-cn.mjs';

const locale = zhCn;
</script>
<style lang="scss" scoped>
@import 'assets/font/font.css';
:deep(.el-table) {
  .el-scrollbar__bar {
        display: block !important;
        opacity: 1 !important;
    &.is-horizontal {
      height: 8px !important;
      transition: all 0.2s;
      .el-scrollbar__thumb {
        opacity:0.35 !important;
        height: 100% !important;
        background-color: #000 !important;
       
      }
    }
    
    &.is-vertical {
      width: 8px !important;
      transition: all 0.2s;
      .el-scrollbar__thumb {
        opacity:0.35 !important;
        width: 100% !important;
        background-color: #000 !important;
      }
    }
    &:hover {
      &.is-vertical{
        width: 10px !important;
      }
      &.is-horizontal{
      transition: all 0.2s;
        height: 10px !important;
      }
      .el-scrollbar__thumb{
          opacity: 0.6 !important;
    }
    }
  }}
</style>
