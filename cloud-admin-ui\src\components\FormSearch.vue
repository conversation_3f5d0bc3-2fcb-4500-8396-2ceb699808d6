<template>
  <el-card shadow="never">
    <el-row :gutter="10">
      <slot :span="span"></slot>
      <el-col :span="span" style="margin-top: 10px">
        <el-space>
          <el-button type="primary" @click="search">查 询</el-button>
          <el-button @click="reset">重 置</el-button>
          <slot name="button"></slot>
          <el-button @click="toggle" v-show="show">
            {{ foldState ? '展 开' : '收 起' }}&ensp;
            <el-icon>
              <Icon
                icon="ArrowDown"
                :style="{
                  transition: 'all 0.3s ease 0s',
                  transform: 'rotateZ(' + rotate + 'turn)',
                  transformOrigin: '50% 50%'
                }"
              />
            </el-icon>
          </el-button>
        </el-space>
      </el-col>
      <div style="margin-top: 10px; flex: 1; display: flex; justify-content: right; margin-right: 5px">
        <slot name="back"></slot>
      </div>
    </el-row>
  </el-card>
</template>

<script>
import { Icon } from '@/utils/Icon.js';
import { $on } from 'vue-happy-bus';

export default {
  name: 'FormSearch',
  componentName: 'FormSearch',
  emits: ['search', 'reset'],
  components: {
    Icon
  },
  props: {
    canFold: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      span: 6,
      show: true,
      foldState: true,
      items: [],
      rotate: 0
    };
  },
  computed: {
    maxShow() {
      return (24 / this.span) * 2 - 1;
    }
  },
  created() {
    const that = this;

    $on('addItem', (item) => {
      if (that.canFold && that.items.length > this.maxShow) {
        item.hide();
      } else {
        item.show();
      }
      that.items.push(item);
    });
  },
  mounted() {
    if (this.canFold && this.items.length > this.maxShow) {
      this.show = true;
    } else {
      this.show = false;
    }
  },
  methods: {
    search() {
      this.$emit('search');
    },
    reset() {
      this.$emit('reset');
    },
    toggle() {
      this.rotate = this.foldState ? 0.5 : 0;

      if (this.foldState) {
        this.unfold();
      } else {
        this.fold();
      }
      this.foldState = !this.foldState;
    },
    fold() {
      for (const i in this.items) {
        if (i > this.maxShow) {
          this.items[i].hide();
        }
      }
    },
    unfold() {
      for (const i in this.items) {
        if (i > this.maxShow) {
          this.items[i].show();
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding-top: 0px;
}
</style>
