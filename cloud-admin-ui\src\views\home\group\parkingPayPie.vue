<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-03-15 18:12:36
 * @LastEditors: 达万安 段世煜
 * @Description: 车场支付占比 图表
 * @FilePath: \cloud-admin-ui\src\views\home\group\parkingPayPie.vue
-->
<template>
  <warp-card size="mini" title="支付渠道统计">
    <div class="container">
      <div class="chart">
        <div>笔数（笔）</div>
        <pie-chart ref="circleChartRef" :startAngle="250" circle :color="circleColor" :radius="['35%', '45%']" :center="['50%', '31%']" />
      </div>
      <div class="chart">
        <div>支付金额（元）</div>
        <pie-chart ref="pieChartRef" :startAngle="250" :color="pieColor" radius="45%" :center="['50%', '31%']" :border="2" />
      </div>
    </div>
  </warp-card>
</template>

<script setup>
import { reactive, ref } from 'vue';
import pieChart from './components/pieChart.vue';
import warpCard from './components/warpCard.vue';

import { fetchIncomeRatio } from '@/api/home/<USER>';

// 图标颜色数组
const circleColor = ['#1890FF', '#00DBF2', '#6DFACD', '#6F6BEC'];
// const pieColor = ['#1890FF', '#FF917C', '#F9F871'];
const pieColor = ['#1890FF', '#00DBF2', '#6DFACD', '#6F6BEC'];
// 图表Ref实体
const circleChartRef = ref(null);
const pieChartRef = ref(null);
// 总体支付分类数据
const allTypeData = reactive([
  { name: '电子支付', value: 0 },
  { name: '第三方会员系统', value: 0 },
  { name: '现金', value: 0 },
  { name: 'ETC', value: 0 }
]);
// 电子支付分类数据
const eleTypeData = reactive([
  { name: '电子支付', value: 0 },
  { name: '第三方会员系统', value: 0 },
  { name: '现金', value: 0 },
  { name: 'ETC', value: 0 }
]);
/**
 * @description 获取数据
 */
const fetchData = async (val) => {
  try {
    const { data } = await fetchIncomeRatio(val);
    allTypeData[0].value = data.electronic_payed_num || 0;
    allTypeData[1].value = data.mall_coo_payed_num || 0;
    allTypeData[2].value = data.cash_payed_num || 0;
    allTypeData[3].value = data.etc_payed_num || 0;

    eleTypeData[0].value = data.electronic_payed_money || 0;
    eleTypeData[1].value = data.mall_coo_payed_money || 0;
    eleTypeData[2].value = data.cash_payed_money || 0;
    eleTypeData[3].value = data.etc_payed_money || 0;
  } finally {
    circleChartRef.value.setData(
      allTypeData.map((item) => {
        return {
          ...item,
          label: {
            show: item.value > 0
          },
          labelLine: {
            show: item.value > 0
          }
        };
      })
    );
    pieChartRef.value.setData(
      eleTypeData.map((item) => {
        return {
          ...item,
          label: {
            show: item.value > 0
          },
          labelLine: {
            show: item.value > 0
          }
        };
      })
    );
  }
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped>
.container {
  width: 100%;
  height: 100%;
  display: flex;
  .chart {
    &:first-child {
      border-right: 1px solid #0b73ca;
    }
    > div {
      color: #90c2ff;
      font-size: 15px;
      margin-top: -10px;
      text-indent: 6px;
      margin-bottom: -10px;
    }
    width: 50%;
    height: 100%;
  }
}
</style>
