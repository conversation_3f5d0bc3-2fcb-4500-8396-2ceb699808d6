<template>
  <el-container class="page-container">
    <el-header height="52px">
      <frame-header />
    </el-header>
    <el-container>
      <Transition>
        <el-aside width="200px" v-show="menuVisable">
          <frame-menu />
        </el-aside>
      </Transition>
      <el-main>
        <frame-tab v-show="tabVisable" />
        <el-main>
          <frame-content />
        </el-main>
      </el-main>
    </el-container>
  </el-container>
</template>

<script name="Home" setup>
import { useUser } from '@/stores/user';
import { onMounted, computed, watch } from 'vue';
import router from '@/router/index';
import FrameHeader from './frame/FrameHeader.vue';
import FrameMenu from './frame/FrameMenu.vue';
import FrameTab from './frame/FrameTab.vue';
import FrameContent from './frame/FrameContent.vue';
import { useMenu } from '@/stores/menu';
import { useRoute } from 'vue-router';

const menu = useMenu();
const menuVisable = computed(() => {
  return menu.state.menuVisable;
});
const route = useRoute();
const tabVisable = computed(() => {
  return route.meta.cached;
});
const frameMainHeight = computed(() => {
  return tabVisable.value ? 'calc(100vh - 52px - 42px)' : 'calc(100vh - 52px)';
});
onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  console.info('停车管理系统');
});
</script>

<style lang="scss" scoped>
:deep(.frame-content) {
  height: v-bind(frameMainHeight);
}
.page-container {
  height: 100vh;
  background-color: #f4f7fd;
}

.el-header {
  padding: 0px;
}

.el-main {
  padding: 0;
}

.v-enter-active,
.v-leave-active {
  transition: all 0.2s ease;
}
.v-enter-to,
.v-enter-to {
  opacity: 1;
  width: 200px;
}
.v-enter-from,
.v-leave-to {
  width: 0;
  opacity: 0;
}
</style>
