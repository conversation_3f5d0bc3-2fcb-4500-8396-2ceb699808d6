var P=Object.defineProperty,p=Object.getOwnPropertySymbols,S=Object.prototype.hasOwnProperty,R=Object.prototype.propertyIsEnumerable,b=(e,r,t)=>r in e?P(e,r,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[r]=t,_=(e,r)=>{for(var t in r||(r={}))S.call(r,t)&&b(e,t,r[t]);if(p)for(var t of p(r))R.call(r,t)&&b(e,t,r[t]);return e};function z(e){return typeof e=="object"&&e!==null}function E(e){return e}function h(e,r){return e=z(e)?e:Object.create(null),new Proxy(e,{get(t,l,s){var n;return l==="key"?((n=r.key)!=null?n:E)(Reflect.get(t,l,s)):Reflect.get(t,l,s)||Reflect.get(r,l,s)}})}function O(e){return e!==null&&typeof e=="object"}function d(e,r){const t=Array.isArray(e)&&Array.isArray(r),l=O(e)&&O(r);if(!t&&!l)throw new Error("Can only merge object with object or array with array");const s=t?[]:{};return[...Object.keys(e),...Object.keys(r)].forEach(o=>{Array.isArray(e[o])&&Array.isArray(r[o])?s[o]=[...Object.values(d(e[o],r[o]))]:r[o]!==null&&typeof r[o]=="object"&&typeof e[o]=="object"?s[o]=d(e[o],r[o]):e[o]!==void 0&&r[o]===void 0?s[o]=e[o]:e[o]===void 0&&r[o]!==void 0&&(s[o]=r[o])}),s}function j(e,r){return r.reduce((t,l)=>l==="[]"&&Array.isArray(t)?t:t==null?void 0:t[l],e)}function m(e,r,t){const l=r.slice(0,-1).reduce((s,n)=>/^(__proto__)$/.test(n)?{}:s[n]=s[n]||{},e);if(Array.isArray(l[r[r.length-1]])&&Array.isArray(t)){const s=l[r[r.length-1]].map((n,o)=>Array.isArray(n)&&typeof n!="object"?[...n,...t[o]]:typeof n=="object"&&n!==null&&Object.keys(n).some(i=>Array.isArray(n[i]))?d(n,t[o]):_(_({},n),t[o]));l[r[r.length-1]]=s}else r[r.length-1]===void 0&&Array.isArray(l)&&Array.isArray(t)?l.push(...t):l[r[r.length-1]]=t;return e}function w(e,r){return r.reduce((t,l)=>{const s=l.split(".");if(!s.includes("[]"))return m(t,s,j(e,s));const n=s.indexOf("[]"),o=s.slice(0,n),i=s.slice(0,n+1),c=s.slice(n+1),u=j(e,i),f=[];for(const a of u)c.length!==0&&(Array.isArray(a)||typeof a=="object")?f.push(w(a,[c.join(".")])):f.push(a);return m(t,o,f)},Array.isArray(e)?[]:{})}function v(e,r,t,l,s){try{const n=r==null?void 0:r.getItem(l);n&&e.$patch(t==null?void 0:t.deserialize(n))}catch(n){s&&console.error(n)}}function I(e={}){return r=>{const{options:{persist:t},store:l}=r;if(!t)return;const s=(Array.isArray(t)?t.map(n=>h(n,e)):[h(t,e)]).map(({storage:n=localStorage,beforeRestore:o=null,afterRestore:i=null,serializer:c={serialize:JSON.stringify,deserialize:JSON.parse},key:u=l.$id,paths:f=null,debug:a=!1})=>({storage:n,beforeRestore:o,afterRestore:i,serializer:c,key:u,paths:f,debug:a}));s.forEach(n=>{const{storage:o,serializer:i,key:c,paths:u,beforeRestore:f,afterRestore:a,debug:y}=n;f==null||f(r),v(l,o,i,c,y),a==null||a(r),l.$subscribe(($,g)=>{try{const A=Array.isArray(u)?w(g,u):g;o.setItem(c,i.serialize(A))}catch(A){y&&console.error(A)}},{detached:!0})}),l.$hydrate=({runHooks:n=!0}={})=>{s.forEach(o=>{const{beforeRestore:i,afterRestore:c,storage:u,serializer:f,key:a,debug:y}=o;n&&(i==null||i(r)),v(l,u,f,a,y),n&&(c==null||c(r))})}}}var N=I();export{N as s};
