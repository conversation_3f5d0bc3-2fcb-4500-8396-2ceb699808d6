/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 疫情防控表格数据查询
export const pagingPreventLists = (data) => {
  return $({
    url: '/console/park/prevent/list/pagingPreventLists',
    method: 'post',
    data
  });
};

// 新增疫情防控
export const createPreventList = (data) => {
  return $({
    url: '/console/park/prevent/list/createPreventList',
    method: 'post',
    data
  });
};

// 修改疫情防控
export const updatePreventList = (data) => {
  return $({
    url: '/console/park/prevent/list/updatePreventList',
    method: 'post',
    data
  });
};

// 删除疫情防控
export const deletePreventList = (id) => {
  return $({
    url: '/console/park/prevent/list/deletePreventList/' + id,
    method: 'post'
  });
};

//导出疫情防控
export const exportPreventLists = (data) => {
  return $({
    url: '/console/park/prevent/list/exportPreventLists',
    method: 'post',
    data
  });
};
