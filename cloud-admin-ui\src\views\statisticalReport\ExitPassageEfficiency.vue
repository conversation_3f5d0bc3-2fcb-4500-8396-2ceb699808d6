<template>
  <div class="container">
    <exit-passage-efficiency-search @form-search="searchExitPassageEfficiency" @reset="resetParamsAndData" />
    <exit-passage-efficiency-table ref="table" />
  </div>
</template>

<script name="ExitPassageEfficiency" setup>
import ExitPassageEfficiencySearch from './exitPassageEfficiency/ExitPassageEfficiencySearch.vue';
import ExitPassageEfficiencyTable from './exitPassageEfficiency/ExitPassageEfficiencyTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const searchExitPassageEfficiency = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};
</script>
