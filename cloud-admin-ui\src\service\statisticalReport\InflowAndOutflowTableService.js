import * as inflowAndOutflowApi from '@/api/statisticalReport/InflowAndOutflowApi';

/**
 * 进出流量
 */
export default {
  /**
   * 分页查询进出流量
   */
  pagingInflowAndOutflow(data) {
    return new Promise((resolve, reject) => {
      try {
        inflowAndOutflowApi.pagingInflowAndOutflow(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        inflowAndOutflowApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
