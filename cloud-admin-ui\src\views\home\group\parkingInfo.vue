<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-08 14:51:46
 * @LastEditTime: 2024-06-24 11:13:12
 * @LastEditors: 达万安 段世煜
 * @Description: 车场信息总览
 * @FilePath: \cloud-admin-ui\src\views\home\group\parkingInfo.vue
-->
<template>
  <warp-card title="车场信息总览" style="width: 850px">
    <div class="card-warp">
      <info-card v-for="item in data" :key="item.type" :data="item" />
    </div>
  </warp-card>
</template>

<script setup>
import { reactive } from 'vue';

import { fetchParkingInfo } from '@/api/home/<USER>';

import warpCard from './components/warpCard.vue';
import infoCard from './components/infoCard.vue';

const data = reactive([
  {
    label: '车场数量',
    value: 0,
    trend: '',
    trendValue: '0%',
    type: 'parking-num'
  },
  {
    label: '通道数量',
    value: 0,
    trend: '',
    trendValue: '0%',
    type: 'exit-num'
  },
  {
    label: '车位总数量',
    value: 0,
    trend: '',
    trendValue: '0%',
    type: 'lot-num'
  },
  {
    label: '在场长租车数量',
    value: 0,
    trend: '',
    trendValue: '0%',
    type: 'rent-num'
  },
  {
    label: '在场临时车数量',
    value: 0,
    trend: '',
    trendValue: '0%',
    type: 'temporary-num'
  }
]);

const fetchData = async (val) => {
  const { data: resData } = await fetchParkingInfo(val);
  data[0].value = resData.park_cnt || 0;
  data[1].value = resData.gateway_cnt || 0;
  data[2].value = resData.space_cnt || 0;
  data[3].value = resData.rent_plate_cnt || 0;
  data[4].value = resData.parking_plate_cnt || 0;
};

defineExpose({
  fetchData
});
</script>

<style lang="scss" scoped>
.card-warp {
  display: flex;
  justify-content: space-around;
}
</style>
