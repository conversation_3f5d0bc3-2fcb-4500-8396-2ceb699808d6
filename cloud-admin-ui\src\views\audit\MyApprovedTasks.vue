<template>
  <div class="container">
    <iframe style="width: 100%;height: 86vh;" :src="src" frameborder="0"></iframe>
    <!-- <biz-audit-search @form-search="searchBizAudit" @reset="resetParamsAndData" @batch="handleBatch" />
    <biz-audit-table ref="table" />   -->
  </div>
</template>

<script setup name="BizAudit">
import { getOpenUrl, GetIamTokenOpenFlag } from '@/utils/iamFlow';
import { ref, reactive, onMounted } from 'vue';
const src = ref("")
onMounted(() => {
  if (!GetIamTokenOpenFlag()) {
    return
  }
  src.value = getOpenUrl(`hdwaCommonBpm/hdwaCommonBpm/export/MyApprovedTasks`,true)
})

</script>
