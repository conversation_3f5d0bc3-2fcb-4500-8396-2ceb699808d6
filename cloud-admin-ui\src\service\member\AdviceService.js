import * as adviceApi from '@/api/member/AdviceApi';

/**
 * 投诉建议
 */
export default {
  /**
   * 分页查询会员投诉建议
   */
  pagingMemberComplaint(param) {
    return new Promise((resolve, reject) => {
      try {
        adviceApi.pagingMemberComplaint(param).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },

  /**
   * 处理投诉建议
   */
  handleComplaint(data) {
    return new Promise((resolve, reject) => {
      try {
        adviceApi.handleComplaint(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
