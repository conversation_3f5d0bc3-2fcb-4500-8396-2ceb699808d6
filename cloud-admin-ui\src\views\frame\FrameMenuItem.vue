<template>
  <a-sub-menu :key="menu.id + '_' + menu.path" popupClassName="frame-menu-popup">
    <template #icon>
      <el-icon size="14">
        <component :is="menu.icon"></component>
      </el-icon>
    </template>
    <template #title>
      <span class="menu-item-text">{{ menu.title }}</span>
    </template>
    <template v-for="item in menu.children" :key="item.id + '_' + item.path">
      <template v-if="!item.children">
        <a-menu-item
          :index="item.id + '_' + item.path"
          :key="item.id + '_' + item.path"
          @click="activeRouteTab({ path: item.path, query: {} })"
          v-if="item.visible === 1"
        >
          <template #icon>
            <el-icon size="14">
              <component :is="item.icon"></component>
            </el-icon>
          </template>
          <span class="menu-item-text">{{ item.title }}</span>
        </a-menu-item>
      </template>
      <template v-else>
        <FrameMenuItem :menu="item" />
      </template>
    </template>
  </a-sub-menu>
</template>

<script setup>
import FrameMenuItem from './FrameMenuItem.vue';
import { activeRouteTab } from '@/utils/tabKit';

const props = defineProps({
  menu: {
    type: Object,
    required: true
  }
});
</script>

<style lang="scss">
.frame-menu-popup {
  .ant-menu-sub {
    border-radius: 4px !important;
    background-color: #005bac !important;
    color: #fff !important;
  }
  .ant-menu-item-active {
    color: #005bac;
    background-color: #fff;
  }
}
</style>
