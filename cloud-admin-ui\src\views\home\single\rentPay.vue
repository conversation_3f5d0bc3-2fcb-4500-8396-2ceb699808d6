<!--
 * @Author: 惠达万安 段世煜
 * @Date: 2024-03-14 09:13:03
 * @LastEditTime: 2024-06-25 14:39:04
 * @LastEditors: 达万安 段世煜
 * @Description: 长租交易数据
 * @FilePath: \cloud-admin-ui\src\views\home\single\rentPay.vue
-->
<template>
  <warp-card height="23.5%" title="长租交易数据">
    <el-table :data="[rentData]" border style="width: 100%">
      <el-table-column prop="cash" label="本月现金收费 (元)" align="center"></el-table-column>
      <el-table-column prop="electron" label="本月电子支付 (元)" align="center"></el-table-column>
      <el-table-column prop="back" label="本月长租退费 (元)" align="center"></el-table-column>
      <el-table-column prop="toApplay" label="用户长租待审核" align="center">
        <template #default="{ row }">
          <el-button
            type="text"
            v-if="row.toApplay > 0"
            @click="
              activeRouteTab({
                path: '/bizAudit/BizAudit'
              })
            "
            >{{ row.toApplay }}
          </el-button>
          <span v-else>- -</span>
        </template>
      </el-table-column>
      <el-table-column prop="new" label="本月新开通" align="center"></el-table-column>
      <el-table-column prop="over" label="本月过期未续费" align="center"></el-table-column>
    </el-table>
  </warp-card>
</template>

<script setup>
import { reactive } from 'vue';
import warpCard from './components/warpCard.vue';
import { fetchRentPay } from '@/api/home/<USER>';
import { activeRouteTab } from '@/utils/tabKit';

const rentData = reactive({
  cash: 0,
  electron: 0,
  back: 0,
  toApplay: 0,
  new: 0,
  over: 0
});

const fetchData = async (params) => {
  const { data } = await fetchRentPay(params);
  rentData.cash = data?.cash_payed_money;
  rentData.electron = data?.electronic_payed_money;
  rentData.back = data?.refund_rent_money;
  rentData.toApplay = data?.audit_rent_num;
  rentData.new = data?.activate_rent_num;
  rentData.over = data?.overdue_rent_num;
};

defineExpose({
  fetchData
});
</script>

<style scoped lang="scss"></style>
