import * as parkCarNotPayRecordApi from '@/api/statisticalReport/ParkCarNotPayRecordApi';

/**
 * 临停应收未付记录
 */
export default {
  /**
   * 分页查询临停应收未付记录
   */
  pagingParkCarNotPayRecord(data) {
    return new Promise((resolve, reject) => {
      try {
        parkCarNotPayRecordApi.pagingParkCarNotPayRecord(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  },
  /**
   * 导出
   */
  exportData(data) {
    return new Promise((resolve, reject) => {
      try {
        parkCarNotPayRecordApi.exportData(data).then(function (res) {
          resolve(res);
        });
      } catch (error) {
        reject(error);
      }
    });
  }
};
