import Mock, { mock } from 'mockjs';
import * as login from './modules/login/LoginApi';
import * as menu from './modules/system/MenuApi';

// 1. 开启/关闭[所有模块]拦截, 通过调[openMock参数]设置;
// 2. 开启/关闭[业务模块]拦截, 通过调用fnCreate方法[isOpen参数]设置;
// 3. 开启/关闭[业务模块中某个请求]拦截, 通过函数返回对象中的[isOpen属性]设置;
const openMock = false;
// 登录
// fnCreate(login, openMock);
// 菜单
fnCreate(menu, openMock);

function fnCreate(module, isOpen = true) {
  if (isOpen) {
    for (const key in module) {
      ((res) => {
        if (res.isOpen !== false) {
          let url = import.meta.env.VITE_BASE_URL;
          if (!url.endsWith('/')) {
            url = url + '/';
          }
          if (res.url !== undefined && res.url.startsWith('/')) {
            res.url = res.url.substring(1, res.url.length);
          }
          url = url + res.url;

          Mock.mock(new RegExp(url), res.type, (opts) => {
            opts.data = opts.body ? JSON.parse(opts.body) : null;
            delete opts.body;
            return res.data;
          });
        }
      })(module[key]() || {});
    }
  }
}

export default mock;
