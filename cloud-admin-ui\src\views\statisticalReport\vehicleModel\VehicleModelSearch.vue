<template>
  <FormSearch @search="handleDataSearch" @reset="handleAllReset" style="margin-top: 10px">
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.park_name" @charge="authCharge(true)" @clear="clearPark" placeholder="停车场名称" />
    </form-search-item>
    <form-search-item>
      <ClearableChargeInput v-model="form.queryParams.department_name" @charge="orgCharge(true)" @clear="clearDepartment" placeholder="组织架构" />
    </form-search-item>
    <form-search-item>
      <el-date-picker
        v-model="form.queryParams.month"
        type="month"
        style="width: 100%"
        placeholder="选择月份"
        format="YYYY-MM"
        value-format="YYYY-MM"
      />
    </form-search-item>
  </FormSearch>
  <!-- 关联车场 -->
  <el-dialog v-if="relatedParkDialogVisible" width="80%" title="关联车场" v-model="relatedParkDialogVisible">
    <park-find-back :park_id="park_id" :park_name="park_name" @authCharge="authCharge(false)" :mode="flag" @renderTableInput="renderTableInput" />
  </el-dialog>

  <!-- 关联组织架构 -->
  <el-dialog v-if="relatedOrgDialogVisible" width="80%" title="关联组织架构" v-model="relatedOrgDialogVisible">
    <org-find-back
      :organization_id="organization_id"
      :department_name="department_name"
      @orgCharge="orgCharge(false)"
      :mode="flag"
      @renderOrgTableInput="renderOrgTableInput"
    />
  </el-dialog>
</template>

<script name="VehicleModelSearch" setup>
import FormSearch from '@/components/FormSearch.vue';
import FormSearchItem from '@/components/FormSearchItem.vue';
import ClearableChargeInput from '@/components/ClearableChargeInput.vue';
import ParkFindBack from './ParkFindBack.vue';
import OrgFindBack from './OrgFindBack.vue';
import { reactive, ref, onMounted, defineExpose } from 'vue';
import { useUser } from '@/stores/user';
import { useRouter } from 'vue-router';
const router = useRouter();

const emits = defineEmits(['form-search']);
const relatedParkDialogVisible = ref(false);
const relatedOrgDialogVisible = ref(false);
const park_id = ref('');
const park_name = ref('');
const organization_id = ref('');
const department_name = ref('');
const form = reactive({
  queryParams: {
    park_id: undefined,
    park_name: undefined,
    organization_id: undefined,
    department_name: undefined,
    month: undefined
  },
  dateRange: []
});

const clearPark = () => {
  form.queryParams.park_id = undefined;
  form.queryParams.park_name = undefined;
};
const clearDepartment = () => {
  form.queryParams.organization_id = undefined;
  form.queryParams.department_name = undefined;
};

onMounted(() => {
  const user = useUser();

  if (!user.token) {
    return router.push({
      name: 'Login'
    });
  }
  // if(user.role_id == 1){
  //     return false;
  // }
  //判断user权限是否只有1个，如果只有1个，添加到删选条件中直接进行查询
  if (user.park_ids !== undefined && user.park_ids.length == 1) {
    form.queryParams.park_id = user.park_ids[0];
    form.queryParams.park_name = user.park_names[0];
  }
  const query = Object.assign(form.queryParams, {});
  emits('form-search', query);
});

const handleDataSearch = () => {
  //判断是否寻找了车场
  if (typeof form.queryParams.park_id !== 'undefined' && form.queryParams.park_id != null && form.queryParams.park_id !== '') {
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
  } else {
    const query = Object.assign(form.queryParams, {});
    emits('form-search', query);
    //判断组织架构是否选择
    // if (typeof form.queryParams.organization_id !== 'undefined' && form.queryParams.organization_id != null && form.queryParams.organization_id !== '') {
    //   const query = Object.assign(form.queryParams, {});
    //   emits('form-search', query);
    // } else {
    //   ElMessage({
    //     message: '请选择停车场或组织进行统计',
    //     type: 'warning'
    //   });
    //   return false;
    // }
  }
};
defineExpose({
  handleDataSearch
});
const handleAllReset = () => {
  form.dateRange = [];
  form.queryParams = {
    park_id: undefined,
    park_name: undefined,
    organization_id: undefined,
    department_name: undefined,
    month: undefined
  };
  emits('reset', form.queryParams);
  // handleDataSearch();
};

// 选择车场
const authCharge = (visible) => {
  if (visible === false) {
    relatedParkDialogVisible.value = false;
  } else {
    park_id.value = form.queryParams.park_id;
    park_name.value = form.queryParams.park_name;
    relatedParkDialogVisible.value = true;
  }
};
const renderTableInput = (val) => {
  form.queryParams.park_id = val[0].park_id;
  form.queryParams.park_name = val[0].park_name;
};

// 选择组织架构
const orgCharge = (visible) => {
  if (visible === false) {
    relatedOrgDialogVisible.value = false;
  } else {
    organization_id.value = form.queryParams.organization_id;
    department_name.value = form.queryParams.department_name;
    relatedOrgDialogVisible.value = true;
  }
};
const renderOrgTableInput = (val) => {
  let arrId = [];
  let arrName = [];
  for (var i = 0; i < val.length; i++) {
    arrId.push(val[i].id);
    arrName.push(val[i].label);
  }
  form.queryParams.organization_id = arrId.toString();
  form.queryParams.department_name = arrName.toString();
};
</script>
<style lang="scss" scoped></style>
