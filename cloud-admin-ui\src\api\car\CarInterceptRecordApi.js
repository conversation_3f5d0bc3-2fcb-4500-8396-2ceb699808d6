/* jshint esversion: 6 */
import $ from '@/utils/axios';

// 分页查询拦截记录
export const pagingCarInterceptRecords = (data) => {
  return $({
    url: '/console/park/car/pagingCarInterceptRecords',
    method: 'post',
    data
  });
};

// 添加拦截记录
export const createCarInterceptRecordByManual = (data) => {
  return $({
    url: '/console/park/car/createCarInterceptRecordByManual',
    method: 'post',
    data
  });
};

// 下载拦截记录模板
export const downloadInterceptRecordTemplate = () => {
  return $({
    url: '/console/park/car/downloadInterceptRecordTemplate',
    method: 'get'
  });
};

//导出拦截记录
export const exportCarInterceptRecords = (data) => {
  return $({
    url: '/console/park/car/exportCarInterceptRecords',
    method: 'post',
    data
  });
};

//删除拦截记录
export const deleteCarInterceptRecordById = (id) => {
  return $({
    url: '/console/park/car/deleteCarInterceptRecordById/' + id,
    method: 'post'
  });
};
