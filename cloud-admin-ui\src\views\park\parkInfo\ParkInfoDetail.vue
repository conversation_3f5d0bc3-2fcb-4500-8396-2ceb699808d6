<template>
  <div class="container">
    <el-form :model="form" label-width="155px">
      <el-card shadow="hover">
        <template #header>
          <div style="display: inline-block; line-height: 32px">基本信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场编号">
                {{ form.code }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场名称" prop="name">
                {{ form.name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场状态" prop="state">
                {{ form.state }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="省市区">
                {{ form.province_name + '-' + form.city_name + '-' + form.district_name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场坐标"> 经度：{{ form.longitude }} ; 纬度：{{ form.latitude }} </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场地址" prop="address">
                {{ form.address }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="停车场类型" prop="type">
                {{ form.type }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="运营方" prop="org_department_id">
                {{ form.org_department_name }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权方">
                {{ form.property_name }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="总车位数" prop="total_spaces">
                {{ form.total_spaces }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="停车场面积"> {{ form.area }} &nbsp;㎡ </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="开放时段">
                {{ form.open_start_time + '-' + form.open_end_time }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="限高">
                {{ form.limited_height }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="终端截止日期">
                {{ form.terminal_by_date }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="租赁期开始时间">
                {{ form.lease_term_start_date }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="租赁期结束时间">
                {{ form.lease_term_end_date }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="产权证号">
                {{ form.ownership_no }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="是否支持月卡一位多车">
                {{ form.more_rent_switch == 1 ? '是' : '否' }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">商户支付信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="子商户ID">
                {{ form.sub_mchid }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="子商户应用ID">
                {{ form.sub_appid }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="支付宝授权令牌">
                {{ form.app_auth_token }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="ETC商户编号">
                {{ form.etc_merchant_id }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC子商户编号">
                {{ form.etc_sub_merchant_id }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC证书密码">
                {{ form.etc_cert_pass }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="ETC商户证书">
                {{ form.etc_cert_path }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="ETC根证书">
                {{ form.etc_cert_root_path }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">微信找零商户信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="商户ID">
                {{ form.wx_mch_id }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="平台证书序列号">
                {{ form.wx_plat_cert_serial_no }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="AppId">
                {{ form.wx_appid }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="apiV3key">
                {{ form.wx_api_v3key }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="6">
              <el-form-item label="商户证书序列号">
                {{ form.wx_mch_cert_serial_no }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="app密钥">
                {{ form.wx_app_secret }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="证书文件内容">
                {{ form.wx_mch_certificate }}
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="私钥文件内容">
                {{ form.wx_mch_private_key }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">备案信息</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="备案证编号">
                {{ form.filing_no }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案车位数">
                {{ form.filing_spaces }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="备案证到期时间">
                {{ form.filing_expired_date }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="收费标准">
                <el-link @click="getFiles">{{ form.fee_stand_name }}</el-link>
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <el-card shadow="hover" style="margin-top: 10px">
        <template #header>
          <div style="display: inline-block; line-height: 32px">增值服务</div>
        </template>
        <div>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="租车服务" prop="car_rent">
                {{ form.car_rent_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="洗车服务" prop="car_wash">
                {{ form.car_wash_desc }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="是否云端值守" prop="cloud_watch">
                {{ form.cloud_watch_desc }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="8">
              <el-form-item label="充电桩快充数量">
                {{ form.fast_charge_piles }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩慢充数量">
                {{ form.slow_charge_piles }}
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="充电桩总充数量">
                {{ pile_count }}
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col :span="24">
              <el-form-item label="充电收费标准">
                {{ form.charge_fee_memo }}
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-card>
      <div class="fixed-bottom">
        <el-button @click="cancel"> 取消 </el-button>
      </div>
    </el-form>
  </div>
</template>

<script name="ParkInfoDetail" setup>
import { reactive, ref, onActivated } from 'vue';
import parkInfoService from '@/service/park/ParkInfoService';
import { closeCurrentTab } from '@/utils/tabKit';
import { useRoute } from 'vue-router';
const route = useRoute();
const pile_count = ref(0);
const feeUrl = ref('');
const params = reactive({
  parkInfoId: undefined
});
const form = reactive({});

onActivated(() => {
  if ({} !== route.query && undefined !== route.query.parkInfoId) {
    params.parkInfoId = route.query.parkInfoId;
    getParkInfo(params.parkInfoId);
  }
});

const getParkInfo = (parkInfoId) => {
  parkInfoService.getParkById(parkInfoId).then((response) => {
    form.code = response.code;
    form.name = response.name;
    form.address = response.address;
    form.province_code = response.province_code;
    form.province_name = response.province_name;
    form.city_code = response.city_code;
    form.city_name = response.city_name;
    form.district_code = response.district_code;
    form.district_name = response.district_name;
    form.state = response.state_desc;
    form.longitude = response.longitude;
    form.latitude = response.latitude;
    form.type = response.type_desc;
    form.org_department_id = response.org_department_id;
    form.org_department_name = response.org_department_name;
    form.property_id = response.property_id;
    form.property_name = response.property_name;
    form.total_spaces = response.total_spaces;
    form.area = response.area;
    form.open_start_time = response.open_start_time;
    form.open_end_time = response.open_end_time;
    form.limited_height = response.limited_height;
    form.terminal_by_date = response.terminal_by_date;
    form.wx_mch_id = response.wx_mch_id;
    form.wx_plat_cert_serial_no = response.wx_plat_cert_serial_no;
    form.wx_appid = response.wx_appid;
    form.wx_api_v3key = response.wx_api_v3key;
    form.wx_mch_cert_serial_no = response.wx_mch_cert_serial_no;
    form.wx_mch_certificate = response.wx_mch_certificate;
    form.wx_app_secret = response.wx_app_secret;
    form.wx_mch_private_key = response.wx_mch_private_key;
    form.filing_no = response.filing_no;
    form.filing_spaces = response.filing_spaces;
    form.filing_expired_date = response.filing_expired_date;
    form.car_rent = response.car_rent + '';
    form.car_wash = response.car_wash + '';
    form.cloud_watch = response.cloud_watch + '';
    form.fast_charge_piles = response.fast_charge_piles;
    form.slow_charge_piles = response.slow_charge_piles;
    form.charge_fee_memo = response.charge_fee_memo;
    form.cloud_watch_desc = response.cloud_watch_desc;
    form.car_wash_desc = response.car_wash_desc;
    form.car_rent_desc = response.car_rent_desc;
    form.fee_stand = response.fee_stand;
    form.fee_stand_name = response.fee_stand_name;
    form.sub_mchid = response.sub_mchid;
    form.sub_appid = response.sub_appid;
    form.app_auth_token = response.app_auth_token;
    form.etc_merchant_id = response.etc_merchant_id;
    form.etc_sub_merchant_id = response.etc_sub_merchant_id;
    form.etc_cert_pass = response.etc_cert_pass;
    form.etc_cert_path = response.etc_cert_path;
    form.etc_cert_root_path = response.etc_cert_root_path;
    form.lease_term_start_date = response.lease_term_start_date;
    form.lease_term_end_date = response.lease_term_end_date;
    form.ownership_no = response.ownership_no;
    form.more_rent_switch = response.more_rent_switch;
    feeUrl.value = response.fee_stand_url;
    pile_count.value = form.fast_charge_piles + form.slow_charge_piles;
  });
};

const cancel = () => {
  closeCurrentTab({
    path: '/park/parkInfo'
  });
};

const getFiles = () => {
  console.log(feeUrl.value);
  window.open(feeUrl.value, '_blank');
};
</script>

<style lang="scss" scoped>
:deep(.required .el-form-item__label::before) {
  padding-right: 5px;
  content: '*  ';
  color: #f5222d;
}
</style>
