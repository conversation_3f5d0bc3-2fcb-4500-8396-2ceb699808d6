<template>
    <el-card shadow="never" style="margin: 10px 0px">
        <template #header>
            <div style="height: 45px">
                <div style="display: flex; justify-content: space-between; height: 45px">
                    <div style="line-height: 45px">
                        <span>{{ docTypeName }}</span>
                    </div>
                    <div style="line-height: 45px"><el-button type="primary" @click="handleAdd(addForm)">立即提交</el-button></div>
                </div>
            </div>
        </template>
        <div class="opers">
            <el-form ref="addForm" label-width="120px" :rules="data.rules" :model="data.form">
                <el-form-item prop="title" label="文档标题" style="width: 30%">
                    <el-input v-model="data.form.title" maxlength="100" />
                </el-form-item>
                <el-form-item prop="content">
                    <div style="border: 1px solid #ccc">
                        <Toolbar style="border-bottom: 1px solid #ccc" :editor="editorRef" :defaultConfig="toolbarConfig" :mode="mode" />
                        <Editor
                                style="height: 500px; overflow-y: hidden"
                                v-model="data.form.content"
                                :defaultConfig="editorConfig"
                                :mode="mode"
                                @onCreated="handleCreated"
                        />
                    </div>
                </el-form-item>
                <el-form-item label="上传附件">
                    <el-space direction="vertical" style="align-items: left">
                        <span>{{ data.form.attachment_name }}</span>
                        <el-upload :limit="1" :action="uploadUrl" :headers="headers" :before-upload="beforeUpload" :on-success="onSuccessUpload">
                            <el-button type="primary">点击上传文件</el-button>
                        </el-upload>
                    </el-space>
                </el-form-item>
            </el-form>
        </div>
    </el-card>
</template>

<script name="DocumentEdit" setup>
    import '@wangeditor/editor/dist/css/style.css';
    import documentService from '@/service/system/DocumentService';
    import { ref, shallowRef, reactive, onActivated } from 'vue';
    import { Editor, Toolbar } from '@wangeditor/editor-for-vue';
    import { ElMessage } from 'element-plus';
    import { getToken } from '@/utils/common';
    import { useRoute } from 'vue-router';
    import { closeCurrentTab } from '@/utils/tabKit';
    {
        Editor, Toolbar;
    }
    // 编辑器实例，必须用 shallowRef
    const editorRef = shallowRef();
    // 内容 HTML
    const route = useRoute();
    const mode = ref('default');
    const addForm = ref();
    const docTypeName = ref('');
    const uploadUrl = ref(import.meta.env.VITE_BASE_URL + '/console/park/document/uploadDocumentData');
    const headers = reactive({
        Authorization: getToken()
    });
    const data = reactive({
        queryParams: {
            id: undefined
        },
        form: {
            id: undefined,
            title: undefined,
            content: undefined,
            doc_type_id: undefined,
            attachment_name: undefined,
            attachment_path: undefined
        },
        rules: {
            title: [
                {
                    required: true,
                    message: '请填写文档标题',
                    trigger: 'blur'
                }
            ]
        }
    });

    onActivated(() => {
        if ({} !== route.query && undefined !== route.query.id) {
            data.form.id = route.query.id;
            getDocumentById(data.form.id);
        }
        if ({} !== route.query && undefined !== route.query.docTypeName) {
            docTypeName.value = route.query.docTypeName;
        }
    });

    const toolbarConfig = {};
    const editorConfig = {
        placeholder: '请输入内容...',
        MENU_CONF: {
            // 配置上传图片
            uploadImage: {
                //server必须要配置正确
                server: import.meta.env.VITE_BASE_URL + '/console/document/uploadDocumentPic',

                maxFileSize: 4 * 1024 * 1024, // 1M
                // 最多可上传几个文件，默认为 100
                maxNumberOfFiles: 10,
                // 选择文件时的类型限制，默认为 ['image/*'] 。如不想限制，则设置为 []
                allowedFileTypes: [],
                // 自定义上传参数，例如传递验证的 token 等。参数会被添加到 formData 中，一起上传到服务端。
                fieldName: 'file',
                meta: {
                    //官网中把token放到了这里，但是请求的时候会看不到token
                },
                headers: {
                    //所以token放这里
                    Authorization: getToken()
                },
                // 将 meta 拼接到 url 参数中，默认 false
                metaWithUrl: false,
                // 跨域是否传递 cookie ，默认为 false
                withCredentials: false,
                // 超时时间，默认为 10 秒
                timeout: 5 * 1000, // 5 秒
                customInsert(res, insertFn) {
                    // JS 语法
                    // res 即服务端的返回结果
                    // 从 res 中找到 url alt href ，然后插图图片
                    insertFn(import.meta.env.VITE_BASE_URL + res.data.document_data);
                }
            }
        }
    };

    const handleCreated = (editor) => {
        editorRef.value = editor; // 记录 editor 实例，重要！
    };

    // 通过文档ID获取文档信息
    const getDocumentById = (id) => {
        documentService.getDocumentById(id).then((response) => {
            if (response.success === true) {
                data.form.id = response.data.id;
                data.form.title = response.data.title;
                data.form.content = response.data.content;
                data.form.doc_type_id = response.data.doc_type_id;
                data.form.attachment_name = response.data.attachment_name;
                data.form.attachment_path = response.data.attachment_path;
            } else {
                ElMessage({
                    message: response.message,
                    type: 'error'
                });
                return false;
            }
        });
    };

    const beforeUpload = (file) => {
        const isLt25M = file.size / 1024 / 1024 < 25;
        if (!isLt25M) {
            this.$message.error('上传文件大小不能超过 25MB!');
        }
    };
    const onSuccessUpload = (response) => {
        if (response.success == true) {
            data.form.attachment_path = response.data.document_data;
            data.form.attachment_name = response.data.document_data_name;
            ElMessage({
                message: response.message,
                type: 'success'
            });
        } else {
            ElMessage({
                message: response.message,
                type: 'error'
            });
        }
    };

    // 保存
    const handleAdd = (addForm) => {
        console.log(data.form);
        addForm.validate().then(() => {
            documentService
                .updateDocument(data.form)
                .then((response) => {
                    if (response.success === true) {
                        ElMessage({
                            message: response.message,
                            type: 'success'
                        });
                        cancelAndClose(addForm);
                    } else {
                        ElMessage({
                            message: response.detail_message != '' ? response.detail_message : response.message,
                            type: 'error'
                        });
                    }
                })
                .catch(() => {
                    cancelAndClose(addForm);
                });
        });
    };
    // 关闭页面
    const cancelAndClose = (addForm) => {
        addForm.resetFields();
        closeCurrentTab({
            path: '/system/documentCenter'
        });
    };
</script>
<style lang="scss" scoped>
  .footer {
    padding-top: 50px;
    padding-bottom: 50px;
    text-align: center;
  }
</style>
