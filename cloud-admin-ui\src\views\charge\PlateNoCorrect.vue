<template>
  <div class="container">
    <plate-no-correct-search @form-search="searchPlateNoCorrectList" @reset="resetParamsAndData" />
    <plate-no-correct-table ref="table" />
  </div>
</template>

<script setup name="PlateNoCorrect">
import PlateNoCorrectSearch from './plateNoCorrect/PlateNoCorrectSearch.vue';
import PlateNoCorrectTable from './plateNoCorrect/PlateNoCorrectTable.vue';
import { ref, reactive } from 'vue';

const table = ref(null);
const params = reactive({});

const pagePlateNoCorrect = (queryParams) => {
  table.value.getList(queryParams);
};

const searchPlateNoCorrectList = (queryParams) => {
  table.value.getList(queryParams);
};
const resetParamsAndData = () => {
  // table.value.getList(params);
};

defineExpose({
  pagePlateNoCorrect
});
</script>
